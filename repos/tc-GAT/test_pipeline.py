#!/usr/bin/env python3
"""
Test pipeline for TC-GAT package.

This script provides comprehensive testing of the TC-GAT implementation,
including unit tests, integration tests, and performance validation.
"""

import sys
import time
import traceback
import jax
import jax.numpy as jnp
import numpy as np
from flax import nnx

# Enable 64-bit precision for testing
jax.config.update("jax_enable_x64", True)

def test_imports():
    """Test that all modules can be imported correctly."""
    print("Testing imports...")
    
    try:
        # Test core imports
        from tc_gat.molecular_gat import (
            GraphAttentionLayer,
            MultiHeadGraphAttention,
            MolecularGraphAttentionNetwork
        )
        print("✓ Core GAT modules imported")
        
        # Test NetKet wrapper imports
        from tc_gat.netket_wrapper import (
            NetKetMolecularGAT,
            TranscorrelatedGAT
        )
        print("✓ NetKet wrapper modules imported")
        
        # Test utility imports
        from tc_gat.molecular_utils import (
            create_h2_adjacency,
            validate_molecular_graph
        )
        print("✓ Utility modules imported")
        
        # Test example imports
        from tc_gat.examples import example_h2_molecule
        print("✓ Example modules imported")
        
        # Test package-level imports
        import tc_gat
        print("✓ Package-level imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during import: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality of core components."""
    print("\nTesting basic functionality...")
    
    try:
        from tc_gat import NetKetMolecularGAT, create_h2_adjacency
        
        # Test adjacency matrix creation
        adjacency = create_h2_adjacency()
        assert adjacency.shape == (2, 2), f"Expected (2,2), got {adjacency.shape}"
        assert jnp.allclose(adjacency, adjacency.T), "Adjacency should be symmetric"
        print("✓ Adjacency matrix creation works")
        
        # Test model creation
        model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            molecular_adjacency=adjacency,
            rngs=nnx.Rngs(42)
        )
        print("✓ Model creation works")
        
        # Test forward pass
        test_input = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
        output = model(test_input)
        assert jnp.isfinite(output), "Output should be finite"
        print(f"✓ Forward pass works (output: {output})")
        
        # Test batch processing
        batch_input = jnp.array([
            [1, 0, 1, 0],
            [0, 1, 1, 0]
        ], dtype=jnp.float64)
        batch_output = model(batch_input)
        assert batch_output.shape == (2,), f"Expected (2,), got {batch_output.shape}"
        print("✓ Batch processing works")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False


def test_netket_integration():
    """Test integration with NetKet framework."""
    print("\nTesting NetKet integration...")
    
    try:
        import netket as nk
        from tc_gat import NetKetMolecularGAT, create_h2_adjacency
        
        # Create model
        model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        
        # Create NetKet components
        hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=4, n_fermions=2)
        sampler = nk.sampler.MetropolisExchange(hilbert, n_chains=4)
        vs = nk.vqs.MCState(sampler, model, n_samples=50)
        
        print(f"✓ Hilbert space created (size: {hilbert.size})")
        print(f"✓ Sampler created")
        print(f"✓ Variational state created (samples shape: {vs.samples.shape})")
        
        # Test sampling
        samples = vs.samples
        assert samples.shape[1] == 4, f"Expected 4 orbitals, got {samples.shape[1]}"
        print("✓ Sampling works")
        
        # Test log probability computation
        log_psi = vs.log_value(samples[:5])
        assert jnp.all(jnp.isfinite(log_psi)), "Log probabilities should be finite"
        print("✓ Log probability computation works")
        
        return True
        
    except Exception as e:
        print(f"✗ NetKet integration test failed: {e}")
        traceback.print_exc()
        return False


def test_transcorrelated_features():
    """Test transcorrelated GAT features."""
    print("\nTesting transcorrelated features...")
    
    try:
        from tc_gat import TranscorrelatedGAT, create_h2_adjacency
        
        # Create transcorrelated model
        tc_model = TranscorrelatedGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            use_jastrow=True,
            jastrow_features=4,
            cusp_regularization=0.1,
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        print("✓ Transcorrelated model creation works")
        
        # Test forward pass
        test_input = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
        tc_output = tc_model(test_input)
        assert jnp.isfinite(tc_output), "TC output should be finite"
        print(f"✓ Transcorrelated forward pass works (output: {tc_output})")
        
        # Compare with standard model
        from tc_gat import NetKetMolecularGAT
        standard_model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        
        standard_output = standard_model(test_input)
        difference = tc_output - standard_output
        print(f"✓ TC vs standard difference: {difference}")
        
        return True
        
    except Exception as e:
        print(f"✗ Transcorrelated features test failed: {e}")
        traceback.print_exc()
        return False


def test_molecular_utilities():
    """Test molecular utility functions."""
    print("\nTesting molecular utilities...")
    
    try:
        from tc_gat.molecular_utils import (
            create_h2_adjacency,
            create_water_adjacency,
            create_linear_chain_adjacency,
            validate_molecular_graph
        )
        
        # Test H2 adjacency
        h2_adj = create_h2_adjacency()
        assert validate_molecular_graph(h2_adj, 2), "H2 adjacency should be valid"
        print("✓ H2 adjacency creation and validation")
        
        # Test water adjacency
        water_adj = create_water_adjacency()
        assert validate_molecular_graph(water_adj, 3), "Water adjacency should be valid"
        print("✓ Water adjacency creation and validation")
        
        # Test linear chain
        chain_adj = create_linear_chain_adjacency(4)
        assert validate_molecular_graph(chain_adj, 4), "Chain adjacency should be valid"
        print("✓ Linear chain adjacency creation and validation")
        
        # Test position-based adjacency
        from tc_gat.molecular_utils import create_molecular_adjacency_from_positions
        positions = jnp.array([[0, 0, 0], [0, 0, 1.5], [0, 0, 3.5]])
        pos_adj = create_molecular_adjacency_from_positions(positions, cutoff=2.0)
        expected_connections = 2  # Only first two atoms should be connected
        actual_connections = jnp.sum(pos_adj)
        assert actual_connections == expected_connections, \
            f"Expected {expected_connections} connections, got {actual_connections}"
        print("✓ Position-based adjacency creation")
        
        return True
        
    except Exception as e:
        print(f"✗ Molecular utilities test failed: {e}")
        traceback.print_exc()
        return False


def test_gradient_computation():
    """Test gradient computation for optimization."""
    print("\nTesting gradient computation...")
    
    try:
        from tc_gat import NetKetMolecularGAT, create_h2_adjacency
        
        # Create model
        model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        
        # Define loss function
        def loss_fn(x):
            output = model(x)
            return jnp.sum(output**2)
        
        # Test gradient computation
        test_input = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
        
        # Compute gradients
        grad_fn = jax.grad(loss_fn)
        grads = grad_fn(test_input)
        
        assert jnp.all(jnp.isfinite(grads)), "Gradients should be finite"
        print(f"✓ Gradient computation works (grad norm: {jnp.linalg.norm(grads)})")
        
        # Test value and gradient together
        value_and_grad_fn = jax.value_and_grad(loss_fn)
        value, grads = value_and_grad_fn(test_input)
        
        assert jnp.isfinite(value), "Loss value should be finite"
        assert jnp.all(jnp.isfinite(grads)), "Gradients should be finite"
        print(f"✓ Value and gradient computation works (value: {value})")
        
        return True
        
    except Exception as e:
        print(f"✗ Gradient computation test failed: {e}")
        traceback.print_exc()
        return False


def test_performance():
    """Test performance characteristics."""
    print("\nTesting performance...")
    
    try:
        from tc_gat import NetKetMolecularGAT, create_h2_adjacency
        
        # Create model
        model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[16, 8],
            n_heads=[4, 1],
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        
        test_input = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
        
        # Warm-up
        _ = model(test_input)
        
        # Time forward pass
        n_trials = 100
        start_time = time.time()
        for _ in range(n_trials):
            output = model(test_input)
        forward_time = (time.time() - start_time) / n_trials
        
        print(f"✓ Forward pass time: {forward_time*1000:.2f} ms")
        
        # Time gradient computation
        def loss_fn(x):
            return jnp.sum(model(x)**2)
        
        grad_fn = jax.grad(loss_fn)
        
        start_time = time.time()
        for _ in range(n_trials):
            grads = grad_fn(test_input)
        grad_time = (time.time() - start_time) / n_trials
        
        print(f"✓ Gradient computation time: {grad_time*1000:.2f} ms")
        
        # Performance thresholds (adjust based on hardware)
        assert forward_time < 0.1, f"Forward pass too slow: {forward_time*1000:.2f} ms"
        assert grad_time < 0.5, f"Gradient computation too slow: {grad_time*1000:.2f} ms"
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        traceback.print_exc()
        return False


def run_comprehensive_test():
    """Run all tests in sequence."""
    print("="*60)
    print("TC-GAT COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("NetKet Integration", test_netket_integration),
        ("Transcorrelated Features", test_transcorrelated_features),
        ("Molecular Utilities", test_molecular_utilities),
        ("Gradient Computation", test_gradient_computation),
        ("Performance", test_performance),
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        print(f"Running: {test_name}")
        print(f"{'-'*40}")
        
        try:
            success = test_func()
            results[test_name] = success
            if success:
                passed_tests += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"✗ {test_name} FAILED with exception: {e}")
            traceback.print_exc()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{test_name:<25}: {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! TC-GAT is working correctly.")
        return True
    else:
        print(f"⚠️  {total_tests - passed_tests} tests failed. Check errors above.")
        return False


def quick_test():
    """Quick test for basic functionality."""
    print("Running quick test...")
    
    try:
        # Test package import
        import tc_gat
        success = tc_gat.quick_test()
        
        if success:
            print("✓ Quick test passed!")
            return True
        else:
            print("✗ Quick test failed!")
            return False
            
    except Exception as e:
        print(f"✗ Quick test failed with exception: {e}")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        success = quick_test()
    else:
        success = run_comprehensive_test()
    
    sys.exit(0 if success else 1)
