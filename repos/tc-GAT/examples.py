"""
Example implementations and test cases for the Molecular GAT.

This module provides complete examples of how to use the molecular GAT
for quantum chemistry problems, including integration with NetKet and
transcorrelated methods.
"""

import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx
import numpy as np

from netket_wrapper import NetKetMolecularGAT, TranscorrelatedGAT
from molecular_utils import (
    create_h2_adjacency,
    create_water_adjacency,
    create_methane_adjacency,
    validate_molecular_graph
)


def example_h2_molecule():
    """
    Complete example: H2 molecule with molecular GAT.
    
    This example demonstrates:
    1. Setting up the molecular system
    2. Creating the GAT model
    3. Integration with NetKet
    4. Basic variational Monte Carlo
    
    Returns:
        Tuple of (model, variational_state, results)
    """
    print("Setting up H2 molecule example...")
    
    # H2 molecule parameters
    n_atoms = 2
    n_orbitals = 4  # 2 spatial orbitals × 2 spins
    n_electrons = 2
    
    # Create molecular adjacency matrix
    molecular_adj = create_h2_adjacency()
    print(f"Molecular adjacency matrix:\n{molecular_adj}")
    
    # Validate the adjacency matrix
    is_valid = validate_molecular_graph(molecular_adj, n_atoms)
    print(f"Adjacency matrix is valid: {is_valid}")
    
    # Create GAT model
    model = NetKetMolecularGAT(
        n_atoms=n_atoms,
        n_orbitals=n_orbitals,
        hidden_features=[16, 8],  # Small for testing
        n_heads=[2, 1],
        molecular_adjacency=molecular_adj,
        dropout_rate=0.0,  # Disable dropout for deterministic testing
        rngs=nnx.Rngs(42)
    )
    
    print(f"Model created with {n_atoms} atoms and {n_orbitals} orbitals")
    
    # Test forward pass
    test_occupation = jnp.array([1, 0, 1, 0], dtype=jnp.float64)  # Ground state
    output = model(test_occupation)
    print(f"Single input output: {output}")
    
    # Test batch processing
    batch_occupation = jnp.array([
        [1, 0, 1, 0],  # Ground state
        [0, 1, 1, 0],  # Single excitation α
        [1, 0, 0, 1],  # Single excitation β
        [0, 1, 0, 1],  # Double excitation
    ], dtype=jnp.float64)
    batch_output = model(batch_occupation)
    print(f"Batch output: {batch_output}")
    
    # NetKet integration
    print("\nSetting up NetKet integration...")
    
    # Create fermionic Hilbert space
    hilbert = nk.hilbert.SpinOrbitalFermions(
        n_orbitals=n_orbitals, 
        n_fermions=n_electrons
    )
    print(f"Hilbert space size: {hilbert.size}")
    
    # Create sampler
    sampler = nk.sampler.MetropolisExchange(hilbert, n_chains=4)
    
    # Create variational state
    vs = nk.vqs.MCState(sampler, model, n_samples=100)
    print(f"Variational state created")
    print(f"Sample shape: {vs.samples.shape}")
    
    # Test sampling
    samples = vs.samples
    print(f"Sample examples:\n{samples[:5]}")
    
    # Test log probability computation
    log_psi = vs.log_value(samples[:10])
    print(f"Log probabilities: {log_psi}")
    
    results = {
        'model': model,
        'variational_state': vs,
        'hilbert': hilbert,
        'test_output': output,
        'batch_output': batch_output,
        'log_probabilities': log_psi
    }
    
    print("H2 example completed successfully!")
    return model, vs, results


def example_transcorrelated_h2():
    """
    Example: H2 molecule with transcorrelated GAT.
    
    This example demonstrates the transcorrelated extension with:
    1. Jastrow factor integration
    2. Cusp condition handling
    3. Enhanced correlation capture
    
    Returns:
        Tuple of (tc_model, variational_state, comparison_results)
    """
    print("Setting up transcorrelated H2 example...")
    
    # H2 molecule parameters
    n_atoms = 2
    n_orbitals = 4
    n_electrons = 2
    
    # Create molecular adjacency
    molecular_adj = create_h2_adjacency()
    
    # Create transcorrelated GAT model
    tc_model = TranscorrelatedGAT(
        n_atoms=n_atoms,
        n_orbitals=n_orbitals,
        hidden_features=[16, 8],
        n_heads=[2, 1],
        use_jastrow=True,
        jastrow_features=8,
        cusp_regularization=0.1,
        molecular_adjacency=molecular_adj,
        rngs=nnx.Rngs(42)
    )
    
    print("Transcorrelated GAT model created")
    
    # Compare with standard GAT
    standard_model = NetKetMolecularGAT(
        n_atoms=n_atoms,
        n_orbitals=n_orbitals,
        hidden_features=[16, 8],
        n_heads=[2, 1],
        molecular_adjacency=molecular_adj,
        rngs=nnx.Rngs(42)
    )
    
    # Test both models on same input
    test_occupation = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
    
    standard_output = standard_model(test_occupation)
    tc_output = tc_model(test_occupation)
    
    print(f"Standard GAT output: {standard_output}")
    print(f"Transcorrelated GAT output: {tc_output}")
    print(f"Difference: {tc_output - standard_output}")
    
    # NetKet integration with transcorrelated model
    hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=n_electrons)
    sampler = nk.sampler.MetropolisExchange(hilbert, n_chains=4)
    tc_vs = nk.vqs.MCState(sampler, tc_model, n_samples=100)
    
    print("Transcorrelated variational state created")
    
    comparison_results = {
        'standard_model': standard_model,
        'tc_model': tc_model,
        'standard_output': standard_output,
        'tc_output': tc_output,
        'tc_variational_state': tc_vs
    }
    
    print("Transcorrelated H2 example completed!")
    return tc_model, tc_vs, comparison_results


def example_water_molecule():
    """
    Example: Water molecule (H2O) with molecular GAT.
    
    This example demonstrates scaling to a larger molecule with:
    1. More complex molecular graph
    2. Different atomic types
    3. Non-linear molecular geometry
    
    Returns:
        Tuple of (model, variational_state, molecular_info)
    """
    print("Setting up water molecule (H2O) example...")
    
    # Water molecule parameters
    n_atoms = 3  # O, H, H
    n_orbitals = 10  # 5 spatial orbitals × 2 spins (simplified)
    n_electrons = 10  # 8 from O + 1 from each H
    
    # Create molecular adjacency matrix
    molecular_adj = create_water_adjacency()
    print(f"Water adjacency matrix:\n{molecular_adj}")
    
    # Validate adjacency
    is_valid = validate_molecular_graph(molecular_adj, n_atoms)
    print(f"Water adjacency matrix is valid: {is_valid}")
    
    # Create GAT model for water
    model = NetKetMolecularGAT(
        n_atoms=n_atoms,
        n_orbitals=n_orbitals,
        hidden_features=[32, 16, 8],  # Deeper network for larger molecule
        n_heads=[4, 2, 1],
        molecular_adjacency=molecular_adj,
        dropout_rate=0.1,
        rngs=nnx.Rngs(42)
    )
    
    print(f"Water GAT model created")
    
    # Test with water ground state configuration (simplified)
    # This is a placeholder - real water electronic structure is more complex
    water_ground_state = jnp.array([1, 1, 1, 1, 1, 0, 0, 0, 0, 0], dtype=jnp.float64)
    output = model(water_ground_state)
    print(f"Water ground state output: {output}")
    
    # Create NetKet setup
    hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=n_electrons)
    sampler = nk.sampler.MetropolisExchange(hilbert, n_chains=8)
    vs = nk.vqs.MCState(sampler, model, n_samples=200)
    
    print(f"Water variational state created")
    print(f"Hilbert space size: {hilbert.size}")
    
    molecular_info = {
        'formula': 'H2O',
        'n_atoms': n_atoms,
        'n_orbitals': n_orbitals,
        'n_electrons': n_electrons,
        'adjacency': molecular_adj,
        'ground_state_output': output
    }
    
    print("Water molecule example completed!")
    return model, vs, molecular_info


def benchmark_gat_performance():
    """
    Benchmark the performance of different GAT configurations.
    
    This function tests various architectural choices and measures:
    1. Forward pass timing
    2. Memory usage
    3. Gradient computation time
    4. Scaling with system size
    
    Returns:
        Dictionary of benchmark results
    """
    print("Running GAT performance benchmarks...")
    
    import time
    
    results = {}
    
    # Test different system sizes
    system_sizes = [
        (2, 4, 2),   # H2
        (3, 6, 4),   # Li2 or similar
        (4, 8, 6),   # Larger system
    ]
    
    for n_atoms, n_orbitals, n_electrons in system_sizes:
        print(f"\nBenchmarking system: {n_atoms} atoms, {n_orbitals} orbitals")
        
        # Create adjacency (linear chain for simplicity)
        from molecular_utils import create_linear_chain_adjacency
        adjacency = create_linear_chain_adjacency(n_atoms)
        
        # Create model
        model = NetKetMolecularGAT(
            n_atoms=n_atoms,
            n_orbitals=n_orbitals,
            hidden_features=[32, 16],
            n_heads=[4, 1],
            molecular_adjacency=adjacency,
            rngs=nnx.Rngs(42)
        )
        
        # Test input
        test_input = jnp.ones(n_orbitals, dtype=jnp.float64)
        test_input = test_input.at[n_electrons:].set(0)  # Set occupation
        
        # Warm-up
        _ = model(test_input)
        
        # Time forward pass
        n_trials = 100
        start_time = time.time()
        for _ in range(n_trials):
            output = model(test_input)
        forward_time = (time.time() - start_time) / n_trials
        
        # Time gradient computation
        def loss_fn(x):
            return jnp.sum(model(x)**2)
        
        grad_fn = jax.grad(loss_fn)
        
        start_time = time.time()
        for _ in range(n_trials):
            grads = grad_fn(test_input)
        grad_time = (time.time() - start_time) / n_trials
        
        results[f"{n_atoms}atoms"] = {
            'n_atoms': n_atoms,
            'n_orbitals': n_orbitals,
            'forward_time_ms': forward_time * 1000,
            'gradient_time_ms': grad_time * 1000,
            'output_value': float(output)
        }
        
        print(f"  Forward pass: {forward_time*1000:.2f} ms")
        print(f"  Gradient computation: {grad_time*1000:.2f} ms")
    
    print("\nBenchmark completed!")
    return results


def run_all_examples():
    """
    Run all example implementations.
    
    This function executes all the examples in sequence and provides
    a comprehensive demonstration of the molecular GAT capabilities.
    
    Returns:
        Dictionary containing all example results
    """
    print("="*60)
    print("MOLECULAR GAT EXAMPLES - COMPLETE DEMONSTRATION")
    print("="*60)
    
    all_results = {}
    
    try:
        # Example 1: Basic H2 molecule
        print("\n" + "="*40)
        print("EXAMPLE 1: H2 MOLECULE")
        print("="*40)
        h2_model, h2_vs, h2_results = example_h2_molecule()
        all_results['h2_basic'] = h2_results
        
        # Example 2: Transcorrelated H2
        print("\n" + "="*40)
        print("EXAMPLE 2: TRANSCORRELATED H2")
        print("="*40)
        tc_model, tc_vs, tc_results = example_transcorrelated_h2()
        all_results['h2_transcorrelated'] = tc_results
        
        # Example 3: Water molecule
        print("\n" + "="*40)
        print("EXAMPLE 3: WATER MOLECULE")
        print("="*40)
        water_model, water_vs, water_info = example_water_molecule()
        all_results['water'] = water_info
        
        # Example 4: Performance benchmarks
        print("\n" + "="*40)
        print("EXAMPLE 4: PERFORMANCE BENCHMARKS")
        print("="*40)
        benchmark_results = benchmark_gat_performance()
        all_results['benchmarks'] = benchmark_results
        
        print("\n" + "="*60)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        # Summary
        print("\nSUMMARY:")
        print(f"✓ H2 basic GAT: {len(h2_results)} components tested")
        print(f"✓ H2 transcorrelated: Standard vs TC comparison completed")
        print(f"✓ Water molecule: {water_info['n_atoms']} atoms, {water_info['n_orbitals']} orbitals")
        print(f"✓ Performance benchmarks: {len(benchmark_results)} system sizes tested")
        
    except Exception as e:
        print(f"\nERROR in examples: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    return all_results


if __name__ == "__main__":
    # Run all examples when script is executed directly
    results = run_all_examples()
    
    if results is not None:
        print("\nExample execution completed successfully!")
        print("Results available in the returned dictionary.")
    else:
        print("\nExample execution failed. Check error messages above.")
