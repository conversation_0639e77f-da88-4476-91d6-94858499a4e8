"""
TC-GAT: Transcorrelated Graph Attention Networks for Quantum Chemistry

This package provides a modular implementation of Graph Attention Networks (GATs)
specifically designed for quantum chemistry applications within the NetKet framework.

Key Features:
- Molecular graph representations with atoms as nodes and bonds as edges
- Integration with NetKet's variational state framework
- Support for transcorrelated quantum chemistry methods
- Modular design allowing easy extension and customization
- Comprehensive examples and utilities for quantum chemistry problems

Modules:
- molecular_gat: Core GAT implementation with Flax/Linen
- netket_wrapper: NetKet-compatible wrappers using nnx.Module
- molecular_utils: Utility functions for molecular graph construction
- examples: Complete examples and demonstrations

Usage:
    from tc_gat import NetKetMolecularGAT, create_h2_adjacency
    
    # Create H2 molecule GAT
    model = NetKetMolecularGAT(
        n_atoms=2,
        n_orbitals=4,
        hidden_features=[32, 16],
        n_heads=[4, 1],
        molecular_adjacency=create_h2_adjacency(),
        rngs=nnx.Rngs(42)
    )
    
    # Use with NetKet
    import netket as nk
    hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=4, n_fermions=2)
    vs = nk.vqs.MCState(sampler, model, n_samples=1000)
"""

# Core GAT implementation
from molecular_gat import (
    GraphAttentionLayer,
    MultiHeadGraphAttention,
    MolecularGraphAttentionNetwork
)

# NetKet integration
from netket_wrapper import (
    NetKetMolecularGAT,
    TranscorrelatedGAT
)

# Utility functions
from molecular_utils import (
    create_h2_adjacency,
    create_water_adjacency,
    create_methane_adjacency,
    create_linear_chain_adjacency,
    create_ring_adjacency,
    create_molecular_adjacency_from_positions,
    validate_molecular_graph,
    integrate_with_pyscf,
    create_transcorrelated_features
)

# Examples and demonstrations
from examples import (
    example_h2_molecule,
    example_transcorrelated_h2,
    example_water_molecule,
    benchmark_gat_performance,
    run_all_examples
)

# Version information
__version__ = "0.1.0"
__author__ = "TC-GAT Development Team"
__description__ = "Transcorrelated Graph Attention Networks for Quantum Chemistry"

# Package metadata
__all__ = [
    # Core GAT classes
    "GraphAttentionLayer",
    "MultiHeadGraphAttention", 
    "MolecularGraphAttentionNetwork",
    
    # NetKet wrappers
    "NetKetMolecularGAT",
    "TranscorrelatedGAT",
    
    # Utility functions
    "create_h2_adjacency",
    "create_water_adjacency",
    "create_methane_adjacency",
    "create_linear_chain_adjacency",
    "create_ring_adjacency",
    "create_molecular_adjacency_from_positions",
    "validate_molecular_graph",
    "integrate_with_pyscf",
    "create_transcorrelated_features",
    
    # Examples
    "example_h2_molecule",
    "example_transcorrelated_h2", 
    "example_water_molecule",
    "benchmark_gat_performance",
    "run_all_examples"
]

# Package-level configuration
import jax
import jax.numpy as jnp

# Enable 64-bit precision by default for quantum chemistry accuracy
jax.config.update("jax_enable_x64", True)

def get_package_info():
    """
    Get information about the TC-GAT package.
    
    Returns:
        Dictionary with package information
    """
    return {
        "name": "tc-gat",
        "version": __version__,
        "description": __description__,
        "author": __author__,
        "jax_version": jax.__version__,
        "precision": "64-bit" if jax.config.jax_enable_x64 else "32-bit",
        "available_modules": __all__
    }

def quick_test():
    """
    Quick test to verify package installation and basic functionality.
    
    Returns:
        True if test passes, False otherwise
    """
    try:
        # Test basic imports
        from flax import nnx
        import netket as nk
        
        # Test basic GAT creation
        model = NetKetMolecularGAT(
            n_atoms=2,
            n_orbitals=4,
            hidden_features=[8, 4],
            n_heads=[2, 1],
            molecular_adjacency=create_h2_adjacency(),
            rngs=nnx.Rngs(42)
        )
        
        # Test forward pass
        test_input = jnp.array([1, 0, 1, 0], dtype=jnp.float64)
        output = model(test_input)
        
        # Test NetKet integration
        hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=4, n_fermions=2)
        sampler = nk.sampler.MetropolisExchange(hilbert, n_chains=2)
        vs = nk.vqs.MCState(sampler, model, n_samples=10)
        
        print("✓ TC-GAT package test passed!")
        print(f"  Model output: {output}")
        print(f"  NetKet integration: OK")
        return True
        
    except Exception as e:
        print(f"✗ TC-GAT package test failed: {e}")
        return False

# Print package info when imported
print(f"TC-GAT v{__version__} - {__description__}")
print(f"JAX precision: {'64-bit' if jax.config.jax_enable_x64 else '32-bit'}")
print("Use tc_gat.quick_test() to verify installation")
print("Use tc_gat.run_all_examples() for comprehensive demonstrations")
