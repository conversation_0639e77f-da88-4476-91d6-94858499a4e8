"""
Utility functions for molecular graph construction and quantum chemistry integration.

This module provides helper functions for:
- Creating molecular adjacency matrices
- Converting between different molecular representations
- Integrating with quantum chemistry packages (PySCF)
- Handling transcorrelated coordinate transformations
"""

import jax.numpy as jnp
import numpy as np
from typing import Optional, Tuple, Dict, Any
from netket.utils.types import Array


def create_h2_adjacency() -> Array:
    """
    Create adjacency matrix for H2 molecule.
    
    Returns:
        Adjacency matrix [2, 2] with bond between the two hydrogen atoms
    """
    # Two atoms connected by a bond
    adjacency = jnp.array([[0., 1.], [1., 0.]], dtype=jnp.float64)
    return adjacency


def create_linear_chain_adjacency(n_atoms: int) -> Array:
    """
    Create adjacency matrix for linear chain of atoms.
    
    Args:
        n_atoms: Number of atoms in the chain
        
    Returns:
        Adjacency matrix [n_atoms, n_atoms] with nearest-neighbor bonds
    """
    adjacency = jnp.zeros((n_atoms, n_atoms), dtype=jnp.float64)
    for i in range(n_atoms - 1):
        adjacency = adjacency.at[i, i+1].set(1.0)
        adjacency = adjacency.at[i+1, i].set(1.0)
    return adjacency


def create_molecular_adjacency_from_positions(positions: Array, 
                                            cutoff: float = 3.0) -> Array:
    """
    Create adjacency matrix from atomic positions using distance cutoff.
    
    Args:
        positions: Atomic positions [n_atoms, 3] in Angstroms
        cutoff: Distance cutoff for bond detection in Angstroms
        
    Returns:
        Adjacency matrix [n_atoms, n_atoms]
    """
    n_atoms = positions.shape[0]
    distances = jnp.linalg.norm(
        positions[:, None, :] - positions[None, :, :], axis=-1
    )
    adjacency = (distances < cutoff) & (distances > 0.1)  # Avoid self-connections
    return adjacency.astype(jnp.float64)


def create_ring_adjacency(n_atoms: int) -> Array:
    """
    Create adjacency matrix for ring molecule (e.g., benzene, cyclic alkanes).
    
    Args:
        n_atoms: Number of atoms in the ring
        
    Returns:
        Adjacency matrix [n_atoms, n_atoms] with ring connectivity
    """
    adjacency = jnp.zeros((n_atoms, n_atoms), dtype=jnp.float64)
    for i in range(n_atoms):
        next_atom = (i + 1) % n_atoms
        adjacency = adjacency.at[i, next_atom].set(1.0)
        adjacency = adjacency.at[next_atom, i].set(1.0)
    return adjacency


def create_water_adjacency() -> Array:
    """
    Create adjacency matrix for water molecule (H2O).
    
    Returns:
        Adjacency matrix [3, 3] with O-H bonds
        Atom order: [O, H1, H2]
    """
    adjacency = jnp.array([
        [0., 1., 1.],  # Oxygen connected to both hydrogens
        [1., 0., 0.],  # H1 connected only to oxygen
        [1., 0., 0.]   # H2 connected only to oxygen
    ], dtype=jnp.float64)
    return adjacency


def create_methane_adjacency() -> Array:
    """
    Create adjacency matrix for methane molecule (CH4).
    
    Returns:
        Adjacency matrix [5, 5] with C-H bonds
        Atom order: [C, H1, H2, H3, H4]
    """
    adjacency = jnp.zeros((5, 5), dtype=jnp.float64)
    # Carbon (atom 0) connected to all hydrogens (atoms 1-4)
    for i in range(1, 5):
        adjacency = adjacency.at[0, i].set(1.0)
        adjacency = adjacency.at[i, 0].set(1.0)
    return adjacency


def orbital_localization_mulliken(occupation_vector: Array, 
                                overlap_matrix: Array,
                                mo_coefficients: Array) -> Array:
    """
    Perform Mulliken population analysis to localize orbitals on atoms.
    
    This function converts molecular orbital occupations to atomic populations
    using Mulliken analysis: P_A = Σ_μ∈A Σ_ν P_μν S_μν
    
    Args:
        occupation_vector: Orbital occupation numbers [n_orbitals]
        overlap_matrix: Atomic orbital overlap matrix [n_ao, n_ao]
        mo_coefficients: MO coefficients [n_ao, n_mo]
        
    Returns:
        Atomic populations [n_atoms]
        
    Note:
        This requires knowledge of which atomic orbitals belong to which atoms.
        In practice, this information comes from the quantum chemistry calculation.
    """
    # This is a simplified implementation
    # In practice, you need the atomic orbital to atom mapping
    n_orbitals = len(occupation_vector)
    n_atoms = mo_coefficients.shape[0] // 2  # Simplified assumption
    
    # Simple redistribution (placeholder)
    orbitals_per_atom = n_orbitals // n_atoms
    atomic_populations = jnp.sum(
        occupation_vector.reshape(-1, orbitals_per_atom), axis=1
    )
    
    return atomic_populations


def create_transcorrelated_features(occupation_vector: Array,
                                  atomic_positions: Array,
                                  nuclear_charges: Array) -> Dict[str, Array]:
    """
    Create features for transcorrelated methods.
    
    This function generates additional features that can help the GAT
    learn transcorrelated effects:
    - Electron-electron distances
    - Electron-nuclear cusps
    - Correlation hole features
    
    Args:
        occupation_vector: Orbital occupation [n_orbitals]
        atomic_positions: Nuclear positions [n_atoms, 3]
        nuclear_charges: Nuclear charges [n_atoms]
        
    Returns:
        Dictionary of transcorrelated features
    """
    n_atoms = len(nuclear_charges)
    n_orbitals = len(occupation_vector)
    
    features = {}
    
    # Nuclear charge features (for cusp conditions)
    features['nuclear_charges'] = nuclear_charges
    
    # Internuclear distances (for correlation effects)
    distances = jnp.linalg.norm(
        atomic_positions[:, None, :] - atomic_positions[None, :, :], axis=-1
    )
    features['internuclear_distances'] = distances
    
    # Electron density proxy (from orbital occupations)
    # This is simplified - in practice would use proper density
    electron_density = jnp.sum(occupation_vector) / n_atoms
    features['electron_density'] = jnp.full(n_atoms, electron_density)
    
    # Correlation length scale (characteristic distance for correlations)
    avg_bond_length = jnp.mean(distances[distances > 0])
    features['correlation_length'] = avg_bond_length
    
    return features


def integrate_with_pyscf(mol, mf_result) -> Tuple[Array, Array, Dict[str, Any]]:
    """
    Extract molecular information from PySCF calculation.
    
    Args:
        mol: PySCF molecule object
        mf_result: PySCF mean-field calculation result
        
    Returns:
        Tuple of (adjacency_matrix, atomic_features, molecular_info)
    """
    try:
        import pyscf
    except ImportError:
        raise ImportError("PySCF is required for quantum chemistry integration")
    
    # Extract basic molecular information
    n_atoms = mol.natm
    atomic_charges = mol.atom_charges()
    atomic_coords = mol.atom_coords()
    
    # Create adjacency matrix from coordinates
    adjacency = create_molecular_adjacency_from_positions(atomic_coords, cutoff=3.0)
    
    # Extract atomic features
    atomic_features = jnp.column_stack([
        atomic_charges,  # Nuclear charges
        jnp.sum(atomic_coords**2, axis=1),  # Radial positions
    ])
    
    # Molecular information
    molecular_info = {
        'n_atoms': n_atoms,
        'n_electrons': mol.nelectron,
        'n_orbitals': mf_result.mo_coeff.shape[1],
        'nuclear_charges': atomic_charges,
        'atomic_positions': atomic_coords,
        'mo_coefficients': mf_result.mo_coeff,
        'mo_energies': mf_result.mo_energy,
        'overlap_matrix': mf_result.get_ovlp(),
        'hf_energy': mf_result.e_tot
    }
    
    return adjacency, atomic_features, molecular_info


def create_molecular_graph_from_smiles(smiles: str) -> Tuple[Array, Array]:
    """
    Create molecular graph from SMILES string.
    
    Args:
        smiles: SMILES representation of the molecule
        
    Returns:
        Tuple of (adjacency_matrix, atomic_features)
        
    Note:
        This requires RDKit for SMILES parsing. This is a placeholder
        implementation that would need to be completed with proper
        chemical informatics tools.
    """
    # Placeholder implementation
    # In practice, would use RDKit to parse SMILES and extract:
    # - Atomic connectivity (bonds)
    # - Atomic types and properties
    # - 3D coordinates (if available)
    
    raise NotImplementedError(
        "SMILES parsing requires RDKit integration. "
        "Use create_molecular_adjacency_from_positions for now."
    )


def validate_molecular_graph(adjacency: Array, 
                           n_atoms: int,
                           check_symmetry: bool = True,
                           check_connectivity: bool = True) -> bool:
    """
    Validate molecular adjacency matrix.
    
    Args:
        adjacency: Adjacency matrix [n_atoms, n_atoms]
        n_atoms: Expected number of atoms
        check_symmetry: Whether to check matrix symmetry
        check_connectivity: Whether to check graph connectivity
        
    Returns:
        True if valid, False otherwise
    """
    # Check dimensions
    if adjacency.shape != (n_atoms, n_atoms):
        return False
    
    # Check diagonal is zero (no self-loops)
    if not jnp.allclose(jnp.diag(adjacency), 0):
        return False
    
    # Check symmetry
    if check_symmetry and not jnp.allclose(adjacency, adjacency.T):
        return False
    
    # Check binary values
    if not jnp.all((adjacency == 0) | (adjacency == 1)):
        return False
    
    # Check connectivity (optional)
    if check_connectivity:
        # Simple connectivity check: ensure graph is connected
        # This is a simplified check - full implementation would use
        # graph traversal algorithms
        total_edges = jnp.sum(adjacency) / 2  # Divide by 2 for undirected graph
        if total_edges < n_atoms - 1:  # Minimum edges for connectivity
            return False
    
    return True
