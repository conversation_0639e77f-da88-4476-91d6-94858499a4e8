"""
Molecular Graph Attention Network (GAT) for NetKet

This module implements a Graph Attention Network specifically designed for quantum chemistry
applications within the NetKet framework. It handles molecular graph representations where
atoms are nodes and bonds are edges, supporting fermionic Hilbert spaces and transcorrelated
quantum chemistry calculations.

Mathematical Foundation:
The attention mechanism computes attention coefficients α_ij between nodes i and j as:

    e_ij = LeakyReLU(a^T [W h_i || W h_j])
    α_ij = softmax_j(e_ij)
    h'_i = σ(Σ_j α_ij W h_j)

Where:
- W: learnable linear transformation matrix
- a: learnable attention vector
- h_i: input features for node i
- ||: concatenation operation
- σ: activation function (ELU by default)

For molecular systems:
- Nodes represent atoms with features derived from orbital occupations
- Edges represent chemical bonds and spatial proximity
- Attention weights capture electron correlation effects
"""

import jax
import jax.numpy as jnp
from jax import random
from flax import nnx
from typing import Sequence, Optional, Callable, Any
import numpy as np

from netket.utils.types import Array, DType


class GraphAttentionLayer(nnx.Module):
    """
    Single Graph Attention Layer implementing the attention mechanism using nnx.

    This layer computes attention weights between connected nodes and aggregates
    features based on these weights. For molecular systems, this captures how
    electrons on different atoms influence each other.

    Mathematical Details:
    For each node i, the output is computed as:
        h'_i = σ(Σ_{j∈N(i)} α_ij W h_j)

    Where α_ij are attention coefficients computed as:
        e_ij = a^T [W h_i || W h_j]
        α_ij = softmax_j(e_ij) for j ∈ N(i)

    Attributes:
        out_features: Number of output features per node
        dropout_rate: Dropout probability for regularization
        use_bias: Whether to include bias terms
        activation: Activation function (default: ELU)
        param_dtype: Data type for parameters
    """

    def __init__(self,
                 in_features: int,
                 out_features: int,
                 dropout_rate: float = 0.1,
                 use_bias: bool = True,
                 activation: Callable = nnx.elu,
                 param_dtype: DType = jnp.float64,
                 *,
                 rngs: nnx.Rngs):
        """
        Initialize the Graph Attention Layer.

        Args:
            in_features: Number of input features per node
            out_features: Number of output features per node
            dropout_rate: Dropout probability for regularization
            use_bias: Whether to include bias terms
            activation: Activation function
            param_dtype: Data type for parameters
            rngs: Random number generator state
        """
        self.out_features = out_features
        self.dropout_rate = dropout_rate
        self.use_bias = use_bias
        self.activation = activation
        self.param_dtype = param_dtype

        # Linear transformation layer
        self.linear = nnx.Linear(
            in_features=in_features,
            out_features=out_features,
            use_bias=use_bias,
            dtype=param_dtype,
            rngs=rngs
        )

        # Attention mechanism parameters
        key1, key2 = jax.random.split(rngs.params())
        self.attention_self = nnx.Param(
            jax.random.normal(key1, (out_features, 1), dtype=param_dtype) * 0.1
        )
        self.attention_neighbor = nnx.Param(
            jax.random.normal(key2, (out_features, 1), dtype=param_dtype) * 0.1
        )

        # Dropout layer
        if dropout_rate > 0:
            self.dropout = nnx.Dropout(rate=dropout_rate, rngs=rngs)

    def __call__(self,
                 node_features: Array,
                 adjacency_matrix: Array,
                 training: bool = False,
                 **kwargs) -> Array:
        """
        Apply graph attention to node features.

        Args:
            node_features: Input node features [n_nodes, in_features]
            adjacency_matrix: Graph adjacency matrix [n_nodes, n_nodes]
                             1.0 for connected nodes, 0.0 otherwise
            training: Whether in training mode (affects dropout)

        Returns:
            Updated node features [n_nodes, out_features]
        """
        # Linear transformation: W * h
        transformed_features = self.linear(node_features)

        # Compute attention logits: a1^T * W*h_i + a2^T * W*h_j
        self_attention = jnp.dot(transformed_features, self.attention_self.value)  # [n_nodes, 1]
        neighbor_attention = jnp.dot(transformed_features, self.attention_neighbor.value)  # [n_nodes, 1]

        # Broadcast to create pairwise attention logits
        attention_logits = self_attention + neighbor_attention.T  # [n_nodes, n_nodes]
        attention_logits = nnx.leaky_relu(attention_logits, negative_slope=0.2)

        # Mask attention for non-connected nodes
        masked_logits = jnp.where(adjacency_matrix > 0,
                                 attention_logits,
                                 -1e9)

        # Compute attention coefficients
        attention_weights = nnx.softmax(masked_logits, axis=1)

        # Apply dropout to attention weights and features if training
        if training and hasattr(self, 'dropout'):
            attention_weights = self.dropout(attention_weights)
            transformed_features = self.dropout(transformed_features)

        # Aggregate features using attention weights
        output_features = jnp.dot(attention_weights, transformed_features)

        return self.activation(output_features)


class MultiHeadGraphAttention(nnx.Module):
    """
    Multi-head Graph Attention Layer using nnx.

    Implements multiple attention heads in parallel to capture different types
    of relationships in the molecular graph. For quantum chemistry, different
    heads can focus on different aspects like σ-bonds, π-bonds, or long-range
    correlations.

    Mathematical Details:
    For H attention heads, the output is either:
    - Concatenation: h' = ||_{h=1}^H head_h(x)  (for hidden layers)
    - Average: h' = (1/H) Σ_{h=1}^H head_h(x)  (for output layer)

    Attributes:
        n_heads: Number of attention heads
        out_features: Output features per head
        dropout_rate: Dropout probability
        is_output_layer: Whether this is the final layer (uses averaging)
        activation: Activation function
        param_dtype: Data type for parameters
    """

    def __init__(self,
                 in_features: int,
                 n_heads: int,
                 out_features: int,
                 dropout_rate: float = 0.1,
                 is_output_layer: bool = False,
                 activation: Callable = nnx.elu,
                 param_dtype: DType = jnp.float64,
                 *,
                 rngs: nnx.Rngs):
        """
        Initialize the Multi-head Graph Attention Layer.

        Args:
            in_features: Number of input features per node
            n_heads: Number of attention heads
            out_features: Output features per head
            dropout_rate: Dropout probability
            is_output_layer: Whether this is the final layer (uses averaging)
            activation: Activation function
            param_dtype: Data type for parameters
            rngs: Random number generator state
        """
        self.n_heads = n_heads
        self.out_features = out_features
        self.is_output_layer = is_output_layer
        self.activation = activation

        # Create attention heads
        self.heads = []
        for head_idx in range(n_heads):
            head_rngs = nnx.Rngs(jax.random.fold_in(rngs.params(), head_idx))
            head = GraphAttentionLayer(
                in_features=in_features,
                out_features=out_features,
                dropout_rate=dropout_rate,
                activation=activation,
                param_dtype=param_dtype,
                rngs=head_rngs
            )
            self.heads.append(head)

    def __call__(self,
                 node_features: Array,
                 adjacency_matrix: Array,
                 training: bool = False,
                 **kwargs) -> Array:
        """
        Apply multi-head attention to node features.

        Args:
            node_features: Input node features [n_nodes, in_features]
            adjacency_matrix: Graph adjacency matrix [n_nodes, n_nodes]
            training: Whether in training mode

        Returns:
            Updated node features [n_nodes, out_features * n_heads] or
            [n_nodes, out_features] if is_output_layer=True
        """
        head_outputs = []

        for head in self.heads:
            head_output = head(node_features, adjacency_matrix, training=training)
            head_outputs.append(head_output)

        if self.is_output_layer:
            # Average the heads for output layer
            return jnp.mean(jnp.stack(head_outputs, axis=0), axis=0)
        else:
            # Concatenate heads for hidden layers
            return jnp.concatenate(head_outputs, axis=-1)


class MolecularGraphAttentionNetwork(nnx.Module):
    """
    Complete Graph Attention Network for molecular quantum states using nnx.

    This network processes molecular graphs where atoms are nodes and bonds are edges.
    It's designed to work with NetKet's variational state framework for quantum chemistry
    applications, particularly transcorrelated methods.

    Architecture:
    1. Input embedding: Maps orbital occupations to atomic features
    2. GAT layers: Multiple graph attention layers with residual connections
    3. Output projection: Aggregates to single scalar output for NetKet compatibility

    For Transcorrelated Methods:
    - Can incorporate Jastrow factors through attention weights
    - Handles cusp conditions via specialized attention patterns
    - Supports basis set convergence acceleration
    """

    def __init__(self,
                 n_atoms: int,
                 n_orbitals: int,
                 hidden_features: Sequence[int] = (64, 32, 16),
                 n_heads: Sequence[int] = (4, 4, 1),
                 dropout_rate: float = 0.1,
                 activation: Callable = nnx.elu,
                 use_residual: bool = True,
                 param_dtype: DType = jnp.float64,
                 *,
                 rngs: nnx.Rngs):
        """
        Initialize the Molecular Graph Attention Network.

        Args:
            n_atoms: Number of atoms in the molecule
            n_orbitals: Number of molecular orbitals (spin orbitals)
            hidden_features: Feature dimensions for each GAT layer
            n_heads: Number of attention heads for each layer
            dropout_rate: Dropout probability for regularization
            activation: Activation function between layers
            use_residual: Whether to use residual connections
            param_dtype: Data type for parameters
            rngs: Random number generator state
        """
        self.n_atoms = n_atoms
        self.n_orbitals = n_orbitals
        self.use_residual = use_residual
        self.activation = activation

        assert len(hidden_features) == len(n_heads), \
            "Number of hidden features must match number of head specifications"

        # Calculate input features per atom (simplified orbital distribution)
        orbitals_per_atom = n_orbitals // n_atoms
        input_features = orbitals_per_atom if n_orbitals % n_atoms == 0 else orbitals_per_atom + 1

        # Input embedding layer
        key1, remaining_key = jax.random.split(rngs.params())
        self.input_embedding = nnx.Linear(
            in_features=input_features,
            out_features=hidden_features[0],
            dtype=param_dtype,
            rngs=nnx.Rngs(key1)
        )

        # GAT layers
        self.gat_layers = []
        current_features = hidden_features[0]

        for i, (features, heads) in enumerate(zip(hidden_features, n_heads)):
            is_output = (i == len(hidden_features) - 1)
            layer_key, remaining_key = jax.random.split(remaining_key)

            layer = MultiHeadGraphAttention(
                in_features=current_features,
                n_heads=heads,
                out_features=features,
                dropout_rate=dropout_rate,
                is_output_layer=is_output,
                activation=activation,
                param_dtype=param_dtype,
                rngs=nnx.Rngs(layer_key)
            )
            self.gat_layers.append(layer)
            current_features = features if is_output else features * heads

        # Output projection to scalar (required for NetKet)
        self.output_projection = nnx.Linear(
            in_features=hidden_features[-1],
            out_features=1,
            dtype=param_dtype,
            rngs=nnx.Rngs(remaining_key)
        )

    def occupation_to_atomic_features(self, occupation_vector: Array) -> Array:
        """
        Convert orbital occupation vector to atomic-centered features.

        This function maps the occupation of molecular orbitals to features
        associated with each atom. The mapping depends on the molecular orbital
        localization scheme used.

        Args:
            occupation_vector: Binary occupation vector [n_orbitals]
                              1 for occupied orbitals, 0 for unoccupied

        Returns:
            Atomic features [n_atoms, n_orbitals]

        Note:
            This is a simplified implementation. In practice, you would use:
            - Mulliken population analysis
            - Löwdin population analysis
            - Localized molecular orbitals (Boys, Pipek-Mezey)
            - Natural bond orbitals (NBO)
        """
        # Simple approach: distribute orbital occupations equally among atoms
        # This should be replaced with proper orbital localization
        orbitals_per_atom = self.n_orbitals // self.n_atoms

        # Reshape occupation vector to atomic contributions
        if self.n_orbitals % self.n_atoms == 0:
            atomic_features = occupation_vector.reshape(self.n_atoms, orbitals_per_atom)
        else:
            # Handle case where orbitals don't divide evenly
            padded_occupation = jnp.pad(occupation_vector,
                                      (0, self.n_atoms * orbitals_per_atom - self.n_orbitals))
            atomic_features = padded_occupation.reshape(self.n_atoms, orbitals_per_atom)

        return atomic_features

    def create_molecular_adjacency(self,
                                 atomic_positions: Optional[Array] = None,
                                 bond_matrix: Optional[Array] = None,
                                 cutoff_distance: float = 3.0) -> Array:
        """
        Create adjacency matrix for the molecular graph.

        Args:
            atomic_positions: 3D coordinates of atoms [n_atoms, 3]
            bond_matrix: Explicit bond connectivity [n_atoms, n_atoms]
            cutoff_distance: Distance cutoff for automatic bond detection

        Returns:
            Adjacency matrix [n_atoms, n_atoms]
        """
        if bond_matrix is not None:
            return bond_matrix

        if atomic_positions is not None:
            # Compute pairwise distances
            distances = jnp.linalg.norm(
                atomic_positions[:, None, :] - atomic_positions[None, :, :],
                axis=-1
            )
            # Create adjacency based on distance cutoff
            adjacency = (distances < cutoff_distance) & (distances > 0)
            return adjacency.astype(self.param_dtype)

        # Default: fully connected graph
        adjacency = jnp.ones((self.n_atoms, self.n_atoms), dtype=self.param_dtype)
        adjacency = adjacency.at[jnp.diag_indices(self.n_atoms)].set(0)
        return adjacency

    def __call__(self,
                 occupation_vector: Array,
                 molecular_adjacency: Optional[Array] = None,
                 training: bool = False) -> Array:
        """
        Forward pass through the molecular GAT.

        Args:
            occupation_vector: Orbital occupation vector [n_orbitals] or [batch, n_orbitals] or [batch1, batch2, n_orbitals]
            molecular_adjacency: Adjacency matrix [n_atoms, n_atoms]
            training: Whether in training mode

        Returns:
            Scalar output for each configuration [batch] or scalar
        """
        original_shape = occupation_vector.shape

        # Flatten to 2D: [total_batch, n_orbitals]
        if occupation_vector.ndim == 1:
            occupation_vector = jnp.expand_dims(occupation_vector, 0)
            squeeze_output = True
        elif occupation_vector.ndim > 2:
            # Flatten all batch dimensions
            batch_shape = original_shape[:-1]
            occupation_vector = occupation_vector.reshape(-1, original_shape[-1])
            squeeze_output = False
        else:
            squeeze_output = False
            batch_shape = original_shape[:-1]

        batch_size = occupation_vector.shape[0]

        # Create default adjacency if not provided
        if molecular_adjacency is None:
            molecular_adjacency = self.create_molecular_adjacency()

        # Vectorized processing
        def process_single(occupation):
            # Convert occupation to atomic features
            atomic_features = self.occupation_to_atomic_features(occupation)

            # Input embedding
            x = self.input_embedding(atomic_features)

            # Apply GAT layers with optional residual connections
            for layer_idx, gat_layer in enumerate(self.gat_layers):
                if (self.use_residual and layer_idx > 0 and
                    x.shape[-1] == gat_layer.out_features):
                    # Residual connection
                    residual = x
                    x = gat_layer(x, molecular_adjacency, training=training)
                    x = x + residual
                else:
                    x = gat_layer(x, molecular_adjacency, training=training)

            # Global pooling: aggregate atomic features to molecular feature
            molecular_feature = jnp.mean(x, axis=0)  # Simple mean pooling

            # Project to scalar output
            output = self.output_projection(molecular_feature)
            return output.squeeze()

        # Use vmap for efficient batch processing
        result = jax.vmap(process_single)(occupation_vector)

        # Reshape back to original batch shape if needed
        if occupation_vector.ndim > 2 and not squeeze_output:
            result = result.reshape(batch_shape)
        elif squeeze_output:
            result = jnp.squeeze(result)

        return result
