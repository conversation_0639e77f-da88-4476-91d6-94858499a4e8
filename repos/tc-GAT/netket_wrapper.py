"""
NetKet-compatible wrapper for Molecular Graph Attention Network.

This module provides the NetKet integration layer following the pattern established
in benchmark.py, using nnx.Module and nnx.bridge.ToNNX for compatibility with
NetKet's variational state framework.
"""

import jax
import jax.numpy as jnp
from flax import nnx
from typing import Sequence, Optional
from netket.utils.types import Array, DType

from molecular_gat import MolecularGraphAttentionNetwork


class NetKetMolecularGAT(nnx.Module):
    """
    NetKet-compatible wrapper for Molecular Graph Attention Network.
    
    This class follows the pattern established in benchmark.py, using nnx.Module
    and nnx.bridge.ToNNX for compatibility with NetKet's variational state framework.
    
    Usage Example:
        # Create molecular system
        n_atoms = 2  # H2 molecule
        n_orbitals = 4  # 2 spatial orbitals × 2 spins
        
        # Initialize model
        model = NetKetMolecularGAT(
            n_atoms=n_atoms,
            n_orbitals=n_orbitals,
            hidden_features=[32, 16],
            n_heads=[4, 1],
            rngs=nnx.Rngs(42)
        )
        
        # Use with NetKet
        hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=2)
        sampler = nk.sampler.MetropolisExchange(hilbert)
        vs = nk.vqs.MCState(sampler, model, n_samples=1000)
    
    Attributes:
        n_atoms: Number of atoms in the molecule
        n_orbitals: Number of molecular orbitals
        hidden_features: Feature dimensions for GAT layers
        n_heads: Number of attention heads per layer
        dropout_rate: Dropout probability
        molecular_adjacency: Optional fixed adjacency matrix
        param_dtype: Parameter data type
    """
    
    def __init__(self,
                 n_atoms: int,
                 n_orbitals: int,
                 hidden_features: Sequence[int] = (64, 32, 16),
                 n_heads: Sequence[int] = (4, 4, 1),
                 dropout_rate: float = 0.1,
                 molecular_adjacency: Optional[Array] = None,
                 param_dtype: DType = jnp.float64,
                 *,
                 rngs: nnx.Rngs):
        """
        Initialize the NetKet-compatible molecular GAT.

        Args:
            n_atoms: Number of atoms in the molecule
            n_orbitals: Number of molecular orbitals (spin orbitals)
            hidden_features: Feature dimensions for each GAT layer
            n_heads: Number of attention heads for each layer
            dropout_rate: Dropout probability for regularization
            molecular_adjacency: Optional fixed adjacency matrix
            param_dtype: Data type for parameters
            rngs: Random number generator state
        """
        self.n_atoms = n_atoms
        self.n_orbitals = n_orbitals
        self.molecular_adjacency = molecular_adjacency

        # Create the nnx GAT model directly (no bridging needed)
        self.gat_model = MolecularGraphAttentionNetwork(
            n_atoms=n_atoms,
            n_orbitals=n_orbitals,
            hidden_features=hidden_features,
            n_heads=n_heads,
            dropout_rate=dropout_rate,
            param_dtype=param_dtype,
            rngs=rngs
        )
    
    def __call__(self, x: jax.Array, **kwargs) -> jax.Array:
        """
        Forward pass compatible with NetKet's expectations.
        
        Args:
            x: Occupation vector(s) [batch, n_orbitals] or [n_orbitals]
            
        Returns:
            Scalar output(s) [batch] or scalar
        """
        return self.gat_model(
            x, 
            molecular_adjacency=self.molecular_adjacency,
            **kwargs
        )


class TranscorrelatedGAT(nnx.Module):
    """
    Extended GAT with transcorrelated quantum chemistry features.
    
    This class extends the basic molecular GAT with features specifically
    designed for transcorrelated methods:
    - Jastrow factor integration
    - Cusp condition handling
    - Basis set convergence acceleration
    
    Mathematical Foundation:
    The transcorrelated wavefunction has the form:
        |Ψ_TC⟩ = exp(J) |Ψ⟩
    
    Where J is the Jastrow factor capturing electron correlation.
    The GAT can learn to approximate both the determinantal part |Ψ⟩
    and incorporate correlation effects through attention weights.
    
    Attributes:
        n_atoms: Number of atoms in the molecule
        n_orbitals: Number of molecular orbitals
        hidden_features: Feature dimensions for GAT layers
        n_heads: Number of attention heads per layer
        use_jastrow: Whether to include Jastrow factor features
        jastrow_features: Number of features for Jastrow factor
        cusp_regularization: Strength of cusp condition regularization
        param_dtype: Parameter data type
    """
    
    def __init__(self,
                 n_atoms: int,
                 n_orbitals: int,
                 hidden_features: Sequence[int] = (64, 32, 16),
                 n_heads: Sequence[int] = (4, 4, 1),
                 dropout_rate: float = 0.1,
                 use_jastrow: bool = True,
                 jastrow_features: int = 16,
                 cusp_regularization: float = 0.1,
                 molecular_adjacency: Optional[Array] = None,
                 param_dtype: DType = jnp.float64,
                 *,
                 rngs: nnx.Rngs):
        """
        Initialize the transcorrelated GAT.
        
        Args:
            n_atoms: Number of atoms in the molecule
            n_orbitals: Number of molecular orbitals (spin orbitals)
            hidden_features: Feature dimensions for each GAT layer
            n_heads: Number of attention heads for each layer
            dropout_rate: Dropout probability for regularization
            use_jastrow: Whether to include Jastrow factor features
            jastrow_features: Number of features for Jastrow factor
            cusp_regularization: Strength of cusp condition regularization
            molecular_adjacency: Optional fixed adjacency matrix
            param_dtype: Data type for parameters
            rngs: Random number generator state
        """
        self.n_atoms = n_atoms
        self.n_orbitals = n_orbitals
        self.use_jastrow = use_jastrow
        self.molecular_adjacency = molecular_adjacency
        
        # Create base GAT model
        key1, key2 = jax.random.split(rngs.params(), 2)
        self.base_gat = MolecularGraphAttentionNetwork(
            n_atoms=n_atoms,
            n_orbitals=n_orbitals,
            hidden_features=hidden_features,
            n_heads=n_heads,
            dropout_rate=dropout_rate,
            param_dtype=param_dtype,
            rngs=nnx.Rngs(key1)
        )

        # Jastrow factor network (if enabled)
        if use_jastrow:
            # Create simple MLP for Jastrow factor using nnx
            key2a, key2b, key2c = jax.random.split(key2, 3)
            self.jastrow_net = nnx.Sequential(
                nnx.Linear(n_orbitals, jastrow_features, dtype=param_dtype, rngs=nnx.Rngs(key2a)),
                nnx.tanh,
                nnx.Linear(jastrow_features, jastrow_features // 2, dtype=param_dtype, rngs=nnx.Rngs(key2b)),
                nnx.tanh,
                nnx.Linear(jastrow_features // 2, 1, dtype=param_dtype, rngs=nnx.Rngs(key2c))
            )
        
        # Cusp regularization parameter
        self.cusp_strength = nnx.Param(jnp.array(cusp_regularization, dtype=param_dtype))
    
    def __call__(self, x: jax.Array, **kwargs) -> jax.Array:
        """
        Forward pass with transcorrelated features.

        Args:
            x: Occupation vector(s) [batch, n_orbitals] or [n_orbitals]

        Returns:
            Transcorrelated wavefunction amplitude [batch] or scalar
        """
        # Base GAT output (determinantal part)
        base_output = self.base_gat(x, molecular_adjacency=self.molecular_adjacency, **kwargs)

        if self.use_jastrow:
            # Process Jastrow factor with same batching as base GAT
            original_shape = x.shape

            # Flatten to 2D for Jastrow processing
            if x.ndim == 1:
                x_flat = jnp.expand_dims(x, 0)
                squeeze_jastrow = True
            elif x.ndim > 2:
                batch_shape = original_shape[:-1]
                x_flat = x.reshape(-1, original_shape[-1])
                squeeze_jastrow = False
            else:
                x_flat = x
                squeeze_jastrow = False
                batch_shape = original_shape[:-1]

            # Apply Jastrow network to each configuration
            def jastrow_single(config):
                return self.jastrow_net(config).squeeze()

            jastrow_output = jax.vmap(jastrow_single)(x_flat)

            # Reshape back to match base output
            if x.ndim > 2 and not squeeze_jastrow:
                jastrow_output = jastrow_output.reshape(batch_shape)
            elif squeeze_jastrow:
                jastrow_output = jnp.squeeze(jastrow_output)

            # Combine: Ψ_TC = exp(J) * Ψ_det ≈ Ψ_det * (1 + J) for small J
            # Using log representation: log(Ψ_TC) = log(Ψ_det) + J
            transcorrelated_output = base_output + self.cusp_strength.value * jastrow_output

            return transcorrelated_output
        else:
            return base_output
