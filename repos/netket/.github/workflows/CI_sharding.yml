name: CI_sharding

on:
  pull_request:

  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !contains(github.ref, 'master')}}

jobs:
  test_sharding:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            python-version: "3.13"
            runscript: "run_test_sharding_1cpu.sh"

          - os: ubuntu-latest
            python-version: "3.13"
            runscript: "run_test_sharding_2cpu.sh"

          - os: ubuntu-latest
            python-version: "3.13"
            runscript: "run_standard_tests_with_sharding.sh"

          - os: ubuntu-latest
            python-version: "3.13"
            runscript: "run_test_sharding_distributed.sh"

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-tags: true

      - name: Set up uv and Python ${{ matrix.python-version }} on ${{ matrix.os }}
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}

      - name: Sync environment
        run: |
          uv sync --extra dev --extra extra

      - name: run tests
        run: |
          uv run ./test_sharding/"${{ matrix.runscript }}" -m 'not slow' 

      - name: Upload coverage reports to Codecov
        if: always()
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }} # required
          verbose: true # optional (default = false)
