# This workflows will upload a Python Package using Twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: Publish to PyPi

on:
  release:
    types: [published]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/netket
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      # make sure tags are fetched so we can get a version
      - run: |
          git fetch --prune --unshallow --tags

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install --upgrade build
          pip install -U build twine 

      - name: Build
        run: |
          python -m build
      
      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
