name: Checks

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  pull_request:

  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}


jobs:
  pre-commit:
    name: <PERSON><PERSON> and <PERSON><PERSON> (pre-commit)
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-python@v3
      with:
        python-version: "3.13"

    - uses: pre-commit/action@v3.0.1