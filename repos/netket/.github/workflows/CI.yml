name: CI

on:
  pull_request:

  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !contains(github.ref, 'master')}}


jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            python-version: "3.13"
            doctest: true

          - os: ubuntu-latest
            python-version: "3.13"
            main_tests: true

          - os: macos-latest
            python-version: "3.13"
            main_tests: true
            env: NUMBA_BOUNDSCHECK=1

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-tags: true
      - name: Set up uv and Python ${{ matrix.python-version }} on ${{ matrix.os }}
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}

      - name: Sync environment
        run: |
          uv sync --extra dev --extra extra --extra pyscf

      - name: Netket tests
        if: ${{ matrix.main_tests }}
        run: |
          export NETKET_EXPERIMENTAL=1
          uv run pytest --cov=netket --cov-append --jax-cpu-disable-async-dispatch --clear-cache-every 200 -m "not slow" test

      - name: NetKet docstring tests
        if: ${{ matrix.doctest }}
        run: |
          uv run pytest --doctest-continue-on-failure --doctest-modules netket/

      - name: Upload coverage reports to Codecov
        if: always()
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }} # required
          verbose: true # optional (default = false)
