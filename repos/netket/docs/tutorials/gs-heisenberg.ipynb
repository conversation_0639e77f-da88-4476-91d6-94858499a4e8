{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground-State: Heisenberg model\n", "\n", "Author: <PERSON> and <PERSON><PERSON><PERSON> (EPFL-CQSL)\n", "\n", "The goal of this tutorial is to review various neural network architectures available in NetKet, in order to learn the ground-state of a paradigmatic spin model: the spin-$1/2$ Heisenberg antiferromagnetic chain.\n", "\n", "The Hamiltonian we will consider for this tutorial is the following\n", "\n", "$$ H = \\sum_{i=1}^{L} \\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{i+1}.$$\n", "\n", "$L$ is the length of the chain, and we will use both open and periodic boundary conditions. $\\vec{\\sigma}=(\\sigma^x,\\sigma^y,\\sigma^z)$ denotes  the vector of Pauli matrices. Please note that there is a factor of $2$ between Pauli-matrices and spin-1/2 operators (thus a factor of $4$ in $H$).\n", "\n", "We will consider in this tutorial 5 possible ways of determining the ground-state of this model."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0. Installing Netket\n", "\n", "If you are executing this notebook on Colab, you will need to install netket. You can do so by running the following cell:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --quiet netket"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We also want make to sure that this notebook is running on the cpu.\n", "You can edit the field by changing \"cpu\" to \"gpu\" to make it run on the GPU if you want.\n", "But you'll need to use much larger systems to see a benefit in the runtime.\n", "For systems with less than 40 spins GPUs slow you down remarkably."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["*After* having defined this environment variable, we can load netket and the various libraries that we will be using throughout the tutorial."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import netket library\n", "import netket as nk\n", "\n", "# Import Json, this will be needed to load log files\n", "import json\n", "\n", "# Helper libraries\n", "import matplotlib.pyplot as plt\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Defining the Hamiltonian\n", "\n", "NetKet covers quite a few standard Hamiltonians and lattices, so let's use this to quickly define the antiferromagnetic Heisenberg chain.\n", "For the moment we assume $L=22$ and simply define a chain lattice in this way (using periodic boundary conditions for now)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Define a 1d chain\n", "L = 22\n", "g = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we need to define the <PERSON><PERSON> space on this graph.\n", "We have here spin-half degrees of freedom, and as we know that the ground-state sits in the zero magnetization sector, we can already impose this as a constraint in the <PERSON>lbert space.\n", "This is not mandatory, but will nicely speeds things up in the following."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Define the <PERSON>lbert space based on this graph\n", "# We impose to have a fixed total magnetization of zero\n", "hi = nk.hilbert.Spin(s=0.5, total_sz=0, N=g.n_nodes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The final element of the triptych is of course the Hamiltonian acting in this Hilbert space, which in our case in already defined in NetKet.\n", "Note that the NetKet Hamiltonian uses Pauli Matrices (if you prefer to work with spin-$1/2$ operators, it's pretty trivial to define your own custom Hamiltonian, as covered in another tutorial)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# calling the Heisenberg Hamiltonian\n", "ha = nk.operator.Heisenberg(hilbert=hi, graph=g)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Exact Diagonalization (as a testbed)\n", "\n", "Just as a matter of future comparison, let's compute the exact ground-state energy (since this is still possible for $L=22$ using brute-force exact diagonalization).\n", "NetKet provides wrappers to the <PERSON><PERSON><PERSON><PERSON> algorithm which we now use:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The exact ground-state energy is E0= -39.147522607062776\n"]}], "source": ["# compute the ground-state energy (here we only need the lowest energy, and do not need the eigenstate)\n", "evals = nk.exact.lanczos_ed(ha, compute_eigenvectors=False)\n", "exact_gs_energy = evals[0]\n", "print(\"The exact ground-state energy is E0=\", exact_gs_energy)\n", "\n", "# Just in case you can't do this calculation, here is the result\n", "# exact_gs_energy = -39.14752260706246"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. The Jastrow ansatz\n", "\n", "Let's start with a simple ansatz for the ground-state: the Jastrow Ansatz.\n", "\n", "$ \\log\\psi(\\sigma) = \\sum_i a_i \\sigma_i + \\sum_{i,j} \\sigma_i J_{i,j} \\sigma_j $\n", "\n", "To show how it's done, we write this simple ansatz as a `flax.nnx` module.\n", "We import this module and call it `nnx`, as it is customary in the Flax documentation.\n", "\n", "An NNX model subclasses `nnx.Module` and defines its parameters inside `__init__` using `nnx.Param`.\n", "\n", "As the module should work with batches of inputs (therefore the input will be a 2d matrix with shape `(N_inputs, N_sites)`, but we are lazy and find it easier to define the function for a single input $\\sigma$, a 1D vector of shape `(N_sites,)`.\n", "Therefore, we write a function called `evaluate_single`, which will evaluate a single 1D input, and use `jax.vmap` to make it work with 2D inputs as well.\n", "\n", "To learn more about jax.vmap, give a look at the jax documentation!"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from flax import nnx\n", "import jax.numpy as jnp\n", "import jax\n", "\n", "\n", "class Jastrow(nnx.Module):\n", "    def __init__(self, N: int, *, rngs: nnx.Rngs):\n", "        k1, k2 = jax.random.split(rngs.params())\n", "        self.J = nnx.Param(0.01 * jax.random.normal(k1, (N, N), dtype=jnp.complex128))\n", "\n", "        self.v_bias = nnx.Param(\n", "            0.01 * jax.random.normal(k2, (N, 1), dtype=jnp.complex128)\n", "        )\n", "\n", "    def __call__(self, x):\n", "        x = x.astype(jnp.complex128)  # keep the dtypes aligned\n", "        quad = jnp.einsum(\"...i,ij,...j->...\", x, self.J, x)\n", "        lin = jnp.squeeze(x @ self.v_bias, -1)  # (...,N) @ (N,1) → (...,1)\n", "        return quad + lin"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that the model is defined, we can instantiate it"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jastrow()\n"]}], "source": ["ma = Jastrow(N=hi.size, rngs=nnx.Rngs(0))\n", "print(ma)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "91ba879f877b40aa83573b68a3f0b3e7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["### <PERSON><PERSON><PERSON> calculation\n", "Has 506 parameters\n", "The <PERSON><PERSON><PERSON> calculation took 14.687036991119385 seconds\n"]}], "source": ["# Build the sampler\n", "sa = nk.sampler.MetropolisExchange(hilbert=hi, graph=g)\n", "\n", "# Optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.01)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.1, holomorphic=True)\n", "\n", "# The variational state\n", "vs = nk.vqs.MCState(sa, ma, n_samples=1008)\n", "\n", "# The ground-state optimization loop\n", "gs = nk.VMC(hamiltonian=ha, optimizer=op, preconditioner=sr, variational_state=vs)\n", "\n", "start = time.time()\n", "gs.run(300, out=\"Jastrow\")\n", "end = time.time()\n", "\n", "print(\"### <PERSON><PERSON><PERSON> calculation\")\n", "print(\"Has\", nk.jax.tree_size(vs.parameters), \"parameters\")\n", "print(\"The <PERSON><PERSON>row calculation took\", end - start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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********************************************/9MNwgSiz4YDQ34nHUwG/fjZcjGG49mpL0p4YLBGdhNSUEThj7FtITR3V/MatoM6SU99EJMmH7w78HgDQv/8cQxdwezDbUlOzPmJpEABa4NXYqPQDU2e5+LTMkvLmLwhWw/3CsyPqYrz63lLK/oZqx+hy7QAQu3Gk+uGemqrUBurfnI3DcKEeVupx6oMlj6cMkuTXeiRJkhe1tUon7oICpb+aMhNRaXOgnkd1eRllxpp+TbzIYEn/TV+/oK/ZkhpamqbpKHy+uoheUNGCB/3jHTv2ryi3Gz+8YhV564dKGlzRPyQBZUr3ho3fx7r1l8Lvj/xgNG4r49DhtwAA1Se+NNR8xdPUdBRfrZmCjZtiDwdu2ToDJaV/xN59T7Von82prduK9esvw4EDv8XWb+5o1+xaLB7PUe014GqIPtwLKK/7urotqKr6HJ5gcON2H8CBA7/Dnr2/MiyWG9HOoplhOE+MzNKWrbdh/frLowbFtbVf6+5fYfhyEu0131xmqa5uW0Qgow/A/FFmwwH67va1hno8n686uIiwX7tO30KjvVVWrmjz64XBElEHpGaD1A/5+vpv4ffXwmxOR8+C6w3bpqefBZPJqb2BhtafU6hF4mpLADVYCs1EaQpuZ1zkN/zNVD0Wh6O34Xo1QGto2KcFGF7v8YiAQZZluILBUmbGBAChpV1E0RrRVyq8NsJsToXNlgtBsECW/fB6K+CP0lByQP+5GD7sNxgy+Ne656K8eauzewKBpohgKJxxGRhXKFgyp2rH1uQ5qg3/Wa052vCh0sahAZIU+hDQD/WVHwutPhB6vPDMXfRgSf+B1xAjo6Bu5/GUIxBoiOg2Hq6+/lst0FUCgtj71du//wUEAg1oaNjb7BItR4781RAsxHPixDqsWXsxTpxYG3Hb9u33BZ+PAElqwo6d81q837bSD7O66nfE3E7fCNblUhoqqy08JMlrbNbY6mG4yFlnsizB5doNWfYbAiNVbW0osyTLfsProLnMUmPjIUiSz1BL5/NVRUw+CC/wVhbcDg6dqwXluuetb2ni9VaHve4FbT/tra7uG2z9Zha2fjOrTZkrBktEHZAaLKmzVtQV7TMyzo4IKkwmGzIzztEuOxy9gwGTMiQV3gMqOWmQsm9/rTJEFfyQC595p6TDQxkGdRguPFhSa1zq6431DOF9npqajsLvr4MgmJGWPh5A6INf1M2EU6lZJJXZnApBMGmd0BubjkR033bYe8NsTkZe3pVwOvtq1/uDS5Cos+yU1gG6zFKUglf9t1tlWEwNllLgsCtLHLlcu1ET/L9JTxuvDYk2NpZi9VeTsHnLzYZ9qFyuXah3hVYnUI4hrDVDjC7e+rUD4wU1+m/QzS1Pc+Toe4bLNTUb4m4PKP+fxyr+T7usr0XRE3XDq/oJBvFUHF8Gt3sfKiqWGq4PBBrR2HgQADB+3AcwmZJQW7tRa59xqugL+OMNferXDmxwKUG0OvwNhL6gAJGz4Zor8I42DOf1VmmzRetdxiCuyVMenKwhRp15G+0172kKvbZk2Y/GxkMRQ9Qnqr/CiZoNWoCqr1mSpEb4/fXa+0b4e4qy31CtlM8XCpZE0aoNnbd0RlxZ+UfYtfsRw5cSvYaG77Bt28/R2HgItXVbASh/F66wv72WYLBE1AGpM+DUNxL1G3ZG+tlRt8/Onqr9brf31DIcZnNKxErmTmc/LeDy+U5ow3DR3tj0wzfqMFxqykiYzSnakJYyY01GXXDWjSo83a1mlZKSBmjtE9T0e7TV1vXBkihatb5F+hlxvrAGmknJg7TfBcEC9Zuqup2aWZKkJkMmJBAls6QPltSu6gBgNqUgI3MiAODEiTWoqv4vACA94yztQ6n6xGr4/XWoqdmoZd7Cs1cVx5ZAkvzYvedxlJd/HGUNreYzS42NB6MOvQKAOxhUAIAvTtG2JPlx7NgSANCC7pYESyUlfwz74IseLOmzPocOLWx2v4BxUWg9dSjZbE5BauoY9O37PwCAI0febtF+28oQLMXJLOmHn9UPZEOwZFizMLKrfDzG4S7lteTRZYLCM17qEG1SUv+ILzhA9Do9NbOk1v253d9FBFU7ds7F11/fgIrjSiCrD5aUYyoP7sMEqyUL8fj9dVp9nyg6tdnFLSnyDgSasHv3Izhy5B3U1KyLuk3poT/jWMUSlJS8jgbd8Onx48ua3X84BktEHZB+GE6SvKip3QQAyAgOX4XLzp4MNTBQgqUewf0ka/tS2Wy5WrbJ663W3qRtUZpgGpYtCGaW7PZ8TCz+HMUTlNl1suyD31+P+jolWFKDKJdrF7Ztvwc7dvwC9fU7tG/kyclDI2YPRguWHLphOOOyMMr1Hk9ZRGZJzZoBSosHUVQCLK11gLZ+n2yoq4iW9tcPw6kFqkrXdSeSkwbDZsuHJDWhVv2/ST8bZktqcHv1w1XSflfPpfr/ceLEWlRVr8Lhw29h//7n2zQMJ8v+mDUYjYbhjtjBktdXiUCgAYJgQlHRXQCA2pqNzQ5V1NSsD9tPZLCk1KOEgrnqE6vR1HRUGd6JM9Sinis1EC8rW4z6+u1obFKCJYe9NwRBQEH+dAiCFfX12+MGMeHCn5ssSzh2bEnU/l2Asa+Xy7UrZk2XfjkcdXhWHyzpg9bwppThl8N5fZGZJf1EgXrXTsPzUgMz/fCwnj/QgNJDC7B371OQZRmyLGmvrdRgM1q3ez8CMerdqqtXIxBoighs1ADOZEqJeO8JEaCGH2o9k9nk1Jr8+v31kCQPtmy9HbuDM4DDVVWt0v5mYv2/qX8bdfXfGFqLhM8MbgkGS0QdkBYs+RtQV/cNJKkRFksmknTBgJ7Vmo201DEAAKejCHabMlRlNqcYmj2q21qCa9Z5fVVaZsnp7AtBsMJiydRuV2pvXMHp9w3BY3PAYsmA1ZqtHafHewyuBuWbdG7uNADA4SN/xbFj/0JZ+d+xfsMVKC39EwAgJXlYRLAUbRjOFiNYUjuge7zHtWApL+/7cDr7IS/vqrD9GovW9cuz6Jd1ifZNVv/NX30zVtYGVBb5zs6+QHd86UhKGgiLOXL5F3djieExsrMvBKD0rKk8rjQM9PqqtPOrBnixljwJX2Q3VsGx+rjq/mNRh3csliykpY2FIFjg8R4zLOESjVrHo04gUKeBy3IAm7fMwO49jxvqYtKCa5GVl/8TW7feji++PDPmh1wos1QDl2snduyci2+3/Y8WeKiZEoslAzk5Sla1pUNxjY2H8eXqidqECUAZHty2/efYveeJqPfRH6ckNcLtLonYRpZlQ2apoWEvJMkPtyGzFHsYrtmaJU9ksKTPLPl81cbi6eD/h9WSGSNrXI99++aj9NCf0dR0CF5vZTBTKGoZ7Ab3Ae3/MCtrCrIyz0NOzkUAlOJxNaskijbtPUM9VxZzasxgyWxO1d4D1LYCJnOSllH3B1yoqdmIqqrPcfjwIkPAqdIPAeszf7IsaUGj+oXB5dptGHpzNeyOKBNoDoMlog5IPxtOPwQXbz2oIUOeQp+in6JHj6u0N0dlGC70hmUyOWEyObXZdD5vdajA25qL8eP+hnFnvKcFJx5PeXC203Xatzj9enbqG2RNzQZIkhdmcwqyMs8PHrvyJpuUNEjrGwUAySlDIYoOw+w7kylU16Jy6Ibh9MGS2hnb663UCrx79boZxRM+RVLSAMM+1MDDuB/1HIa+hUer39DXkKjDE/rjyM6aov2ekX4mBCF6bYj6Rq8O9aUkD4XNlg9Z9qOs/B8AlA9KNfvjcCjNPMODIpU6zGENDmXGKvJuac2SepsS/DqQkjICQPyhOKWGSzlnKSnDAYSGzNzuA6iu/gJHjryrDeOKohX5+dcCAA6WvILqE18GZzBujrJ3XU2Or1rLJjU2lmjtIRyOQm3bgnxlJl55+cctWjKkqmolvN4KbegRCA2ZxQoQ1Q9jNfh2uSKzWF5vRfA1LkIUHZAkL9yNB9DUpK9Z0meWmoL7VL4oNDcDLFrNkr4gGzC25lADZIs1K2qw5Gkq17J+Xm+1ti+bLVebSdrYWKLV0jkdRRgzZgGGDP5fAMrrTi1ot1pztUAn9LcSO7NksaSHgqvgLD6TKTQMF/C7DK+N8rDZo4FAE6qqPtcuK7NSa7F371P4cnUxvvjybDQ2HtaCR1n2IxBwQRBMSE87EwBQqbt/SzBYIuqAzMGAxB9o0NL5aWlnxL1PcvIg9O9/P0wmmzbjzeEoNAxxWS3ZwX+DvZZ8VVpTSJPJgdTUUUhK6q8VWtbVfwufrxr19d9q34r1b4DW4BteXfCNzenoayisFgQzxo59C8UTPkdR7zvQu3CmFvTpszzh2S/AWLOkHg8QymR4PcdDtUja8JpReLBkMtkjrgNiZJZ03/w9Wq+n0HFkZBRrxcvpGco3cXUYTk8NltRhP5M5BRnpyjJM+pof9Ru5GvDV1X8Llyuy0aYaRGVlTgIAwzZ+fz22bLkNR468p9X3AMqHdHX1amz95k7Uh/W6CgVLymsiPVh8X1OzAXV132LVf8eiVLdMDhDKKlksGbqeWCeC/9YEn5tf+8A2mZKRm3MJBMFqXDA4RsZLX7OkDzAqK5WVHOy6YCkz8xyYzenw+2u19Q7jCQVGh7R6KjULFz6sqzyPgPb/n5mhnPNoRd4NDUrg4HAUIjl5CACguuq/hpoy/TCcGhxZdF3l9Y4d+z/s2v0oAoHg2mpRhuH0BdnKcYWCuOYyS2oQqjzvE1rbAJstX5sg4vOd0IbhTMEO91ZrdjCgl7UCfJs1G2ZTSvCYWhYsqetlapklwzCcy9AjKrzVhn4IDlCC2ZLSP6H00J/h9VbC56uKWsfmcPRFWrqS4Qxfoqg5DJaIOqBQzVKD1sRN31upOVlZk3HG2HcwaODDEPXBTXAfluAblddbpRU6i7rsjjqrSz+soKbc9W+A6n7UmSZ2R2FwmRMle5OdfSFs1mw4HD0xYMADGDjwIQjBtaH0Q3FilJolqzUnWKQdNgwXDJaaPEe1bFWsDurhgZEoOgyzs1RRm1JGGYbTB0smkx29et0Mu70ncnOmRRynSg2WQq0HUpCeflbEdmpWIzNzEtLSxiMQaMCWLbcZ6lIkyacFN+pwSGXlZzha9iEA4Pjxz1BV/V/s3vOYoVbI561C6aE/o7LyM3y9+UbU1GzUblODEfUDMj1d+eZdU7sRR46+B7+/DuXlxlYHaqbFbi8I1b+FBUtA6MPcZEqCxZJqGLoEYmfP1GBJkprQpAv61GyW0xFaSkeZIanWsRmHLv1+Fw4cfAX79s1HWdliSJJX+/IhSV6tLqzRrQZLkXVXHk8FZNkPQTAjK2syAGj1eXpqJs/p7Ifk4ESDihhd84FQE0p9o9Samo3afvbtfw5HjryNw0feDtZu6WamBhs3qlmc1OAQvDGzpARLFmumoWZJLd7WB9M+X422L7s937CQtZp1NesyyunBIdWy4OsuOWWoFuhoRfiWtJZllrRgKUlX4F2H2tot2vYNDXsNXwqqq78AAKSkKIsJN3mOwlWvNMxU/0bLj/0z4nGTkwdpX/Bau7YlgyWiDkg/DKdmT6J9EMciCCIyMs6OmA1ntSmBhppZ8nmrtG+0+uyOKfiG446yvIMxs6Tsxx1MxzsdvSGKNi07Et4TSk+fDYpW4C0IolZ7ZdEPwwWfQ6hOQTQEMXrhNUuiaIs65Of31+PQ4bdw9Gio7kX/TV8NysIfZ+CAeThn4n+1dgaWuMNwLm0f0YIlNQgym1MxetQfkZQ0EB7vMZSUvq7b5jgAGYJgQXb2hehdOBMAsHPng8oHbaPyQatv8gcoHwyhjsz12PrNHVqQrB+GA0IfhG73d9qsIWWR4tCsNjV4tNsKYNU+WKuD/9aEtvOoQabyeu7b526kpo5FRoYymzBasCTLkqH4O1q2SD8MB4ReE/qZWT5fDTZvuQXfffciSkpfx46dc3H4yNuGQt9Gt/J/o/4f6ZuVqsu/1Ac/hG22PC0rUVu3JWK6uvrFIsnZTxvKVBulqqINw6kTItyNJdj09Y3YsnVmsNhaCTQPlb6pnW810JflAAIBt5bFUYeE9RmvUBBsHIZT6730QajPX6PLLOXp1mas0dUqhprRhrLcMkTRjj5FP9GG4dRg1G7vafi71meKLWZdZkk/DKdltLfB76+BKNqQFXxu+uBH7Sml1ig2NZVpwVSPHsp14cPVgFISoAZpPl0bh5ZgsETUAelnw6k9gqIN8bRmX0DoA1HLLPmqtQJv/Rub+qbVEKUTd7TMkkp9Ix4+/LcYMeJlZGWdH/O4zM0MwwGhxpTRMkvaMVjStGxVOH1mSRRtwRly1ojt3O7vsGfP49i566HQ+lZRakhiBWWh20PPKdQ5vRSyLBsyS05nX+3bvr4GDFBmBVksaRjQ/wEAQEXFMi1QUT8AbNYcCIKIAQMeRE729wBIOH78k4iZcWrticdToQWXgmAxDFmFB0sWS7p2PzXTIklNhkxEKLPUM/Tho2aWdAswq8erPseUlGE4c/yH2vJA0YrYlfMfqicL7yUlCCbYgkG0Sv1A1GeWtn5zJ+rqtsJsTkdW5nkAgMOH3zL08GlsKlWCDkNXaeV5lJd/hK3f3I5t2/9He67JSQNhNqcgEGjQJjQEAk3Yt/85LbvndPZFXo+rtKVv1OuUfesKvMOG4RobSwBIaGwsQVNTmTZE6/EeQ2npGwCUIFFt+6F0w1aeb1bWecF9lGrLyqid762WLNh1mSX1WIzPuUbrDG+35WmZJVn2a+fUpFtoWl8SUFR0J+z2AkNWCAAc9l6GrHZy0mDtd7Mus6T+f5hMDu1L2okTawAAKSkjkJ+vvFaOHVui/R2pkxpyc6ZBEEyQZZ/2fMIXl+7R4zLdMQzSsuv6YvuWYLBE1AGZzKFhuFBdTuRMqxbtS4xSs2QNZZbUAm/98FRoXbXIrsz6D3c1pa1Sa0lSkoegR+4lcY9L3yxTjJLtAQBnkrJCuZoRUx9fP3vOHOe8GIMlR/DfyMcKdTaWtCAiWsFtc8GSEpApwVhmxjkQBFOweLtC67psNqVAEASMHPEKhg59FpmZ5xj2oZ5fpRYnBV5vhfZNuimYiVEzBYIgICtb+ebtcu3WhpNU6gw0SWqCLAcgig7tgy5WsASEhuL0GnQLFRuDpdCQjfJvjW67YP1KWECodotvipJZCl9mp0lXW6PctwCiaDFeFwxM1W7STU1HUVu7EYJgwrgz3sWgQY8CUAOSkMbGQ4YgUP88jh79G4BQh3q7vQCCYNLOX21wKLO8/B8oKXkNgYALTmd/ZGdfALM5CX37ztb2mRZc3kefWVIzl9Hq7ep09ToAcKxCKUZXivCDWV/3geDwoAnJycOCXygkrWmn2gDTYsmEyZSs/Z1Em1Hr89WEXge23GBtnz14LtWp/aH/w6SkgUhPPwspKSO1BcH1wRSgBHZmXbCkZJtDQ/COsKazJlMSsjInGSaDpKWNRXbWBTCZktDUdBh1dZuDRf4y7PZC2O35sFlDWTO7rQApycMN7y1pqWOQlnYGTKZkpKWNNZQgtAaDJaIOKPQtzaV9eMQLCuKJlllSh8883uNabUu0zFIk0RCARGSW7JHN72IxFHjHqG3o22c2Bg18BAX507XrBEHQPhyV/aTHfAx9FkkdfjNFCZb01Nll0RY2bS5YUgrXlSxYUvIg2II9odyNpYbMEgCkpY1BQf70iA9LNVgSRavWbPRYxb9R79ql1croa1DUYuJ6105tGC43V/k23SP3UsOsQ6ezSMsaNbjjBEtpoWBJDTL1w1dqEGS3F8BiDQZLXiXI0C9BE55ZUqnBntfbfLAUzhmlwaLaTsIbzLSoHe9TUkYgOXkQnM6+2ixDvabGQ4bmnYASOLjdJaipNc4GVLOc6WnBAvhgfy116Kug4HpMOHup9tx6Ftyg/T2ow4762iNtGM4S+XddEwyOnc7+cDpDMzzNpmTt9aNmV6zWXIiiGUnO/gDUlgUeLWNjtWZCEAQMH/YCBg96AinBtRyNz/mEdu7Uvy3170p9fej/DwVBxLgz3sWZ4/+hvW+Yw9aMtIdllqzW7FDNpDkNmcEJCiqTyYm0tLEYPuw3WvYsPW0cTCZ7MHuqDMWp5yYtTQlA9S1GkpIHQRAEw1qdDmcfjBn9JoonfAabLVf7gqf28GopBktEHZDWv8hzDOqQhKXNw3C6zJLVmFnS9ycxZmuiBwUmk9PQvkDfoVcQLLDbI5vfxWKoWYoRwNhsPVBYOCPieKy20Ad7/GDJFvG7/rrwxYOB0IdQIEpWrblgSdkm+A3e2Vf7YG8Irt+l3G78fwwPOPWBY24wO3f48F+wfv1lWv2SvgYlOWkgAAE+X1VwhpCI4cOex/nnbUFm5jmGiQEORx+tnix+Zmm89rtad6bv56Qv8A4VzCofPsYC72CDQnP0YElpQWBsxhkrWFKDPHtYvRIArSO8J9jl+kSN2m4j1MRVPySsFgY3NpZG9PDx+U6gPNjSIT39bDgcfQAoMz0BIC0YLNXWbIIsy2gIBpHp6WcZhoNF0YqxYxdh2NDn0aPHFcFrZXg8FfB6q0LDcFG+BGmzS519Me6Md7T/m+TkoaEh8uDjqkNsTu3/db+WVRIEs/Z6y86egl69fhTxfwEoAa567tQhzfC/K7UmSU//XmAyhwdLPQ2ZJYslQ/t/UmZRFmj/pwC0bXv0uAxjRi9A3773aD3JeuQp5+/YsX+jNtjSQg2W9BkqtSmtWvAOAE5HH5jNKVpdmzo8qjbTbSkGS0QdkLY2nF9dCiD6lPfW7AsIBUmhokf1m5WxlidesKSn/6C323tGrFsXT3Oz4eLR1y3FahsAhGWWggGZfsjPpgsQVFpmKdownKn5YKmo6A7k5ExDZuZ5Wg2XWiSsdgDXi+hmrvswy8yYFPy/kINDfMrwk75mxGRyGgqeHY5ehnW29MGS01GkC5aUponqsJM+WLLbCzB0yDMYNnS+Vg+jLhehDisq2/XU1bd4EQi4DDPK1Jqa8MySvrO8WuS9/7vfYOOm67XamXCFvW6FKDqQHZyRphcq8DZmljIyQssD6e+Xk6NkKhqbDkUMXfp8NVr/q54FN2DM6DcxcMBDWu1LauooXePOQ1pNVbLug1/lcPRGfv73IYqhNRq3fnM7vlx9jtbCIFpmSW0BYLPlwWrNQvGE5Rgx/Pfo3XtmKFgKPq4tOLlA+39179NqoyyWjIh6vvAMEKCsI6dmorSgIkbGMxb9fi2WTJjNSYbMksWSgYKC65GSMhwZGcUAoM0uDN9/ZuY56Nf3f7T3k8yMc2CxZMLnq0L1idUAQn8DhsxSMFhSG/TarD0igjyTyaYVq/taMSPO3OItiei0iSj6bcVMuMh9RQ7Dmc1JcNh7ozHYMM9kchi+JbY0WNLXLIXPUGqOWRckxCrwjkUfLEX7sFEZMksmR8R1VluOYSV2IPQhFIiyVldLiuwL8qdrw4bhwZLaAVzPYjYudKz/Nm4y2TB61BtwuXahR4/LIIp2eL3V2pCQKjl5iJYhcQYzIdpz1AdLzj66hoOHgsNkMgBRm9WmPY9goaw65OZ2H4AkedAUbAcginZYLJnBonkHJKkRPt8Jw3p9WjYt7PUsCAKs1lw0Nh6Ex1MBqzUbJSWvQ5Z9UXsCiaIdBQU3oKDguqgBuVWXWWpsPIKmpkPB+qJx2jbp6WfDbE5FINCE3Jxp+O67F+H1VmoLGqvPocG9L1inIyAnZypMJid6956p7cdksiM1ZQRq6zbj2LH/g99fA0CEMzgMFovFkgWf70TEIq7RMqPqebNrazwma8Fa+DCcOmM0NAy3LzQTLqymEIjMAAHQ6pyU2aLJweMyvh6iZaT09EGJ+l6gz2pbrJnB7NZN2nXZWZO1zv6x6haV47Jg6JAnsWPnL+D310MUHUhOUoaf9X8Lan+5zMxJ6FP0E8NwnJ7VkonGgAtebxVEsWUtWRgsEXVA5rA3JkucgKA5hoJsXfYgOWWIFiyFFz1HZlBEAFLEh57FECy1vF5JuW+67hhbFyzpM0Lhb+p6zQ3Dmc2pEEWbofOzx1MOn68uajfolmSW9NQ6GfWDLVoQavywFCL+L9LTxxuGxRwOY2EsoMw0Ute7cuiaggLGoVKHow+slqxgE8cabX03JQMRPStos+XBbE6B31+Ptesu1YqrlYJnIfgYGWjyNMLrOxG1V1G0D2ibrUcwWDoGr/e4NvvLFaXho9WaHXys6MeoBs+y7MXxSuU8pKSMNHyAm0x2nDH2bQQC7mDj1TT4/bVaMXVqygjU1CqNONXji1VLl5F5DmrrNuPQ4b8AUF770VpSGI8xE2535PXxJm5ECxxDi2wHO+IHa9bUzJLbfVBroWCJ0pst/G8YgFZHZbXmav+nEcNwzWSW9P/HdnuviPuEB+MADMFsc0NiOTkXYULqWBws+QNSU0ZAFJXwJTQMFwpYBUFE//5zYu7LYs1CY1MpfL5q2FqYsOcwHFEHFP4mfTKZJYslDb0LZxp6oQBK/UPo8YzBirH5YrL27S38uEwmpxZ8tDazpE/zn9QwXNzMUvwCb5No175J2+29tMLpBvfeNs2GC6cGkGogEDVYsoY+RJSasNa/LSclh6ZlO8ODJV1g6XQWQRAE7YNVXUon2nCkShAEpCQrS5o0Nh7UMm+GvjlakXe1toq8XrQPWjUQ8HiP4Xjlcu16dc2uaBnRWEwmm1YrpnZ7jrbodErKMC3w1Af3gmDWamDUpUziBf/qkJ467BdtCC6cPmjVi5cZjbYAbvhrSM2e2O09IYp2yLJXaxIbLbMUrfZIezxr7FrA1gzDqe8F+r/raF9qRNGCPkU/hc2Wh9yci+PuH1CGCAcPekxbOgcAkpOHQRTtSE8/EyZTyyIfaxtmxDGzRNQBCYIVgmDW0vFtbRugGjjwoYjr9LNiwhey1b8h22w5sFpz0NR0OCKoUmZ/ZcLjKWtDZknfZyn+t/Jw6tRzoLmapSitA3QZANHkgNmcAp+vCklJA7SlLRpce7XmhHqtD5aMAWTUYMlsDJbaIiWYXQAigyU1u6CsCaict6SkAait3aj1s2kuGBk06FFUHF+G5KRBqK39GmXlH2nF50Aow+jxlMdoNxH5vNRgqanpCKqqVupuUXpKORxFWpapueNT9pcbzBQpgYK6GGwsSUn9UV//LczmVAwa+KiWXVFbBTiC2ZFoUlNHwWLJ0LJoSS0IlsIL+bXr47x+owZLJv0XmSTt/1sQRCQ5+6PetT2UMYzymPGCHqv+78rSuiBe/9pWz53Vkg2HozfMphRDU0u9/v3vR//+98fddzw2Ww7OmfhFRDY+ntByTy3vtcRgiagDEgQBJpMz1DagjTPh4tFnlsSwIZjwtdhCmaXIN6Tc3EtwvGKpNqW6pYxvxq3NLLV+NpxW4B12nfpNW635qK7+Qpt+Ha61wZLZnBysVamKeX/jcGTbgiWHo3dwqMwVsZiw+sHgcPTWhljUbdR6reaCkeTkwVo9SG7utIjgW/2/DG+KqYpW76IGS8eOLYk6A87h6N2qYMlqzdGyXuH1StH073c/UlNHo0fuZbBas3D8+GeG2+1xgn9BMCEz81wcC3aVTkqO7F0UcXzB/4ekpIEIBBpD/Yv0QYajKFh7pky8iD4MF9o+JWWEYfg0KWkA6l3btfMQLbMkilaIojUYFIrB1426pFL0lhzNZZWUbXTDcGpmSTRjwtnLAIhxFwE/WdYYgWgsahDZmi7eHIYj6qD0b1AnMwwXi34YpVHXQkB5PH2wlK3VIET70Bs08JeYOPG/rVq7DlBn+CnDZK0NEmK9qYczGQq8bVGuc2gfKEnJg+Bw9gGgzJIKNes01ji1lj7jFq3mScmwKR8kLflQikYQTBg96g2MGvmqoVszAGRlnYv0tDNR2Ou20HWZ52sz64CWBSPxNBcsRZuBpU4jV7MzybrsGGBc/61FmSXdshYpKaOazTTY7QUo7HVLqPdP2OsoXmYJMM6uS47S6DFi++wLYLVmo6joLi3wBIyLOzscvbXnYTanRn0O+r/N1NSRhtsygzMXVdFqlpTHVIu40w2BRqxawJa8Lg0F3rpzpwRnHSsv05b14RgsEXVQ+jeokx2Gi0b/TU+/BASgvsEpmRibNRf5ed9HdvaFMdd6a8u3RkEQgr1rxKjfoOPRB2bxgiXB0DogsoO3SbSjb7970Lv37UqGQWtYV6PVLKmBmSBY2tS+Qd9E0WyJDJYEwaTV27Q1swQoheDqlHg9qzUb48a9h4KCUGPPpKR+hgJYUyszZhGPEfxgjbY8DhD9w1b/f+509sewofMNt+ubSNrClriJegy6Lu/6lgEtZQnLwjRXg5eZeS5MJicslgw4g0F2PKmpI3HupHXIz7vaEFwJQuhvzW7L16bCx/qbMARLKcbZXj1yL4PdHjruWHVS6v+HMsU/PbS9YRgudH1LhrgsljSYzSlaD6WOzKKtYFCt9aRqTscK94hIo59CHq8I9GSEzwQzPL45BV5vE6zWHDidfTB61B/b/fFHj/4zvN7KVgdLomhFYeFt8DSVa1mv6Nvps0iRfZZEkwNpqaORljoaQOg8G4OlbDQ1HYbZnNKmoLC5zBKgfDD5/TWG//NTrXfhj1FV+TlO1KxFuq5vU1uoz1Gdgm61ZmvNLoHoGcnk5KFwOPrAbi/AyBG/h9mcbng92u35Wt1eazNL+maULRWRWWqmBs9qzcT4cX+HIJhaHUSnBovJgeCQu2iHH7Ww2fLgD7hQV7c5ar0SED+zpBRM34ldux8GEHumqJoFslozDYGssTO+PrMUuyg89Ng2nDn+H8HzEdnstSNRvxTVu3Zi7bqLWnQfBktEHZRhwdpTMAwHAMOGPodt23+GPn3ujrhNCZaOG7pltze7PR92e37zG0YxaODDzW5jGHKLVrMUNt1bLbb16NYsC/Wmav4DIxpDsBQjg2OxZKCx8WCbh+HaQhBEjBmzCI2NpRFF4a2VFlav5rAXGoKlaLPhzOYkFE/4zBCA2u0F2lCe2ZwGiyVDeQ22pGbJpmYAzYamnS0VGg5VGoCGL9gcTXILapWiyc6agoKC67XsmTpEbLPnaZ3j7bbofxdqEKNkcCKzX/n512Df/uchSU1ICq6tGC6UWcqIOevQkFlq4evyZF9Hp4tWs+SrhiRJLboPgyWiDkr/bbyt68I1p0ePy5CWPs6wGKXK4SiC2/1di+oxOqpogZGxdYCxsFydoacflgwFS20bqmpJsKQOY53MMFxbiKI55gdqazgcPWGz5cPjCa4Z5yhErW4x2FhBYHimzm7rqQuWUlHU+w7U1KyP2VxQLzVlJATBguysya2aGRU6FhPM5lT4/bWw2wtPaUGyIIgYOuQp7bJaC+ewFyIt7Qw0NOxFQc8bot43JWUEiop+gtTUkVGPURRtKJ7wKQKBhphBphr4RwRLumE45ZiU4DFan6zOLNbwZDyC3JqV5Ciquro6pKWlIT8/H6LIMjBqHz5/LaTg9HWLJTMBqW0JshwwFAJ3NpLUpK1VZjanwmRyGq+zpIUFTLIhqwQIMJmSEAi4IIrWiLqWFh4FPB6lH4/Fkh7RdBJQlrUJBBphMjlPSTH/6aB/vSrnzI3QrK6WrRno99dpa8Up7SFa+34qQfmAb1ug4/VWQpb9EEVb3Gan7U2SfZAl72nLLKrDzGpHebUhZPj/k8dbAchSp35dxqIu8izLIqqqfKitrUVqauznyGCpHajBEhEREXU+zQVLHIZrR8wsUXtSVmRXljRQliHga6u1JMmrW1RUyepIkkebrm6xZEQU5ypLbygNCgVB1JoPmkwpre4HpfL5TkCSPLBac2IsKyJDkjzBYzl1wz+nkiz7tTolZQ02N2TZD0EQdQs3x6esL1cLQGh10X97UP+f1Cxk1yYBELVMqyCYI4btlJoeL8zm5BYVeXcmktQISQpAFJ0oKytrdnsGS+1o165dcSNTotb47rvf4sDBlwAAk8//thu8ebe/mpqN2PS10u5g1MjXkJPzPZw4sQ5fb/4hAGDsmLeQmTnRcJ+166bplvTohXMmroIsyydVw+L3u+DzVbe6y3lnIssS/vvFePj9tRgx/Hc4dGghaus2w2HvjYkTP2/RPk7UbMDXX98AqzUX505ac4qPOFJV1Rc4fOQtDB78q4h+VV2V11uNrd/cjry876Ow182G27ZsnYmqqpUYOOCX6N37xwk6wlOrpSNDDJaIOii1wFvp79O2jEZ3p6/zij4bLvK86mtV1PucbLGv2Zzc5tl0nYUgiCjIn46y8sVITR0Ls2UxgOZXq9dLSx2D3NxLkd5M9+1TJSvrXGRlnZuQx04UqzUTZ45fHPW2HrmXoaFhPzIzzznNR9XxMFgi6qDUYk+zOfWUzszpylrSZymcvgFoSxfmJMXAgQ9hwIB5EARR6ynVmqJlUbRg5IiXTtXhUSvl51+D/PxrEn0YHQKLIIg6KHXYzXIK1oXrLkRDB2+1dYB+bbjIYMis6y8TbeYaxafW1qnrGZ7ORptEpwqDJaIOSu2909LiWIoUNbMUtjZcOIuuW7qJwVKbaZmlLj78SN0Dh+GIOqiMjIno3/8XyMyY2PzGFJWhPkmrWYrdlBIIdfEGogdT1DI2u1Ig3ZJO2EQdHYMlog5KXeeJ2s5Q4B0MfJRi6zQIghi1nsZiGIZjzVJb5eddC1GwIjtnaqIPheikMVgioi5LFB0wm1MgSX5tWEgUrcEFPwWIYuRboL5micNwbWc2J6FnjCU7iDobBktE1GWJohljx/4VshwwzGxzOoti3kc/G040MVgiIgZLRNTFpaaMaNX2FmaWiCgMZ8MREekYapaYWSIiMFgiIjJgZomIwjFYIiLSEUUHBMEa/J3BEhExWCIiMhAEQWtMyWE4IgIYLBERRVCH4qIth0JE3Q+DJSKiMDab0n3aYslM8JEQUUfA1gFERGEGDvwlTlSvRmbmpEQfChF1AAyWiIjCJCcNRHLSwEQfBhF1EByGIyIiIoqDwRIRERFRHAyWiIiIiOJgsEREREQUB4MlIiIiojgYLBERERHFwWCJiIiIKA4GS0RERERxMFgiIiIiioPBEhEREVEcDJaIiIiI4mCwRERERBQHgyUiIiKiOBgsEREREcXBYImIiIgojk4TLF155ZXo3bs37HY78vPzcfPNN+Po0aOGbd5//32MGTMGTqcTRUVFeO655+Lu8+DBg5g5cyb69u0Lh8OB/v3747HHHoPX6z2VT4WIiIg6EXOiD6ClpkyZgoceegj5+fk4cuQI5syZg+nTp+Orr74CAPznP//BTTfdhJdeegkXXXQRdu7ciVmzZsHhcGD27NlR97lr1y5IkoTXX38dAwYMwLZt2zBr1iw0NDTg+eefP51Pj4iIiDooQZZlOdEH0Rb//Oc/cfXVV8Pj8cBiseCHP/whfD4fPvjgA22bl156CfPnz0dpaSkEQWjRfp977jm8+uqr+O6771p8LHV1dUhLS0NtbS1SU1Nb/VyIiIjo9Gvp53enySzpVVdX4+2338bEiRNhsVgAAB6PB06n07Cdw+HA4cOHUVJSgj59+rRo37W1tcjMzIy7jcfjgcfj0S7X1dW17gkQERFRp9FpapYA4IEHHkBSUhKysrJQWlqKjz/+WLvt4osvxuLFi7F8+XJIkoQ9e/bghRdeAACUlZW1aP/79u3DSy+9hDvvvDPudk8//TTS0tK0n8LCwrY/KSIiIurQEhoszZs3D4IgxP3ZtWuXtv3cuXOxefNmfPLJJzCZTLjlllugjiLOmjULs2fPxuWXXw6r1YoJEybghhtuAACIYvNP88iRI5g2bRp+8IMfYNasWXG3ffDBB1FbW6v9HDp06CTOAhEREXVkCa1ZOn78OKqqquJu069fP1it1ojrDx8+jMLCQnz11VcoLi7Wrg8EAigvL0dOTg6WL1+OSy+9FBUVFcjJyYn5GEePHsXkyZMxYcIELFy4sEXBlR5rloiIiDqfTlGzlJOTEzeIiUeSJAAw1A4BgMlkQs+ePQEA7777LoqLi+M+xpEjRzBlyhSMGzcOCxYsaHWgRERERF1bpyjwXrduHTZs2IBJkyYhIyMD+/fvxyOPPIL+/ftrWaXKykp8+OGHmDx5MpqamrBgwQJ88MEHWLVqlbaf9evX45ZbbsHy5cvRs2dPHDlyBJMnT0ZRURGef/55HD9+XNs2Ly/vtD9PIiIi6ng6RbDkdDqxePFiPPbYY2hoaEB+fj6mTZuGhx9+GDabTdtu0aJFmDNnDmRZRnFxMVauXImzzjpLu93tdmP37t3w+XwAgE8//RT79u3Dvn370KtXL8NjdtKOCkRERNTOOm2fpY6ENUtERESdT0s/v1mgQ0RERBQHgyUiIiKiOBgsEREREcXBYImIiIgoDgZLRERERHEwWCIiIiKKg8ESERERURwMloiIiIjiYLBEREREFAeDJSIiIqI4GCwRERERxcFgiYiIiCgOBktEREREcTBYIiIiIoqDwRIRERFRHAyWiIiIiOJgsEREREQUB4MlIiIiojgYLBERERHFwWCJiIiIKA4GS0RERERxMFgiIiIiioPBEhEREVEcDJaIiIiI4mCwRERERBQHgyUiIiKiOBgsEREREcVhTvQBdAWyLAMA6urqEnwkRERE1FLq57b6OR4Lg6V2UFVVBQAoLCxM8JEQERFRa9XX1yMtLS3m7QyW2kFmZiYAoLS0NO7JpubV1dWhsLAQhw4dQmpqaqIPp1PjuWw/PJfth+eyffA8tg9ZllFfX4+CgoK42zFYageiqJR+paWl8UXbTlJTU3ku2wnPZfvhuWw/PJftg+fx5LUkycECbyIiIqI4GCwRERERxcFgqR3YbDY89thjsNlsiT6UTo/nsv3wXLYfnsv2w3PZPngeTy9Bbm6+HBEREVE3xswSERERURwMloiIiIjiYLBEREREFAeDJSIiIqI4GCydpFdeeQV9+vSB3W7H2WefjfXr1yf6kDq8xx9/HIIgGH6GDBmi3d7U1IS7774bWVlZSE5OxrXXXotjx44l8Ig7jv/+97+44oorUFBQAEEQ8NFHHxlul2UZjz76KPLz8+FwODB16lTs3bvXsE11dTVuuukmpKamIj09HTNnzoTL5TqNz6JjaO5czpgxI+J1Om3aNMM2PJfA008/jTPPPBMpKSnIzc3F1Vdfjd27dxu2acnfdGlpKS677DI4nU7k5uZi7ty58Pv9p/OpJFxLzuXkyZMjXpd33XWXYRuey/bHYOkk/O1vf8N9992Hxx57DF9//TVGjx6Niy++GBUVFYk+tA5v+PDhKCsr036+/PJL7bZ7770X//rXv/DBBx9g1apVOHr0KK655poEHm3H0dDQgNGjR+OVV16Jevv8+fPx+9//Hq+99hrWrVuHpKQkXHzxxWhqatK2uemmm7B9+3Z8+umnWLJkCf773//ijjvuOF1PocNo7lwCwLRp0wyv03fffddwO88lsGrVKtx9991Yu3YtPv30U/h8Plx00UVoaGjQtmnubzoQCOCyyy6D1+vFV199hUWLFmHhwoV49NFHE/GUEqYl5xIAZs2aZXhdzp8/X7uN5/IUkanNzjrrLPnuu+/WLgcCAbmgoEB++umnE3hUHd9jjz0mjx49OuptNTU1ssVikT/44APtup07d8oA5DVr1pymI+wcAMj/+Mc/tMuSJMl5eXnyc889p11XU1Mj22w2+d1335VlWZZ37NghA5A3bNigbfOf//xHFgRBPnLkyGk79o4m/FzKsizfeuut8lVXXRXzPjyX0VVUVMgA5FWrVsmy3LK/6X//+9+yKIpyeXm5ts2rr74qp6amyh6P5/Q+gQ4k/FzKsiyff/758s9//vOY9+G5PDWYWWojr9eLTZs2YerUqdp1oihi6tSpWLNmTQKPrHPYu3cvCgoK0K9fP9x0000oLS0FAGzatAk+n89wXocMGYLevXvzvDbjwIEDKC8vN5y7tLQ0nH322dq5W7NmDdLT0zF+/Hhtm6lTp0IURaxbt+60H3NHt3LlSuTm5mLw4MH4yU9+gqqqKu02nsvoamtrAYQWGG/J3/SaNWswcuRI9OjRQ9vm4osvRl1dHbZv334aj75jCT+XqrfffhvZ2dkYMWIEHnzwQbjdbu02nstTgwvptlFlZSUCgYDhBQkAPXr0wK5duxJ0VJ3D2WefjYULF2Lw4MEoKyvDE088gXPPPRfbtm1DeXk5rFYr0tPTDffp0aMHysvLE3PAnYR6fqK9JtXbysvLkZuba7jdbDYjMzOT5zfMtGnTcM0116Bv377Yv38/HnroIVxyySVYs2YNTCYTz2UUkiThnnvuwTnnnIMRI0YAQIv+psvLy6O+btXbuqNo5xIAfvjDH6KoqAgFBQX45ptv8MADD2D37t1YvHgxAJ7LU4XBEp12l1xyifb7qFGjcPbZZ6OoqAjvv/8+HA5HAo+MKOSGG27Qfh85ciRGjRqF/v37Y+XKlbjwwgsTeGQd1913341t27YZahCpbWKdS31N3MiRI5Gfn48LL7wQ+/fvR//+/U/3YXYbHIZro+zsbJhMpogZHceOHUNeXl6CjqpzSk9Px6BBg7Bv3z7k5eXB6/WipqbGsA3Pa/PU8xPvNZmXlxcxAcHv96O6uprntxn9+vVDdnY29u3bB4DnMtzs2bOxZMkSfP755+jVq5d2fUv+pvPy8qK+btXbuptY5zKas88+GwAMr0uey/bHYKmNrFYrxo0bh+XLl2vXSZKE5cuXo7i4OIFH1vm4XC7s378f+fn5GDduHCwWi+G87t69G6WlpTyvzejbty/y8vIM566urg7r1q3Tzl1xcTFqamqwadMmbZsVK1ZAkiTtTZeiO3z4MKqqqpCfnw+A51IlyzJmz56Nf/zjH1ixYgX69u1ruL0lf9PFxcX49ttvDcHnp59+itTUVAwbNuz0PJEOoLlzGc2WLVsAwPC65Lk8BRJdYd6Zvffee7LNZpMXLlwo79ixQ77jjjvk9PR0wywEinT//ffLK1eulA8cOCCvXr1anjp1qpydnS1XVFTIsizLd911l9y7d295xYoV8saNG+Xi4mK5uLg4wUfdMdTX18ubN2+WN2/eLAOQX3zxRXnz5s1ySUmJLMuy/Mwzz8jp6enyxx9/LH/zzTfyVVddJfft21dubGzU9jFt2jR57Nix8rp16+Qvv/xSHjhwoHzjjTcm6iklTLxzWV9fL8+ZM0des2aNfODAAfmzzz6TzzjjDHngwIFyU1OTtg+eS1n+yU9+IqelpckrV66Uy8rKtB+3261t09zftN/vl0eMGCFfdNFF8pYtW+SlS5fKOTk58oMPPpiIp5QwzZ3Lffv2yb/61a/kjRs3ygcOHJA//vhjuV+/fvJ5552n7YPn8tRgsHSSXnrpJbl3796y1WqVzzrrLHnt2rWJPqQO7/rrr5fz8/Nlq9Uq9+zZU77++uvlffv2abc3NjbKP/3pT+WMjAzZ6XTK3//+9+WysrIEHnHH8fnnn8sAIn5uvfVWWZaV9gGPPPKI3KNHD9lms8kXXnihvHv3bsM+qqqq5BtvvFFOTk6WU1NT5dtuu02ur69PwLNJrHjn0u12yxdddJGck5MjWywWuaioSJ41a1bEFyGeSznqOQQgL1iwQNumJX/TBw8elC+55BLZ4XDI2dnZ8v333y/7fL7T/GwSq7lzWVpaKp933nlyZmambLPZ5AEDBshz586Va2trDfvhuWx/gizL8unLYxERERF1LqxZIiIiIoqDwRIRERFRHAyWiIiIiOJgsEREREQUB4MlIiIiojgYLBERERHFwWCJiIiIKA4GS0RERERxMFgiImoHffr0wW9/+9tEHwYRnQIMloio05kxYwauvvpqAMDkyZNxzz33nLbHXrhwIdLT0yOu37BhA+64447TdhxEdPqYE30AREQdgdfrhdVqbfP9c3Jy2vFoiKgjYWaJiDqtGTNmYNWqVfjd734HQRAgCAIOHjwIANi2bRsuueQSJCcno0ePHrj55ptRWVmp3Xfy5MmYPXs27rnnHmRnZ+Piiy8GALz44osYOXIkkpKSUFhYiJ/+9KdwuVwAgJUrV+K2225DbW2t9niPP/44gMhhuNLSUlx11VVITk5GamoqrrvuOhw7dky7/fHHH8eYMWPw1ltvoU+fPkhLS8MNN9yA+vr6U3vSiKjVGCwRUaf1u9/9DsXFxZg1axbKyspQVlaGwsJC1NTU4IILLsDYsWOxceNGLF26FMeOHcN1111nuP+iRYtgtVqxevVqvPbaawAAURTx+9//Htu3b8eiRYuwYsUK/OIXvwAATJw4Eb/97W+RmpqqPd6cOXMijkuSJFx11VWorq7GqlWr8Omnn+K7777D9ddfb9hu//79+Oijj7BkyRIsWbIEq1atwjPPPHOKzhYRtRWH4Yio00pLS4PVaoXT6UReXp52/csvv4yxY8fiqaee0q578803UVhYiD179mDQoEEAgIEDB2L+/PmGferrn/r06YNf//rXuOuuu/CHP/wBVqsVaWlpEATB8Hjhli9fjm+//RYHDhxAYWEhAOAvf/kLhg8fjg0bNuDMM88EoARVCxcuREpKCgDg5ptvxvLly/Hkk0+e3IkhonbFzBIRdTlbt27F559/juTkZO1nyJAhAJRsjmrcuHER9/3ss89w4YUXomfPnkhJScHNN9+MqqoquN3uFj/+zp07UVhYqAVKADBs2DCkp6dj586d2nV9+vTRAiUAyM/PR0VFRaueKxGdeswsEVGX43K5cMUVV+DZZ5+NuC0/P1/7PSkpyXDbwYMHcfnll+MnP/kJnnzySWRmZuLLL7/EzJkz4fV64XQ62/U4LRaL4bIgCJAkqV0fg4hOHoMlIurUrFYrAoGA4bozzjgDf//739GnTx+YzS1/m9u0aRMkScILL7wAUVQS7++//36zjxdu6NChOHToEA4dOqRll3bs2IGamhoMGzasxcdDRB0Dh+GIqFPr06cP1q1bh4MHD6KyshKSJOHuu+9GdXU1brzxRmzYsAH79+/HsmXLcNttt8UNdAYMGACfz4eXXnoJ3333Hd566y2t8Fv/eC6XC8uXL0dlZWXU4bmpU6di5MiRuOmmm/D1119j/fr1uOWWW3D++edj/Pjx7X4OiOjUYrBERJ3anDlzYDKZMGzYMOTk5KC0tBQFBQVYvXo1AoEALrroIowcORL33HMP0tPTtYxRNKNHj8aLL76IZ599FiNGjMDbb7+Np59+2rDNxIkTcdddd+H6669HTk5ORIE4oAynffzxx8jIyMB5552HqVOnol+/fvjb3/7W7s+fiE49QZZlOdEHQURERNRRMbNEREREFAeDJSIiIqI4GCwRERERxcFgiYiIiCgOBktEREREcTBYIiIiIoqDwRIRERFRHAyWiIiIiOJgsEREREQUB4MlIiIiojgYLBERERHF8f/2F04013HkyAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import the data from log file\n", "data_Jastrow = json.load(open(\"Jastrow.log\"))\n", "\n", "iters_Jastrow = data_Jastrow[\"Energy\"][\"iters\"]\n", "energy_Jastrow = data_Jastrow[\"Energy\"][\"Mean\"][\"real\"]\n", "\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_J<PERSON>row, energy_Jastrow, color=\"C8\", label=\"Energy (Jastrow)\")\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "plt.axis([0, iters_J<PERSON>row[-1], exact_gs_energy - 0.1, exact_gs_energy + 0.4])\n", "plt.axhline(\n", "    y=exact_gs_energy,\n", "    xmin=0,\n", "    xmax=iters_J<PERSON>row[-1],\n", "    linewidth=2,\n", "    color=\"k\",\n", "    label=\"Exact\",\n", ")\n", "ax1.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Well that's not too bad for a simple ansatz. But we can do better, can't we?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Learning with a Restricted Boltzmann Machine (RBM)\n", "\n", "We will now consider another celebrated ansatz, the Restricted Boltzmann Machine (RBM). It simply consists of two layers: a visible one representing the $L$ spin 1/2 degrees of freedom, and an hidden one which contains a different number $M$ of hidden units. There are connections between all visible and hidden nodes. The ansatz is the [following](https://www.netket.org/docs/machine_RbmSpin/)\n", "\n", "$\\Psi_{\\rm RBM} (\\sigma_1^z,\\sigma_2^z, ..., \\sigma_L^z)  = \\exp ( \\sum_{i=1}^L a_i \\sigma_i^z ) \\prod_{i=1}^M \\cosh (b_i + \\sum_j W_{ij} \\sigma^z_j)$\n", "\n", "$a_i$ (resp. $b_i$) are the visible (resp. hidden) bias. Together with the weights $W_{ij}$, they are variational parameters that we (or rather NetKet) will optimize to minimize the energy. Netket gives you the control on the important parameters in this ansatz, such as $M$ and the fact that you want to use or not the biases. The full explanation is [here](https://www.netket.org/docs/machine_RbmSpin/).\n", "\n", "More conveniently (especially if you want to try another $L$ in this tutorial), let's define the hidden unit density $\\alpha = M / L$, and invoke the RBM ansatz in NetKet with as many hidden as visible units."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# RBM ansatz with alpha=1\n", "ma = nk.models.RBM(alpha=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let us use the same sampler (Metropolis exchange) with some different random initial parameters, optimizer (stochastic gradient), and variational method (stochastic reconfiguration) as for the Jastrow ansatz, and let's run things!"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "990870db0a4741088dd4fa81476ce387", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["### RBM calculation\n", "Has 528 parameters\n", "The RBM calculation took 23.886502027511597 seconds\n"]}], "source": ["# Build the sampler\n", "sa = nk.sampler.MetropolisExchange(hilbert=hi, graph=g)\n", "\n", "# Optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# The variational state\n", "vs = nk.vqs.MCState(sa, ma, n_samples=1008)\n", "\n", "# The ground-state optimization loop\n", "gs = nk.VMC(hamiltonian=ha, optimizer=op, preconditioner=sr, variational_state=vs)\n", "\n", "start = time.time()\n", "gs.run(out=\"RBM\", n_iter=600)\n", "end = time.time()\n", "\n", "print(\"### RBM calculation\")\n", "print(\"Has\", vs.n_parameters, \"parameters\")\n", "print(\"The RBM calculation took\", end - start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import the data from log file\n", "data = json.load(open(\"RBM.log\"))\n", "\n", "# Extract the relevant information\n", "iters_RBM = data[\"Energy\"][\"iters\"]\n", "energy_RBM = data[\"Energy\"][\"Mean\"]\n", "\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_J<PERSON>row, energy_Jastrow, color=\"C8\", label=\"Energy (Jastrow)\")\n", "ax1.plot(iters_RBM, energy_RBM, color=\"red\", label=\"Energy (RBM)\")\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "plt.axis([0, iters_RBM[-1], exact_gs_energy - 0.03, exact_gs_energy + 0.2])\n", "plt.axhline(\n", "    y=exact_gs_energy, xmin=0, xmax=iters_RBM[-1], linewidth=2, color=\"k\", label=\"Exact\"\n", ")\n", "ax1.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that this plot zooms closer to the exact ground-state energy. With 600 iterations, we start to see convergence and reach the ground-state energy within about one per thousand, this is already nice! But we are not totally there yet, and in particular the simpler (less parameters) Jastrow wave-function seems to perform better for this example. How can we improve things? As an exercise, try to increase the number of hidden units and/or the number of iterations. What is happening? You can also check out the influence of the learning rate.\n", "\n", "By playing with these parameters, you have hopefully arrived at an improved result, but likely at an increased CPU time cost. Let's do things differently, and take to our advantage the symmetries of the Hamiltonian in the neural network construction.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. RBM again, this time with lattice symmetries\n", "\n", "Let's define a similar RBM machine, which takes into account that the model has translational symmetries. All sites are equivalent and thus many of the wave-functions coefficients are related by symmetry. We use the same exact hyperparameters as in the previous RBM calculation ($\\alpha=1$, same learning rate, and number of samples and iterations in the Variational Monte Carlo) and run now a symmetric RBM."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e6f195f946c48fa90ff89c590e4470f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["### Symmetric RBM calculation\n", "Has 24 parameters\n", "The Symmetric RBM calculation took 13.*************** seconds\n"]}], "source": ["## Symmetric RBM Spin Machine\n", "ma = nk.models.RBMSymm(symmetries=g.translation_group(), alpha=1)\n", "\n", "# Metropolis Exchange Sampling\n", "# Notice that this sampler exchanges two neighboring sites\n", "# thus preservers the total magnetization\n", "sa = nk.sampler.MetropolisExchange(hi, graph=g)\n", "\n", "# Optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.01)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# The variational state\n", "vs = nk.vqs.MCState(sa, ma, n_samples=1008)\n", "\n", "# The ground-state optimization loop\n", "gs = nk.VMC(hamiltonian=ha, optimizer=op, preconditioner=sr, variational_state=vs)\n", "\n", "start = time.time()\n", "gs.run(out=\"RBMSymmetric\", n_iter=300)\n", "end = time.time()\n", "\n", "print(\"### Symmetric RBM calculation\")\n", "print(\"Has\", vs.n_parameters, \"parameters\")\n", "print(\"The Symmetric RBM calculation took\", end - start, \"seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The simulation was much faster, wasn't it? There were of course much less parameters to optimize. Now let's extract the results and plot them using a zoomed scale, and together with the previous results with the RBM."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## import the data from log file\n", "data = json.load(open(\"RBMSymmetric.log\"))\n", "\n", "# Extract the relevant information\n", "iters_symRBM = data[\"Energy\"][\"iters\"]\n", "energy_symRBM = data[\"Energy\"][\"Mean\"]\n", "\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(iters_J<PERSON>row, energy_Jastrow, color=\"C8\", label=\"Energy (Jastrow)\")\n", "ax1.plot(iters_RBM, energy_RBM, color=\"red\", label=\"Energy (RBM)\")\n", "ax1.plot(iters_symRBM, energy_symRBM, color=\"blue\", label=\"Energy (Symmetric RBM)\")\n", "\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "if exact_gs_energy:\n", "    plt.axis([0, iters_symRBM[-1], exact_gs_energy - 0.06, exact_gs_energy + 0.12])\n", "    plt.axhline(\n", "        y=exact_gs_energy,\n", "        xmin=0,\n", "        xmax=iters_RBM[-1],\n", "        linewidth=2,\n", "        color=\"k\",\n", "        label=\"Exact\",\n", "    )\n", "ax1.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Not only the simulation was faster in terms of CPU time, but we are now reaching the ground-state in a much lower number of iterations! Imposing symmetries greatly helps, and NetKet allows to do this. Note that there is also a symmetric version of the Jastrow ansatz that we tested earlier in NetKet, which is called `JastrowSymm`. As an exercise, check it out. What you will find is that while it converges slightly faster in terms of iterations with respect to the non-symmetric Jastrow, it does not improve the estimate of the ground-state energy. We actually see that the symmetric RBM sets the standard very high."]}, {"cell_type": "markdown", "metadata": {"lines_to_next_cell": 2}, "source": ["## 5. Learning with Feed Forward Neural Networks\n", "\n", "Now let's try a more complex network, namely a Feed Forward Neural Network (FFNN). There you will have more freedom to construct your own specific architecture. We'll try two different FFNN in this tutorial.\n", "\n", "The first one is a simple structure: the first layer takes as input L-dimensional input, applies a bias and outputs two times more data, just followed by a `Lncosh` activation layer. The final layer `SumOutput` is needed to obtain a single number for the wave-function coefficient associated to the input basis state.\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5949a3c26a884b718577f01c31dd7d75", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["### Feed Forward calculation\n", "Has 1012 parameters\n", "The Feed Forward calculation took 51.89680004119873 seconds\n"]}], "source": ["class Model(nnx.Module):\n", "\n", "    def __init__(self, N: int, *, rngs: nnx.Rngs):\n", "        self.linear = nnx.Linear(\n", "            in_features=N, out_features=2 * N, dtype=jnp.complex128, rngs=rngs\n", "        )\n", "\n", "    def __call__(self, x: jax.Array):\n", "        x = self.linear(x)\n", "        x = nk.nn.activation.log_cosh(x)\n", "        return jnp.sum(x, axis=-1)\n", "\n", "\n", "ffnn = Model(N=hi.size, rngs=nnx.Rngs(1))\n", "\n", "sa = nk.sampler.MetropolisExchange(hi, graph=g)\n", "\n", "# The variational state\n", "vs = nk.vqs.MCState(sa, ffnn, n_samples=1008)\n", "\n", "opt = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.1, holomorphic=True)\n", "\n", "# The ground-state optimization loop\n", "gs = nk.VMC(hamiltonian=ha, optimizer=op, preconditioner=sr, variational_state=vs)\n", "\n", "\n", "start = time.time()\n", "gs.run(out=\"FF\", n_iter=300)\n", "end = time.time()\n", "\n", "print(\"### Feed Forward calculation\")\n", "print(\"Has\", vs.n_parameters, \"parameters\")\n", "print(\"The Feed Forward calculation took\", end - start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import the data from log file\n", "data = json.load(open(\"FF.log\"))\n", "\n", "# Extract the relevant information\n", "iters_FF = data[\"Energy\"][\"iters\"]\n", "energy_FF = data[\"Energy\"][\"Mean\"][\"real\"]\n", "\n", "fig, ax1 = plt.subplots()\n", "ax1.plot(\n", "    it<PERSON>_<PERSON><PERSON><PERSON>,\n", "    energy_<PERSON><PERSON><PERSON>,\n", "    color=\"C8\",\n", "    linestyle=\"None\",\n", "    marker=\"d\",\n", "    label=\"Energy (Jastrow)\",\n", ")\n", "ax1.plot(\n", "    iters_RBM,\n", "    energy_RBM,\n", "    color=\"red\",\n", "    marker=\"o\",\n", "    linestyle=\"None\",\n", "    label=\"Energy (RBM)\",\n", ")\n", "ax1.plot(\n", "    iters_symRBM,\n", "    energy_symRBM,\n", "    color=\"blue\",\n", "    linestyle=\"None\",\n", "    marker=\"o\",\n", "    label=\"Energy (Symmetric RBM)\",\n", ")\n", "ax1.plot(\n", "    iters_FF,\n", "    energy_FF,\n", "    color=\"orange\",\n", "    marker=\"s\",\n", "    linestyle=\"None\",\n", "    label=\"Energy (Feed Forward, take 1)\",\n", ")\n", "ax1.legend(bbox_to_anchor=(1.05, 0.3))\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "plt.axis([0, iters_FF[-1], exact_gs_energy - 0.02, exact_gs_energy + 0.1])\n", "plt.axhline(\n", "    y=exact_gs_energy, xmin=0, xmax=iters_RBM[-1], linewidth=2, color=\"k\", label=\"Exact\"\n", ")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"lines_to_next_cell": 2}, "source": ["The results are clearly better than a simple (non-symmetrized RBB), and perform slightly better than the Jastrow ansatz. Let us increase the number of layers by adding a fully-connected layer with bias and  `Lncosh` activation (with $2L$ inputs and outputs) before the final `SumOutput` layer."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a93dc276691a4b878c71b90821011e2c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["### Feed Forward (more layers) calculation\n", "Has 1012 parameters\n", "The Feed Forward (more layers) calculation took 99.36077523231506 seconds\n"]}], "source": ["class Model2(nnx.Module):\n", "\n", "    def __init__(self, N: int, *, rngs: nnx.Rngs):\n", "        self.linear1 = nnx.Linear(\n", "            in_features=N, out_features=2 * N, dtype=jnp.complex128, rngs=rngs\n", "        )\n", "        self.linear2 = nnx.Linear(\n", "            in_features=2 * N, out_features=N, dtype=jnp.complex128, rngs=rngs\n", "        )\n", "\n", "    def __call__(self, x: jax.Array):\n", "        x = self.linear1(x)\n", "        x = nk.nn.activation.log_cosh(x)\n", "        x = self.linear2(x)\n", "        x = nk.nn.activation.log_cosh(x)\n", "        return jnp.sum(x, axis=-1)\n", "\n", "\n", "ffnn2 = Model2(N=hi.size, rngs=nnx.Rngs(1))\n", "\n", "# The variational state\n", "vs = nk.vqs.MCState(sa, ffnn, n_samples=1008)\n", "\n", "opt = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.1, holomorphic=True)\n", "\n", "# The ground-state optimization loop\n", "gs = nk.VMC(hamiltonian=ha, optimizer=op, preconditioner=sr, variational_state=vs)\n", "\n", "\n", "start = time.time()\n", "gs.run(out=\"FF2\", n_iter=600)\n", "end = time.time()\n", "\n", "\n", "print(\"### Feed Forward (more layers) calculation\")\n", "print(\"Has\", vs.n_parameters, \"parameters\")\n", "print(\"The Feed Forward (more layers) calculation took\", end - start, \"seconds\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import the data from log file\n", "data = json.load(open(\"FF2.log\"))\n", "\n", "# Extract the relevant information\n", "iters_FF_morelayers = data[\"Energy\"][\"iters\"]\n", "energy_FF_morelayers = data[\"Energy\"][\"Mean\"][\"real\"]\n", "\n", "fig, ax1 = plt.subplots()\n", "# ax1.plot(iters_J<PERSON>row, energy_Jastrow, color='C8',linestyle=\"None\", marker='d',label='Energy (Jastrow)')\n", "# ax1.plot(iters_RBM, energy_RBM, color='red', label='Energy (RBM)')\n", "# ax1.plot(iters_symRBM, energy_symRBM, color='blue',linestyle=\"None\",marker='o',label='Energy (Symmetric RBM)')\n", "ax1.plot(\n", "    iters_FF,\n", "    energy_FF,\n", "    color=\"orange\",\n", "    marker=\"s\",\n", "    alpha=0.5,\n", "    linestyle=\"None\",\n", "    label=\"Energy (Feed Forward, take 1)\",\n", ")\n", "ax1.plot(\n", "    iters_FF_morelayers,\n", "    energy_FF_morelayers,\n", "    color=\"green\",\n", "    marker=\"s\",\n", "    linestyle=\"None\",\n", "    alpha=1,\n", "    label=\"Energy (Feed Forward, more layers)\",\n", ")\n", "ax1.legend(bbox_to_anchor=(1.05, 0.5))\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "plt.axis([0, iters_RBM[-1], exact_gs_energy - 0.02, exact_gs_energy + 0.06])\n", "plt.axhline(\n", "    y=exact_gs_energy, xmin=0, xmax=iters_RBM[-1], linewidth=2, color=\"k\", label=\"Exact\"\n", ")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The results are even better, but at the price of an increase in computational time....\n", "\n", "Note that more complex structures, such as Convolutional Neural Networks (CNN), can also be used within Netket. However, for such 1d systems, they do not bring too much compared to the symmetric RBM (as a matter of fact, the symmetric RBM is a special type of a simple CNN. CNNs show their full strength for more complex systems, such as 2d quantum systems. Convolutional Neural Networks will be the topic of another tutorial in NetKet (and we'll make there the connection with the special case of the symmetric RBM).\n", "\n", "Finally let us conclude that another type of machine, Matrix Product States (MPS), is also available in NetKet. They do perform extremely well for 1d quantum systems. Since however they are a bit different, they will be presented in another tutorial."]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python (Netket development)", "language": "python", "name": "dev-netket"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}