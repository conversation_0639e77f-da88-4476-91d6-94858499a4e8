{"cells": [{"cell_type": "markdown", "id": "bcc1a161", "metadata": {}, "source": ["# Ground-State: <PERSON><PERSON> in a 3D Harmonic trap\n", "\n", "<PERSON> (EPFL-CQSL)\n", "\n", "13 September, 2023"]}, {"cell_type": "markdown", "id": "ae721781-2989-46c5-a92b-ca528b145e6c", "metadata": {}, "source": ["In this Tutorial we will explore the capabilities of NetKet to study the ground-state of bosonic many-body quantum systems in continuous space. As a first step we will consider an easy problem of non-interacting harmonic oscillators in free, three dimensional space, to introduce and explain the moving parts needed to perform a VMC optimization. Subsequently a more challenging example of interacting bosons in one dimensions with periodic boundary conditions will be studied.\n", "\n", "Specifically, we will study the following two Hamiltonians:\n", "\n", "$$\n", "\n", "\\mathcal{H}= -\\frac{\\hbar^2}{2m}\\sum_i \\nabla_{\\mathbf{x}_i}^2 + \\frac{1}{2} m \\omega^2 \\sum_i |\\mathbf{x}_i|^2\n", "\n", "$$\n", "\n", "and\n", "\n", "$$\n", "\n", "\\mathcal{H}= -\\frac{\\hbar^2}{2m}\\sum_i \\nabla_{\\mathbf{x}_i}^2 + \\epsilon \\sum_{i<j} \\exp\\left[-\\frac{1}{2}|\\mathbf{r}_{ij}|^2\\right]\n", "\n", "$$\n", "\n", ",where the first term represents the kinetic energy contribution of particles of mass $m$, and the second term is the potential (interaction) energy ( $ \\mathbf{r}_{ij} = \\mathbf{x}_i-\\mathbf{x}_j $ ). In the following we set $m = \\omega = \\hbar = \\frac{\\epsilon}{2} =1$."]}, {"cell_type": "markdown", "id": "ee2e7463-5f8b-416e-9383-6f95bd3c4098", "metadata": {"tags": []}, "source": [":::{note}\n", "If you are executing this notebook on **Colab**, you will need to install NetKet.\n", "You can do so by uncommenting and running the following cell.\n", "\n", "Keep in mind that this notebook was designed for NetKet version `3.9.1`, which requires Python >=3.8. If you do not have access to a recent version of Python we strongly recomend to run this notebook on google Colab.\n", ":::"]}, {"cell_type": "code", "execution_count": 1, "id": "43e97750", "metadata": {"tags": []}, "outputs": [], "source": ["# %pip install --upgrade netket>=3.9.1"]}, {"cell_type": "markdown", "id": "3fb82509-cbdf-4e50-bf52-1c66afbc9fb5", "metadata": {}, "source": ["Please verify that you are running on the correct version of NetKet. If the installation installed an older version of NetKet, it might be because your Python version is too old.\n", "In that case, you will need to upgrade python or use Colab.\n", "\n", "It might also be that in the future NetKet changes. This notebook was authored for the following version of NetKet:"]}, {"cell_type": "code", "execution_count": 2, "id": "5a4c83c9", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version (requires >=3.9) 3.13.2\n", "NetKet version (requires >=3.9.1) 3.17.2.dev7+g24d80ac4.d20250527\n"]}], "source": ["import platform\n", "import netket as nk\n", "\n", "print(\"Python version (requires >=3.9)\", platform.python_version())\n", "print(\"NetKet version (requires >=3.9.1)\", nk.__version__)"]}, {"cell_type": "markdown", "id": "d8b7effe", "metadata": {}, "source": ["## 1. Defining The Hilbert Space and Sampler\n", "\n", "The first ingredient in defining our problem in NetKet, consists of defining the Hilbert Space to study.\n", "For continuous space systems, the <PERSON><PERSON> space takes information about the number of particles, the size of the system and the type of boundary conditions: open or periodic. The dimensionality of space is inferred from the size of the system.\n", "The species of particles (bosonic or fermionic) is not relevant for the <PERSON>lbert space but only in defining the variational Ansatz (symmetric or antisymmetric).\n", "In the following the <PERSON>lbert space for $N=10$ particles in open (infinite) space is defined."]}, {"cell_type": "code", "execution_count": 3, "id": "65e5273d", "metadata": {"tags": []}, "outputs": [], "source": ["import jax.numpy as jnp\n", "\n", "N = 10\n", "\n", "# 10 particles in 3D space without periodic boundary conditions.\n", "# The length of the L-tuple indicates the spatial dimension of space.\n", "geo = nk.experimental.geometry.FreeSpace(d=3)\n", "hi = nk.experimental.hilbert.Particle(N=N, geometry=geo)"]}, {"cell_type": "markdown", "id": "fa84f3e3-5a80-4d9e-91d0-6b13e3924839", "metadata": {}, "source": ["We can now generate random configurations in the Hilbert space, we just defined. For the above specifications the random state will consists of a vector of $N*d = 10*3$ numbers ($d$ number of spatial dimensions), representing the 10 3D positions of the particles under consideration."]}, {"cell_type": "code", "execution_count": 4, "id": "4b2bf39c-ae7f-4cb0-b141-093c448138e5", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["Array([[-0.20584214, -0.78476578,  1.81608667],\n", "       [ 0.18784401,  0.08086788, -0.37211079],\n", "       [ 1.19016372,  0.33864229,  0.08482584],\n", "       [-0.87181784,  1.05451609, -1.5594979 ],\n", "       [ 0.36753958,  2.51635215,  0.25856516],\n", "       [-0.28371043, -0.53389115,  0.36794769],\n", "       [-0.1959642 ,  1.50407679,  0.05904905],\n", "       [ 0.1415813 ,  0.18851565,  0.04672933],\n", "       [-1.4134907 , -0.19496338,  1.11302457],\n", "       [-2.22703843, -0.56259627,  0.30136156]], dtype=float64)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import jax\n", "\n", "states = hi.random_state(jax.random.key(0), 1)\n", "\n", "# You can always reshape those configurations to NxD matrix\n", "states.reshape(N, 3)"]}, {"cell_type": "markdown", "id": "67bdd81a-b602-47be-a584-1592c8b4c7db", "metadata": {}, "outputs": [], "source": ["Of course we are not particularily interested in random states of our Hilbert space, but we rather want to be able to sample from a probability distribution, defined in the Hilbert space. To do this, NetKet provides the Metropolis-Hasting sampler. This sampler uses an initial state in the Hilbert space (e.g. a random state) and produces a new state according to a transtition rule. The new state is accepted (e.g. becomes the initial state for another update according to the transition rule) if its probability is high enough (the probability of the state is determined by the variational Ansatz, see later). While you can write your own transition rule, NetKet already implements a common transition rule, that often is sufficient to run a VMC optimization.\n", "\n", "For distributed computing across multiple devices, NetKet uses JAX's automatic sharding capabilities. This allows for efficient parallelization of sampling and computation without requiring manual configuration.\n", "\n", "The transition rule is given by adding normally distributed noise to the initial state:\n", "\n", "$$\n", "\n", "\\mathbf{X} = (\\mathbf{x}_1,...,\\mathbf{x}_N) \\mapsto \\mathbf{X} + \\mathbf{\\Delta} = (\\mathbf{x}_1 + \\mathbf{\\delta}_1,...,\\mathbf{x}_N +\\mathbf{\\delta}_N),\n", "\n", "$$\n", "\n", "where $ \\mathbf{\\delta}_i \\sim \\mathcal{N}(0,\\sigma^2) $ and $ \\sigma $ is chosen by the user, dependent on the system under consideration.\n", "To decorrelate samples, the sampler provides functionality to insert more substeps of the above rule (e.g. add Gaussian noise multiple times). You can also specify how many parallel chains of the above rule you want to run. In the following, we initialize the sampler, with $ \\sigma=0.1 $, 16 parallel chains, with 32 substeps of the transition rule."]}, {"cell_type": "code", "execution_count": 5, "id": "46cbd4f6-fd75-4886-a5e5-833a2479e9c3", "metadata": {"tags": []}, "outputs": [], "source": ["sa = nk.sampler.MetropolisGaussian(hi, sigma=0.1, n_chains=16, sweep_size=32)"]}, {"cell_type": "markdown", "id": "6ec6a212-501a-46a0-8308-75261b191735", "metadata": {}, "source": ["## 2. Defining The Hamiltonian\n", "\n", "Now that the <PERSON>lbert space is defined, we also have to specify the Hamiltonian the particles are subject to. As written above we will start with a simple example of non-interacting particles, confined by a harmonic potential e.g. a collection of harmonic oscillators.\n", "A Hamiltonian in continuous space usually consists of two parts: kinetic and potential energy.\n", "The form of the kinetic energy is always the same and only depends on the mass of the particles.\n", "NetKet provides a full implementation of the the kinetic energy, which is simply the trace of the (weighted) Hessian taken with respect to all the input variables of the variational state.\n", "\n", "$$\n", "    E_{kin} = \\sum_{i=1}^N \\frac{1}{2m_i}\\frac{\\partial}{\\partial \\mathbf{x}_i} \\cdot \\frac{\\partial}{\\partial \\mathbf{x}_i} = \\sum_{i=1}^N \\sum_{j=1}^D \\frac{1}{2m_i}\\frac{\\partial^2}{\\partial x_{ij}^2}\n", "$$\n", "\n", "In the following we initialize the kinetic energy operator for particles of masses $ m=1 $ (one could also provide a tuple of different masses for each particle).\n"]}, {"cell_type": "code", "execution_count": 6, "id": "af038a91-76dd-401b-b1af-4ada588de369", "metadata": {"lines_to_next_cell": 2, "tags": []}, "outputs": [], "source": ["ekin = nk.operator.KineticEnergy(hi, mass=1.0)"]}, {"cell_type": "markdown", "id": "59025d8c-dede-496f-b2f0-6ad63966be1d", "metadata": {"lines_to_next_cell": 2}, "source": ["The potential energy part changes when changing from one system to another and therefore needs to be implemented from scratch. In NetKet this amounts to writing a function that takes a single sample $ \\mathbf{X} $ and outputs a single number representing the potential energy of the given configuration. For a harmonic confinement we have:\n", "\n", "$$\n", "\n", "\\mathbf{X} \\mapsto \\frac{1}{2}\\sum_i |\\mathbf{x}_i|^2\n", "\n", "$$\n", "\n", "or in code:"]}, {"cell_type": "code", "execution_count": 7, "id": "fca27abf-6920-4ec6-b014-30d9363b8215", "metadata": {"tags": []}, "outputs": [], "source": ["def v(x):\n", "    return 0.5 * jnp.linalg.norm(x) ** 2"]}, {"cell_type": "markdown", "id": "0ea6a83f-6766-4449-8462-6ec218764aaf", "metadata": {}, "source": ["To use this function as an operator, NetKet provides a potential energy operator:"]}, {"cell_type": "code", "execution_count": 8, "id": "46782f67-cde8-448c-bafe-f59c4d15f784", "metadata": {"tags": []}, "outputs": [], "source": ["pot = nk.operator.PotentialEnergy(hi, v)"]}, {"cell_type": "markdown", "id": "1d3eedb3-40f3-4620-b8fa-fc8884d2750f", "metadata": {}, "source": ["All left to do to define the complete Hamiltonian, is combine the kinetic and potential energy operators into a single operator object:"]}, {"cell_type": "code", "execution_count": 9, "id": "59723a64-37d4-40a5-9c0e-71ccbd969e7e", "metadata": {"tags": []}, "outputs": [], "source": ["ha = ekin + pot"]}, {"cell_type": "markdown", "id": "8e51cc79", "metadata": {}, "source": ["## 3. Exact <PERSON>\n", "\n", "The most important thing for every VMC simulation is the variational Ansatz to the problem. If the Ansatz is capable of representing the true ground-state, the VMC results will be significantly better compared to a poorly chosen Ansatz that cannot represent the ground-state.\n", "For the case of non-interacting harmonic oscillators we know exactly what the many-body ground-state looks like:\n", "\n", "$$\n", "\n", "\\psi(\\mathbf{X}) = \\prod_i \\exp\\left[-\\frac{1}{2} |\\mathbf{x}_i|^2\\right].\n", "\n", "$$\n", "\n", "with associated probability distribution $P(\\mathbf{X}) = \\prod_i \\exp\\left[-|\\mathbf{x}_i|^2\\right]$.\n", "To provide some first experience with NetKet in continuous space, we first choose a variational parameterization that can exactly represent the above state:\n", "\n", "$$\n", "\n", "\\psi(\\mathbf{X}) = \\exp\\left[-\\frac{1}{2} \\mathbf{X}^T\\Sigma^{-1}\\mathbf{X}\\right].\n", "\n", "$$\n", "\n", "which is the ground-state for $ \\Sigma^{-1} = \\mathbb{I} $.\n", "\n", "The Model can be defined using one of the several *functional* jax frameworks such as Jax/Stax, Flax or Haiku.\n", "NetKet includes several pre-built models and layers built with [Flax](https://github.com/google/flax), including the above multivariate Gaussian Ansatz. For completeness the model definition is copied below (to make sure that $ \\Sigma^{-1} $ is a proper covariance matrix (positive definite), we parameterize it as $ \\Sigma^{-1} = T T^T $):"]}, {"cell_type": "code", "execution_count": 10, "id": "967e1611", "metadata": {"tags": []}, "outputs": [], "source": ["import flax.linen as nn\n", "\n", "from flax.linen.dtypes import promote_dtype\n", "from flax.linen.initializers import normal\n", "\n", "from netket.utils.types import DType, Array, NNInitFunc\n", "\n", "\n", "class Gaussian(nn.<PERSON>):\n", "    r\"\"\"\n", "    Multivariate Gaussian function with mean 0 and parametrised covariance matrix\n", "    :math:`\\Sigma_{ij}`.\n", "\n", "    The wavefunction is given by the formula: :math:`\\Psi(x) = \\exp(\\sum_{ij} x_i \\Sigma_{ij} x_j)`.\n", "    The (positive definite) :math:`\\Sigma_{ij} = AA^T` matrix is stored as\n", "    non-positive definite matrix A.\n", "    \"\"\"\n", "\n", "    param_dtype: DType = jnp.float64\n", "    \"\"\"The dtype of the weights.\"\"\"\n", "    kernel_init: NNInitFunc = normal(stddev=1.0)\n", "    \"\"\"Initializer for the weights.\"\"\"\n", "\n", "    @nn.compact\n", "    def __call__(self, x_in: Array):\n", "        nv = x_in.shape[-1]\n", "\n", "        kernel = self.param(\"kernel\", self.kernel_init, (nv, nv), self.param_dtype)\n", "        kernel = jnp.dot(kernel.T, kernel)\n", "\n", "        kernel, x_in = promote_dtype(kernel, x_in, dtype=None)\n", "        y = -0.5 * jnp.einsum(\"...i,ij,...j\", x_in, kernel, x_in)\n", "\n", "        return y"]}, {"cell_type": "markdown", "id": "c1ccb969-487e-43e6-ac87-50cde68c68e1", "metadata": {}, "source": ["The model above gives instructions on how to initialize the variational parameters and evaluate the model. To use it in the context of the VMC optimization, NetKet provides the Monte-Carlo-sampled Variational State `netket.vqs.MCState`, which requires a sampler and a model from which to sample from. We also have to provide the number of samples from which expectation values of operators are computed, at each iteration of the VMC optimization.\n", "In the following we construct the variational state from the multivariate Gaussian Ansatz above, using the Gaussian sampler to sample from it and we choose a total of 10000 samples per iteration."]}, {"cell_type": "code", "execution_count": 11, "id": "c2cb0ac3-bc4c-4743-adec-03bb4e2d7c55", "metadata": {"tags": []}, "outputs": [], "source": ["# Create an instance of the model.\n", "# Notice that this does not create the parameters.\n", "Gauss = Gaussian()\n", "\n", "# Construct the variational state using the model and the sampler above.\n", "# n_samples specifies how many samples should be used to compute expectation\n", "# values.\n", "vstate = nk.vqs.MCState(sa, Gauss, n_samples=10**4, n_discard_per_chain=100)"]}, {"cell_type": "markdown", "id": "75f68957-2ea1-4405-b742-7ae5eb009c35", "metadata": {}, "source": ["You can play around with the variational state: for example, you can compute expectation values yourself or inspect it's parameters"]}, {"cell_type": "code", "execution_count": 12, "id": "578fae25-6b39-41c1-a25e-51de791b7aa3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x127e8c980>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# you can inspect the parameters which form a N*dxN*d matrix\n", "plt.imshow(vstate.parameters[\"kernel\"])\n", "plt.colorbar()"]}, {"cell_type": "code", "execution_count": 13, "id": "0ea20c03-dcfa-4cda-8f2f-b1d86a95872b", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["243.7 ± 1.9 [σ²=7275.1, R̂=1.0092]\n"]}], "source": ["# Expectation value: notice that it also provides an error estimate.\n", "E = vstate.expect(ha)\n", "print(E)"]}, {"cell_type": "code", "execution_count": 14, "id": "df25e787-deb1-4c3c-a6a8-5150948a6f3f", "metadata": {"lines_to_next_cell": 2, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean                  : 243.67295169464077\n", "Error                 : 1.94587920493069\n", "Variance              : 7275.126948395135\n", "Convergence indicator : 1.0092192481951645\n", "Correlation time      : 2.102322892122845\n"]}], "source": ["# the energy (expectation value) is a structure with a lot of fields:\n", "print(\"Mean                  :\", E.mean)\n", "print(\"Error                 :\", E.error_of_mean)\n", "print(\"Variance              :\", E.variance)\n", "print(\"Convergence indicator :\", E.R_hat)\n", "print(\"Correlation time      :\", E.tau_corr)"]}, {"cell_type": "markdown", "id": "1b5b6302", "metadata": {"tags": []}, "source": ["## 4. Variational Monte Carlo"]}, {"cell_type": "markdown", "id": "8ab77267-892e-48d2-97e2-eee6c8eae796", "metadata": {}, "source": ["We will now try to optimise the covariance matrix $ \\Sigma^{-1} $ in order to best approximate the ground state of the hamiltonian, which has a ground-state energy of $ \\frac{1}{2}N*d=15 $.\n", "\n", "At first, we'll try to do this by ourself by writing the training loop, but then we'll switch to using a pre-made\n", "solution provided by netket for simplicity."]}, {"cell_type": "markdown", "id": "a10ade91-7ad7-4a8c-997d-e4ee9098b2d7", "metadata": {}, "source": ["### 4. Use NetKet's optimisation driver\n", "\n", "The optimisation (or training) loop must do a very simple thing: at every iteration it must compute the energy and it’s gradient, then multiply the gradient by a certain learning rate and lastly it must update the parameters with this rescaled gradient.\n", "\n", "We use NetKet's default VMC optimisation loop for that"]}, {"cell_type": "code", "execution_count": 15, "id": "4572b5c5", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9ce62c4fcfd4842bf7f5eb383c12d66", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy and relative error:  14.999987 ± 0.000031 [σ²=0.000002, R̂=1.0079] 8.498904531251128e-07\n"]}], "source": ["# First we reset the parameters to run the optimisation again\n", "vstate.init_parameters(normal(stddev=1.0))\n", "\n", "# Then we create an optimiser from the standard library.\n", "# You can also use optax.\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# build the optimisation driver\n", "gs = nk.driver.VMC(ha, optimizer, variational_state=vstate)\n", "\n", "# run the driver for 150 iterations. This will display a progress bar\n", "# by default.\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=150, out=log)\n", "\n", "energy = vstate.expect(ha)\n", "error = jnp.abs(energy.mean - 15.0) / 15.0\n", "print(\"Optimized energy and relative error: \", energy, error)"]}, {"cell_type": "code", "execution_count": 16, "id": "4f4b1a2d-fcbf-4248-ae18-d0f527ac67e5", "metadata": {"lines_to_next_cell": 2, "tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x13382d450>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAesAAAGiCAYAAADHpO4FAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAJ5lJREFUeJzt3QtsVFX+wPHfFGkBoSAiLYXKU0UEioIgIggLS0XDirIJDyOPIAQFAzQK1uUprl11ZVndCvGBaFYE2Qisj9RgFVhikQASl0RYQdyi0gJuoFCk4NzzzznY+TMwhc7cKb1n7vdjTsrc3tN7rwP9ze88A0opJQAAwLOSavsGAADAxRGsAQDwOII1AAAeR7AGAMDjCNYAAHgcwRoAAI8jWAMA4HEEawAAPI5gDQCAxxGsAQDwOII1AABR2LRpkwwdOlQyMjIkEAjI2rVrL1lnw4YNcsstt0hKSop06NBBli9fHs0lCdYAAESjvLxcsrKyJD8/v1rn79+/X+655x4ZMGCA7Ny5U6ZPny4PPfSQfPzxx9W+ZoCNPAAAiI3OrNesWSPDhg2r8pxZs2bJhx9+KLt27QodGzlypBw9elQKCgqqdZ0rxGMcx5Eff/xRGjVqZP4nAADsonPA48ePm2bipKSaa8A9deqUnD59Oi73e3680c3VusRDUVGRDBo0KOxYdna2ybCry3PBWgfqzMzM2r4NAIBLBw4ckFatWtVYoG7buqGUHAq6/lkNGzaUEydOhB2bN2+ezJ8/X+KhpKRE0tLSwo7p12VlZfLzzz9L/fr17QvWOqPW/rujjaQ2rN4nsvuu71LDdwUAqK5f5Ixslo9Cv89rwunTp02g3r+9taQ2ij17LzvuSNvu/zUfLFJTU0PH45VVx0uNBWvd8f7888+bTxS6I/6ll16Snj17XrJeZVOEDtTVfQOuCNR1fb8AgDj5dSTU5ejKTG1U/Vhx0Z+TmhoWrOMpPT1dSktLw47p1/p61cmqtRrpTFi1apXk5OSYZoQdO3aYYK3b5w8dOlQTlwMA+FRQOa5LTevdu7cUFhaGHVu/fr05Xl01EqwXLVokEydOlPHjx0unTp1k6dKl0qBBA1m2bFlNXA4A4FOOKNclWrp/W0/B0qVyapb+c3FxsXmdm5srY8aMCZ0/efJk+fbbb2XmzJmye/duefnll+Xdd9+VGTNm1F6w1v0I27dvDxv5pkcD6td6RNz5KioqTCf7uQUAgOpw4vBftLZt2yY333yzKZpuSdZ/njt3rnl98ODBUODW2rZta6Zu6WxatzS/8MIL8tprr5kW51rrsz5y5IgEg8GII9/0J4rz5eXlyYIFC+J9GwAA1Ij+/fub6V5VibQ6ma7z5ZdfxnzNWl/BTDcXHDt2LFT0iDwAAKojqJTrYoO4Z9bNmjWTOnXqRBz5pkfEnS+eE88BAP7ixNjvfG59G8Q9s05OTpbu3buHjXzTq5Lp19GMfAMAADU4z1p3to8dO1Z69Ohh5lYvXrzYLHyuR4cDABAvjigJ+iCzrpFgPWLECDl8+LAZGacXRenWrZtZrPz8QWeXWpWsuoudfPzj2eHz0cjO6BZ1HQCAtzg+aQavsRXMpk6dagoAAHDHc2uDAwBQXUGXI7p9OxocAIDLxfm1uKlvg1qfZw0AAC6OzBoAYK2gy9HgbupeTgRrAIC1gupscVPfBgRrAIC1HPqsAQCAF5BZAwCs5UhAghJwVd8GBGsAgLUcdba4qW8DmsEBAPA4MmsAgLWCLpvB3dS9nBIiWMeyKUe0m3+w8QcAeE/QJ8GaZnAAADwuITJrAIA/OSpgipv6NiBYAwCsFaQZHAAAeAGZNQDAWkFJMiX2+nYgWAMArKVc9lnr+jYgWAMArBWkzxoAAHgBmTUAwFpBlWRK7PXFCgRrAIC1HAmI46KR2BE7ojXN4AAAeJxvM+to1/pmLXEA8J6gTwaY+TZYAwDsF3TdZ00zOAAAiAMyawCA5QPMAq7q24BgDQCwluNyuVFGgwMAgLggswYAWCvokwFmBGsAgNXN4I4PmsEJ1gAAawVVwBQ39W1AnzUAAB5HZg0AsFbQ5WjwIM3gAADULEclmRJ7fYJ1QqnptcRjuQYAwB8I1gAAawVpBgcAwNsclyO6dX0bMBocAACPI7MGAPh4UZQksQHBGgDg4+VGk8QGdtwlAAA+RmYNALCWw37WAAB4W9AnzeAEawCAj+dZJ4kN7LhLAAB8jMwaAGAtRwVMcVPfBgRrAIC1HJfN4Myz9rlYNuWIdvMPNv4AAH8gWAMAfLxFZpLYgGANALBWUAKmuKlvg7h/pJg/f74EAoGw0rFjx3hfBgAA36iRzPqmm26STz755P8vcgUJPAAg/hyawV380CuukPT09GqdW1FRYUqlsrKymrglAEACCrpsytb1bVAjHym++eYbycjIkHbt2skDDzwgxcXFVZ6bl5cnjRs3DpXMzMyauCUAAKwV92Ddq1cvWb58uRQUFMiSJUtk//790rdvXzl+/HjE83Nzc+XYsWOhcuDAgXjfEgAgwZvBHRfFl83gQ4YMCf25a9euJni3bt1a3n33XZkwYcIF56ekpJgCAEC0gj7ZyKPG77JJkyZy/fXXy969e2v6UgAAn1G/bpEZa9H1Y5Gfny9t2rSRevXqmaR069atFz1/8eLFcsMNN0j9+vVNd++MGTPk1KlT3gnWJ06ckH379kmLFi1q+lIAANS4VatWSU5OjsybN0927NghWVlZkp2dLYcOHYp4/ooVK+SJJ54w53/99dfy+uuvm5/x5JNP1l6wfuyxx2Tjxo3y3Xffyeeffy733Xef1KlTR0aNGhXvSwEAfC74azO4mxKtRYsWycSJE2X8+PHSqVMnWbp0qTRo0ECWLVsW8XwdC/v06SOjR4822fjgwYNNTLxUNl6jfdbff/+9uYmffvpJrrnmGrnjjjtky5Yt5s+I71rfrCUOwO+cOO26df604arGU50+fVq2b99uBkdXSkpKkkGDBklRUVHEa9x+++3y97//3QTnnj17yrfffisfffSRPPjgg7UXrFeuXBnvHwkAQI06f9qwbrLWK3Ke78iRIxIMBiUtLS3suH69e/fuiD9bZ9S6nk5elVLyyy+/yOTJk6NqBmdpMQCAtYIut8isrKunDaempoaOx3OW0oYNG+SZZ56Rl19+2QxG0wOup02bJgsXLpQ5c+ZU62cQrAEA4vdm8NTU1LBgXZVmzZqZcVilpaVhx/Xrqlbu1AFZN3k/9NBD5nWXLl2kvLxcJk2aJH/4wx9MM/ql2DHBDAAAD0hOTpbu3btLYWFh6JjjOOZ17969I9Y5efLkBQFZB3xNN4tXB5k1AMBajiSZ4qZ+tPS0rbFjx0qPHj3MgDE9h1pnynp0uDZmzBhp2bKlWU5bGzp0qBlBfvPNN4eawXW2rY9XBu1LIVgDAKwVVAFT3NSP1ogRI+Tw4cMyd+5cKSkpkW7dupkltisHnen9MM7NpGfPnm22i9Zff/jhBzM7SgfqP/7xj9W+ZkBVNwe/TPTweb2hR3+5V64I1K3t2/E0pm4B8KJf1BnZIOvMfg/V6Qd2Eyse/tf9ktIw9lhRceKMLOn7Xo3eazyQWQMAxO8DzLyOYA0AsJZyuXOWrm8DgjUAwFpBCZjipr4N7PhIAQCAj5FZW6ym1xKP5RoAcDk5yl2/s65vA4I1AMBajss+azd1Lyc77hIAAB8jswYAWMuRgClu6tuAYA0AsFawFlYwqw00gwMA4HFk1gAAazk+GWBGsAYA2N1nrRK/z9qOjxQAAPgYmTUAwFrK5WhwXd8GBGsAgLUcdt0CAMDbHJ8MMLPjLgEA8DEyax+JZVOOaDf/YOMPAJeTQzM4AADe5vhkuVGawQEA8DgyawCAtRyawQEA8DbHJ8GaZnAAADyOzBoAYC3HJ5k1wRoAYC3HJ8GaZnAAADyOzBoAYC3lcq60rm8DgjUAwFqOT5rBCdYAAGs5BGsg+rW+WUscAOKPYA0AsJZDZg0AgLc5PgnWTN0CAMDjyKwBANZSKmCKm/o2IFgDAKzlsJ81AADwAjJrAIC1HJ8MMCNYAwCspXzSZ00zOAAAHkdmDQCwlkMzOAAA3qZ80gxOsIZVa4nHcg0AiUu5zKxtCdb0WQMA4HFk1gAAaymTHburn5CZ9aZNm2To0KGSkZEhgUBA1q5dG/Z9pZTMnTtXWrRoIfXr15dBgwbJN998E897BgAgbAUzNyUhg3V5eblkZWVJfn5+xO8/99xz8uKLL8rSpUvliy++kCuvvFKys7Pl1KlT8bhfAAB8J+pm8CFDhpgSic6qFy9eLLNnz5Z7773XHHvrrbckLS3NZOAjR450f8cAAPhsNHhcB5jt379fSkpKTNN3pcaNG0uvXr2kqKgoYp2KigopKysLKwAARDPP2nFRfBesdaDWdCZ9Lv268nvny8vLMwG9smRmZsbzlgAAsF6tT93Kzc2VY8eOhcqBAwdq+5YAAJZQyn3x3dSt9PR087W0tNSMBq+kX3frFnkhi5SUFFMAAIiWos86em3btjUBu7CwMHRM90HrUeG9e/eO56UAAPCNqDPrEydOyN69e8MGle3cuVOaNm0q1157rUyfPl2efvppue6660zwnjNnjpmTPWzYsHjfOwDA55RPMuuog/W2bdtkwIABodc5OTnm69ixY2X58uUyc+ZMMxd70qRJcvToUbnjjjukoKBA6tWrF987BwD4nqMCEmDXrQv179/fzKeuil7V7KmnnjIFqIlNOaLd/IONP4DEpVwOErNlgFmtjwYHAAAXx0YeAADLM+uAq/o2IFgDAKylfDLAjGZwAAA8jswaAGD3ftbirr4NCNYAAGspmsEBAIAXkFkDAOyl/NEOTmYNALCXOtsMHmvR9WORn58vbdq0Matz9urVS7Zu3XrR8/WKnlOmTDGbXOnNq66//nr56KOPqn09MmsAgLVULaxgtmrVKrPU9tKlS02gXrx4sWRnZ8uePXukefPmF5x/+vRp+e1vf2u+949//ENatmwp//3vf6VJkybVvibBGgCAKCxatEgmTpwo48ePN6910P7www9l2bJl8sQTT1xwvj7+v//9Tz7//HOpW7euOaaz8mgQrGGdaNf6Zi1xIHGpOI0G19s5n0s3VesSKUvevn275Obmho4lJSXJoEGDpKioKOI1/vnPf5ptonUz+Lp16+Saa66R0aNHy6xZs6ROnTrVuk/6rAEA9lIB90VEMjMzpXHjxqGSl5cX8XJHjhyRYDAoaWlpYcf165KSkoh1vv32W9P8revpfmq9dfQLL7xgtpOuLjJrAIDvHThwQFJTU0OvI2XVsXIcx/RXv/LKKyaT7t69u/zwww/y/PPPy7x586r1MwjWAADx+wCz1NTUsGBdlWbNmpmAW1paGnZcv05PT49YR48A133V5zZ533jjjSYT183qycnJl7wuzeAAAPvnWSsXJQo6sOrMuLCwMCxz1q91v3Qkffr0kb1795rzKv3nP/8xQbw6gVojWAMAEAU9bevVV1+VN998U77++mt5+OGHpby8PDQ6fMyYMWED0PT39WjwadOmmSCtR44/88wzZsBZddEMDgCwlqqFtcFHjBghhw8flrlz55qm7G7duklBQUFo0FlxcbEZIV5JD177+OOPZcaMGdK1a1czz1oHbj0avLoI1gAAu6nLf8mpU6eaEsmGDRsuOKabyLds2RLz9WgGBwDA48isAQDWUj7ZIpNgDQCwl/LHrlsEawCAxQK/Fjf1vY9gjYRX02uJx3INAIgGwRoAYC9FMzgAAN6m/BGsmboFAIDHkVkDAOyl/n+by5jrW4BgDQAQv++65XU0gwMA4HFk1gAAeyl/DDAjWAMA7KX80WdNMzgAAB5HZg0AsFZAnS1u6tuAYA0AsJeizxoAAG9T/uizJlgDcdiUI9rNP9j4A0A0CNYAAHspmsEBAPA25Y9gzdQtAAA8jswaAGAv5Y/MmmANALCX8sdocJrBAQDwODJrAIC1AqxgBgCAxyl/9FnTDA4AgMcRrAEA8DiawQEA1gq47He2Yyw4wRqIi2jX+mYtcSBOFFO3AACAB5BZAwDspRgNHtGmTZtk6NChkpGRIYFAQNauXRv2/XHjxpnj55a77rornvcMAEB4sHZTEjFYl5eXS1ZWluTn51d5jg7OBw8eDJV33nnH7X0CAOBbUTeDDxkyxJSLSUlJkfT0dDf3BQDAJQV8soJZjQww27BhgzRv3lxuuOEGefjhh+Wnn36q8tyKigopKysLKwAAVIuiGTwmugn8rbfeksLCQnn22Wdl48aNJhMPBoMRz8/Ly5PGjRuHSmZmZrxvCQAAq8V9NPjIkSNDf+7SpYt07dpV2rdvb7LtgQMHXnB+bm6u5OTkhF7rzJqADQCoFsVo8Lho166dNGvWTPbu3Vtl/3ZqampYAQAgmj7rgItigxoP1t9//73ps27RokVNXwoAgIQUdTP4iRMnwrLk/fv3y86dO6Vp06amLFiwQIYPH25Gg+/bt09mzpwpHTp0kOzs7HjfOwDA75Q/lhuNOlhv27ZNBgwYEHpd2d88duxYWbJkiXz11Vfy5ptvytGjR83CKYMHD5aFCxea5m4Al2ct8ViuAVhJ+aPPOupg3b9/f1Gq6qf7+OOP3d4TAADVEmCeNQAA8AI28gAA2EvRDA4AgLcpl03ZlgRrmsEBAPA4MmsAgL0UzeAAAHib8kewphkcAACPI7MGAFgrwDxrAADgBQRrAAA8jmZwAIC9lD8GmBGsAQvEsilHtJt/sPEHbBTwSZ81wRoAYDclCY8+awAAPI7MGgBgL0WfNQAAnhbwSZ81zeAAAHgcmTUAwF6KZnAAADwtQDM4AADwAoI1AMD+ZnDlosQgPz9f2rRpI/Xq1ZNevXrJ1q1bq1Vv5cqVEggEZNiwYVFdj2ANALCXuvzBetWqVZKTkyPz5s2THTt2SFZWlmRnZ8uhQ4cuWu+7776Txx57TPr27Rv1NQnWAADfKysrCysVFRVVnrto0SKZOHGijB8/Xjp16iRLly6VBg0ayLJly6qsEwwG5YEHHpAFCxZIu3btor4/BpgBCSratb5ZSxx+HmCWmZkZdlxnzfPnz7/g/NOnT8v27dslNzc3dCwpKUkGDRokRUVFVV7nqaeekubNm8uECRPkX//6V9T3SbAGAIjfp24dOHBAUlNTQ4dTUlIinn7kyBGTJaelpYUd1693794dsc7mzZvl9ddfl507o/tAfC6CNQBA/B6sU1NTw4J1vBw/flwefPBBefXVV6VZs2Yx/xyCNQAA1aQDbp06daS0tDTsuH6dnp5+wfn79u0zA8uGDh0aOuY4jvl6xRVXyJ49e6R9+/aXvC4DzAAA1vdZB1yUaCQnJ0v37t2lsLAwLPjq1717977g/I4dO8q///1v0wReWX73u9/JgAEDzJ/P7yuvCpk1AMBe6vIvN6qnbY0dO1Z69OghPXv2lMWLF0t5ebkZHa6NGTNGWrZsKXl5eWYedufOncPqN2nSxHw9//jFEKwBAIjCiBEj5PDhwzJ37lwpKSmRbt26SUFBQWjQWXFxsRkhHk8EawCAtQK1tDb41KlTTYlkw4YNF627fPnyqK9HsAYA2Ev5Y9ctBpgBAOBxZNYAAHspf2TWBGsAgLUCvxY39W1AsAZwWdYSj+UaAM4iWAMA7KVoBgcAwNMCtTR163IjWAMA7KX8kVkzdQsAAI8jswYA2E1JwiNYAwCsFfBJnzXN4AAAeByZNQDAXsofA8wI1gAAawVoBgcAAF5AZg0AsJeiGRwAAE8L+KQZnGANICaxbMoR7eYfbPwBnEWwBgDYS/mjGTyqAWZ5eXly6623SqNGjaR58+YybNgw2bNnT9g5p06dkilTpsjVV18tDRs2lOHDh0tpaWm87xsAAAkFazcl0YL1xo0bTSDesmWLrF+/Xs6cOSODBw+W8vLy0DkzZsyQ999/X1avXm3O//HHH+X++++viXsHAPhcQLkvCdcMXlBQEPZ6+fLlJsPevn279OvXT44dOyavv/66rFixQn7zm9+Yc9544w258cYbTYC/7bbb4nv3AAD4gKt51jo4a02bNjVfddDW2fagQYNC53Ts2FGuvfZaKSoqivgzKioqpKysLKwAAFAtimbwi3IcR6ZPny59+vSRzp07m2MlJSWSnJwsTZo0CTs3LS3NfK+qfvDGjRuHSmZmZqy3BADwmYBSrktCB2vdd71r1y5ZuXKlqxvIzc01GXplOXDggKufBwBAoolp6tbUqVPlgw8+kE2bNkmrVq1Cx9PT0+X06dNy9OjRsOxajwbX34skJSXFFAAAoqaYunUBpZQJ1GvWrJFPP/1U2rZtG/b97t27S926daWwsDB0TE/tKi4ult69e8fvrgEAEEaDV9n0rUd6r1u3zsy1ruyH1n3N9evXN18nTJggOTk5ZtBZamqqPProoyZQMxIcAIDLEKyXLFlivvbv3z/suJ6eNW7cOPPnv/zlL5KUlGQWQ9EjvbOzs+Xll1+O8fYAALgInzSDXxFtM/il1KtXT/Lz800BADdrfbOWOC4l4JONPNjPGgAAj2MjDwCAvRTN4AAAeFrAJ83gBGsAgL2UPzJr+qwBAPA4MmsAgNUClmTHbhCsAQD2UupscVPfAjSDAwDgcWTWAABrBRgNDgCAxylGgwMAAA8gswbg27XEY7kGvCXgnC1u6tuAYA0AsJeiGRwAAHgAmTUAwFoBRoMDAOBxyh+LohCsAQDWCvgks6bPGgAAjyOzBgDYS/ljNDjBGgBgrQDN4AAAwAvIrAEA9lKMBgcAwNMCNIMDAAAvILMGkDBi2ZQj2s0/2PjDYxSjwQEA8LQAzeAAAMALyKwBAPZy1Nnipr4FCNYAAHsp+qwBAPC0gMt+Z13fBvRZAwDgcWTWAAB7KVYwAwDA0wJM3QIAAJHk5+dLmzZtpF69etKrVy/ZunWrVOXVV1+Vvn37ylVXXWXKoEGDLnp+JARrAID9o8GVixKlVatWSU5OjsybN0927NghWVlZkp2dLYcOHYp4/oYNG2TUqFHy2WefSVFRkWRmZsrgwYPlhx9+qPY1CdYAAGsFlHJdtLKysrBSUVFR5TUXLVokEydOlPHjx0unTp1k6dKl0qBBA1m2bFnE899++2155JFHpFu3btKxY0d57bXXxHEcKSwsrPZz0mcNwNeiXeubtcQTU2ZmZthrnTXPnz//gvNOnz4t27dvl9zc3NCxpKQk07Sts+bqOHnypJw5c0aaNm1a7fsjWAMA7OX8WtzUF5EDBw5Iampq6HBKSkrE048cOSLBYFDS0tLCjuvXu3fvrtYlZ82aJRkZGSbAVxfBGgBgrcA5Tdmx1td0oD43WNeUP/3pT7Jy5UrTj60Hp1UXwRoAgGpq1qyZ1KlTR0pLS8OO69fp6ekXrfvnP//ZBOtPPvlEunbtKtFggBkAwF7q8o4GT05Olu7du4cNDqscLNa7d+8q6z333HOycOFCKSgokB49ekT9mGTWAAB7qcu/gpmetjV27FgTdHv27CmLFy+W8vJyMzpcGzNmjLRs2VLy8vLM62effVbmzp0rK1asMHOzS0pKzPGGDRuaUh0EawCAtQK1sILZiBEj5PDhwyYA68Crp2TpjLly0FlxcbEZIV5pyZIlZhT573//+2qNOI+EYA0AQJSmTp1qSiR68Ni5vvvuO3GLYA0AsJdiIw8AADwt4JwtburbgNHgAAB4HJk1AMBeimZwAMBlXks8lmv4mopt56yw+hagGRwAAI8jswYAiN/XBk+ozFqvxnLrrbdKo0aNpHnz5jJs2DDZs2dP2Dn9+/eXQCAQViZPnhzv+wYAQEJ91m5KogXrjRs3ypQpU2TLli2yfv16sx/n4MGDzTJr59Kbch88eDBU9JqoAADgMjSD6+XUzrV8+XKTYeuNuPv16xc63qBBg0vuPgIAgGvK5X7WfhhgduzYMfO1adOmYcfffvtts41Y586dJTc3V06ePFnlz6ioqJCysrKwAgBANH3WARcloQeY6S3Bpk+fLn369DFBudLo0aOldevWkpGRIV999ZXMmjXL9Gu/9957VfaDL1iwINbbAACI36duKXf1EzlY677rXbt2yebNm8OOT5o0KfTnLl26SIsWLWTgwIGyb98+ad++/QU/R2feeruxSjqzzszMjPW2AABIODEFa73TyAcffCCbNm2SVq1aXfTcXr16ma979+6NGKxTUlJMAQAgaooVzC6glJJHH31U1qxZY7YAa9u27SXr7Nx5dvUenWEDABBXju64dlk/0YK1bvpesWKFrFu3zsy11ptua40bN5b69eubpm79/bvvvluuvvpq02c9Y8YMM1K8a9euNfUMAAAktKiC9ZIlS0ILn5zrjTfekHHjxklycrJ88sknsnjxYjP3Wvc9Dx8+XGbPnh3fuwYAQPyzglnUzeAXo4OzXjgFABD7phzRbv7h640/lD/6rNnIAwAAj2MjDwCAvZQ/MmuCNQDAXsofwZpmcAAAPI7MGgBgL4d51gAAeFqAqVsAAHicos8aAAB4AJk1AMBejtJt2e7qW4BgDQCwl6IZHAAAeACZNQB4TLRrfft7LXHlMju2I7MmWAMA7KVoBgcAAB5AZg0AsJejM2NGgwMA4F3KOVvc1LcAzeAAAHgcmTUAwF7KHwPMCNYAAHs59FkDAOBtyh+ZNX3WAAB4HJk1AMBeymV2bEdiTbAGAFhM+aMZnGANAJar6bXEY7kG4otgDQCwl6MXNXFc1vc+gjUAwF7KH83gjAYHAMDjyKwBAPZS/sisCdYAAHs5/ljBjGZwAAA8jswaAGAtpRxT3NS3AcEaAGAvpdw1ZdNnDQBADVMu+6wtCdb0WQMA4HFk1gAAezmOSMBFvzN91gAA1DDlj2ZwgjUA+Ewsm3JEs/lH2XFHrro+6kvgIgjWAABrKccR5aIZnKlbAADUNOWPZnBGgwMA4HFk1gAAezlKJJD4mTXBGgBgL6WDrZPwwZpmcAAAPI7MGgBgLeUoUS6awRWZNQAANUw57ksM8vPzpU2bNlKvXj3p1auXbN269aLnr169Wjp27GjO79Kli3z00UdRXY9gDQCwO7N23JVorVq1SnJycmTevHmyY8cOycrKkuzsbDl06FDE8z///HMZNWqUTJgwQb788ksZNmyYKbt27ar2NQnWAABEYdGiRTJx4kQZP368dOrUSZYuXSoNGjSQZcuWRTz/r3/9q9x1113y+OOPy4033igLFy6UW265Rf72t7/Z22dd2X/wi5xxNc8dABA/egnRap97wrls/cG/qApXm3GYWKPvuaws7HhKSoop5zt9+rRs375dcnNzQ8eSkpJk0KBBUlRUFPEa+rjOxM+lM/G1a9faG6yPHz9uvm6W6NrzAQA1J5a1vvXv88aNG9fE7UhycrKkp6fL5hL3saJhw4aSmZkZdkw3cc+fP/+Cc48cOSLBYFDS0tLCjuvXu3fvjvjzS0pKIp6vj1sbrDMyMuTAgQPSqFEjCQQCYd/Tn3z0/1D9/dTUVPEDPz6zX5/bj8+s8dyJ99w6o9aBWv8+ryn16tWT/fv3m0w3Hvd7fryJlFXXJs8Fa92c0KpVq4ueo/9iJ9pf7kvx4zP79bn9+Mwaz51YaiqjPj9g63I5NWvWTOrUqSOlpaVhx/VrnelHoo9Hc34kDDADACCK5vfu3btLYWFh6JjjOOZ17969I9bRx889X1u/fn2V51uRWQMA4GU5OTkyduxY6dGjh/Ts2VMWL14s5eXlZnS4NmbMGGnZsqXk5eWZ19OmTZM777xTXnjhBbnnnntk5cqVsm3bNnnllVcSM1jrPgTd6e+1voSa5Mdn9utz+/GZNZ7bX8+dCEaMGCGHDx+WuXPnmkFi3bp1k4KCgtAgsuLiYtOlW+n222+XFStWyOzZs+XJJ5+U6667zowE79y5c7WvGVC2rLUGAIBP0WcNAIDHEawBAPA4gjUAAB5HsAYAwOMI1gAAeJw1wTravUNtp9ek1cvfnVv0XqiJZNOmTTJ06FCzJKF+vvMXtdcTFfTUiBYtWkj9+vXNQvnffPONJPpzjxs37oL3Xu/YYzM93/TWW281ywg3b97cbA+4Z8+esHNOnTolU6ZMkauvvtqs1Tx8+PALVn1KxOfu37//Be/35MmTa+2e4U1WBOto9w5NFDfddJMcPHgwVDZv3iyJRC8ioN9L/UEskueee05efPFFs/3cF198IVdeeaV53/Uv9UR+bk0H53Pf+3feeUdstnHjRhOIt2zZYlZuOnPmjAwePNj8v6g0Y8YMef/992X16tXm/B9//FHuv/9+SfTn1vR2i+e+3/rvPhBGWaBnz55qypQpodfBYFBlZGSovLw8lajmzZunsrKylF/ov4pr1qwJvXYcR6Wnp6vnn38+dOzo0aMqJSVFvfPOOypRn1sbO3asuvfee1UiO3TokHn2jRs3ht7bunXrqtWrV4fO+frrr805RUVFKlGfW7vzzjvVtGnTavW+4H2ez6wr9w7VTaDV3Ts0UegmX91U2q5dO3nggQfMqjh+oXfT0SsDnfu+640BdBdIor/v2oYNG0yz6Q033CAPP/yw/PTTT5JIjh07Zr42bdrUfNX/xnXWee77rbt9rr322oR6v89/7kpvv/222SBCr2il90k+efJkLd0hvMrzy43GsndoItBBafny5eaXtW4WW7BggfTt21d27dpl+r8SXeU+r273gLWRbgLXzb9t27aVffv2meUJhwwZYoKW3u3HdnrTg+nTp0ufPn1Cyy3q91RvkNCkSZOEfb8jPbc2evRoad26tflg/tVXX8msWbNMv/Z7771Xq/cLb/F8sPYr/cu5UteuXU3w1v+g3333XZkwYUKt3htq1siRI0N/7tKli3n/27dvb7LtgQMHiu10H67+0JloYzBife5JkyaFvd96QKV+n/UHNf2+A5rnm8Fj2Ts0EemM4/rrr5e9e/eKH1S+t35/3zXdDaL/HSTCez916lT54IMP5LPPPgvbt16/p7rL6+jRown5flf13JHoD+ZaIrzf8FGwjmXv0ER04sQJ80lbf+r2A90ErH9Jn/u+l5WVmVHhfnrfte+//970Wdv83uuxdDpgrVmzRj799FPz/p5L/xuvW7du2Putm4L1OA2b3+9LPXckO3fuNF9tfr/h02bwS+0dmogee+wxMxdXN33rKSx62ppuYRg1apQk0geQc7MHPahM/6LSg2/0wCLdv/f000+b7eT0L7k5c+aYfj09VzVRn1sXPT5BzzHWH1b0B7SZM2dKhw4dzLQ1m5uA9RaB69atM2MuKvuh9aBBPYdef9XdO/rfuv5/kJqaKo8++qgJ1Lfddpsk6nPr91d//+677zbzy3WftZ7C1q9fP9P9AYQoS7z00kvq2muvVcnJyWYq15YtW1QiGzFihGrRooV53pYtW5rXe/fuVYnks88+M9NYzi966lLl9K05c+aotLQ0M2Vr4MCBas+ePSqRn/vkyZNq8ODB6pprrjFTmVq3bq0mTpyoSkpKlM0iPa8ub7zxRuicn3/+WT3yyCPqqquuUg0aNFD33XefOnjwoErk5y4uLlb9+vVTTZs2NX/HO3TooB5//HF17Nix2r51eAz7WQMA4HGe77MGAMDvCNYAAHgcwRoAAI8jWAMA4HEEawAAPI5gDQCAxxGsAQDwOII1AAAeR7AGAMDjCNYAAHgcwRoAAPG2/wN0o+c0ywuRZgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# we can again inspect the parameter:\n", "kernel = vstate.parameters[\"kernel\"]\n", "plt.imshow(jnp.dot(kernel.T, kernel))\n", "plt.colorbar()"]}, {"cell_type": "markdown", "id": "d216a4d6", "metadata": {}, "source": ["## 5. Neural-Network Quantum State\n", "\n", "Rarely do we know the functional form of the ground state wave-function. We therefore have to resort to highly flexible variational Ansaetze, capable of representing many different many-body states. We can for example use neural networks as universal function approximators.\n", "In the following, we use a neural network architecture called \"DeepSets\" to solve the more interesting case of bosons interacting via a repulsive Gaussian interaction:\n", "\n", "$$\n", "\n", "\\epsilon  \\sum_{i<j} \\exp\\left[-|\\mathbf{r}_{ij}|^2\\right].\n", "\n", "$$\n", "\n", "We study the bulk of the system by confining it into a box of length $L$ with periodic boundary conditions. In this extended system we define the particle density as $ D = N/L^d $ and we study the case $ D=1 $ for $ N=20 $ particles in $ d=1 $ dimensions with $ \\epsilon = 2 $. To account for the periodicity we use the minimum image convention when evaluating the potential energy of the system. For more information see:\n", "\n", "The DeepSets architecture we use is defined as:\n", "\n", "$$\n", "\n", "\\mathrm{DS}(\\mathbf{X}) = \\rho\\left[\\sum_{i,j} \\phi \\left( \\mathbf{y}_{ij}\\right) \\right]\n", "\n", "$$\n", "\n", "where the functions $ \\phi $ and $ \\rho $ are parameterized by simple feed-forward neural network with a GeLu activation function with one hidden layer.\n", "To account for periodicity we choose as input $ \\mathbf{y}_{ij} = (\\sin(\\frac{2\\pi}{L} \\mathbf{r}_{ij}),  \\cos(\\frac{2\\pi}{L} \\mathbf{r}_{ij})) $.\n", "\n", "See [PRR _4_, 023138 (2022)](https://journals.aps.org/prresearch/pdf/10.1103/PhysRevResearch.4.023138) for more information."]}, {"cell_type": "markdown", "id": "021f6753-b90d-41e6-947e-44f6037972cc", "metadata": {"lines_to_next_cell": 2}, "source": ["First we define the <PERSON>lbert space, sampler and hamiltonian:"]}, {"cell_type": "code", "execution_count": 17, "id": "11b9e993-5d02-45d6-a982-9faf9583c4ce", "metadata": {"tags": []}, "outputs": [], "source": ["def minimum_distance(x, N, sdim):\n", "    \"\"\"Computes distances between particles using minimum image convention\"\"\"\n", "    x = x.reshape(-1, N, sdim)\n", "    idx = jnp.triu_indices(N, 1)\n", "    distances = (-x[..., None, :, :] + x[..., :, None, :])[..., idx[0], idx[1], :]\n", "    # minimum image convention\n", "    distances = jnp.remainder(distances + L / 2.0, L) - L / 2.0\n", "    return jnp.linalg.norm(distances, axis=-1)\n", "\n", "\n", "def potential(x, N, epsilon, sdim):\n", "    dis = minimum_distance(x, N, sdim)\n", "    return epsilon * jnp.sum(jnp.exp(-((dis) ** 2)))\n", "\n", "\n", "N = 20\n", "D = 1.0\n", "dim = 1\n", "epsilon = 2.0\n", "L = (N / D) ** (1 / dim)\n", "\n", "\n", "geo = nk.experimental.geometry.Cell(d=1, L=(L,), pbc=True)\n", "hilb = nk.experimental.hilbert.Particle(N=N, geometry=geo)\n", "sa = nk.sampler.MetropolisGaussian(hilbert=hilb, sigma=0.4, n_chains=16, sweep_size=10)\n", "\n", "\n", "ekin = nk.operator.KineticEnergy(hilb, mass=1.0)\n", "pot = nk.operator.PotentialEnergy(hilb, lambda x: potential(x, N, epsilon, dim))\n", "ha = ekin + pot"]}, {"cell_type": "code", "execution_count": 18, "id": "2b5c30e7", "metadata": {"tags": []}, "outputs": [], "source": ["class DS(nn.<PERSON><PERSON><PERSON>):\n", "\n", "    # You can define attributes at the module-level\n", "    # with a default. This allows you to easily change\n", "    # some hyper-parameter without redefining the whole\n", "    # flax module.\n", "    L: float\n", "    N: int\n", "    sdim: int\n", "    hidden_units: int = 8\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        x = x.reshape(-1, self.N, self.sdim)\n", "        xij = x[..., None, :] - x[..., None, :, :]\n", "        yij = jnp.concatenate(\n", "            (jnp.sin(2 * jnp.pi / self.L * xij), jnp.cos(2 * jnp.pi / self.L * xij)),\n", "            axis=-1,\n", "        )\n", "\n", "        ### PHI\n", "        # here we construct the first dense layer using a\n", "        # pre-built implementation in flax.\n", "        # features is the number of output nodes\n", "        y = nn.Dense(features=self.hidden_units)(yij)\n", "        # the non-linearity is a simple GeLu\n", "        y = nn.gelu(y)\n", "        # we apply the dense layer to the input\n", "        y = nn.Dense(features=self.hidden_units)(y)\n", "        # we sum over i,j\n", "        y = jnp.sum(y, axis=(-3, -2))\n", "\n", "        ### RHO\n", "        # the function rho has a single output\n", "        y = nn.Dense(features=self.hidden_units)(y)\n", "        y = nn.gelu(y)\n", "        y = nn.<PERSON><PERSON>(features=1)(y)\n", "        return y.squeeze()\n", "\n", "\n", "model = DS(L=L, N=N, sdim=dim)\n", "\n", "# choose number of samples that is divided by the number of chains\n", "vstate = nk.vqs.MCState(sa, model, n_samples=1008, n_discard_per_chain=16)"]}, {"cell_type": "markdown", "id": "f29016f4", "metadata": {}, "source": ["We then proceed to the optimization as before."]}, {"cell_type": "code", "execution_count": 19, "id": "279c052e", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9cbdedf64a35458da15973ca36825ecd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy and relative error:  24.7591 ± 0.0083 [σ²=0.0691, R̂=1.0064] 0.0006925251586689619\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.02)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    ha,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.05),\n", ")\n", "\n", "# Store the energy along the simulation in a RuntimeLogger, which is kept in memory.\n", "# You could also use a JsonLog to save it to a file.\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=200, out=log)\n", "\n", "ds_energy = vstate.expect(ha)\n", "error = abs((ds_energy.mean - 24.742) / 24.742)\n", "print(\"Optimized energy and relative error: \", ds_energy, error)"]}, {"cell_type": "markdown", "id": "68f501c7", "metadata": {}, "source": ["## 6. Measuring Other Properties\n", "\n", "Once the model has been optimized, we can of start to measure other observables than the energy as well. An interesting quantity could for example be the radial distribution function, which is related to the structure factor measured in scattering experiments. It is defined as:\n", "\n", "$$\n", "\n", "g(r) = C \\langle \\sum_{i<j} \\delta(r-r_{ij}) \\rangle\n", "\n", "$$\n", "\n", "where $ \\langle \\cdot \\rangle $ denotes the expectation value w.r.t. the probability distribution defined by the optimized wave-function. The constant $C$ is chosen such that $\\int \\mathrm{d}r g(r) = N*(N-1)$.\n", "\n", "To evaluate the above expression we need samples from the optimized wave-function:"]}, {"cell_type": "code", "execution_count": 20, "id": "200d636f-a8e8-4b74-90d9-c38b956d788e", "metadata": {"tags": []}, "outputs": [], "source": ["samples = vstate.sample(chain_length=3 * 10**4)"]}, {"cell_type": "markdown", "id": "72986628-7bf3-4b52-b531-cfeedc772661", "metadata": {}, "source": ["Then we compute the above observable by computing the distances $r_{ij}$ and computing a histogram of the result (the finer the histogram the closer we approximate the delta-distribution)."]}, {"cell_type": "code", "execution_count": 21, "id": "164f53f8-ab53-4b5f-99be-779186d49fd7", "metadata": {"tags": []}, "outputs": [], "source": ["import numpy as np\n", "import jax\n", "\n", "\n", "def gr(samples, n_particles, sdim, L, bins=80):\n", "    dists = minimum_distance(samples, n_particles, sdim)\n", "    hist, bin_edges = np.histogram(dists.flatten(), bins=bins, range=(0, L / 2))\n", "    delta_r = bin_edges[1] - bin_edges[0]\n", "    centers = (bin_edges[1:] + bin_edges[:-1]) / 2\n", "\n", "    gC = hist / (dists.shape[0] * n_particles)\n", "\n", "    def get_jacobian():\n", "        if sdim == 1:\n", "            return np.ones_like(centers)\n", "        elif sdim == 2:\n", "            return 2 * np.pi * centers\n", "        elif sdim == 3:\n", "            return 4 * np.pi * centers**2\n", "        else:\n", "            raise NotImplementedError(\n", "                f\"Jacobian factor not yet implemented for sdim={sdim}\"\n", "            )\n", "\n", "    def get_volume():\n", "        return L**sdim\n", "\n", "    def get_density():\n", "        return n_particles / get_volume()\n", "\n", "    gU = get_jacobian() * delta_r * get_density()\n", "\n", "    return centers, gC / gU"]}, {"cell_type": "code", "execution_count": 22, "id": "9add27da-af3c-406e-90f2-de46dad7261c", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c, g = gr(samples, n_particles=N, sdim=dim, L=L)\n", "plt.plot(c, g)"]}, {"cell_type": "code", "execution_count": null, "id": "5e4afaed-0620-47a9-8692-94a72ca63e21", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}