{"cells": [{"cell_type": "markdown", "id": "4ad0c645", "metadata": {}, "source": ["# Ground-State: Bosonic Matrix Model\n", "\n", "<PERSON>, July 13, 2022\n", "\n", "In this tutorial we will use the open-source package [NetKet](https://www.netket.org/) to find the ground state (in a variational sense) of a quantum mechanical system of matrices.\n", "This system is referred to as a free bosonic matrix model and was studied in [this PRX Quantum paper](https://journals.aps.org/prxquantum/abstract/10.1103/PRXQuantum.3.010324) using quantum technologies (e.g. based on [qutip](https://qutip.org) and [qiskit](https://qiskit.org)) and La<PERSON>ce <PERSON> methods.\n", "\n", "Specifically, we will study the system defined by the Hamiltonian:\n", "\n", "$$ \\hat{H} = {\\rm Tr}\\left(\\frac{1}{2}\\hat{P}_I^2+\\frac{m^2}{2}\\hat{X}_I^2-\\frac{g^2}{4}[\\hat{X}_I,\\hat{X}_J]^2\\right) $$\n", "\n", "where\n", "$$\\hat{P}_I=\\sum_{\\alpha=1}^{N^2-1}\\hat{P}_I^\\alpha\\tau_\\alpha$$\n", "\n", "and\n", "\n", "$$\\hat{X}_I=\\sum_{\\alpha=1}^{N^2-1}\\hat{X}_I^\\alpha\\tau_\\alpha$$\n", "\n", "for a SU(N) gauge group with $N^2-1$ generators $\\tau_\\alpha$ (normalized such that ${\\rm Tr}(\\tau_\\alpha \\tau_\\beta) = \\delta_{\\alpha\\beta}$).\n", "In the Hamiltonian, $g^2$ is the coupling constant representing the strenght of the interaction between matrices $X_I$. The physical states of this system are gauge singlets.\n", "We also set $m^2=1$ so that the first two terms in the Hamiltonian represent a set of free harmonic oscillators with unit frequency."]}, {"cell_type": "markdown", "id": "d5f684b2", "metadata": {}, "source": ["## 0. Installing Netket\n", "\n", "Installing NetKet is relatively straightforward. For this tutorial, if you are running it locally on your machine, we recommend that you create a clean virtual environment (e.g. with `conda`) and install `NetKet` within (using `pip`):\n", "\n", "```bash\n", "conda create --name netket python pip ipython\n", "conda activate netket\n", "pip install --upgrade netket\n", "```\n", "\n", "**If you are on Google Colab**, uncomment and run the following cell to install the required packages."]}, {"cell_type": "code", "execution_count": 1, "id": "5543a317", "metadata": {}, "outputs": [], "source": ["#!pip install --upgrade netket"]}, {"cell_type": "markdown", "id": "66285620", "metadata": {}, "source": ["You can check that the installation was succesfull doing"]}, {"cell_type": "code", "execution_count": 2, "id": "51c4a47a", "metadata": {}, "outputs": [], "source": ["import netket as nk"]}, {"cell_type": "markdown", "id": "749c45c1", "metadata": {}, "source": ["You should also check that your version of netket is at least 3.0"]}, {"cell_type": "code", "execution_count": 3, "id": "21ae69d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NetKet version: 3.4.3.dev29+gfe47c195\n"]}], "source": ["print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "8285c606", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====================================================\n", "==         NetKet Diagnostic Information         ==\n", "====================================================\n", "NetKet version       : 3.4.3.dev29+gfe47c195\n", "\n", "# Python\n", "  - implementation   : CPython\n", "  - version          : 3.10.5\n", "  - distribution     : Clang 13.0.1 \n", "  - path             : /Users/<USER>/miniforge3/envs/netket-mpi/bin/python\n", "\n", "# Host information\n", "  - System           : macOS-12.4-arm64-arm-64bit\n", "  - Architecture     : arm64\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/site-packages/netket/tools/info.py\", line 167, in <module>\n", "    info()\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/site-packages/netket/tools/info.py\", line 81, in info\n", "    platform_info = cpu_info()\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/site-packages/netket/tools/_cpu_info.py\", line 41, in cpu_info\n", "    return get_sysctl_cpu()\n", "  File \"/Users/<USER>/miniforge3/envs/netket-mpi/lib/python3.10/site-packages/netket/tools/_cpu_info.py\", line 78, in get_sysctl_cpu\n", "    flags = [flag.lower() for flag in info[\"features\"].split()]\n", "KeyError: 'features'\n", "\u001b[0m"]}], "source": ["!python -m netket.tools.info"]}, {"cell_type": "code", "execution_count": 5, "id": "d7b452b9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "d0d3635e", "metadata": {}, "source": ["## 1. Defining The Hamiltonian\n", "\n", "The first step in our journey consists in defining the Hamiltonian we are interested in.\n", "For this purpose, we first need to define the kind of degrees of freedom we are dealing with (i.e. if we have spins, bosons, fermions etc).\n", "This is done by specifying the <PERSON>lbert space of the problem.\n", "\n", "For our problem we focus on the gauge group SU(2), which will determine the total number of bosons to be $N^2-1 = 3$ for each matrix ($\\alpha, \\beta \\in \\{ 1, 2, 3\\}$).\n", "We will focus on 2 matrices $I, <PERSON> \\in \\{ 1, 2 \\}$, for a total of 6 bosonic degrees of freedom $\\hat{X}_{I,\\alpha}$."]}, {"cell_type": "markdown", "id": "e774cc75", "metadata": {}, "source": ["### 1.1 The Hilbert space\n", "\n", "For each bosonic degree of freedom we work in the basis of the Fock space, truncated up to a finite excitation level (cutoff) represented by $\\Lambda$."]}, {"cell_type": "code", "execution_count": 6, "id": "3185985e", "metadata": {}, "outputs": [], "source": ["Lambda = 4  # cutoff of each bosonic Fock space\n", "N = 6  # number of bosons\n", "\n", "hi = nk.hilbert.Fock(\n", "    n_max=Lambda - 1, N=N\n", ")  # n_max -> Maximum occupation for a site (inclusive)"]}, {"cell_type": "markdown", "id": "bdb07d5b", "metadata": {}, "source": ["Test if the space is constructed as we expected.\n", "We want each of the $N$ bosons to have up to $\\Lambda$ excitations: the total Hilbert space is the tensor product of $N$ individual Fock spaces of dimension $\\Lambda$ for a total size of $\\Lambda^N = 4^6 = 4096$."]}, {"cell_type": "code", "execution_count": 7, "id": "1d4203ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4, 4, 4, 4, 4, 4)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# The size of the hilbert space on every site (we have 4 levels on 6 sites).\n", "hi.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "34e90091", "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# The total number number of degrees of freedom (6 bosons).\n", "hi.size"]}, {"cell_type": "code", "execution_count": 9, "id": "ed2d7c1f", "metadata": {}, "outputs": [{"data": {"text/plain": ["4096"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# The total dimension of the many-body <PERSON>lbert space (4^6 = 4096)\n", "hi.n_states"]}, {"cell_type": "code", "execution_count": 10, "id": "cc0c71aa", "metadata": {}, "outputs": [], "source": ["assert Lambda**N == hi.n_states"]}, {"cell_type": "markdown", "id": "01093ab0", "metadata": {}, "source": ["We have checked that all the dimensions correspond to our expectations. We can think of this as the Hilbert state of a many-body system with $N$ bosonic sites, each with excitations up to level $\\Lambda$ (inclusive, starting from 0 to $\\Lambda-1$).\n", "In other words, it is a collection of harmonic oscillators in the $\\mid n \\rangle$ basis."]}, {"cell_type": "markdown", "id": "7b8a402e", "metadata": {}, "source": ["### 1.2 The free Hamiltonian\n", "\n", "We now need to specify the Hamiltonian.\n", "We will use some predefined operators for bosons, such as the `create` and `destroy` operators for each boson in the Hilber state (see the API documentation [here](https://www.netket.org/docs/api.html#pre-defined-operators)).\n", "\n", "In this specifc case, $\\hat{a}^\\dagger_I$ for each boson $I$ (we can regard this index as a _site_ index in `netket`).\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c1d99951", "metadata": {}, "outputs": [], "source": ["from netket.operator.boson import create, destroy"]}, {"cell_type": "markdown", "id": "949dac00", "metadata": {}, "source": ["We now start by defining the free part of the Hamiltonian, which is just a collection of harmonic oscillators (for this part we could also use the `number` operator which is pre-defined in `netket`)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "98c4e4a4", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["# we loop over all \"sites\" -> loop over N bosons\n", "H_free = sum([0.5 + create(hi, i) * destroy(hi, i) for i in range(N)])"]}, {"cell_type": "markdown", "id": "1cef604e", "metadata": {}, "source": ["`NetKet` automatically recognizes products of local operators as tensor products and the addition of scalars as if they were multiplied by the identity matrix in the tensor space. In this case, `H_free` acts on a 4096 dimensional space."]}, {"cell_type": "markdown", "id": "dce71800", "metadata": {}, "source": ["In general, when manipulating `NetKet` objects, you should always assume that you can safely operate on them like\n", "you would in mathematical equations, therefore you can sum and multiply them with ease."]}, {"cell_type": "markdown", "id": "b97e1af5", "metadata": {}, "source": ["### 1.3 Exact Diagonalization\n", "\n", "Let us check this free Hamiltonian, whose eigenvectors are analytically known: the ground state has vacuum energy for $N$ independent harmonic oscillators: $0.5N = 0.5 \\cdot 6 = 3$"]}, {"cell_type": "markdown", "id": "d6af47c2", "metadata": {}, "source": ["In `netket` we can  convert our Hamiltonian operator into a sparse matrix of size $\\Lambda^N \\times \\Lambda^N $."]}, {"cell_type": "markdown", "id": "2f37afa5", "metadata": {}, "source": ["Or we can use the fact that `netket` provides a wrapper for `eigsh` in the form of a Lanczos [exact solver](https://www.netket.org/docs/_generated/exact/netket.exact.lanczos_ed.html#netket.exact.lanczos_ed) which does not require to manipulate the Hamiltonian into a sparse matrix using `.to_sparse`."]}, {"cell_type": "code", "execution_count": 13, "id": "db4df2d0", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Eigenvalues with exact lanczos (sparse): [3. 4. 4. 4. 4. 4. 4. 5. 5. 5. 5. 5. 5.]\n"]}], "source": ["eig_vals = nk.exact.lanczos_ed(\n", "    H_free, k=13, compute_eigenvectors=False\n", ")  # args to scipy.sparse.linalg.eighs like can be passed with scipy_args={'tol':1e-8}\n", "print(f\"Eigenvalues with exact lanczos (sparse): {eig_vals}\")"]}, {"cell_type": "markdown", "id": "90533143", "metadata": {}, "source": ["*Note*: a full (dense) exact diagonalization can also be done for small systems using [this exact solver](https://www.netket.org/docs/_generated/exact/netket.exact.full_ed.html#netket.exact.full_ed)"]}, {"cell_type": "code", "execution_count": 14, "id": "f1b62db4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Eigenvalues with exact diag (dense): [ 3.  4.  4. ... 20. 20. 21.]\n", "Number of eigs: 4096\n"]}], "source": ["eig_vals = nk.exact.full_ed(H_free, compute_eigenvectors=False)\n", "print(\n", "    f\"Eigenvalues with exact diag (dense): {eig_vals}\\nNumber of eigs: {eig_vals.shape[0]}\"\n", ")"]}, {"cell_type": "markdown", "id": "f5171c50", "metadata": {}, "source": ["Clearly this is the correct energy for the ground state and we can also see the correct degeneracy (6) of the excited states due to the symmetry of the system (all sites are interchangeable)"]}, {"cell_type": "markdown", "id": "f5a99cc0", "metadata": {}, "source": ["### 1.4 The interaction Hamiltonian\n", "\n", "We can now continue building our full Hamiltonian by writing the interaction term.\n", "It is convenient to write the interaction using the `position` operators built from the creation and annihilation bosonic operators\n", "\n", "$$\\hat{x}_I = \\frac{1}{\\sqrt{2}} \\left( \\hat{a}^\\dagger_I + \\hat{a}_I \\right)$$\n"]}, {"cell_type": "code", "execution_count": 15, "id": "b84f7fa0", "metadata": {}, "outputs": [], "source": ["x_list = [(1 / np.sqrt(2)) * (create(hi, i) + destroy(hi, i)) for i in range(N)]"]}, {"cell_type": "markdown", "id": "3e582590", "metadata": {}, "source": ["The interactoin terms can be written using the Levi-Civita completely antisymmetric tensor with 3 indices or by writing out all the terms by hand:"]}, {"cell_type": "code", "execution_count": 16, "id": "2a944b06", "metadata": {}, "outputs": [], "source": ["### Quartic Interaction for bosons\n", "V_b = (\n", "    x_list[2] * x_list[2] * x_list[3] * x_list[3]\n", "    + x_list[2] * x_list[2] * x_list[4] * x_list[4]\n", "    + x_list[1] * x_list[1] * x_list[3] * x_list[3]\n", "    + x_list[1] * x_list[1] * x_list[5] * x_list[5]\n", "    + x_list[0] * x_list[0] * x_list[4] * x_list[4]\n", "    + x_list[0] * x_list[0] * x_list[5] * x_list[5]\n", "    - 2 * x_list[0] * x_list[2] * x_list[3] * x_list[5]\n", "    - 2 * x_list[0] * x_list[1] * x_list[3] * x_list[4]\n", "    - 2 * x_list[1] * x_list[2] * x_list[4] * x_list[5]\n", ")"]}, {"cell_type": "markdown", "id": "a55c7048", "metadata": {}, "source": ["And the full Hamiltonian with 't <PERSON><PERSON> coupling $\\lambda = g^2 N_g$ (where $N_g$ is the gauge group number of color,  2 in this example) is"]}, {"cell_type": "code", "execution_count": 17, "id": "75887531", "metadata": {}, "outputs": [], "source": ["g2N = 0.2  # 't <PERSON><PERSON> coupling lambda\n", "H = H_free + (g2N / 2) * V_b"]}, {"cell_type": "markdown", "id": "cacc14e4", "metadata": {}, "source": ["## 2. Exact Diagonalization\n", "\n", "We can repeat for the full Hamiltonian at fixed gauge coupling $g^2N_g$ the exact diagonalization procedure we have used on the free Hamiltonian.\n", "\n", "When the gauge coupling constant is small, the difference of the energy of the ground state with the free case $g=0$ will be small."]}, {"cell_type": "code", "execution_count": 18, "id": "07c63698", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Eigenvalues with exact diag (dense): [ 3.13406307  4.21516634  4.21516634 ... 22.94954115 22.94954115\n", " 23.42952917]\n", "Number of eigs: 4096\n"]}], "source": ["w = nk.exact.full_ed(H, compute_eigenvectors=False)\n", "print(f\"Eigenvalues with exact diag (dense): {w}\\nNumber of eigs: {w.shape[0]}\")"]}, {"cell_type": "markdown", "id": "eeaabe15", "metadata": {}, "source": ["Or we can use the <PERSON><PERSON><PERSON><PERSON> solver and only approximately retrieve the lowest $k$ eigenvalues (this is much much quicker)"]}, {"cell_type": "code", "execution_count": 19, "id": "a6a77698", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Eigenvalues with exact lanczos (sparse): [3.13406307 4.21516634 4.21516634 4.21516634]\n"]}], "source": ["eig_vals = nk.exact.lanczos_ed(\n", "    H, k=4, compute_eigenvectors=False, scipy_args={\"tol\": 1e-8}\n", ")\n", "print(f\"Eigenvalues with exact lanczos (sparse): {eig_vals}\")"]}, {"cell_type": "markdown", "id": "09a709c1", "metadata": {}, "source": ["In [this PRX Quantum paper](https://journals.aps.org/prxquantum/abstract/10.1103/PRXQuantum.3.010324), the author have shown how the ground state energy changes when the truncation level $\\Lambda$ of the Fock space is taken to infinity. The convergence to the infinite cutoff limit is exponentially fast and we can work with a small cutoff $\\Lambda=4$ in this example, to keep the computational requirements to a minimum."]}, {"cell_type": "markdown", "id": "a305e4ce", "metadata": {}, "source": ["## 3. <PERSON>-<PERSON>\n", "\n", "We now would like to find a variational approximation of the ground state of this Hamiltonian. As a first step, we can try to use a very simple mean field ansatz:\n", "\n", "$$ \\langle X_1,\\dots X_N| \\Psi_{\\mathrm{mf}} \\rangle = \\Pi_{i=1}^{N} \\Phi(X_i), $$\n", "\n", "where the variational parameters are individual boson's wave functions $\\Phi(X_i)$. This is probably a bad ansatz, but it will show how the variational <PERSON> procedure works in `netket`\n", "\n", "We can further write $ \\Phi(X) = \\sqrt{P(X)}e^{i \\phi(X)}$. This is the same form used in the Deep Learning section of [the same preprint cited above](https://arxiv.org/abs/2108.02942), where, instead of the Fock basis for the states, a coordinate basis was used.\n", "\n", "In order to simplify the explanation of the Variational Monte Carlo method, we take the phase $\\phi=0$, and consider only the modulus of the wave function (the phase will be irrelevant for the ground state of this model)."]}, {"cell_type": "markdown", "id": "0864a589", "metadata": {}, "source": ["For the normalized single-boson probability we will take a sigmoid form:\n", "\n", "$$P(X; \\gamma) = 1/(1 + e^{-\\gamma X})$$\n", "thus depending on the single, real-valued variational parameter $\\gamma$.\n", "\n", "In `NetKet` one has to define a variational function approximating the **logarithm** of the wave-function amplitudes (or density-matrix values).\n", "\n", "$$ \\langle X_1,\\dots X_N| \\Psi_{\\mathrm{mf}} \\rangle = \\exp{(\\textrm{Model}(X; \\theta))} $$\n", "\n", "where $\\theta$ is a set of parameters.\n", "\n", "In this case, the parameter of the model will be just one: $\\gamma$.\n", "\n", "The Model can be defined using one of the several functional jax frameworks such as Jax/Stax, Flax or Haiku.\n", "`NetKet` includes several pre-built models and layers built with Flax, so we will be using it for the rest of the notebook."]}, {"cell_type": "code", "execution_count": 20, "id": "5947ff93", "metadata": {}, "outputs": [], "source": ["# always use jax.numpy for automatic differentiation of operations\n", "import jax.numpy as jnp\n", "\n", "# Flax is a framework to define models using jax\n", "import flax.linen as nn\n", "\n", "\n", "# A Flax model must be a class subclassing `nn.Module` (or later of nk.nn.Module)\n", "class MF(nn.Module):\n", "\n", "    # The most compact way to define the model is this.\n", "    # The __call__(self, x) function should take as\n", "    # input a batch of states x.shape = (n_samples, L)\n", "    # and should return a vector of n_samples log-amplitudes\n", "    @nn.compact\n", "    def __call__(self, x):\n", "\n", "        # A tensor of variational parameters is defined by calling\n", "        # the method `self.param` where the arguments will be:\n", "        # - arbitrary name used to refer to this set of parameters\n", "        # - an initializer used to provide the initial values.\n", "        # - The shape of the tensor\n", "        # - The dtype of the tensor.\n", "        gam = self.param(\"gamma\", nn.initializers.normal(), (1,), float)\n", "\n", "        # compute the probabilities\n", "        p = nn.log_sigmoid(gam * x)\n", "\n", "        # sum the output to return a single number (on a batch of input configurations)\n", "        return 0.5 * jnp.sum(p, axis=-1)"]}, {"cell_type": "markdown", "id": "db4fea5e", "metadata": {}, "source": ["### 3.1 Variational State\n", "\n", "The model itself is only a set of instructions on how to initialise the parameters and how to compute the result.\n", "\n", "To actually create a **variational state** with its parameters, the easiest way is to construct a Monte-Carlo-sampled Variational State.\n", "\n", "To do this, we first need to define a sampler.\n", "\n", "In [`netket.sampler`](https://www.netket.org/docs/api.html#list-of-samplers) several samplers are defined, each with its own peculiarities.\n", "\n", "In the following example, we will be using a simple sampler that chooses the occupation number of each boson at random (uniform between 0 and $\\Lambda-1$) one by one.\n", "\n", "You can read more about how the sampler works by checking the documentation with `?nk.sampler.MetropolisLocal`"]}, {"cell_type": "code", "execution_count": 21, "id": "60770131", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["# Create an instance of the model.\n", "# Notice that this does not create the parameters.\n", "mf_model = MF()\n", "\n", "# Create the local sampler on the hilbert space\n", "sampler = nk.sampler.MetropolisLocal(hi, n_chains=4)\n", "\n", "# Construct the variational state using the model and the sampler above.\n", "# n_samples specifies how many samples should be used to compute expectation\n", "# values.\n", "vstate = nk.vqs.MCState(sampler, mf_model, n_samples=2**9)"]}, {"cell_type": "markdown", "id": "eda9293a", "metadata": {}, "source": ["You can play around with the variational state: for example, you can compute expectation values yourself or inspect it's parameters"]}, {"cell_type": "code", "execution_count": 22, "id": "56b3c6b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["FrozenDict({\n", "    gamma: <PERSON>ceA<PERSON>y([0.00979636], dtype=float64),\n", "})"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# you can inspect the parameters which contain the single\n", "# variational parameter `gamma`\n", "vstate.parameters"]}, {"cell_type": "code", "execution_count": 23, "id": "93e03c76", "metadata": {}, "outputs": [{"data": {"text/plain": ["12.95 ± 0.15 [σ²=8.93, R̂=1.0034]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Expectation value: notice that it also provides an error estimate. and the monte carlo chain properties, such as the G<PERSON>man-<PERSON> statistics across chains\n", "vstate.expect(H)"]}, {"cell_type": "markdown", "id": "773bf421", "metadata": {}, "source": ["The last cell was run with a randomly initialized parameter $\\gamma$ and for 512 samples (notice the metrics for the convergence of the <PERSON> algorithm in square brackets)."]}, {"cell_type": "markdown", "id": "3fadf491", "metadata": {}, "source": ["## 4. Variational Monte Carlo"]}, {"cell_type": "markdown", "id": "a8761095", "metadata": {}, "source": ["We will now try to optimise $\\gamma$ in order to best approximate the ground state of the hamiltonian.\n", "\n", "To do so, first I need to pick an iterative optimiser. The following is a choice that can be further optimized later on, depending on the learning curve for the specific ansatz and system.\n", "\n", "We choose stochastic gradient descent with a learning rate of $\\eta = 0.05$.\n", "\n", "Then, we must provide all the elements to the variational monte carlo driver, which takes case of setting up and running the whole optimisation.\n", "\n", "For example, the driver can be run for a specific number of iterations. Each iteration will have to run the Monte Carlo sampling defined by the variational state `nk.vqs.MCState`."]}, {"cell_type": "code", "execution_count": 24, "id": "83a91165", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:04<00:00, 70.32it/s, Energy=3.228 ± 0.022 [σ²=0.087, R̂=1.0334]]    \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy 3.258 ± 0.026 [σ²=0.118, R̂=1.0193] \n", " and relative error: 3.956%\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# build the optimisation driver\n", "gs = nk.driver.VMC(H, optimizer, variational_state=vstate)\n", "\n", "# Loggers that work together with optimisation drivers are defined in nk.logging.\n", "# RuntimeLog keeps the metrics in memory, JsonLog stores them to a json file which can be read\n", "# as a dict, TensorBoardLog can be used to log to TensorBoard.\n", "log = nk.logging.RuntimeLog()\n", "\n", "# One or more logger objects must be passed to the keyword argument `out`.\n", "gs.run(n_iter=300, out=log)\n", "\n", "# expectation value of the energy\n", "mf_energy = vstate.expect(H)\n", "# compare with the <PERSON><PERSON><PERSON><PERSON> exact diagonalization energy eigenvalue\n", "error = abs((mf_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy {mf_energy} \\n and relative error: {error*100:.3f}%\")"]}, {"cell_type": "markdown", "id": "1e751765", "metadata": {}, "source": ["The optimization process outputs useful information such as the energy value, its standard error, its variance, and the G<PERSON><PERSON>-<PERSON> statistics $\\hat{R}$ (R-hat)."]}, {"cell_type": "code", "execution_count": 25, "id": "6d513d8d", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final optimized parameter:  [-5.28496854]\n"]}], "source": ["# we can also inspect the parameter:\n", "print(\"Final optimized parameter: \", vstate.parameters[\"gamma\"])"]}, {"cell_type": "code", "execution_count": 26, "id": "816e9c3f", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final optimized energy relative error (with mean-field ansatz): 3.96%\n"]}], "source": ["print(\n", "    f\"Final optimized energy relative error (with mean-field ansatz): {error*100:.2f}%\"\n", ")"]}, {"cell_type": "markdown", "id": "3f4b81cd", "metadata": {}, "source": ["Even a very rudimentary ansatz without interactions between the degrees of freedom is sufficient to reach a <10% error on the ground state energy with a single parameter to optimize.\n", "\n", "We will see more complicated ansatzë below."]}, {"cell_type": "markdown", "id": "f6d77238", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "We have seen that the mean field ansatz yields about sub-10% error on the ground-state energy. Let's now try to do better, using a more correlated ansatz.\n", "\n", "We will now take a short-range Jastrow ansatz which is already inlcuded in `netket` as [`nk.models.Jastrow`](https://www.netket.org/docs/_generated/models/netket.models.Jastrow.html#netket.models.Jastrow):\n", "\n", "$$\\log{\\Psi(X)} = \\sum_{ij} X_i W_{ij} X_j$$\n", "\n", "where $W_{ij}$ is a symmetric matrix of learnable complex parameters (the _kernel_ of the quadratic form)."]}, {"cell_type": "code", "execution_count": 27, "id": "d9f2f3e7", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["# change the model: initialize weights with normal distributions\n", "model = nk.models.Jastrow(kernel_init=nn.initializers.normal())\n", "\n", "# we use the same MetropolicLocal sapmler as before. Now we sample for longer to get a more accurate result\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=2**10)"]}, {"cell_type": "code", "execution_count": 28, "id": "40e15610", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FrozenDict({\n", "    kernel: DeviceArray([[ 0.01049682+0.01166978j,  0.00682185+0.00620781j,\n", "                   0.00733915+0.01080751j,  0.00185571+0.00749652j,\n", "                  -0.00214337+0.00025017j, -0.00318804-0.00774995j],\n", "                 [-0.00512184-0.00538546j,  0.00184588-0.01090576j,\n", "                   0.00651656-0.00574499j,  0.00724852-0.00697769j,\n", "                  -0.01310518-0.00781628j,  0.0107602 +0.00490359j],\n", "                 [-0.00207881-0.00916165j,  0.00700885+0.00145953j,\n", "                   0.00113884-0.00484843j, -0.00317073-0.01036345j,\n", "                   0.00207871+0.01423066j,  0.01065844+0.00981316j],\n", "                 [ 0.00393085+0.01117876j,  0.00657261+0.00599069j,\n", "                  -0.01086216-0.00661874j,  0.00182235+0.00389626j,\n", "                  -0.00074458+0.0142108j ,  0.00068885+0.00487905j],\n", "                 [ 0.00323924+0.00014168j, -0.00236898+0.01239787j,\n", "                  -0.00583072-0.00946589j,  0.00549118-0.0123498j ,\n", "                  -0.00418706+0.00392197j, -0.00949517-0.00561518j],\n", "                 [-0.00515065+0.00286905j, -0.00319454-0.00696432j,\n", "                  -0.01778186-0.01000898j, -0.00727098+0.00334154j,\n", "                  -0.01086984+0.00141303j,  0.0006615 -0.00233334j]],            dtype=complex128),\n", "})\n"]}], "source": ["# look at the parameters\n", "print(vstate.parameters)"]}, {"cell_type": "markdown", "id": "7970814b", "metadata": {}, "source": ["We then optimize it, however this time we also introduce a stochastic reconfiguration (natural gradient) preconditioner:"]}, {"cell_type": "code", "execution_count": 29, "id": "631e6c02", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "preconditioner = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# driver for running the simulation\n", "gs = nk.driver.VMC(\n", "    H, optimizer, variational_state=vstate, preconditioner=preconditioner\n", ")"]}, {"cell_type": "markdown", "id": "6338e91e", "metadata": {"lines_to_next_cell": 2}, "source": ["We now log the intermediate results of the optimization, so that we can visualize them at a later stage.\n", "`netket` is very flexible and allows you to implement callbacks that can be used to report important information during the training process.\n", "Here we add a callback to the VMC driver in order to save the acceptance rate of the sampling algorithm at each step of the optimization.\n", "A callback takes the `step` of the training process, the `logged_data` dictionary and the `driver` as its arguments. The `driver` contains information about the acceptance rate in its `state.sampler_state`."]}, {"cell_type": "code", "execution_count": 30, "id": "e7bd2edd", "metadata": {}, "outputs": [], "source": ["# define acceptance logger callback\n", "def cb_acc(step, logged_data, driver):\n", "    logged_data[\"acceptance\"] = float(driver.state.sampler_state.acceptance)\n", "    return True"]}, {"cell_type": "code", "execution_count": 31, "id": "5ef5cd4c", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:06<00:00, 47.81it/s, Energy=3.1532-0.0000j ± 0.0024 [σ²=0.0035, R̂=1.0031]]            \n"]}, {"data": {"text/plain": ["(RuntimeLog():\n", "  keys = ['Energy', 'acceptance'],)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Loggers that work together with optimisation drivers are defined in nk.logging.\n", "# RuntimeLog keeps the metrics in memory, JsonLog stores them to a json file which can be read\n", "# as a dict, TensorBoardLog can be used to log to TensorBoard.\n", "log = nk.logging.RuntimeLog()\n", "\n", "# One or more logger objects must be passed to the keyword argument `out`. The callback is added with the `callback` argument\n", "gs.run(n_iter=300, out=log, callback=cb_acc)"]}, {"cell_type": "code", "execution_count": 32, "id": "fbd57d5e", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final optimized kernel parameters: [[-1.70920011+1.71433180e-02j -0.05665743+5.16675585e-03j\n", "  -0.06777346+9.84367778e-03j -0.02855007-3.18257508e-04j\n", "  -0.11838393+2.13016722e-04j -0.12411947-9.27935996e-03j]\n", " [-0.06860111-6.42651787e-03j -1.64852954-2.35979880e-02j\n", "  -0.06522334-8.32805942e-05j -0.09636936-6.51420533e-03j\n", "  -0.03033081-3.64966909e-03j -0.11377063+2.69651066e-03j]\n", " [-0.07719142-1.01254905e-02j -0.06473105+7.12124247e-03j\n", "  -1.67554447-1.28902126e-02j -0.10571423-1.22707167e-02j\n", "  -0.10023615+1.11159834e-02j -0.01610114+1.94671388e-02j]\n", " [-0.02647493+3.36398030e-03j -0.09704527+6.45416707e-03j\n", "  -0.11340566-8.52600511e-03j -1.66012998+1.25668208e-02j\n", "  -0.05191383+6.62303095e-03j -0.06245702+5.02777372e-03j]\n", " [-0.11300132+1.04528231e-04j -0.01959461+1.65644798e-02j\n", "  -0.10814557-1.25805576e-02j -0.04567807-1.99375665e-02j\n", "  -1.65049581+1.04955161e-02j -0.06156012-8.77069255e-03j]\n", " [-0.12608207+1.33964782e-03j -0.12772536-9.17140560e-03j\n", "  -0.04454144-3.54995508e-04j -0.07041686+3.49026603e-03j\n", "  -0.06293479-1.74248425e-03j -1.65837699-6.98319376e-03j]]\n", "\n", "Optimized energy : 3.171-0.000j ± 0.011 [σ²=0.023, R̂=1.0233]\n", "relative error   : 1.19%\n"]}], "source": ["print(f\"Final optimized kernel parameters: {vstate.parameters['kernel']}\\n\")\n", "jas_energy = vstate.expect(H)\n", "error = abs((jas_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy : {jas_energy}\")\n", "print(f\"relative error   : {error*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "ee1808aa", "metadata": {}, "source": ["You can now see that this ansatz is almost one order of magnitude more accurate than the mean field!\n", "\n", "In order to visualize what happened during the optimization, we can use the data that has been stored by the logger. There are several available loggers in NetKet, here we have just used a simple one that stores the intermediate results as values in a dictionary."]}, {"cell_type": "code", "execution_count": 33, "id": "67d1f221", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Energy': History(\n", "   keys  = ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'R_hat', '<PERSON><PERSON><PERSON><PERSON>'], \n", "   iters = [0, 1, ... 298, 299] (300 steps),\n", "), 'acceptance': History(\n", "   keys  = ['value'], \n", "   iters = [0, 1, ... 298, 299] (300 steps),\n", ")}\n"]}], "source": ["data_jastrow = log.data\n", "print(data_jastrow)"]}, {"cell_type": "code", "execution_count": 34, "id": "c9a44c66", "metadata": {}, "outputs": [], "source": ["energy_history = data_jastrow[\"Energy\"].Mean.real\n", "energy_error_history = data_jastrow[\"Energy\"].Sigma.real"]}, {"cell_type": "markdown", "id": "4740f3b0", "metadata": {}, "source": ["These report several intermediate quantities, that can be easily plotted. For example we can plot the value of the energy (with its error bar) at each optimization step."]}, {"cell_type": "code", "execution_count": 35, "id": "a5c7c5f1", "metadata": {"lines_to_next_cell": 2, "scrolled": true}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Energy (Re)')"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10, 5))\n", "ax.errorbar(data_jastrow[\"Energy\"].iters, energy_history, yerr=energy_error_history)\n", "ax.axhline(\n", "    y=eig_vals[0],\n", "    xmin=0,\n", "    xmax=data_jastrow[\"Energy\"].iters[-1],\n", "    linewidth=2,\n", "    color=\"k\",\n", "    label=\"Exact\",\n", ")\n", "ax.legend()\n", "ax.set_xlabel(\"Iterations\")\n", "ax.set_ylabel(\"Energy (Re)\")"]}, {"cell_type": "markdown", "id": "f6e48ada", "metadata": {}, "source": ["You can also look at the MCMC chain properties, like the Gelman-Rubin statistic (it should be very close to 1.00 for all chains to be equilibrated)"]}, {"cell_type": "code", "execution_count": 36, "id": "bbacb696", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Iterations')"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(data_jastrow[\"Energy\"].R_hat)\n", "plt.ylabel(\"R_hat\")\n", "plt.xlabel(\"Iterations\")"]}, {"cell_type": "markdown", "id": "e16ebb14", "metadata": {}, "source": ["Some chains have `R_hat` larger than ones, which means they might not have converged within the $2^{10}=1024$ samples that we had specified..."]}, {"cell_type": "markdown", "id": "8e2a49a7", "metadata": {}, "source": ["Moreover, we can look at the acceptance rate. In this figure below it is clear that the sampler is failing to explore the entirety of the Hilbert space and after 50 iterations the sampling starts failing and the local transition moves made by the sampler are not accepted by the Metropolis accept/reject step."]}, {"cell_type": "code", "execution_count": 37, "id": "6479b3d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Acc. Rate')"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10, 5))\n", "ax.errorbar(\n", "    data_jastrow[\"Energy\"].iters, data_jastrow[\"acceptance\"][\"value\"], label=\"Jastrow\"\n", ")\n", "ax.axhline(0, c=\"k\")\n", "ax.legend()\n", "\n", "ax.set_xlabel(\"Iterations\")\n", "ax.set_ylabel(\"Acc. Rate\")"]}, {"cell_type": "markdown", "id": "278881e1", "metadata": {}, "source": ["### 5.1 Exact Sampling"]}, {"cell_type": "markdown", "id": "a8cc490f", "metadata": {}, "source": ["We can change the sampler to an exact sampler which does not suffer from autocorrelation effects and can sample from the exact Born distribution $|\\psi(x)|^2$ which is pre-computed on the entire Hilbert space. This is feasible only on small Hilbert spaces, like the one we are using here"]}, {"cell_type": "code", "execution_count": 38, "id": "11b3b897", "metadata": {}, "outputs": [], "source": ["# change the model: initialize weights with normal distributions\n", "model = nk.models.Jastrow(kernel_init=nn.initializers.normal())\n", "\n", "# Create the an exact sampler over the <PERSON><PERSON>ber space , if it is not too large\n", "sampler = nk.sampler.ExactSampler(hi)\n", "\n", "# Construct the variational state using the model and the sampler above.\n", "# n_samples specifies how many samples should be used to compute expectation\n", "# values.\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=2**10)"]}, {"cell_type": "markdown", "id": "d80ba338", "metadata": {}, "source": ["The VMC driver and the optimization algorithms can stay the same. Only the sampling has changed"]}, {"cell_type": "code", "execution_count": 39, "id": "7e5dadc0", "metadata": {}, "outputs": [], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "preconditioner = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# driver for running the simulation\n", "gs = nk.driver.VMC(\n", "    H, optimizer, variational_state=vstate, preconditioner=preconditioner\n", ")"]}, {"cell_type": "markdown", "id": "5c3e1517", "metadata": {}, "source": ["We do not call the `cb_acc` callback here because this exact sampler has no acceptance rate (it is always 1!!)"]}, {"cell_type": "code", "execution_count": 40, "id": "57a67510", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:05<00:00, 58.28it/s, Energy=3.1607+0.0000j ± 0.0028 [σ²=0.0117]]\n"]}, {"data": {"text/plain": ["(RuntimeLog():\n", "  keys = ['Energy'],)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# Loggers that work together with optimisation drivers are defined in nk.logging.\n", "# RuntimeLog keeps the metrics in memory, JsonLog stores them to a json file which can be read\n", "# as a dict, TensorBoardLog can be used to log to TensorBoard.\n", "log = nk.logging.RuntimeLog()\n", "\n", "# One or more logger objects must be passed to the keyword argument `out`.\n", "gs.run(n_iter=300, out=log)"]}, {"cell_type": "code", "execution_count": 41, "id": "94e7c583", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final optimized kernel parameters: [[-1.70783517+1.01069444e-02j -0.07502678-6.54106176e-05j\n", "  -0.06653237+4.43772603e-05j -0.02631243-1.19484642e-02j\n", "  -0.110641  -1.79247926e-03j -0.13704972-6.22070195e-04j]\n", " [-0.08793935-6.39385428e-03j -1.70901641+8.05897637e-03j\n", "  -0.06646917-5.48065451e-03j -0.09646871+1.79193912e-02j\n", "  -0.01617804+5.92073249e-03j -0.13177261-1.01622964e-02j]\n", " [-0.07401405+4.76853168e-03j -0.06063318+1.79889508e-03j\n", "  -1.70474991-1.50494694e-03j -0.12238211+8.51743651e-03j\n", "  -0.11521443+1.01223791e-02j -0.04089995+1.69172518e-03j]\n", " [-0.03594193-1.29897463e-02j -0.10875634+1.54751813e-02j\n", "  -0.11966952+3.21274355e-03j -1.70394268+2.01133534e-02j\n", "  -0.07491248+4.08243238e-04j -0.06654971-6.35513162e-03j]\n", " [-0.08794507-5.28773463e-04j -0.00926488-1.13772738e-02j\n", "  -0.11087839-1.46275006e-02j -0.06629862-1.60724854e-02j\n", "  -1.71097248+1.11144418e-02j -0.07972108+6.90643908e-03j]\n", " [-0.1249983 +1.75356359e-03j -0.13349868-2.64028565e-03j\n", "  -0.04512771-7.72897598e-03j -0.0730808 -7.43176665e-03j\n", "  -0.08663991+7.97132918e-03j -1.71506481+3.25330448e-03j]]\n", "\n", "Optimized energy : 3.1586+0.0000j ± 0.0030 [σ²=0.0094]\n", "relative error   : 0.78%\n"]}], "source": ["print(f\"Final optimized kernel parameters: {vstate.parameters['kernel']}\\n\")\n", "jas_energy = vstate.expect(H)\n", "error = abs((jas_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy : {jas_energy}\")\n", "print(f\"relative error   : {error*100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 42, "id": "728c6d21", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["data_jastrow = log.data\n", "energy_history = data_jastrow[\"Energy\"][\"Mean\"].real\n", "energy_error_history = data_jastrow[\"Energy\"][\"Sigma\"]\n", "\n", "fig, ax = plt.subplots(figsize=(10, 5))\n", "ax.errorbar(\n", "    data_jastrow[\"Energy\"].iters,\n", "    energy_history,\n", "    yerr=energy_error_history,\n", "    label=\"J<PERSON><PERSON>\",\n", ")\n", "ax.axhline(\n", "    y=eig_vals[0],\n", "    xmin=0,\n", "    xmax=data_jastrow[\"Energy\"].iters[-1],\n", "    linewidth=2,\n", "    color=\"k\",\n", "    label=\"Exact\",\n", ")\n", "ax.legend()\n", "ax.set_xlabel(\"Iterations\")\n", "ax.set_ylabel(\"Energy (Re)\")"]}, {"cell_type": "markdown", "id": "1ec51caf", "metadata": {}, "source": ["The result obtained with the exact sampler is encouraging because we can see that we have achieved the same accuracy on the ground state energy as before. Even if the Metropolis local sampler does not work well for this ground state, the resulting ground state is still the correct one.\n", "The failure in sampling from the computational basis states is due to the fact that, for this system, the ground state is a quantum state that has overlap only on one basis $|s\\rangle$. This happens at very small couplings: remember that the zero-coupling limit is just un-coupled harmonic oscillators."]}, {"cell_type": "markdown", "id": "14df00f6", "metadata": {"lines_to_next_cell": 2}, "source": ["## 6. Neural-Network Quantum State\n", "\n", "We now want to use a more sofisticated ansatz, based on a neural network representation of the wave function. At this point, this is quite straightforward, since we can again take advantage of automatic differentiation.\n", "\n", "Let us define a simple fully-connected feed-forward network with a ReLu activation function and a sum layer."]}, {"cell_type": "code", "execution_count": 43, "id": "8654b98b", "metadata": {}, "outputs": [], "source": ["class FFN(nn.Module):\n", "\n", "    # You can define attributes at the module-level\n", "    # with a default. This allows you to easily change\n", "    # some hyper-parameter without redefining the whole\n", "    # flax module. This is the ratio of neurons to input dofs\n", "    alpha: int = 1\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "\n", "        # here we construct the first dense layer using a\n", "        # pre-built implementation in flax.\n", "        # features is the number of output nodes\n", "        dense = nn.Dense(features=self.alpha * x.shape[-1], param_dtype=complex)\n", "\n", "        # we apply the dense layer to the input\n", "        y = dense(x)\n", "\n", "        # the non-linearity is a simple ReLu\n", "        y = nn.relu(y)\n", "\n", "        # sum the output\n", "        return jnp.sum(y, axis=-1)"]}, {"cell_type": "markdown", "id": "61311c11", "metadata": {}, "source": ["We will keep using the exact sampler to avoid ergodicity issues"]}, {"cell_type": "code", "execution_count": 44, "id": "38254134", "metadata": {}, "outputs": [], "source": ["# it is easy here to pass the hyper-parameter value\n", "model = FFN(alpha=1)\n", "\n", "# Create the an exact sampler over the <PERSON><PERSON>ber space , if it is not too large\n", "sampler = nk.sampler.ExactSampler(hi)\n", "\n", "# create the VMC state\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=2**10)"]}, {"cell_type": "markdown", "id": "9aa48375", "metadata": {}, "source": ["We then proceed to the optimization as before."]}, {"cell_type": "code", "execution_count": 45, "id": "dad6111e", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:05<00:00, 58.55it/s, Energy=3.1573+0.0003j ± 0.0056 [σ²=0.0342]]            \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy: 3.1550+0.0002j ± 0.0027 [σ²=0.0059] \n", "Relative error: 0.67%\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "preconditioner = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    H, optimizer, variational_state=vstate, preconditioner=preconditioner\n", ")\n", "\n", "# logging and running : sometimes need to rung longer than 300 to exit a plateaux\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=300, out=log)\n", "\n", "ffn_energy = vstate.expect(H)\n", "error = abs((ffn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy: {ffn_energy} \\nRelative error: {error*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "25c8fbc9", "metadata": {}, "source": ["And we can compare the results between the two different ansatze. The fully-connected ansatz needs more iterations to converge:"]}, {"cell_type": "code", "execution_count": 46, "id": "30fdf161", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Energy (Re)')"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAl4AAAE9CAYAAADaqWzvAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAAsTAAALEwEAmpwYAABB0ElEQVR4nO3deZicVZn+8e9Te+9Lur<PERSON>29kCCSUiAEAQM+ybgK<PERSON>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", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["data_FFN = log.data\n", "\n", "fig, ax = plt.subplots(figsize=(10, 5))\n", "ax.errorbar(\n", "    data_jastrow[\"Energy\"].iters,\n", "    data_jastrow[\"Energy\"].Mean.real,\n", "    yerr=data_jastrow[\"Energy\"].Sigma,\n", "    label=\"J<PERSON><PERSON>\",\n", ")\n", "ax.errorbar(\n", "    data_FFN[\"Energy\"].iters,\n", "    data_FFN[\"Energy\"].Mean.real,\n", "    yerr=data_FFN[\"Energy\"].Sigma,\n", "    label=\"FFN\",\n", ")\n", "ax.axhline([eig_vals[0]], xmin=0, xmax=300, color=\"black\", label=\"Exact\")\n", "ax.legend()\n", "\n", "ax.set_xlabel(\"Iterations\")\n", "ax.set_ylabel(\"Energy (Re)\")"]}, {"cell_type": "markdown", "id": "c45424d8", "metadata": {}, "source": ["The plateaux in the figure corresponds to a configuration of bosonic levels where all \"sites\" are in the ground state but one. Getting out of that plateaux is difficult and requires changing the learning rate or the initial set of parameters."]}, {"cell_type": "markdown", "id": "14c552ed", "metadata": {}, "source": ["### 6.1 Increasing the features\n", "\n", "We can try to use more _neurons_ in the dense layer to see if we can get a better result.\n", "This can be done simply by instantiating the `FFN` model again with a larger `alpha`."]}, {"cell_type": "code", "execution_count": 47, "id": "8515affe", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:05<00:00, 54.37it/s, Energy=3.1492-0.0006j ± 0.0010 [σ²=0.0011]]            \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy: 3.148e+00-5.581e-04j ± 7.850e-17 [σ²=1.775e-30] \n", "Relative error: 0.45%\n"]}], "source": ["# it is easy here to pass the hyper-parameter value\n", "model = FFN(alpha=2)\n", "\n", "sampler = nk.sampler.ExactSampler(hi)\n", "\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=2**10)\n", "\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    H,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.1),\n", ")\n", "gs.run(n_iter=300, out=log)\n", "\n", "ffn_energy = vstate.expect(H)\n", "error = abs((ffn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy: {ffn_energy} \\nRelative error: {error*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "89d31b22", "metadata": {}, "source": ["This is the best we can do without taking further steps to optimize the learning rate and stochastic gradient descent iterations, which will require more resources."]}, {"cell_type": "markdown", "id": "691675e4", "metadata": {}, "source": ["Clearly we can run for longer, by starting from the current `vstate`"]}, {"cell_type": "code", "execution_count": 48, "id": "527e7647", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:03<00:00, 82.31it/s, Energy=3.1502-0.0003j ± 0.0014 [σ²=0.0022]]            \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy: 3.148e+00-3.001e-04j ± 9.583e-21 [σ²=1.175e-38] \n", "Relative error: 0.45%\n"]}], "source": ["gs.run(n_iter=300, out=log)\n", "\n", "ffn_energy = vstate.expect(H)\n", "error = abs((ffn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy: {ffn_energy} \\nRelative error: {error*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "16864b46", "metadata": {}, "source": ["The very small variance again indicates that the state has a probability distribution peaked on a single state"]}, {"cell_type": "markdown", "id": "37bfbc77", "metadata": {}, "source": ["We can visualize the ground state using `vstate.to_array()` to expand the wave function on the computational basis of states"]}, {"cell_type": "code", "execution_count": 49, "id": "e20a58d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x2af31cac0>"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10, 5))\n", "ax.bar(np.arange(0, hi.n_states), np.abs(vstate.to_array()), label=\"FNNmodel\")\n", "ax.set_xlim([0, 49])\n", "ax.set_xlabel(\"basis coordinate\")\n", "ax.set_ylabel(r\"$\\mid \\psi \\mid$\")\n", "ax.legend(loc=0)"]}, {"cell_type": "markdown", "id": "0b8df244", "metadata": {}, "source": ["**Note**: sometimes the FNN ansatz gets stuck on a parameter set that represent the state where only the one \"site\" is not in the $|0\\rangle$ state, in particular if a non-exact sampler (e.g. Metropolis) is used."]}, {"cell_type": "code", "execution_count": 50, "id": "d6d9bf3c", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 300/300 [00:05<00:00, 52.97it/s, Energy=3.1578-0.0013j ± 0.0039 [σ²=0.0082, R̂=1.0695]]            "]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy: 3.1666-0.0012j ± 0.0058 [σ²=0.0180, R̂=1.1561] \n", "Relative error: 1.04%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["model = FFN(alpha=1)\n", "\n", "vstate = nk.vqs.MCState(nk.sampler.MetropolisLocal(hi), model, n_samples=2**10)\n", "\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    H,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.1),\n", ")\n", "gs.run(n_iter=300, out=log)\n", "\n", "ffn_energy = vstate.expect(H)\n", "error = abs((ffn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy: {ffn_energy} \\nRelative error: {error*100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 51, "id": "8bf32a60", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2160x576 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(ncols=N, figsize=(30, 8))\n", "for i, axi in enumerate(ax):\n", "    axi.plot(vstate.samples[:, :, i].flatten(), \".\", label=f\"site {i}\")\n", "    axi.set_ylim(-0.1, Lambda - 0.9)\n", "    axi.legend()"]}, {"cell_type": "code", "execution_count": 52, "id": "6f605cec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of parameters (FFN(alpha=1)): 42\n"]}], "source": ["print(f\"Number of parameters (FFN(alpha=1)): {vstate.n_parameters}\")"]}, {"cell_type": "markdown", "id": "5b5103bb", "metadata": {}, "source": ["## 7. Autoregressive models\n", "\n", "In [this paper](https://arxiv.org/abs/2108.02942), an autoregressive flow model was used to parametrize the modulus of the wave function, while a fully-connected layer was used for the phase. Although the paper consider matrices in the coordinate basis, we here continue using the discrete Fock basis.\n", "\n", "Autoregressive models are included in `netket`, together with direct samplers for their conditional distributions (see [this link](https://www.netket.org/docs/_generated/models/netket.models.ARNNDense.html#netket-models-arnndense) for a fully-connected autoregressive model)"]}, {"cell_type": "code", "execution_count": 53, "id": "d5f44c9a", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["model = nk.models.ARNNDense(hilbert=hi, layers=3, features=10)\n", "\n", "# the autoregressive model has an exact sampler because of the conditional prob\n", "sampler = nk.sampler.ARDirectSampler(hi)\n", "\n", "# With direct sampling, we don't need many samples in each step to form a\n", "# Markov chain, and we don't need to discard samples\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=2**6)"]}, {"cell_type": "code", "execution_count": 54, "id": "6f06ca78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of parameters (ARNNDense): 5544\n"]}], "source": ["print(f\"Number of parameters (ARNNDense): {vstate.n_parameters}\")"]}, {"cell_type": "markdown", "id": "7c157447", "metadata": {}, "source": ["This model has a much larger number of parameters compared to the fully-connected ansatz we used above."]}, {"cell_type": "markdown", "id": "1faf6806", "metadata": {}, "source": ["Then we can call our driver as before"]}, {"cell_type": "code", "execution_count": 55, "id": "ec64b70e", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1000/1000 [00:13<00:00, 75.88it/s, Energy=3.150e+00 ± nan [σ²=0.000e+00]]      \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy: 3.150e+00 ± nan [σ²=0.000e+00] \n", "Relative error: 0.51%\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.1)\n", "preconditioner = nk.optimizer.SR(diag_shift=0.1)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    H, optimizer, variational_state=vstate, preconditioner=preconditioner\n", ")\n", "\n", "# logging and running\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=1000, out=log)\n", "\n", "arnn_energy = vstate.expect(H)\n", "error = abs((arnn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy: {arnn_energy} \\nRelative error: {error*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "4c6923be", "metadata": {}, "source": ["## 8. Measuring Other Properties\n", "\n", "Once the model has been optimized, we can measure other observables that are not the energy.\n", "For example, we could measure the generators of the gauge transformations.\n", "\n", "We could also use a larger number of samples to have a sufficiently low error bar on their measurement, by changing `vstate.n_samples` on the fly."]}, {"cell_type": "markdown", "id": "0d458987", "metadata": {}, "source": ["The Casimir operator for the gauge group is defined using a combination of $\\hat{a}^\\dagger_I$ and $\\hat{a}_I$ for each boson. We are assuming that sites 0, 1, and 2 are for the 3 gauge dof of one matrix and site 3, 4, and 5 are for the other matrix. Given this assignment we can write\n", "\n", "$$ \\hat{G}_{\\alpha}=i \\sum_{\\beta, \\gamma, I} f_{\\alpha \\beta \\gamma} \\hat{a}_{I \\beta}^{\\dagger} \\hat{a}_{I \\gamma} $$\n", "\n", "using the matrices on the 6 bosonic sites defined at the beginning.\n", "Overall, the Gauge <PERSON> operator is\n", "\n", "$$ \\hat{G^2} = \\sum_{\\alpha} \\hat{G}_{\\alpha} \\hat{G}_{\\alpha}$$"]}, {"cell_type": "code", "execution_count": 56, "id": "b5443f01", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["g_list = [0] * 3\n", "g_list[0] = 1j * (\n", "    create(hi, 1) * destroy(hi, 2)\n", "    - create(hi, 2) * destroy(hi, 1)\n", "    + create(hi, 4) * destroy(hi, 5)\n", "    - create(hi, 5) * destroy(hi, 4)\n", ")\n", "g_list[1] = 1j * (\n", "    create(hi, 2) * destroy(hi, 0)\n", "    - create(hi, 0) * destroy(hi, 2)\n", "    + create(hi, 5) * destroy(hi, 3)\n", "    - create(hi, 3) * destroy(hi, 5)\n", ")\n", "g_list[2] = 1j * (\n", "    create(hi, 0) * destroy(hi, 1)\n", "    - create(hi, 1) * destroy(hi, 0)\n", "    + create(hi, 3) * destroy(hi, 4)\n", "    - create(hi, 4) * destroy(hi, 3)\n", ")\n", "G = sum([g * g for g in g_list])"]}, {"cell_type": "markdown", "id": "264597e3", "metadata": {}, "source": ["We can sample 1024 times to get a more accurate result. We expect to find zero if the ground state is gauge invariant"]}, {"cell_type": "code", "execution_count": 57, "id": "014dfac0", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.000e+00+0.000e+00j ± nan [σ²=0.000e+00]"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["vstate.n_samples = 2**10\n", "vstate.expect(G)"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3.10.5 ('netket-mpi')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}, "vscode": {"interpreter": {"hash": "90199b5ce43e5eaf94df96e295907989efb0b134b9aac5e2aaf485a0967e1c01"}}}, "nbformat": 4, "nbformat_minor": 5}