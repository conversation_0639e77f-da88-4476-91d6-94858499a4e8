{"cells": [{"cell_type": "markdown", "id": "1805c599-bb29-4dc4-be58-b24d4efd6526", "metadata": {}, "source": ["# La<PERSON>ce Fermions, from Slater Determinants to Neural Backflow Transformations"]}, {"cell_type": "markdown", "id": "06d04f69-4b10-4ea3-a0eb-2eb3b93a7af7", "metadata": {}, "source": ["Author: <PERSON> (Computational Quantum Science Lab - EPFL)"]}, {"cell_type": "markdown", "id": "2f4dd82e-dabe-4146-a3a3-e9c9915f0994", "metadata": {}, "source": ["In this tutorial, we will introduce you to studying fermionic quantum many-body systems using NetKet. We will start by introducing fermionic operators and how to work with them in NetKet. We will then proceed to implement and optimize three different wave functions: a Slater determinant wave function, a Neural Jastrow-Slater state, and a Slater Backflow neural wave function. We will focus on a Hamiltonian of spinless fermions in two dimensions. In the following we will also assume the reader is familiar with the main concepts in second quantization, including creation and destruction operators, as well as the role of anticommutation relations."]}, {"cell_type": "markdown", "id": "876a8c86-e31d-45fd-b3cb-7e4ca7ae20b0", "metadata": {}, "source": ["## Fermionic Operators in NetKet"]}, {"cell_type": "markdown", "id": "e179b60d-6f21-4c79-9d7d-9eded189200e", "metadata": {}, "source": ["Fermionic operators are fundamental to describing quantum systems with fermions (e.g., electrons). NetKet provides these operators directly via the `netket.operator.fermion` module. Let's start by setting up the necessary environment and defining our quantum system."]}, {"cell_type": "code", "execution_count": 1, "id": "d5b69c97-2cf4-49ef-a276-5ac694243901", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:03.332390Z", "iopub.status.busy": "2025-07-13T11:38:03.332258Z", "iopub.status.idle": "2025-07-13T11:38:04.763056Z", "shell.execute_reply": "2025-07-13T11:38:04.762642Z"}, "lines_to_next_cell": 2, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NetKet version: 3.19.dev23+gfbd40743f.d20250713\n"]}], "source": ["import netket as nk\n", "\n", "print(\"NetKet version:\", nk.__version__)"]}, {"cell_type": "markdown", "id": "df6e09af-ebd4-4a40-b7e7-30ecc7b3ed17", "metadata": {}, "source": ["We will work with a Hamiltonian of spinless fermions on a two-dimensional lattice:\n", "\n", "$$\n", "\\mathcal{H} = -t \\sum_{\\langle i,j \\rangle } \\left( c^{\\dagger}_i c_j + c^{\\dagger}_j c_i \\right ) + V \\sum_{\\langle i,j \\rangle } n_i n_j\n", "$$"]}, {"cell_type": "markdown", "id": "f157b9c1-b018-4036-9899-7226db01efe7", "metadata": {}, "source": ["Here, $\\langle i,j \\rangle $ denotes nearest-neighbors on a square lattice of $N=L\\times L$ sites, $c_i (c^{\\dagger}_i)$ are destruction (creation) fermionic operators on site $i$, whereas $n_i=c^{\\dagger}_i c_i$ are density operators.\n", "\n", "## Defining the lattice and the <PERSON><PERSON> space:"]}, {"cell_type": "code", "execution_count": 2, "id": "7a88642a-fcc3-452f-af4d-506b13e6dcfb", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.765034Z", "iopub.status.busy": "2025-07-13T11:38:04.764808Z", "iopub.status.idle": "2025-07-13T11:38:04.771906Z", "shell.execute_reply": "2025-07-13T11:38:04.771622Z"}, "tags": []}, "outputs": [], "source": ["L = 4  # Side of the square\n", "graph = nk.graph.Square(L)\n", "N = graph.n_nodes"]}, {"cell_type": "markdown", "id": "cff3ecf2-ede7-4a28-b06c-1954d655c88b", "metadata": {}, "source": ["The last variable contains the total number of sites on the lattice."]}, {"cell_type": "markdown", "id": "a33c9f12-d335-4d8e-966e-32e3b8146fad", "metadata": {}, "source": ["We now define also the Hilbert space associated with a system of $N_{\\mathrm{f}}$ spinless fermions:"]}, {"cell_type": "code", "execution_count": 3, "id": "0b3735ad-7d52-4664-9d6b-804dffb1818e", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.773798Z", "iopub.status.busy": "2025-07-13T11:38:04.773629Z", "iopub.status.idle": "2025-07-13T11:38:04.778225Z", "shell.execute_reply": "2025-07-13T11:38:04.777899Z"}, "tags": []}, "outputs": [], "source": ["N_f = 5\n", "\n", "hi = nk.hilbert.SpinOrbitalFermions(N, s=None, n_fermions=N_f)"]}, {"cell_type": "markdown", "id": "140d98de-3d20-4c4e-8c56-90751f7321b4", "metadata": {}, "source": ["Notice that in the definition we have specified `s=None`, meaning that these fermions do not carry spin. If you wanted to work with spinful fermions, you would need to specify for example `s=1/2`."]}, {"cell_type": "markdown", "id": "087952c6-8c42-475a-af5f-ceb3d7fbf626", "metadata": {}, "source": ["## Fermionic Operators and Hamiltonian"]}, {"cell_type": "markdown", "id": "ab9e7ea7-20c0-413b-8fa9-face9cff32d7", "metadata": {}, "source": ["To describe the Hamiltonian of our quantum system, we need to work with the fermionic operators associated with the Hilbert space defined above. These operators include creation (cdag), annihilation (c), and number (nc) operators. We will use these operators to build our Hamiltonian."]}, {"cell_type": "code", "execution_count": 4, "id": "3be0c7bb-85f3-45b7-ac07-bc9fb2caca95", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.780036Z", "iopub.status.busy": "2025-07-13T11:38:04.779928Z", "iopub.status.idle": "2025-07-13T11:38:04.782133Z", "shell.execute_reply": "2025-07-13T11:38:04.781794Z"}, "tags": []}, "outputs": [], "source": ["from netket.operator.fermion import destroy as c\n", "from netket.operator.fermion import create as cdag\n", "from netket.operator.fermion import number as nc"]}, {"cell_type": "markdown", "id": "b28a6286-8265-4f24-92fa-05b55dcf259f", "metadata": {}, "source": ["With these operators, we can now construct the Hamiltonian for our system. In this example, we have a tight-binding hopping term proportional to $t$ and a density-density interaction term proportional to $V$. We can easily define the Hamiltonian by adding terms one by one looping over the edges of the lattice:\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a88229ce-460e-4d58-a355-60d174c8ca4f", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.783601Z", "iopub.status.busy": "2025-07-13T11:38:04.783417Z", "iopub.status.idle": "2025-07-13T11:38:04.799348Z", "shell.execute_reply": "2025-07-13T11:38:04.798995Z"}, "tags": []}, "outputs": [], "source": ["t = 1.0\n", "V = 4.0\n", "\n", "H = 0.0\n", "for i, j in graph.edges():\n", "    H -= t * (cdag(hi, i) * c(hi, j) + cdag(hi, j) * c(hi, i))\n", "    H += V * nc(hi, i) * nc(hi, j)"]}, {"cell_type": "markdown", "id": "5171f72b", "metadata": {}, "source": ["The hamiltonian above commutes with the total fermion number operator.However the operator we just defined does not exploit this property, and simply computes one connected element for every off-diagonal term:"]}, {"cell_type": "code", "execution_count": 6, "id": "26704116", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.801027Z", "iopub.status.busy": "2025-07-13T11:38:04.800871Z", "iopub.status.idle": "2025-07-13T11:38:04.804637Z", "shell.execute_reply": "2025-07-13T11:38:04.804298Z"}}, "outputs": [{"data": {"text/plain": ["65"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["H.max_conn_size"]}, {"cell_type": "markdown", "id": "6e040a3b", "metadata": {}, "source": ["many of which are zero.\n", "\n", "This can be reduced by using a more efficient implementation, to which we can easily convert our hamiltonian to:"]}, {"cell_type": "code", "execution_count": 7, "id": "64850758", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:04.806011Z", "iopub.status.busy": "2025-07-13T11:38:04.805911Z", "iopub.status.idle": "2025-07-13T11:38:12.398531Z", "shell.execute_reply": "2025-07-13T11:38:12.398206Z"}}, "outputs": [], "source": ["from netket.experimental.operator import ParticleNumberConservingFermioperator2nd\n", "\n", "H_pnc = ParticleNumberConservingFermioperator2nd.from_fermionoperator2nd(H)"]}, {"cell_type": "markdown", "id": "104a98a5", "metadata": {}, "source": ["Now the number of connected elements is reduced to"]}, {"cell_type": "code", "execution_count": 8, "id": "3059645a", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:12.400226Z", "iopub.status.busy": "2025-07-13T11:38:12.400120Z", "iopub.status.idle": "2025-07-13T11:38:12.452927Z", "shell.execute_reply": "2025-07-13T11:38:12.452641Z"}}, "outputs": [{"data": {"text/plain": ["21"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["H_pnc.max_conn_size"]}, {"cell_type": "markdown", "id": "fc323e70", "metadata": {}, "source": ["greatly reducing the number of wavfunction-evaluations needed to compute the local energies."]}, {"cell_type": "markdown", "id": "3c010018-6d22-4424-aa8c-7a5886b79312", "metadata": {}, "source": ["## Exact Diagonalization\n", "\n", "Since the system is relatively small, the <PERSON>lbert space is also not too big, and we can still use exact diagonalization to compute the ground state energy. This is achieved by first converting the Hamiltonian to a sparse matrix, and then diagonalizing it with scipy:"]}, {"cell_type": "code", "execution_count": 9, "id": "68131322-d81b-4835-95b9-d83c60d5dac7", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:12.454422Z", "iopub.status.busy": "2025-07-13T11:38:12.454320Z", "iopub.status.idle": "2025-07-13T11:38:15.771852Z", "shell.execute_reply": "2025-07-13T11:38:15.771521Z"}, "tags": []}, "outputs": [], "source": ["# Convert the Hamiltonian to a sparse matrix\n", "sp_h = H.to_sparse()"]}, {"cell_type": "code", "execution_count": 10, "id": "592dee7c-9d5d-434c-93e8-20ad014fe92d", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:15.773627Z", "iopub.status.busy": "2025-07-13T11:38:15.773527Z", "iopub.status.idle": "2025-07-13T11:38:15.853207Z", "shell.execute_reply": "2025-07-13T11:38:15.852685Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact ground state energy: -6.85901355431958\n"]}], "source": ["from scipy.sparse.linalg import eigsh\n", "\n", "eig_vals, eig_vecs = eigsh(sp_h, k=2, which=\"SA\")\n", "\n", "E_gs = eig_vals[0]\n", "\n", "print(\"Exact ground state energy:\", E_gs)"]}, {"cell_type": "markdown", "id": "71d6a17e-2252-4ad6-953c-164fb0b8cb20", "metadata": {}, "source": ["## Slater Determinant\n", "\n", "Now, let's move on to defining and optimizing a simple variational wave function based on a mean-field state: the Slater determinant.\n", "\n", "Formally, we can write the state as filling up $N_{\\mathrm{f}}$ orbitals:\n", "\n", "$$\n", "|\\Phi_s\\rangle = \\Pi_{\\alpha=1}^{N_{\\mathrm{f}}} \\phi^{\\dagger}_{\\alpha} |0\\rangle,\n", "$$\n", "\n", "where $0\\rangle$ is the vacuum state and the single-particle orbitals are created by the operators $\\phi^{\\dagger}_{\\alpha}$. In turn, these creation operators are, in general, a linear combination of the original creation operators:\n", "\n", "$$\n", "\\phi^{\\dagger}_{\\alpha} = \\sum_i M_{\\alpha, i} c^{\\dagger}_i.\n", "$$\n", "\n", "The rectangular ($N\\times N_{\\mathrm{f}}$) matrix $M$ constitutes a set of free variational parameters (Where $N$ is the number of orbitals and $N_{\\mathrm{f}}$ the number of fermions).\n", "\n", "It can be shown that the amplitudes of the wave function in the computational basis $|n_1,\\dots,n_N\\rangle$ are determinants:\n", "\n", "$$\n", "\\langle n_1,\\dots,n_N |\\Phi_s\\rangle = \\mathrm{det}\\left\\{A(\\bf{n})\\right\\},\n", "$$\n", "\n", "where the $N_{\\mathrm{f}}\\times N_{\\mathrm{f}}$ matrix is\n", "\n", "$$\n", "A(n)_{\\alpha,\\beta} = M_{R_{\\alpha}(\\bf{n}), \\beta},\n", "$$\n", "\n", "where $R_{\\alpha}(\\bf{n})$ denotes the index of the $\\alpha$-th occupied site (non-zero $n_i$) in $\\bf{n}=(n_1,\\dots,n_N)$.\n", "This can also be interepted as selecting $N_\\mathrm{f}$ rows of the matrix $M$ in corresponding to the $N_\\mathrm{f}$ occupied orbitals.\n", "For more details see Chapter 5 of Reference [1].\n"]}, {"cell_type": "markdown", "id": "58d7d9b6-d9f9-4c14-ad87-a329f0ecde9c", "metadata": {}, "source": ["To write down this variational amplitudes, we start by defining a convenience function to compute the logarithm of the determinant of a matrix, in the complex domain, and using jax:"]}, {"cell_type": "code", "execution_count": 11, "id": "0fa75214-d754-4fe9-a49b-f9f6544853ce", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:15.855254Z", "iopub.status.busy": "2025-07-13T11:38:15.855100Z", "iopub.status.idle": "2025-07-13T11:38:15.857759Z", "shell.execute_reply": "2025-07-13T11:38:15.857434Z"}, "tags": []}, "outputs": [], "source": ["import jax\n", "import jax.numpy as jnp\n", "\n", "\n", "# Note: This function can also be found inside of netket, in `nk.jax.logdet_cmplx`, but we implement it here\n", "# for pedagogical purposes.\n", "def _logdet_cmplx(A):\n", "    sign, logabsdet = jnp.linalg.slogdet(A)\n", "    return logabsdet.astype(complex) + jnp.log(sign.astype(complex))"]}, {"cell_type": "markdown", "id": "09ace357-4b63-4cf7-8268-23484a4600e5", "metadata": {}, "source": ["Next, we define a wave function using Flax. As you might have seen also in other Tutorials, NetKet defines the logarithm of the wave function amplitudes, mostly to avoid overflow/underflow when computing relevant quantities. The model wave function is then:"]}, {"cell_type": "code", "execution_count": 12, "id": "8457a6c8-36da-4ce9-817b-a43e43ffc0e2", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:15.859235Z", "iopub.status.busy": "2025-07-13T11:38:15.859122Z", "iopub.status.idle": "2025-07-13T11:38:15.862707Z", "shell.execute_reply": "2025-07-13T11:38:15.862262Z"}, "tags": []}, "outputs": [], "source": ["from flax import nnx\n", "from netket.nn.masked_linear import default_kernel_init\n", "from typing import Any\n", "from functools import partial\n", "\n", "DType = Any\n", "\n", "\n", "class LogSlaterDeterminant(nnx.Module):\n", "    hilbert: nk.hilbert.SpinOrbitalFermions\n", "\n", "    def __init__(\n", "        self,\n", "        hi<PERSON>,\n", "        kernel_init=default_kernel_init,\n", "        param_dtype=float,\n", "        *,\n", "        rngs: nnx.Rngs,\n", "    ):\n", "        self.hilbert = hilbert\n", "\n", "        # To generate random numbers we need to extract the key from the `rngs` object.\n", "        key = rngs.params()\n", "\n", "        # the N x Nf matrix of the orbitals\n", "        self.M = nnx.Param(\n", "            kernel_init(\n", "                key,\n", "                (\n", "                    self.hilbert.n_orbitals,\n", "                    self.hilbert.n_fermions,\n", "                ),\n", "                param_dtype,\n", "            )\n", "        )\n", "\n", "    def __call__(self, n: jax.Array) -> jax.Array:\n", "        # For simplicity, we write a function that operates on a single configuration of size (N,)\n", "        # and we vectorize it using `jnp.vectorize` with the signature='(n)->()' argument, which specifies\n", "        # that the function is defined to operate on arrays of shape (n,) and return scalars.\n", "        @partial(jnp.vectorize, signature=\"(n)->()\")\n", "        def log_sd(n):\n", "            # Find the positions of the occupied orbitals\n", "            R = n.nonzero(size=self.hilbert.n_fermions)[0]\n", "\n", "            # Extract from the (N, Nf) matrix the (Nf, Nf) submatrix of M corresponding to the occupied orbitals.\n", "            A = self.M[R]\n", "\n", "            return _logdet_cmplx(A)\n", "\n", "        return log_sd(n)"]}, {"cell_type": "markdown", "id": "7c64f92f-f773-44d5-8687-458df911471f", "metadata": {}, "source": ["This Flax module defines the variational parameters to be the rectangular matrix $M$. In general, these parameters can be real or complex valued. In the following we will work with real parameters, for simplicity."]}, {"cell_type": "markdown", "id": "9ea0f64e-2edc-4d14-94a9-529672d2a7ad", "metadata": {}, "source": ["## Optimizing the Slater Determinant Wave Function"]}, {"cell_type": "markdown", "id": "152dfafb-d5e8-49d6-a1b8-4efab32406e7", "metadata": {}, "source": ["We now create an instance of the `LogSlaterDeterminant` class and of a suitable Monte Carlo Sampler to obtain expected values using Variational Monte Carlo:"]}, {"cell_type": "code", "execution_count": 13, "id": "73cda74d-e4c7-44ff-bf0a-cce57c939cb3", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:15.864573Z", "iopub.status.busy": "2025-07-13T11:38:15.864423Z", "iopub.status.idle": "2025-07-13T11:38:16.291038Z", "shell.execute_reply": "2025-07-13T11:38:16.290673Z"}, "lines_to_next_cell": 2, "tags": []}, "outputs": [], "source": ["# Create the Slater determinant model, using the seed 0\n", "model = LogSlaterDeterminant(hi, rngs=nnx.Rngs(0))\n", "\n", "# Define the Metropolis-Hastings sampler\n", "sa = nk.sampler.MetropolisFermionHop(hi, graph=graph)"]}, {"cell_type": "markdown", "id": "1036b67a-3903-409b-a06f-62cce32e29e6", "metadata": {}, "source": ["Here we use a sampler that exchanges the occupation numbers of two sites. This allows to keep the total number of fermions constant."]}, {"cell_type": "markdown", "id": "15652348-90f0-4eb6-8847-73980c3e4c85", "metadata": {}, "source": ["We also define the `VariationalState` necessary to compute expected values over the variational state using <PERSON> sampling. We will use a total of 16 independent Markov Chains and $2^{12}$ samples per chain. We will also discard the first 16 samples of each chain, to allow thermalization:"]}, {"cell_type": "code", "execution_count": 14, "id": "52c43700-d123-4a9f-bb59-b58a19b0db79", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:16.293133Z", "iopub.status.busy": "2025-07-13T11:38:16.292795Z", "iopub.status.idle": "2025-07-13T11:38:16.553183Z", "shell.execute_reply": "2025-07-13T11:38:16.552849Z"}, "tags": []}, "outputs": [], "source": ["vstate = nk.vqs.MCState(sa, model, n_samples=2**12, n_discard_per_chain=16)"]}, {"cell_type": "markdown", "id": "c744aab5-e09d-45a1-bf7b-c150df700c21", "metadata": {}, "source": ["For example, we can generate samples distributed according to the square modulus of our variational state, and check its shape:"]}, {"cell_type": "code", "execution_count": 15, "id": "62904844-8216-4106-837e-c40f8edfb34a", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:16.554946Z", "iopub.status.busy": "2025-07-13T11:38:16.554817Z", "iopub.status.idle": "2025-07-13T11:38:17.552671Z", "shell.execute_reply": "2025-07-13T11:38:17.552245Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["(16, 64, 16)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["vstate.samples.shape"]}, {"cell_type": "markdown", "id": "79ca215f-813f-4535-a269-ac44da5b2d86", "metadata": {}, "source": ["You see here that the first index corresponds to the number of chain, the second to the samples on each chain, and the last one is the index of the occupation number, for example one configuration sampled looks like:"]}, {"cell_type": "code", "execution_count": 16, "id": "2cf29943-0244-47cc-8ac3-9f78096431c1", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:17.554323Z", "iopub.status.busy": "2025-07-13T11:38:17.554193Z", "iopub.status.idle": "2025-07-13T11:38:17.597968Z", "shell.execute_reply": "2025-07-13T11:38:17.597639Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["Array([1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1], dtype=int8)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["vstate.samples[0, 0]"]}, {"cell_type": "markdown", "id": "cbf552ad-c184-4480-a0ef-a9194f5201b2", "metadata": {}, "source": ["As you can see, everything is compatible with what you specified in the Hilbert space, namely there are exactly $N_{\\mathrm{f}}=5$ non-zero occupation numbers."]}, {"cell_type": "markdown", "id": "91bdeeab-cdce-44c4-8f2d-d3fdb1a15550", "metadata": {}, "source": ["Then, we can proceed and optimize for the ground state wave function, defining a suitable optimizer and, in this case, also a preconditioner based on the Quantum Natural Gradient (or Stochastic Reconfiguration):"]}, {"cell_type": "code", "execution_count": 17, "id": "5881547b-2ae4-47c9-b7f2-4c481c9f8370", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:17.599777Z", "iopub.status.busy": "2025-07-13T11:38:17.599639Z", "iopub.status.idle": "2025-07-13T11:38:17.601841Z", "shell.execute_reply": "2025-07-13T11:38:17.601585Z"}, "lines_to_next_cell": 2, "tags": []}, "outputs": [], "source": ["# Define the optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Define a preconditioner\n", "preconditioner = nk.optimizer.SR(diag_shift=0.05)\n", "\n", "# Create the VMC (Variational Monte Carlo) driver\n", "gs = nk.VMC(H, op, variational_state=vstate, preconditioner=preconditioner)"]}, {"cell_type": "markdown", "id": "58855e89-ef5c-4fb5-beb6-c4999cc4dced", "metadata": {}, "source": ["A more detailed explanation of `SR` and the regularization parameter (`diag_shift`) can be found in the Documentation."]}, {"cell_type": "markdown", "id": "a6e1dd08-99d6-4e9e-a9cd-b521e49dc76c", "metadata": {}, "source": ["We can now finally optimize the wave function for 300 steps of VMC:"]}, {"cell_type": "code", "execution_count": 18, "id": "3cd80857-ea67-4c27-8728-36838578c712", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:17.603195Z", "iopub.status.busy": "2025-07-13T11:38:17.603064Z", "iopub.status.idle": "2025-07-13T11:38:42.008962Z", "shell.execute_reply": "2025-07-13T11:38:42.008574Z"}, "tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5d0f6162df245188500e74e9f79eebe", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(RuntimeLog():\n", "  keys = ['acceptance', 'Energy'],)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Construct the logger to visualize the data later on\n", "slater_log = nk.logging.RuntimeLog()\n", "\n", "# Run the optimization for 300 iterations\n", "gs.run(n_iter=300, out=slater_log)"]}, {"cell_type": "markdown", "id": "02387eca-c8e8-476f-8976-da00d5b14e0e", "metadata": {}, "source": ["After optimizating the wave function, we can evaluate the energy on the final set of paramaters and compare to the exact value:"]}, {"cell_type": "code", "execution_count": 19, "id": "10ffa5b1-06ed-4301-9588-fed354e2014b", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:42.010748Z", "iopub.status.busy": "2025-07-13T11:38:42.010645Z", "iopub.status.idle": "2025-07-13T11:38:42.356051Z", "shell.execute_reply": "2025-07-13T11:38:42.355610Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized energy : -5.118-0.000j ± 0.073 [σ²=5.148, R̂=1.0190]\n", "Relative error   : 0.2538024650300583\n"]}], "source": ["sd_energy = vstate.expect(H)\n", "error = abs((sd_energy.mean - E_gs) / E_gs)\n", "\n", "print(f\"Optimized energy : {sd_energy}\")\n", "print(f\"Relative error   : {error}\")"]}, {"cell_type": "markdown", "id": "a7ffbf16-aff7-4753-a1a7-168433e5a60a", "metadata": {}, "source": ["As you can see, the mean field energy of the Slater Determinant is about 25% off in this case where interactions are strong, thus far from the single-particle regime in which the Slater Determinant is accurate."]}, {"cell_type": "markdown", "id": "f2560270-d82d-425b-bb03-6c27cb219167", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>-Slater Wave Function\n", "\n", "To improve on the simple mean field wave function considered above, we can construct a variational state that is the product of a Slater determinant times a many-body neural Jastrow factor :\n", "\n", "$$\n", "\\langle n_1,\\dots,n_N |\\Phi_s\\rangle \\times \\exp(J(\\bf{n})),\n", "$$\n", "\n", "where $J(\\bf{n})$ is parameterized via a suitable neural network taking as inputs the occupation numbers $\\bf{n}=(n_1,\\dots,n_N)$ and returning a scalar. See also References [2] and [3], for example choices of the neural network part.\n", "\n", "In the following, we will parameterize the neural Jastrow with a simple two-layer feedforward network consisting of real parameters only. This means that the neural Jastrow will not be able to change the overall sign of the wave function, but it will nonetheless be able to capture strong correlations by modifying the absolute value of the wave function amplitudes.\n", "\n", "We define the corresponding variational wave function in the following Flax class:"]}, {"cell_type": "code", "execution_count": 20, "id": "d912d6f1-c07e-4964-ac85-9c41f35b6e63", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:42.357666Z", "iopub.status.busy": "2025-07-13T11:38:42.357536Z", "iopub.status.idle": "2025-07-13T11:38:42.361484Z", "shell.execute_reply": "2025-07-13T11:38:42.361211Z"}, "tags": []}, "outputs": [], "source": ["from flax import nnx\n", "from netket.nn.masked_linear import default_kernel_init\n", "from typing import Any\n", "\n", "DType = Any\n", "\n", "\n", "class LogNeuralJastrowSlater(nnx.Module):\n", "    hilbert: nk.hilbert.SpinOrbitalFermions\n", "\n", "    def __init__(\n", "        self,\n", "        hi<PERSON>,\n", "        jastrow_hidden_units: int,\n", "        kernel_init=default_kernel_init,\n", "        param_dtype=float,\n", "        *,\n", "        rngs: nnx.Rngs,\n", "    ):\n", "        self.hilbert = hilbert\n", "\n", "        # To generate random numbers we need to extract the key from the `rngs` object.\n", "        key = rngs.params()\n", "\n", "        # the N x Nf matrix of the orbitals\n", "        self.M = nnx.Param(\n", "            kernel_init(\n", "                key,\n", "                (\n", "                    self.hilbert.n_orbitals,\n", "                    self.hilbert.n_fermions,\n", "                ),\n", "                param_dtype,\n", "            )\n", "        )\n", "\n", "        # The Neural Jastrow wf\n", "        self.jastrow_linear = nnx.Linear(\n", "            in_features=hilbert.n_orbitals,\n", "            out_features=jastrow_hidden_units,\n", "            param_dtype=param_dtype,\n", "            rngs=rngs,\n", "        )\n", "\n", "    def __call__(self, n: jax.Array) -> jax.Array:\n", "        # For simplicity, we write a function that operates on a single configuration of size (N,)\n", "        # and we vectorize it using `jnp.vectorize` with the signature='(n)->()' argument, which specifies\n", "        # that the function is defined to operate on arrays of shape (n,) and return scalars.\n", "        @partial(jnp.vectorize, signature=\"(n)->()\")\n", "        def log_sd(n):\n", "            # Construct the Neural Jastrow\n", "            J = self.jastrow_linear(n)\n", "            J = jax.nn.tanh(J)\n", "            J = J.sum()\n", "\n", "            # Construct the slater determinant\n", "            R = n.nonzero(size=self.hilbert.n_fermions)[0]\n", "            # Extract from the (N, Nf) matrix the (Nf, Nf) submatrix of M corresponding to the occupied orbitals.\n", "            A = self.M[R]\n", "            log_slater = _logdet_cmplx(A)\n", "\n", "            # Multiply log slater with the jastrow (and since we are working in log space, this becomes a sum)\n", "            return log_slater + J\n", "\n", "        return log_sd(n)"]}, {"cell_type": "markdown", "id": "946444ff-a310-4416-a333-e17412a8537e", "metadata": {}, "source": ["We then optimize this variational state, as done before."]}, {"cell_type": "code", "execution_count": 21, "id": "660eee6c-8830-48a9-84d7-89b5a16908a2", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:38:42.362879Z", "iopub.status.busy": "2025-07-13T11:38:42.362794Z", "iopub.status.idle": "2025-07-13T11:39:12.170745Z", "shell.execute_reply": "2025-07-13T11:39:12.170468Z"}, "tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "44c3e2dd053e45a99e10ce9dce36286d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(RuntimeLog():\n", "  keys = ['acceptance', 'Energy'],)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a Neural Jastrow Slater wave function\n", "model = LogNeuralJastrowSlater(hi, jastrow_hidden_units=N, rngs=nnx.Rngs(2))\n", "\n", "# Define a Metropolis exchange sampler\n", "sa = nk.sampler.MetropolisFermionHop(hi, graph=graph)\n", "\n", "# Define an optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Create a variational state\n", "vstate = nk.vqs.MCState(sa, model, n_samples=2**12, n_discard_per_chain=16)\n", "\n", "# Create a Variational Monte Carlo driver\n", "preconditioner = nk.optimizer.SR(diag_shift=0.05)\n", "gs = nk.VMC(H, op, variational_state=vstate, preconditioner=preconditioner)\n", "\n", "# Construct the logger to visualize the data later on\n", "nj_log = nk.logging.RuntimeLog()\n", "\n", "# Run the optimization for 300 iterations\n", "gs.run(n_iter=300, out=nj_log)"]}, {"cell_type": "markdown", "id": "96fd4c14-86ab-44ca-9a58-deef259b09c0", "metadata": {}, "source": ["As you can see, this significantly improves over the mean field wave function. If we compute the error on the ground state energy we get:"]}, {"cell_type": "code", "execution_count": 22, "id": "735a5e95-2d4d-46ab-9844-7607d2320e79", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:12.173281Z", "iopub.status.busy": "2025-07-13T11:39:12.173149Z", "iopub.status.idle": "2025-07-13T11:39:12.482869Z", "shell.execute_reply": "2025-07-13T11:39:12.482565Z"}, "lines_to_next_cell": 2, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized energy : -6.788+0.000j ± 0.020 [σ²=0.409, R̂=1.0080]\n", "Relative error   : 0.010378533346465605\n"]}], "source": ["sd_energy = vstate.expect(H)\n", "error = abs((sd_energy.mean - E_gs) / E_gs)\n", "\n", "print(f\"Optimized energy : {sd_energy}\")\n", "print(f\"Relative error   : {error}\")"]}, {"cell_type": "markdown", "id": "701762d2-3cf3-4070-bec1-90ba24bd4274", "metadata": {"lines_to_next_cell": 2}, "source": ["## Neural-Backflow Wave Function\n", "\n", "An alternative approach to go beyond the simple mean field approximation, and also effectively change the nodal structure of the wave functions is given by the Neural Backflow transformation of Reference [4].\n", "The idea is to promote the matrix $M$ appearing in the Slater determinant to be a function of all the occupation numbers, through a neural network. Specifically, we take an additive form of the backflow transformation:\n", "\n", "$$\n", "M^{\\mathrm{bf}}_{\\alpha,i}(\\bf{n}) = M_{\\alpha, i} + F_{\\alpha,i}(\\bf{n})\n", "$$\n", "\n", "and parameterize $F$ with a multilayer perceptron taking $N$ inputs and having an output of $N\\times N_{\\mathrm{f}}$ numbers."]}, {"cell_type": "code", "execution_count": 23, "id": "4bd721a6", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:12.484428Z", "iopub.status.busy": "2025-07-13T11:39:12.484301Z", "iopub.status.idle": "2025-07-13T11:39:12.487908Z", "shell.execute_reply": "2025-07-13T11:39:12.487629Z"}}, "outputs": [], "source": ["class LogNeuralBackflow(nnx.Module):\n", "    hilbert: nk.hilbert.SpinOrbitalFermions\n", "\n", "    def __init__(\n", "        self,\n", "        hi<PERSON>,\n", "        hidden_units: int,\n", "        kernel_init=default_kernel_init,\n", "        param_dtype=float,\n", "        *,\n", "        rngs: nnx.Rngs,\n", "    ):\n", "        self.hilbert = hilbert\n", "\n", "        # To generate random numbers we need to extract the key from the `rngs` object.\n", "        key = rngs.params()\n", "\n", "        # the N x Nf matrix of the orbitals\n", "        self.M = nnx.Param(\n", "            kernel_init(\n", "                key,\n", "                (\n", "                    self.hilbert.n_orbitals,\n", "                    self.hilbert.n_fermions,\n", "                ),\n", "                param_dtype,\n", "            )\n", "        )\n", "\n", "        # Construct the Backflow. Takes as input strings of $N$ occupation numbers, outputs an $N x Nf$ matrix\n", "        # that modifies the bare orbitals.\n", "        self.backflow = nnx.Sequential(\n", "            # First layer, input (..., N,) output (..., hidden_units)\n", "            nnx.Linear(\n", "                in_features=hilbert.size,\n", "                out_features=hidden_units,\n", "                param_dtype=param_dtype,\n", "                rngs=rngs,\n", "            ),\n", "            nnx.tanh,\n", "            # Last layer, input (..., hidden_units,) output (..., N x Nf)\n", "            nnx.Linear(\n", "                in_features=hidden_units,\n", "                out_features=hilbert.n_orbitals * hilbert.n_fermions,\n", "                param_dtype=param_dtype,\n", "                rngs=rngs,\n", "            ),\n", "            # Reshape into the orbital shape, (..., N, Nf)\n", "            lambda x: x.reshape(\n", "                x.shape[:-1] + (hilbert.n_orbitals, hilbert.n_fermions)\n", "            ),\n", "        )\n", "\n", "    def __call__(self, n: jax.Array) -> jax.Array:\n", "        # For simplicity, we write a function that operates on a single configuration of size (N,)\n", "        # and we vectorize it using `jnp.vectorize` with the signature='(n)->()' argument, which specifies\n", "        # that the function is defined to operate on arrays of shape (n,) and return scalars.\n", "        @partial(jnp.vectorize, signature=\"(n)->()\")\n", "        def log_sd(n):\n", "            # Construct the Backflow. Takes as input strings of $N$ occupation numbers, outputs an $N x Nf$ matrix\n", "            # that modifies the bare orbitals.\n", "            F = self.backflow(n)\n", "            # Add the backflow correction to the bare orbitals\n", "            M = self.M + F\n", "\n", "            # Find the positions of the occupied, backflow-modified orbitals\n", "            R = n.nonzero(size=self.hilbert.n_fermions)[0]\n", "            A = M[R]\n", "            return _logdet_cmplx(A)\n", "\n", "        return log_sd(n)"]}, {"cell_type": "markdown", "id": "5b7ecd79-dbb5-46ec-8a7e-1c45f838000b", "metadata": {}, "source": ["We can then proceed as above to optimize this variational state using VMC."]}, {"cell_type": "code", "execution_count": 24, "id": "390637bf-2eda-4d2a-bc46-a210ed5b7a90", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:12.489226Z", "iopub.status.busy": "2025-07-13T11:39:12.489138Z", "iopub.status.idle": "2025-07-13T11:39:45.565681Z", "shell.execute_reply": "2025-07-13T11:39:45.565381Z"}, "tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "37d1ff30c5ce485185bbc27a0733dbb0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                                         …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(RuntimeLog():\n", "  keys = ['acceptance', 'Energy'],)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a neural backflow wave function\n", "model = LogNeuralBackflow(hi, hidden_units=N, rngs=nnx.Rngs(3))\n", "\n", "# Define a Metropolis exchange sampler\n", "sa = nk.sampler.MetropolisFermionHop(hi, graph=graph)\n", "\n", "# Define an optimizer\n", "op = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# Create a variational state\n", "vstate = nk.vqs.MCState(sa, model, n_samples=2**12, n_discard_per_chain=16)\n", "\n", "# Create a Variational Monte Carlo driver\n", "preconditioner = nk.optimizer.SR(diag_shift=0.05)\n", "gs = nk.VMC(H, op, variational_state=vstate, preconditioner=preconditioner)\n", "\n", "# Construct the logger to visualize the data later on\n", "bf_log = nk.logging.RuntimeLog()\n", "\n", "# Run the optimization for 300 iterations\n", "gs.run(n_iter=300, out=bf_log)"]}, {"cell_type": "markdown", "id": "2cae5aca-8d47-4748-96b8-71d7dd376019", "metadata": {}, "source": ["We can further check how good the optimized energy is:"]}, {"cell_type": "code", "execution_count": 25, "id": "1155dc2b-4d5b-41ca-80dc-a25b9a49cc2a", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:45.567502Z", "iopub.status.busy": "2025-07-13T11:39:45.567375Z", "iopub.status.idle": "2025-07-13T11:39:45.890435Z", "shell.execute_reply": "2025-07-13T11:39:45.890082Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized energy : -6.784+0.000j ± 0.023 [σ²=0.588, R̂=1.0109]\n", "Relative error   : 0.010985338151858374\n"]}], "source": ["sd_energy = vstate.expect(H)\n", "error = abs((sd_energy.mean - E_gs) / E_gs)\n", "\n", "print(f\"Optimized energy : {sd_energy}\")\n", "print(f\"Relative error   : {error}\")"]}, {"cell_type": "markdown", "id": "9b8dd5a7-5546-4ec0-9cb1-01acee8e0abc", "metadata": {}, "source": ["Thus, as expected, the Neural Backflow achieves a significantly higher level of precision (~0.5%) versus the ~25% error of the mean field state. Notice that in this case the precision achieved is similar to the simpler Neural Jastrow-Slater wave function. However, the backflow result can be further improved by playing with the feedforward architecture defining the backflow, for example by increasing 'hidden_units' or by improving the optimization increasing the number of samples and/or the number of steps."]}, {"cell_type": "markdown", "id": "92dfc06c-e7af-4056-9451-c31574b497ff", "metadata": {}, "source": ["## Plotting all together\n", "\n", "Finally, we can visualize the results obtained with the three wave functions we have covered in this Tutorial, by using the data logged during the optimization. In order to visualize what happened during the optimization, we can use the data that has been stored by the logger. There are several available loggers in NetKet, in the previous runs we have just used a simple one that stores the intermediate results as values in a dictionary."]}, {"cell_type": "code", "execution_count": 26, "id": "9fc7b627-ab04-466a-b158-75cebf5efbfb", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:45.892362Z", "iopub.status.busy": "2025-07-13T11:39:45.892217Z", "iopub.status.idle": "2025-07-13T11:39:45.894573Z", "shell.execute_reply": "2025-07-13T11:39:45.894289Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HistoryDict with 2 elements:\n", "\t'Energy' -> History(keys=['Mean', 'Varian<PERSON>', '<PERSON>', 'R_hat', '<PERSON><PERSON>orr'], n_iters=300)\n", "\t'acceptance' -> History(keys=['value'], n_iters=300)\n"]}], "source": ["data_slater = slater_log.data\n", "print(data_slater)"]}, {"cell_type": "markdown", "id": "2413cd15-58a0-42b6-9a5e-7b39773fbd52", "metadata": {}, "source": ["These report several intermediate quantities, that can be easily plotted. For example we can plot the value of the energy (with its error bar) at each optimization step."]}, {"cell_type": "code", "execution_count": 27, "id": "2b9f6192-6d26-464c-ba6b-6a38dc523c52", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T11:39:45.896276Z", "iopub.status.busy": "2025-07-13T11:39:45.896117Z", "iopub.status.idle": "2025-07-13T11:39:46.025227Z", "shell.execute_reply": "2025-07-13T11:39:46.024922Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x12e984ef0>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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**********************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["logged_data = (slater_log.data, nj_log.data, bf_log.data)\n", "labels = (\"<PERSON>\", \"Neural Jastrow\", \"Neural Backflow\")\n", "\n", "from matplotlib import pyplot as plt\n", "\n", "for data, label in zip(logged_data, labels):\n", "    plt.errorbar(\n", "        data[\"Energy\"].iters,\n", "        data[\"Energy\"].Mean.real,\n", "        yerr=data[\"Energy\"].Sigma,\n", "        label=label,\n", "    )\n", "\n", "plt.hlines([E_gs], xmin=0, xmax=300, color=\"black\", label=\"Exact\")\n", "\n", "plt.xlabel(\"Iterations\")\n", "plt.ylabel(\"Energy\")\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "16c1b832-91f7-4dee-8701-fc9a9eef220c", "metadata": {}, "source": ["## References\n", "\n", "[1] <PERSON>, <PERSON><PERSON>, S. Quantum Monte Carlo Approaches for Correlated Systems. (Cambridge University Press, 2017).\n", "\n", "[2] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>, <PERSON><PERSON>mann machine learning for solving strongly correlated quantum systems. Phys. Rev. B 96, 205152 (2017).\n", "\n", "[3] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON>, G. Phases of two-dimensional spinless lattice fermions with first-quantized deep neural-network quantum states. Phys. Rev. B 102, 205122 (2020).\n", "\n", "[4] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>. Backflow Transformations via Neural Networks for Quantum Many-Body Wave Functions. Phys. Rev. Lett. 122, 226401 (2019).\n", "\n"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "netket_pro", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"00cd2d99224544559a283c3865fd39fd": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "19dc8de7d2474836884252846829b7f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1c65af6bc2544b5caebb802fae2590de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b23ef32409a541228800381154bb7b0f", "max": 300.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_4cbe64be9e30438f92dc9dfbb8db7a74", "tabbable": null, "tooltip": null, "value": 300.0}}, "23738aedc2b242e6b69e4ffd1472deec": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "23c27e0954174c688ae378f4b61b908d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "23ec37f614c448e9ada0c695b9f6a8c0": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dde4414c46e4071983ee839106fd5bd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ea292926b5904ae2a68f51b4d3a158d5", "placeholder": "​", "style": "IPY_MODEL_92e81d716ed94ba9a354d6bc1517a393", "tabbable": null, "tooltip": null, "value": " 300/300 [00:23&lt;00:00, 13.12it/s, Energy=-5.048+0.000j ± 0.079 [σ²=5.827, R̂=1.0062]]"}}, "37d1ff30c5ce485185bbc27a0733dbb0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5c284e5b073043348461c3003af21ec9", "IPY_MODEL_1c65af6bc2544b5caebb802fae2590de", "IPY_MODEL_84d9c299bd574923b081619bf440a364"], "layout": "IPY_MODEL_23738aedc2b242e6b69e4ffd1472deec", "tabbable": null, "tooltip": null}}, "3ec523175c3e43bbb4ece58cfc05036f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "44c3e2dd053e45a99e10ce9dce36286d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8bc722c6f72d41c3bcda06dffaf8db4a", "IPY_MODEL_fcd590d885b04458af5ea4d38098214c", "IPY_MODEL_746b72a060fc487d8b79b2cd0585d8da"], "layout": "IPY_MODEL_3ec523175c3e43bbb4ece58cfc05036f", "tabbable": null, "tooltip": null}}, "4cbe64be9e30438f92dc9dfbb8db7a74": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5b5a0d702f7f4309b430d448fa0e3f76": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_8769dab0ba10426fad4e3ed4bac6e68e", "placeholder": "​", "style": "IPY_MODEL_81e08c91160f49fc9cf74a1158186c34", "tabbable": null, "tooltip": null, "value": "100%"}}, "5c284e5b073043348461c3003af21ec9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_bdb1d2fde63b41e79990016c29bb0ee5", "placeholder": "​", "style": "IPY_MODEL_cf93d03022fe44a6ae9d8d8000698c08", "tabbable": null, "tooltip": null, "value": "100%"}}, "746b72a060fc487d8b79b2cd0585d8da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_23c27e0954174c688ae378f4b61b908d", "placeholder": "​", "style": "IPY_MODEL_793648e1f50949598b600e78b9328704", "tabbable": null, "tooltip": null, "value": " 300/300 [00:27&lt;00:00, 10.71it/s, Energy=-6.808-0.000j ± 0.025 [σ²=0.614, R̂=1.0096]]"}}, "78814a4400354f6789cf457b54a30382": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ed66ef4c086b4c63a3c95fcfa03931fc", "max": 300.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_19dc8de7d2474836884252846829b7f5", "tabbable": null, "tooltip": null, "value": 300.0}}, "793648e1f50949598b600e78b9328704": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "81e08c91160f49fc9cf74a1158186c34": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "84d9c299bd574923b081619bf440a364": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_9626f0e3e47b452288a6cfd968830d63", "placeholder": "​", "style": "IPY_MODEL_c68f78f4ec334e368453c07212c14f97", "tabbable": null, "tooltip": null, "value": " 300/300 [00:31&lt;00:00,  9.83it/s, Energy=-6.808-0.000j ± 0.023 [σ²=0.490, R̂=1.0063]]"}}, "8769dab0ba10426fad4e3ed4bac6e68e": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8bc722c6f72d41c3bcda06dffaf8db4a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_23ec37f614c448e9ada0c695b9f6a8c0", "placeholder": "​", "style": "IPY_MODEL_a03412f72d3544d4adcaa3281ef725d8", "tabbable": null, "tooltip": null, "value": "100%"}}, "92e81d716ed94ba9a354d6bc1517a393": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "9626f0e3e47b452288a6cfd968830d63": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a03412f72d3544d4adcaa3281ef725d8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "a625e361b0c2469ea37a0085ecd25bf0": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b23ef32409a541228800381154bb7b0f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5d0f6162df245188500e74e9f79eebe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5b5a0d702f7f4309b430d448fa0e3f76", "IPY_MODEL_78814a4400354f6789cf457b54a30382", "IPY_MODEL_2dde4414c46e4071983ee839106fd5bd"], "layout": "IPY_MODEL_00cd2d99224544559a283c3865fd39fd", "tabbable": null, "tooltip": null}}, "bdb1d2fde63b41e79990016c29bb0ee5": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c68f78f4ec334e368453c07212c14f97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "cf93d03022fe44a6ae9d8d8000698c08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "ea292926b5904ae2a68f51b4d3a158d5": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed66ef4c086b4c63a3c95fcfa03931fc": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fcd590d885b04458af5ea4d38098214c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a625e361b0c2469ea37a0085ecd25bf0", "max": 300.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_ff54c0ce6f574b509ced4a44bf26befe", "tabbable": null, "tooltip": null, "value": 300.0}}, "ff54c0ce6f574b509ced4a44bf26befe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}