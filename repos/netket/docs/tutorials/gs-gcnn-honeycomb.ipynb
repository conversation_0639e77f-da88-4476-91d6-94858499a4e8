{"cells": [{"cell_type": "markdown", "id": "74190a36", "metadata": {"id": "74190a36"}, "source": ["# Symmetries: Honeycomb Heisenberg model\n", "\n", "The goal of this tutorial is to learn about group convolutional neural networks (G-CNNs), a useful tool for simulating lattices with high symmetry.\n", "The G-CNN is a generalization to the convolutional neural network (CNN) to non-abelian symmetry groups (groups that contain at least one pair of non-commuting elements).\n", "G-CNNs are a natural fit for lattices that have both point group and translational symmetries, as rotations, reflections and translations don't commute with one-another.\n", "G-CNN can be used to study both the ground-state and excited-states.\n", "\n", "In this tutorial we will learn the ground state of the antiferromagnetic Heisenberg model on  the honeycomb lattice. The Heisenberg Hamiltonian is defined as follows:\n", "\n", "$$ H = \\sum_{i,j \\in \\langle \\rangle} \\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{j},$$\n", "\n", "where $\\vec{\\sigma}_{i}$ are Pauli matrices and $<>$ denotes nearest neighbor interactions.\n", "\n", "For this tutorial, many of the calculations will be much faster on a GPU.\n", "If you don't have access to a GPU, you can open a [Google Colab](https://colab.research.google.com/) notebook, and set runtime type to GPU.\n", "To launch this notebook on Colab simply press the rocket button on the top bar.\n", "\n", "This tutorial wil be split into two parts:\n", " - First I'll provide a brief introduction to G-CNNs and describe what advantages they bring.\n", " - Second, we'll use NetKet to find the ground state of the antiferromagnetic Heisenberg model on the honeycomb lattice. First we will simulate a lattice with $N=18$ sites in order to compare with exact diagonalization. Then we will simulate a lattice with $N=72$ sites."]}, {"cell_type": "markdown", "id": "acf6606d", "metadata": {"id": "acf6606d"}, "source": ["## G-CNNs are generalizations of CNNs to non-abelian groups\n", "\n", "The convolutional neural network (CNN) has revolutionized the field of computer vision. The CNN enforces translational invariance, which means that feeding a CNN translated copies of an image will produce the exact same output. This is important for recognizing objects, which may located differently in different images.\n", "\n", "The  hidden layers of a CNN contain a group of ${\\bf features}$, corresponding to translations of the image, where each feature is represented by a vector. At each layer, the CNN integrates over these features to produce a different set of features over the translation group:\n", "\n", "$$ C^i_{x,y} = \\sum_h {\\bf W}_{x'-x, y'-y} \\cdot {\\bf f}_{x,y} $$\n", "\n", "As you can see, the index of the filter W is based on the displacement between the input feature {x',y'} and the output feature {x, y}. This is known as an equivariant operation, as displacements in the input are propagated as displacements in the output (equivariance is actually bit more general, we'll get to that in a moment). In the last layer, the CNN averages over these different features, forcing the output to be invariant to the input.\n", "\n", "To generalize the CNN to the G-CNN, lets abstract away from the specifics of the convolution. Instead of indexing the features with translations, we will use elements from a general symmetry group which may contain non-commuting operations. In this case we must define a particular order of operations. For example, we could define an operation in the $p6m$ space group, as a translation, followed by a rotation and a reflection about the origin. Non-abelian groups still maintain associativity and a closed algebra. This is easy to see with lattice symmetry groups. If two successive symmetry operations leave the lattice unchanged, applying both must also leave the lattice unchanged and therefore be symmetry operation in the group.\n", "\n", "For G-convolutions, the building blocks of the G-CNN, this algebra is all we need. The G-convolution also indexes the filters by looking at the \"difference\" between group elements, however this time there is an orientation to it. The G-convolution is defined as follows:\n", "\n", "$$ C^i_g = \\sum_h {\\bf W}_{g^{-1} h} \\cdot {\\bf f}_h $$\n", "\n", "The filters are indexed by $g^{-1} h$, which describes the mapping from $g \\rightarrow h$ but not vice-versa. This causes the output to be an ${\\bf involution}$ of the input, meaning that the group elements are mapped to their respective inverses.\n", "\n", "G-convolutions are the most expressive linear transformation over a particular symmetry group. Therefore, if you want to define a linear-based model with a particular symmetry, G-CNNs maximize the number of parameters you can fit into a given memory profile. G-CNNs can be mapped down to other symmetry-averaged multi-layer linear models by masking filters (setting them to zero). On the Honeycomb lattice, the G-CNN (approximately) has a factor of 12 more parameters than a CNN averaged over $d_6$ and a factor of $12 N$ more parameters than a feedforward neural network averaged over $p6m$ (where N is the number of sites) under an identical memory constraint.\n", "\n", "If you'd like to learn more about G-CNNs, check out the [original paper](http://proceedings.mlr.press/v48/cohenc16.pdf) by Cohen ${\\it et \\ al.}$ or [this paper](https://arxiv.org/pdf/2104.05085.pdf) by Roth ${\\it et \\ al.}$ that applies G-CNNs to quantum many-body systems."]}, {"cell_type": "code", "execution_count": null, "id": "33bf8c3c-998a-4414-83b0-ef4d3d397ca4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWARNING: You are using pip version 22.0.2; however, version 22.0.3 is available.\n", "You should consider upgrading via the '/home/<USER>/Documents/pythonenvs/utils-jupuyterlab/bin/python -m pip install --upgrade pip' command.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --quiet netket"]}, {"cell_type": "code", "execution_count": null, "id": "4291e426", "metadata": {"id": "4291e426"}, "outputs": [], "source": ["import netket as nk\n", "\n", "# Import Json, this will be needed to examine log files\n", "import json\n", "\n", "# Helper libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "f31538da", "metadata": {"id": "f31538da"}, "source": ["## Defining the Hamiltonian\n", "\n", "We begin by defining the Hamiltonian as a list of lattice points. NetKet will automatically convert these points into a graph with nearest neighbor connections. The honeycomb lattice is a triangular lattice with two sites per unit cell."]}, {"cell_type": "code", "execution_count": null, "id": "863df491", "metadata": {"id": "863df491"}, "outputs": [], "source": ["# Basis Vectors that define the positioning of the unit cell\n", "basis_vectors = [[0, 1], [np.sqrt(3) / 2, -1 / 2]]\n", "\n", "# Locations of atoms within the unit cell\n", "atom_positions = [[0, 0], [np.sqrt(3) / 6, 1 / 2]]\n", "\n", "# Number of unit cells in each direction\n", "dimensions = [3, 3]\n", "\n", "# Define the graph\n", "graph = nk.graph.<PERSON><PERSON>ce(\n", "    basis_vectors=basis_vectors, atoms_coord=atom_positions, extent=dimensions\n", ")"]}, {"cell_type": "markdown", "id": "07c7ee5f", "metadata": {"id": "07c7ee5f"}, "source": ["Lets check to see if our graph looks as expected. Since we have two sites per unit cell, we should have $3 \\times 3 \\times 2 = 18$ sites. The coordination number of a hexagonal lattice is 3, so we should have $\\frac{18 \\times 3}{2} = 27$ edges. Finally we have p6m symmetry, which should give ue $3 \\times 3 \\times 12 = 108$ symmetry operations."]}, {"cell_type": "code", "execution_count": null, "id": "53035660", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "53035660", "outputId": "7ab92adc-1828-421e-95a9-bffcca4f131c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18\n", "27\n", "216\n"]}], "source": ["# Use Netket to find symmetries of the graph\n", "symmetries = graph.automorphisms()\n", "\n", "# Check that graph info is correct\n", "print(graph.n_nodes)\n", "print(graph.n_edges)\n", "print(len(symmetries))"]}, {"cell_type": "markdown", "id": "6b381714", "metadata": {"id": "6b381714"}, "source": ["Oops! It looks like we have twice as many symmetries elements as we thought. Luckily for us, the ground state is still symmetric with respect to this extra symmetry that is unique to the $3 \\times 3$ lattice. We use this graph to define our Hilbert space and Hamiltonian:"]}, {"cell_type": "code", "execution_count": null, "id": "072f0470", "metadata": {"id": "072f0470"}, "outputs": [], "source": ["# Define the <PERSON>lbert space\n", "hi = nk.hilbert.Spin(s=1 / 2, N=graph.n_nodes, total_sz=0)\n", "\n", "# Define the Hamiltonian\n", "ha = nk.operator.Heisenberg(hilbert=hi, graph=graph, sign_rule=True)"]}, {"cell_type": "markdown", "id": "053d242b", "metadata": {"id": "053d242b"}, "source": ["Since the Hexagonal lattice is bipartite, we know the phases obey a <PERSON><PERSON><PERSON><PERSON> sign rule. Therefore, we can use a real valued NN and just learn the amplitudes of the wavefunction.\n", "\n", "For models with a more complicated phase structure, its often better to learn the phases in an equal-amplitude configuration before training the amplitudes as detailed in [this paper](https://journals.aps.org/prresearch/pdf/10.1103/PhysRevResearch.2.033075). This can be implemented by first optimizing the weights on a modified model that sets $Re[log(\\psi)] = 0$\n", "\n", "We also optimize over states with total $S_z$ of zero since we know the ground state has spin 0.\n", "\n", "## Defining the GCNN\n", "\n", "We can define a GCNN with an arbitrary number of layers and specify the feature dimension of each layer accordingly:"]}, {"cell_type": "code", "execution_count": null, "id": "0ff16a8f", "metadata": {"id": "0ff16a8f"}, "outputs": [], "source": ["# Feature dimensions of hidden layers, from first to last\n", "feature_dims = (8, 8, 8, 8)\n", "\n", "# Number of layers\n", "num_layers = 4\n", "\n", "# Define the GCNN\n", "ma = nk.models.GCNN(symmetries=symmetries, layers=num_layers, features=feature_dims)"]}, {"cell_type": "markdown", "id": "81d91df2", "metadata": {"id": "81d91df2"}, "source": ["This a G-CNN with four layers, where each hidden layer contains a feature vector of length 8 for each element in p6m. This means that each hidden state has $8 \\times 192 = 768$ nodes. This is a huge model! But since we're not symmetry-averaging, we only need to compute one wavefunction for each ${\\bf \\sigma}$.\n", "\n", "Feel free to try different shaped models. By default, the GCNN weights are initialized with variance scaling, which ensures that the activations will be unit-normal throughout the model at the start of training. Additionally, GCNN defaults to a SELU non-linearity, which moves the activations in the direction of unit-normal, even when they start to deviate. These features ensure that our model will behave well, even when we stack a large number of layers."]}, {"cell_type": "markdown", "id": "de96a27f", "metadata": {"id": "de96a27f"}, "source": ["## Variational Monte Carlo\n", "\n", "In order to perform VMC we need to define a sampler and an optimizer. We sample using Metropolis-Hastings, which uses the exchange rule to propose new states. The exchange rule swaps the spin of two neighbouring sites, keeping the magnetization fixed (in this case, 0). We optimize using stochastic reconfiguration, which uses curvature information to find the best direction of descent."]}, {"cell_type": "code", "execution_count": null, "id": "698d79a0", "metadata": {"id": "698d79a0"}, "outputs": [], "source": ["# Metropolis-Hastings with two spins flipped that are at most second nearest neighbors\n", "sa = nk.sampler.MetropolisExchange(hilbert=hi, graph=graph, d_max=2)\n", "\n", "# Stochastic reconfiguration\n", "op = nk.optimizer.Sgd(learning_rate=1e-2)\n", "sr = nk.optimizer.SR(diag_shift=0.01)\n", "\n", "# Define a variational state so we can keep the parameters if we like\n", "vstate = nk.variational.MCState(sampler=sa, model=ma, n_samples=100)\n", "\n", "# Define a driver that performs VMC\n", "gs = nk.driver.VMC(ha, op, sr=sr, variational_state=vstate)"]}, {"cell_type": "markdown", "id": "1043dc35", "metadata": {"id": "1043dc35"}, "source": ["Lets start by running for 100 iterations. This took about 15 seconds per iteration on my CPU and about 1 second per iteration on the Tesla P100 GPU (If you're using the free version of Colab you may get a Tesla K80 which is slightly slower). GPUs are fast! As you'll see later, the speedup is even more pronounced on larger lattices."]}, {"cell_type": "code", "execution_count": null, "id": "415609da", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "415609da", "outputId": "f9900b67-e565-4c80-bb06-ef786da62689"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [01:06<00:00,  1.51it/s, Energy=-40.471 ± 0.046 [σ²=0.238, R̂=0.9744]]\n"]}, {"data": {"text/plain": ["(<netket.logging.json_log.JsonLog at 0x7fea898371d0>,)"]}, "execution_count": 15, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["# Run the optimization\n", "gs.run(n_iter=100, out=\"out\")"]}, {"cell_type": "markdown", "id": "12c16d79", "metadata": {"id": "12c16d79"}, "source": ["This should get us under 0.1% error. Lets see how the energy evolves as we train."]}, {"cell_type": "code", "execution_count": null, "id": "c4145a4d", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 296}, "id": "c4145a4d", "outputId": "d8fbb71f-ec9f-4928-80d3-93c7a588415c"}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fe6a2426510>]"]}, "execution_count": 16, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["# Get data from log and\n", "energy = []\n", "data = json.load(open(\"out.log\"))\n", "for en in data[\"Energy\"][\"Mean\"]:\n", "    energy.append(en)\n", "\n", "# plot the energy during the optimization\n", "plt.xlabel(\"Number of Iterations\")\n", "plt.ylabel(\"Energy\")\n", "\n", "plt.plot(energy)"]}, {"cell_type": "markdown", "id": "4555e14d", "metadata": {"id": "4555e14d"}, "source": ["Looks like the first 40 iterations did most of the work! In order to get a more precise estimate, we can run 100 more iterations with a larger batch size. This will take about 15 minutes on the GPU (If you're using a CPU, I suggest you skip this section). We access the batch size via the variational state."]}, {"cell_type": "code", "execution_count": null, "id": "d0975d82", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d0975d82", "outputId": "597e9d9c-25de-4c27-dacd-df1b17522541"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [10:43<00:00,  6.43s/it, Energy=-40.3884 ± 0.0045 [σ²=0.0207, R̂=1.0033]]\n"]}, {"data": {"text/plain": ["(<netket.logging.json_log.JsonLog at 0x7fe6a31f2d90>,)"]}, "execution_count": 17, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["# Change batch size\n", "vstate.n_samples = 1000\n", "\n", "# Driver uses new batch size\n", "gs = nk.driver.VMC(ha, op, sr=sr, variational_state=vstate)\n", "\n", "# Run for 100 more iterations\n", "gs.run(n_iter=100, out=\"out\")"]}, {"cell_type": "markdown", "id": "2870a049", "metadata": {"id": "2870a049"}, "source": ["You will notice that the variance continues to get even smaller, giving evidence that we are nearing an eigenstate."]}, {"cell_type": "markdown", "id": "6a39b900", "metadata": {"id": "6a39b900"}, "source": ["## Checking with ED\n", "\n", "It seems likely that our ground state is correct, as we approached an eigenstate with low energy, but lets be safe and check our work. We can do Lanczos diagonalization for small lattices in NetKet."]}, {"cell_type": "code", "execution_count": null, "id": "bdae352f", "metadata": {"id": "bdae352f"}, "outputs": [], "source": ["# Exact Diagonalization\n", "E_gs = nk.exact.lanczos_ed(ha, compute_eigenvectors=False)"]}, {"cell_type": "markdown", "id": "305f6db1", "metadata": {"id": "305f6db1"}, "source": ["Lets compare the VMC energy with the ED energy, by taking average energy over the last $20$ iterations"]}, {"cell_type": "code", "execution_count": null, "id": "ae4f3061", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ae4f3061", "outputId": "6b991605-ba59-4a20-b7b1-c7694b57d0e9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-2.2437762156893766\n", "-2.243814630334411\n", "1.7120240021148795e-05\n"]}], "source": ["# Get data from larger batch size\n", "energy = []\n", "data = json.load(open(\"out.log\"))\n", "for en in data[\"Energy\"][\"Mean\"]:\n", "    energy.append(en)\n", "\n", "vmc_energy_18sites = np.mean(np.asarray(energy)[-20:]) / 18\n", "\n", "ED_energy_18sites = E_gs[0] / 18\n", "\n", "print(vmc_energy_18sites)\n", "print(ED_energy_18sites)\n", "print((ED_energy_18sites - vmc_energy_18sites) / ED_energy_18sites)"]}, {"cell_type": "markdown", "id": "0241c966", "metadata": {"id": "0241c966"}, "source": ["Looks like our model did a good job! If you just trained for the first 100 iterations the error should be less than $10^{-4}$ and if you trained with the larger batch size, the error should be close to $10^{-5}$"]}, {"cell_type": "markdown", "id": "ea3128c9", "metadata": {"id": "ea3128c9"}, "source": ["## Simulating A Larger <PERSON>\n", "\n", "Lets see how the GCNN does on a larger lattice that cannot be simulated with exact diagonalization. We'll do a $6 \\times 6$ lattice which has $72$ sites. We need to redefine a few things:"]}, {"cell_type": "code", "execution_count": null, "id": "466e14c7", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "466e14c7", "outputId": "85aee6d3-e9cd-4c93-9207-65717669400a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["432\n"]}], "source": ["# Redefine bigger graph\n", "dimensions = [6, 6]\n", "\n", "# Define the graph\n", "graph = nk.graph.<PERSON><PERSON>ce(\n", "    basis_vectors=basis_vectors, atoms_coord=atom_positions, extent=dimensions\n", ")\n", "# Redefine the Hilbert/Hamiltonian for larger lattice space\n", "hi = nk.hilbert.Spin(s=1 / 2, N=graph.n_nodes, total_sz=0)\n", "ha = nk.operator.Heisenberg(hilbert=hi, graph=graph, sign_rule=True)\n", "\n", "# Compute the symmetries for the bigger graph\n", "symmetries = graph.automorphisms()\n", "print(len(symmetries))\n", "\n", "# Redefine everything on bigger graph\n", "ma = nk.models.GCNN(symmetries=symmetries, layers=num_layers, features=feature_dims)\n", "sa = nk.sampler.MetropolisExchange(hilbert=hi, graph=graph, d_max=2)\n", "vstate = nk.variational.MCState(\n", "    sampler=sa, model=ma, n_samples=100, n_discard_per_chain=100\n", ")\n", "gs = nk.driver.VMC(ha, op, sr=sr, variational_state=vstate)"]}, {"cell_type": "markdown", "id": "4c736a85", "metadata": {"id": "4c736a85"}, "source": ["Looks like we have no extra symmetries this time, since $6 \\times 6 \\times 12 = 432$. Let's run this model for 100 iterations. You will see that we quickly get close  to the ground state. This takes 15 minutes on a P100 GPU"]}, {"cell_type": "code", "execution_count": null, "id": "O04c-p7Yv7Bx", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "O04c-p7Yv7Bx", "lines_to_next_cell": 2, "outputId": "18c8581f-602e-4140-f111-8023bd08998f"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [14:53<00:00,  8.93s/it, Energy=-155.43 ± 0.53 [σ²=31.79, R̂=0.9946]]\n"]}], "source": ["energy = []\n", "variance = []\n", "\n", "gs.run(n_iter=100, out=\"out\")\n", "\n", "data = json.load(open(\"out.log\"))\n", "\n", "for en in data[\"Energy\"][\"Mean\"]:\n", "    energy.append(en)\n", "for var in data[\"Energy\"][\"Variance\"]:\n", "    variance.append(var)"]}, {"cell_type": "markdown", "id": "6DfCNu30xMy-", "metadata": {"id": "6DfCNu30xMy-"}, "source": ["We can plot the energy and variance to see if we're approaching an eigenstate"]}, {"cell_type": "code", "execution_count": null, "id": "THwnPjzyw60t", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 297}, "id": "THwnPjzyw60t", "outputId": "05723ad2-50f9-47d5-b859-990ffb20c3ce"}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f20e9c2b190>]"]}, "execution_count": 12, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["plt.xlabel(\"Number of Iterations\")\n", "plt.ylabel(\"Energy\")\n", "plt.plot(energy)"]}, {"cell_type": "code", "execution_count": null, "id": "_mWuKCV3xkVv", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 297}, "id": "_mWuKCV3xkVv", "outputId": "73cd6d59-7565-4665-f270-7c3d413d9059"}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f20e9702e90>]"]}, "execution_count": 13, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["plt.xlabel(\"Number of Iterations\")\n", "plt.ylabel(\"Variance\")\n", "plt.plot(variance)"]}, {"cell_type": "markdown", "id": "k-bxulx_xjD8", "metadata": {"id": "k-bxulx_xjD8"}, "source": ["It seems we are near an eigenstate. We can do a back-of-the-envelope calculation to see if this is a realistic ground state energy\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ZICy6iG9yR7_", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZICy6iG9yR7_", "outputId": "db7f16a5-8dc5-4072-9bcf-8739cc2112f8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-2.243814630334411\n", "-2.158805247393738\n"]}], "source": ["print(ED_energy_18sites)\n", "print(energy[-1] / graph.n_nodes)"]}, {"cell_type": "markdown", "id": "TMOADxFoyi7Q", "metadata": {"id": "TMOADxFoyi7Q"}, "source": ["The energy for the bigger lattice is slightly less negative (as is typical for Heisenberg models with PBC) but they are pretty similar. It's clear we are approaching the ground state"]}, {"cell_type": "markdown", "id": "CLQ6AlwTvZJG", "metadata": {"id": "CLQ6AlwTvZJG"}, "source": ["We will benchmark how well our model is performing by tracking the relationship between the mean and variance of the energy over 400 more iterations. Then we will extrapolate this relationship to estimate the true ground state energy. This can tell us how our error evolves over time."]}, {"cell_type": "code", "execution_count": null, "id": "eeda94f6", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eeda94f6", "outputId": "966d7791-0b83-4d74-bd52-881cefc35416"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 20/20 [02:56<00:00,  8.81s/it, Energy=-157.22 ± 0.32 [σ²=11.34, R̂=0.9780]]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "-156.9362404126101\n", "10.740704288189896\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["intervals = 20\n", "\n", "en_estimates = []\n", "var_estimates = []\n", "\n", "for interval in range(intervals):\n", "    # run for 100 iterations\n", "    gs.run(n_iter=20, out=\"out\")\n", "\n", "    # load data from iterations\n", "    data = json.load(open(\"out.log\"))\n", "\n", "    # append energies and variances to data\n", "    for en in data[\"Energy\"][\"Mean\"]:\n", "        energy.append(en)\n", "    for var in data[\"Energy\"][\"Variance\"]:\n", "        variance.append(var)\n", "\n", "    en_est = np.mean(energy[-20:])\n", "    var_est = np.mean(variance[-20:])\n", "    en_estimates.append(en_est)\n", "    var_estimates.append(var_est)\n", "\n", "    print(\"\\n\")\n", "    print(en_est)\n", "    print(var_est)"]}, {"cell_type": "markdown", "id": "f59fbc87", "metadata": {"id": "f59fbc87"}, "source": ["Lets plot the energy vs. variance and draw a line of best fit"]}, {"cell_type": "code", "execution_count": null, "id": "56763669", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 297}, "id": "56763669", "outputId": "6e602837-1336-412e-9b4d-fcce368d28ec"}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f20e4704410>]"]}, "execution_count": 17, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["plt.xlabel(\"Variance\")\n", "plt.ylabel(\"Energy\")\n", "plt.scatter(var_estimates, en_estimates)\n", "fit = np.polyfit(var_estimates, en_estimates, 2)\n", "x = np.arange(100) * np.max(var_estimates) / 100\n", "plt.plot(x, fit[2] + fit[1] * x + fit[0] * np.square(x))"]}, {"cell_type": "markdown", "id": "6pUW039uYnhW", "metadata": {"id": "6pUW039uYnhW"}, "source": ["You can see that that the relationship between energy and variance is fairly well captured by a quadratic function. We can use this extrapolation to estimate the true ground state energy and the error in our wavefunction. Lets estimate the final energy of our Ansatz using our last 10,000 samples"]}, {"cell_type": "code", "execution_count": 20, "id": "1odflAb6ZWBH", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1odflAb6ZWBH", "outputId": "177c30e1-b048-492e-8bea-c54cc184b0aa"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0012942848437390558\n"]}], "source": ["final_en = np.mean(en_estimates[-5:])\n", "extrapolated_est = fit[2]\n", "\n", "print((extrapolated_est - final_en) / extrapolated_est)"]}, {"cell_type": "markdown", "id": "f479b107", "metadata": {"id": "f479b107"}, "source": ["If everything went well your error should be around .1%!\n", "\n", "This concludes the tutorial. Only an hour ago we knew nothing about the Heisenberg model on a Hexagonal lattice. Now we have an accurate approximation of the ground state wavefunction!"]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "name": "G-CNN Honeycomb.ipynb", "provenance": [], "toc_visible": true}, "jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}}, "nbformat": 4, "nbformat_minor": 5}