{"cells": [{"cell_type": "markdown", "metadata": {"id": "oNeFQPUVCAj8"}, "source": ["# Vision Transformer wave function\n", "\n", "Authors: <AUTHORS>\n", "\n", "The transformer architecture has become the state-of-art model for natural language processing tasks\n", "and, more recently, also for computer vision tasks, thus defining the Vision Transformer (ViT) architecture.\n", "The key feature is the ability to describe long-range correlations among the elements of the input\n", "sequences, through the so-called self-attention mechanism. In this tutorial, we will present the ViT wave function, an adaptation of the ViT\n", "architecture to define a class of variational Neural-Network Quantum States (NQS) for quantum spin\n", "systems (see Ref. [VT23, VIT23]).\n", "\n", "\n", "We begin by importing the necessary libraries, using [flax](https://github.com/google/flax)'s legacy linen interface for building neural networks."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cLdx0VxER_-o", "lines_to_next_cell": 2, "outputId": "2944888a-8adc-4a5d-cfac-5746ad0bb945"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[CudaDevice(id=0)]\n"]}], "source": ["import matplotlib.pyplot as plt\n", "\n", "import netket as nk\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "print(jax.devices())\n", "\n", "import flax\n", "from flax import linen as nn\n", "\n", "from einops import rearrange\n", "\n", "seed = 0\n", "key = jax.random.key(seed)"]}, {"cell_type": "markdown", "metadata": {"id": "DkFLKj68R3_p"}, "source": ["## ViT architecture\n", "\n", "The process of constructing the amplitude corresponding to a physical spin configuration $\\boldsymbol{\\sigma}$ involves the following steps (see Ref. [VIT23]):\n", "\n", "1. *Embedding*\n", "  + The input spin configuration $\\boldsymbol{\\sigma}$ is initially divided into $n$ patches. The specific shape of the patches depends on the structure of the lattice and its dimensionality, see for example Refs. [VT23, VIT23, RAS24]\n", "  + The patches are linearly projected into a $d$-dimensional embedding space, resulting in a sequence of vectors $(\\mathbf{x}_1, \\cdots, \\mathbf{x}_n)$, where $\\mathbf{x}_i \\in \\mathbb{R}^d$.\n", "2. *Transformer Encoder*\n", "  + A Transformer Encoder with real-valued parameters processes these embedded patches, producing another sequence of vectors $(\\mathbf{y}_1, \\cdots, \\mathbf{y}_n)$, where $\\mathbf{y}_i \\in \\mathbb{R}^d$.\n", "3. *Output layer*\n", "  + The hidden representation $\\boldsymbol{z}$ of the configuration $\\boldsymbol{\\sigma}$ is defined by summing all these output vectors: $\\boldsymbol{z}=\\sum_{i=1}^n \\mathbf{y}_i \\in \\mathbb{R}^d$.\n", "  + A fully-connected layer with complex-valued parameters maps $\\boldsymbol{z}$ to a single complex number, defining the amplitude $\\text{Log}[\\Psi_{\\theta}(\\boldsymbol{\\sigma})]$ corresponding to the input configuration $\\boldsymbol{\\sigma}$.\n", "\n", "A schematic illustration of the ViT architecture is provided in the following:\n", "\n", "![ViTarchitecture](https://s3.gifyu.com/images/bSz0i.gif)\n", "\n", "\n", "In the first part of this notebook, we implement the Transformer architecture by hand to get through to the smallest details. To be concrete, we consider a spin-$1/2$ system on a two dimensional $L\\times L$ square lattice."]}, {"cell_type": "markdown", "metadata": {"id": "I68_598ARre-", "lines_to_next_cell": 2}, "source": ["## 1. Embedding\n", "\n", "We begin by writing a flax module that, given a batch of $M$ spin configurations, first splits each configuration of shape $L\\times L$ into $L^2/b^2$ patches of size $b\\times b$. Then, each patch is embedded in $\\mathbb{R}^d$, with $d$ the *embedding dimension*.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3OeYHGI-Hx27"}, "outputs": [], "source": ["def extract_patches2d(x, patch_size):\n", "    batch = x.shape[0]\n", "    n_patches = int((x.shape[1] // patch_size**2) ** 0.5)\n", "    x = x.reshape(batch, n_patches, patch_size, n_patches, patch_size)\n", "    x = x.transpose(0, 1, 3, 2, 4)\n", "    x = x.reshape(batch, n_patches, n_patches, -1)\n", "    x = x.reshape(batch, n_patches * n_patches, -1)\n", "    return x\n", "\n", "\n", "class Embed(nn.Mo<PERSON>le):\n", "    d_model: int  # dimensionality of the embedding space\n", "    patch_size: int  # linear patch size\n", "    param_dtype = jnp.float64\n", "\n", "    def setup(self):\n", "        self.embed = nn.<PERSON><PERSON>(\n", "            self.d_model,\n", "            kernel_init=nn.initializers.xavier_uniform(),\n", "            param_dtype=self.param_dtype,\n", "        )\n", "\n", "    def __call__(self, x):\n", "        x = extract_patches2d(x, self.patch_size)\n", "        x = self.embed(x)\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d8qi6voW3Eff", "lines_to_next_cell": 2, "outputId": "63b24a33-4796-4d8d-ba4c-58666b342d96"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["spin_configs.shape = (200, 100)\n", "embedded_configs.shape = (200, 25, 32)\n"]}], "source": ["# test embedding module implementation\n", "d_model = 32  # embedding dimension\n", "patch_size = 2  # linear patch size\n", "\n", "# initialize a batch of spin configurations, considering a system on a 10x10 square lattice\n", "M = 200\n", "L = 10\n", "\n", "key, subkey = jax.random.split(key)\n", "spin_configs = jax.random.randint(subkey, shape=(M, L * L), minval=0, maxval=1) * 2 - 1\n", "\n", "print(f\"{spin_configs.shape = }\")\n", "\n", "# initialize the embedding module\n", "embed_module = Embed(d_model, patch_size)\n", "\n", "key, subkey = jax.random.split(key)\n", "params_embed = embed_module.init(subkey, spin_configs)\n", "\n", "# apply the embedding module to the spin configurations\n", "embedded_configs = embed_module.apply(params_embed, spin_configs)\n", "\n", "print(f\"{embedded_configs.shape = }\")"]}, {"cell_type": "markdown", "metadata": {"id": "1uzzNlT15dAu"}, "source": ["Working with configurations of shape $10\\times 10$ and choosing a patch size of $2\\times 2$, the embedding module maps each configuration into a sequence of vectors $(\\mathbf{x}_1, \\cdots, \\mathbf{x}_n)$, with $\\mathbf{x}_i \\in \\mathbb{R}^d$ for all $i$. In the considered setup, the resulting number of vectors is $n=25$ and we have chosen an embedding dimension of $d=32$.\n", "\n", ":::{warning}\n", "The function that extracts the patches from the spin configuration must be adapted to the specific lattice geometry. In particular, the function `extract_patches2d` is designed for square lattice without basis.\n", ":::"]}, {"cell_type": "markdown", "metadata": {"id": "1EeJK9A85Ngr", "lines_to_next_cell": 2}, "source": ["## 2. Transformer Encoder\n", "\n", "The Transformer Encoder block is composed of four ingredients: Multi-Head Attention, two-layer feed-forward neural network, layer normalization and skip connections. These elements are applied sequentially in the Encoder block as represented in the following figure:\n", "\n", "![](https://i.ibb.co/V02p9Gst/transformer-encoder.jpg)\n", "\n", "In the following we analyze the different building blocks.\n", "\n", "### Multi-Head Attention\n", "\n", "The core element of the Transformer architecture is the so-called *attention layer*, which processes the sequence of input vectors $(\\mathbf{x}_1, \\cdots, \\mathbf{x}_n)$, with $\\mathbf{x}_i \\in \\mathbb{R}^d$ for all $i$, producing a new sequence $(\\boldsymbol{A}_1, \\dots, \\boldsymbol{A}_n)$, with $\\boldsymbol{A}_i \\in \\mathbb{R}^d$. The goal of this transformation is to construct context-aware output vectors by combining all input vectors (see Ref. [VA17]):\n", "\n", "\\begin{equation}\n", "    \\boldsymbol{A}_i = \\sum_{j=1}^n \\alpha_{ij}(\\boldsymbol{x}_i, \\boldsymbol{x}_j) V \\boldsymbol{x}_j \\ .\n", "\\end{equation}\n", "\n", "The attention weights $\\alpha_{ij}(\\boldsymbol{x}_i, \\boldsymbol{x}_j)$ form a $n\\times n$ matrix, where $n$ is the number of patches, which measure the relative importance of the $j$-$th$ input when computing the new representation of the $i$-$th$ input. To parametrize the ViT wave function, we consider a simplified attention mechanism, called *factored attention* (see Ref. [RM24, SV22]), taking the attention weights only depending on positions $i$ and $j$, but not on the actual values of the spins in these patches. In equations, factored attention leads to $\\alpha_{ij}(\\boldsymbol{x}_i, \\boldsymbol{x}_j)=\\alpha_{ij}$. Below, we show how to implement the factored attention module in flax."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MZoldlup3rns"}, "outputs": [], "source": ["class FactoredAttention(nn.Module):\n", "    n_patches: int  # lenght of the input sequence\n", "    d_model: int  # dimensionality of the embedding space (d in the equations)\n", "\n", "    def setup(self):\n", "        self.alpha = self.param(\n", "            \"alpha\", nn.initializers.xavier_uniform(), (self.n_patches, self.n_patches)\n", "        )\n", "        self.V = self.param(\n", "            \"V\", nn.initializers.xavier_uniform(), (self.d_model, self.d_model)\n", "        )\n", "\n", "    def __call__(self, x):\n", "        y = jnp.einsum(\"i j, a b, M j b-> M i a\", self.alpha, self.V, x)\n", "        return y"]}, {"cell_type": "markdown", "metadata": {"id": "kXuTJnqAGEvo"}, "source": ["For the specific application of approximating ground states of\n", "quantum many-body spin Hamiltonians, factored attention yields equivalent performance with respect to the standard attention mechanism,\n", "while reducing the computational cost and parameter usage (see Ref.[RA25]).\n", "\n", "To improve the expressivity of the self-attention mechanism, Multi-Head attention can be considered, where for each position $i$ different attention representations $\\boldsymbol{A}_i^{\\mu}$ are computed, where $\\mu = 1, \\dots, h$ with $h$ the total number of heads. The different vectors $\\boldsymbol{A}_i^{\\mu} \\in \\mathbf{R}^{d/h}$ are computed in\n", "parallel, concatenated together, and linearly combined through a matrix of weights $W$.\n", "\n", "Below we build a flax module that implements the Factored Multi-Head Attention mechanism. In addition, we also provide a translational invariant implementation.\n", "\n", ":::{note}\n", "For approximating ground states of translationally invariant Hamiltonians, it is useful to implement translationally invariant attention mechanisms where $\\alpha_{ij} = \\alpha_{i-j}$.\n", ":::"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2EQnUHRARScb"}, "outputs": [], "source": ["from functools import partial\n", "\n", "\n", "@partial(jax.vmap, in_axes=(None, 0, None), out_axes=1)\n", "@partial(jax.vmap, in_axes=(None, None, 0), out_axes=1)\n", "def roll2d(spins, i, j):\n", "    side = int(spins.shape[-1] ** 0.5)\n", "    spins = spins.reshape(spins.shape[0], side, side)\n", "    spins = jnp.roll(jnp.roll(spins, i, axis=-2), j, axis=-1)\n", "    return spins.reshape(spins.shape[0], -1)\n", "\n", "\n", "class FMHA(nn.Module):\n", "    d_model: int  # dimensionality of the embedding space\n", "    n_heads: int  # number of heads\n", "    n_patches: int  # lenght of the input sequence\n", "    transl_invariant: bool = False\n", "    param_dtype = jnp.float64\n", "\n", "    def setup(self):\n", "        self.v = nn.<PERSON><PERSON>(\n", "            self.d_model,\n", "            kernel_init=nn.initializers.xavier_uniform(),\n", "            param_dtype=self.param_dtype,\n", "        )\n", "        self.W = nn.<PERSON><PERSON>(\n", "            self.d_model,\n", "            kernel_init=nn.initializers.xavier_uniform(),\n", "            param_dtype=self.param_dtype,\n", "        )\n", "        if self.transl_invariant:\n", "            self.alpha = self.param(\n", "                \"alpha\",\n", "                nn.initializers.xavier_uniform(),\n", "                (self.n_heads, self.n_patches),\n", "                self.param_dtype,\n", "            )\n", "            sq_n_patches = int(self.n_patches**0.5)\n", "            assert sq_n_patches * sq_n_patches == self.n_patches\n", "            self.alpha = roll2d(\n", "                self.alpha, jnp.arange(sq_n_patches), jnp.arange(sq_n_patches)\n", "            )\n", "            self.alpha = self.alpha.reshape(self.n_heads, -1, self.n_patches)\n", "        else:\n", "            self.alpha = self.param(\n", "                \"alpha\",\n", "                nn.initializers.xavier_uniform(),\n", "                (self.n_heads, self.n_patches, self.n_patches),\n", "                self.param_dtype,\n", "            )\n", "\n", "    def __call__(self, x):\n", "        # apply the value matrix in paralell for each head\n", "        v = self.v(x)\n", "\n", "        # split the representations of the different heads\n", "        v = rearrange(\n", "            v,\n", "            \"batch n_patches (n_heads d_eff) -> batch n_patches n_heads d_eff\",\n", "            n_heads=self.n_heads,\n", "        )\n", "\n", "        # factored attention mechanism\n", "        v = rearrange(\n", "            v, \"batch n_patches n_heads d_eff -> batch n_heads n_patches d_eff\"\n", "        )\n", "        x = jnp.matmul(self.alpha, v)\n", "        x = rearrange(\n", "            x, \"batch n_heads n_patches d_eff  -> batch n_patches n_heads d_eff\"\n", "        )\n", "\n", "        # concatenate the different heads\n", "        x = rearrange(\n", "            x, \"batch n_patches n_heads d_eff ->  batch n_patches (n_heads d_eff)\"\n", "        )\n", "\n", "        # the representations of the different heads are combined together\n", "        x = self.W(x)\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LM8DtDRQRSQD", "lines_to_next_cell": 2, "outputId": "c837d2dd-6e2b-4385-88d6-f4e479036663"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["attention_vectors.shape = (200, 25, 32)\n"]}], "source": ["# test Factored MultiHead Attention module\n", "n_heads = 8  # number of heads\n", "n_patches = embedded_configs.shape[1]  # lenght of the input sequence\n", "\n", "# initialize the Factored Multi-Head Attention module\n", "fmha_module = FMHA(d_model, n_heads, n_patches)\n", "\n", "key, subkey = jax.random.split(key)\n", "params_fmha = fmha_module.init(subkey, embedded_configs)\n", "\n", "# apply the Factored Multi-Head Attention module to the embedding vectors\n", "attention_vectors = fmha_module.apply(params_fmha, embedded_configs)\n", "\n", "print(f\"{attention_vectors.shape = }\")"]}, {"cell_type": "markdown", "metadata": {"id": "pCeUca5HX-c3", "lines_to_next_cell": 2}, "source": ["### Encoder Block\n", "\n", "In each encoder block, the MultiHead attention mechanism is followed by a two-layers feed-forward neural network. Layer normalization and skip connections are also added to stabilize the training of deep architectures."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6N23TCYHYLH7", "lines_to_next_cell": 2}, "outputs": [], "source": ["class EncoderBlock(nn.Module):\n", "    d_model: int  # dimensionality of the embedding space\n", "    n_heads: int  # number of heads\n", "    n_patches: int  # lenght of the input sequence\n", "    transl_invariant: bool = False\n", "    param_dtype = jnp.float64\n", "\n", "    def setup(self):\n", "        self.attn = FMHA(\n", "            d_model=self.d_model,\n", "            n_heads=self.n_heads,\n", "            n_patches=self.n_patches,\n", "            transl_invariant=self.transl_invariant,\n", "        )\n", "\n", "        self.layer_norm_1 = nn.LayerNorm(param_dtype=self.param_dtype)\n", "        self.layer_norm_2 = nn.LayerNorm(param_dtype=self.param_dtype)\n", "\n", "        self.ff = nn.Sequential(\n", "            [\n", "                nn.<PERSON><PERSON>(\n", "                    4 * self.d_model,\n", "                    kernel_init=nn.initializers.xavier_uniform(),\n", "                    param_dtype=self.param_dtype,\n", "                ),\n", "                nn.gelu,\n", "                nn.<PERSON><PERSON>(\n", "                    self.d_model,\n", "                    kernel_init=nn.initializers.xavier_uniform(),\n", "                    param_dtype=self.param_dtype,\n", "                ),\n", "            ]\n", "        )\n", "\n", "    def __call__(self, x):\n", "        x = x + self.attn(self.layer_norm_1(x))\n", "\n", "        x = x + self.ff(self.layer_norm_2(x))\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "8BjwIlRLbzQS", "lines_to_next_cell": 2}, "source": ["Based on this block, we can implement a module for the full Transformer Encoder applying a sequence of encoder blocks. The number of these blocks is defined by the number of layers of the Transformer architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bHWROcz5bud5"}, "outputs": [], "source": ["class Encoder(nn.Module):\n", "    num_layers: int  # number of layers\n", "    d_model: int  # dimensionality of the embedding space\n", "    n_heads: int  # number of heads\n", "    n_patches: int  # lenght of the input sequence\n", "    transl_invariant: bool = False\n", "\n", "    def setup(self):\n", "        self.layers = [\n", "            EncoderBlock(\n", "                d_model=self.d_model,\n", "                n_heads=self.n_heads,\n", "                n_patches=self.n_patches,\n", "                transl_invariant=self.transl_invariant,\n", "            )\n", "            for _ in range(self.num_layers)\n", "        ]\n", "\n", "    def __call__(self, x):\n", "\n", "        for l in self.layers:\n", "            x = l(x)\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uGTDosLzcGkk", "outputId": "82172451-fa4f-4ea1-9980-77227ff9dfab"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["y.shape = (200, 25, 32)\n"]}], "source": ["# test Transformer Encoder module\n", "num_layers = 4  # number of layers\n", "\n", "# initialize the Factored Multi-Head Attention module\n", "encoder_module = Encoder(num_layers, d_model, n_heads, n_patches)\n", "\n", "key, subkey = jax.random.split(key)\n", "params_encoder = encoder_module.init(subkey, embedded_configs)\n", "\n", "# apply the Factored Multi-Head Attention module to the embedding vectors\n", "x = embedded_configs\n", "y = encoder_module.apply(params_encoder, x)\n", "\n", "print(f\"{y.shape = }\")"]}, {"cell_type": "markdown", "metadata": {"id": "ed8aW_HrcilR"}, "source": ["The Transformer Encoder processes the embedded patches $(\\mathbf{x}_1, \\cdots, \\mathbf{x}_n)$, with $\\mathbf{x}_i \\in \\mathbb{R}^d$, producing another sequence of vectors $(\\mathbf{y}_1, \\cdots, \\mathbf{y}_n)$, with $\\mathbf{y}_i \\in \\mathbb{R}^d$."]}, {"cell_type": "markdown", "metadata": {"id": "U_8fwA1XdaPy"}, "source": ["## 3. Output layer\n", "For each configuration $\\boldsymbol{\\sigma}$, we compute its hidden representation $\\boldsymbol{z}=\\sum_{i=1}^n \\mathbf{y}_i$. Then, we produce a single complex number representing its amplitude using the fully-connected layer defined in Ref. [CS17]:\n", "\n", "\\begin{equation}\n", "    \\text{Log}[\\Psi(\\boldsymbol{\\sigma})] = \\sum_{\\alpha=1}^d \\log\\cosh \\left( b_{\\alpha} + \\boldsymbol{w}_{\\alpha} \\cdot \\boldsymbol{z} \\right) \\ ,\n", "\\end{equation}\n", "\n", ":::{note}\n", "The parameters $\\{ b_\\alpha, \\boldsymbol{w}_\\alpha \\}$ are taken to be complex valued, contrary to the parameters of the Transformer Encoder which are all real valued.\n", ":::"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "GVuWv-BRfcnT", "lines_to_next_cell": 2}, "outputs": [], "source": ["log_cosh = (\n", "    nk.nn.activation.log_cosh\n", ")  # Logarithm of the hyperbolic cosine, implemented in a more stable way\n", "\n", "\n", "class OuputHead(nn.Module):\n", "    d_model: int  # dimensionality of the embedding space\n", "    param_dtype = jnp.float64\n", "\n", "    def setup(self):\n", "        self.out_layer_norm = nn.LayerNorm(param_dtype=self.param_dtype)\n", "\n", "        self.norm2 = nn.LayerNorm(\n", "            use_scale=True, use_bias=True, param_dtype=self.param_dtype\n", "        )\n", "        self.norm3 = nn.LayerNorm(\n", "            use_scale=True, use_bias=True, param_dtype=self.param_dtype\n", "        )\n", "\n", "        self.output_layer0 = nn.Dense(\n", "            self.d_model,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=nn.initializers.xavier_uniform(),\n", "            bias_init=jax.nn.initializers.zeros,\n", "        )\n", "        self.output_layer1 = nn.Dense(\n", "            self.d_model,\n", "            param_dtype=self.param_dtype,\n", "            kernel_init=nn.initializers.xavier_uniform(),\n", "            bias_init=jax.nn.initializers.zeros,\n", "        )\n", "\n", "    def __call__(self, x):\n", "\n", "        z = self.out_layer_norm(x.sum(axis=1))\n", "\n", "        out_real = self.norm2(self.output_layer0(z))\n", "        out_imag = self.norm3(self.output_layer1(z))\n", "\n", "        out = out_real + 1j * out_imag\n", "\n", "        return jnp.sum(log_cosh(out), axis=-1)"]}, {"cell_type": "markdown", "metadata": {"id": "ajN6baa2gn_b", "lines_to_next_cell": 2}, "source": ["Combining the Embedding, Encoder and OutputHead modules we can implement a module for the full ViT architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "leWFMMrNhF-e"}, "outputs": [], "source": ["class ViT(nn.Module):\n", "    num_layers: int  # number of layers\n", "    d_model: int  # dimensionality of the embedding space\n", "    n_heads: int  # number of heads\n", "    patch_size: int  # linear patch size\n", "    transl_invariant: bool = False\n", "\n", "    @nn.compact\n", "    def __call__(self, spins):\n", "        x = jnp.atleast_2d(spins)\n", "\n", "        Ns = x.shape[-1]  # number of sites\n", "        n_patches = Ns // self.patch_size**2  # lenght of the input sequence\n", "\n", "        x = Embed(d_model=self.d_model, patch_size=self.patch_size)(x)\n", "\n", "        y = Encoder(\n", "            num_layers=self.num_layers,\n", "            d_model=self.d_model,\n", "            n_heads=self.n_heads,\n", "            n_patches=n_patches,\n", "            transl_invariant=self.transl_invariant,\n", "        )(x)\n", "\n", "        log_psi = OuputHead(d_model=self.d_model)(y)\n", "\n", "        return log_psi"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dYar0GnThjMH", "outputId": "6a62bea6-845e-4f12-b5a6-70b83237ac37"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["log_psi.shape = (200,)\n"]}], "source": ["# test ViT module\n", "\n", "# initialize the ViT module\n", "vit_module = ViT(num_layers, d_model, n_heads, patch_size)\n", "\n", "key, subkey = jax.random.split(key)\n", "params = vit_module.init(subkey, spin_configs)\n", "\n", "# apply the ViT module\n", "log_psi = vit_module.apply(params, spin_configs)\n", "\n", "print(f\"{log_psi.shape = }\")"]}, {"cell_type": "markdown", "metadata": {"id": "F4fLHmeZ4cDN"}, "source": ["## Ground state optimization\n", "\n", "We show how to train the ViT wave function on the two-dimensional $J_1$- $J_2$ Heisenberg model on a $10\\times 10$ square lattice.\n", "The system is described by the following Hamiltonian (with periodic boundary conditions):\n", "\n", "$$\n", "    \\hat{H} = J_1 \\sum_{\\langle {\\boldsymbol{r}},{\\boldsymbol{r'}} \\rangle} \\hat{\\boldsymbol{S}}_{\\boldsymbol{r}}\\cdot\\hat{\\boldsymbol{S}}_{\\boldsymbol{r'}}\n", "    + J_2 \\sum_{\\langle \\langle {\\boldsymbol{r}},{\\boldsymbol{r'}} \\rangle \\rangle} \\hat{\\boldsymbol{S}}_{\\boldsymbol{r}}\\cdot\\hat{\\boldsymbol{S}}_{\\boldsymbol{r'}} \\ .\n", "$$\n", "\n", "We fix $J_2/J_1=0.5$ and we use the VMC_SRt driver (see Ref.[RAS24]) implemented in NetKet to optimize the ViT wave function."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sP1jBIXb6H_W", "lines_to_next_cell": 2, "outputId": "1b432288-79e0-4caf-cd84-00b16d2d3dae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of parameters =  155620\n"]}], "source": ["seed = 0\n", "key = jax.random.key(seed)\n", "\n", "L = 10\n", "n_dim = 2\n", "J2 = 0.5\n", "\n", "lattice = nk.graph.Hypercube(length=L, n_dim=n_dim, pbc=True, max_neighbor_order=2)\n", "\n", "# Hilbert space of spins on the graph\n", "hilbert = nk.hilbert.Spin(s=1 / 2, N=lattice.n_nodes, total_sz=0)\n", "\n", "# Heisenberg J1-J2 spin hamiltonian\n", "hamiltonian = nk.operator.<PERSON><PERSON><PERSON>(\n", "    hilbert=hilbert, graph=lattice, J=[1.0, J2], sign_rule=[False, False]\n", ").to_jax_operator()  # No Marshall sign rule\n", "\n", "# Intiialize the ViT variational wave function\n", "vit_module = ViT(\n", "    num_layers=4, d_model=60, n_heads=10, patch_size=2, transl_invariant=True\n", ")\n", "\n", "key, subkey = jax.random.split(key)\n", "params = vit_module.init(subkey, spin_configs)\n", "\n", "# Metropolis Local Sampling\n", "N_samples = 4096\n", "sampler = nk.sampler.MetropolisExchange(\n", "    hilbert=hilbert,\n", "    graph=lattice,\n", "    d_max=2,\n", "    n_chains=N_samples,\n", "    sweep_size=lattice.n_nodes,\n", ")\n", "\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.0075)\n", "\n", "key, subkey = jax.random.split(key, 2)\n", "vstate = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    model=vit_module,\n", "    sampler_seed=subkey,\n", "    n_samples=N_samples,\n", "    n_discard_per_chain=0,\n", "    variables=params,\n", "    chunk_size=512,\n", ")\n", "\n", "N_params = nk.jax.tree_size(vstate.parameters)\n", "print(\"Number of parameters = \", N_params, flush=True)\n", "\n", "# Variational monte carlo driver\n", "from netket.experimental.driver.vmc_srt import VMC_SRt\n", "\n", "vmc = VMC_SRt(\n", "    hamiltonian=hamiltonian,\n", "    optimizer=optimizer,\n", "    diag_shift=1e-4,\n", "    variational_state=vstate,\n", "    jacobian_mode=\"complex\",\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 86, "referenced_widgets": ["a8dbb3f874ac4f8baac39b8ebe18fd9d", "25f3f464d5a24e9284ba5ea140f4944a", "3ceb6d5780a24fd2bb8f203bca36b661", "feb52ce513814bbd8ada812695fc1718", "d72add58f069432a898851ed1ad825da", "2345d611347c4c239a92a245589ac1a8", "550f50e1dd7f4cdc8131ee99638cfdc7", "f3970deece314655b106677a604e82ad", "175b3b3073bb46a0b90867e1b833e49f", "fb9ff265fcbd48bbad4931702d59eeb1", "9d68301885f0446db22eb115d8ff948b"]}, "id": "NaSPVYboM3K-", "outputId": "4f534963-f32f-4a99-f2dd-76e1e7180940"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 800/800 [1:40:04<00:00,  7.51s/it, Energy=-198.581-0.005j ± 0.042 [σ²=7.227]] \n"]}], "source": ["# Optimization\n", "log = nk.logging.RuntimeLog()\n", "\n", "N_opt = 800\n", "vmc.run(n_iter=N_opt, out=log)"]}, {"cell_type": "markdown", "metadata": {"id": "gcZOaRr3VIj7"}, "source": [":::{note}\n", "The previous cell requires approximately two hours to run on a single A100 GPU.\n", ":::\n", "\n", "We can visualize the training dynamics by plotting the mean energy as a function of the optimization steps:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 449}, "id": "pJLZjm6JR65_", "outputId": "749046b9-38e1-4eba-d614-3137664c225c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Last value:  -0.4964522368959598\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["energy_per_site = log.data[\"Energy\"][\"Mean\"].real / (L * L * 4)\n", "\n", "print(\"Last value: \", energy_per_site[-1])\n", "\n", "plt.plot(energy_per_site)\n", "\n", "plt.xlabel(\"Iterations\")\n", "plt.ylabel(\"Energy per site\")\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "apySasQ1V9mC"}, "source": ["The final variational energy obtained is approximately $E_{\\text{var}} \\approx -0.4964$. To improve this result, longer simulations with a larger number of samples should be conducted on several GPUs in parallel."]}, {"cell_type": "markdown", "metadata": {"id": "efM4dYrc2aei"}, "source": ["## Pretrained model (Hugging Face)\n", "\n", "We provide a pretrained Vision Transformer (ViT) architecture with $P = 434760$ parameters at [Hugging Face ViT](https://huggingface.co/nqs-models/j1j2_square_10x10_05). Below, we demonstrate how the model can be easily downloaded and used within the NetKet framework with just a few lines of code."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 595, "referenced_widgets": ["1b191cfa8c23430cb5fdf2c3bc7c4f3f", "4a1dfd6b5dc24ea18d764336ea22c73b", "d12a4d807c084a2e8ada609222451593", "35b177119bd649ff9c2eb4e7b7d16006", "05f3fe3cf76a4b208cb61cd313a81d84", "d3bd0f3007a74db49aa9871f27318403", "c0547a61bab744dc8bdd1961392db1a4", "198659bb5be343388bb96525a1d2a716", "039a69c2c5dc45cdb41f20a370fe691a", "6c85dddaa458418a95e0a0a0e8a16848", "a0b8d94847fc412394d600a71806fbd3", "26299012fe9948fd84e494ab2cf6a5f9", "f7eceef080aa49b393658306e7b1eeb6", "540be49a24a642d58cc194a4a7513ede", "4d3b112f42534b79be249a793a7a218a", "d1e381dcef4448608c3a9ff9528994af", "a2b99659c19049c3a20236ee35f2bcc9", "a378ba5272c04401b53dffdd2d8ad4ed", "7a871b6a985d40979a7c3fa862284c3c", "d7225a56d9bd46bcb5d7c1cc098ac43a", "870b858a65fb4ffcab5640381daa4843", "2aa8f77212b84a559b437840904d7404", "369a12d13e914409869048258a20d3a5", "60b798acf66b4a7596c9b071cac1560e", "be49f75ea338407bbadbf5cd38c9f48d", "b932fa821a8544a4a5b6ff0504914ca9", "491cfb2653e84bc9a37ce01099350a85", "0e66e84b9a964a5ca0816b0b2c14e20b", "46680451d39b4a17969585065685d5f0", "cedf9000b7284e64a15936e64f836764", "14f5b7cef4534f6d9e53d9cd29882120", "aa99850e2b814fd2ae0e60be6bc0e384", "5c5db6dc04df4d6db8a81514e637f1e2", "12d075e39a9e40b7996c0038161b4169", "5cc80eb516e44849957adf7eaaed0239", "61481d82173a474f98b609be520c5885", "e4187851ae4b4a988f51609941f5772c", "6ef382e6ac034d39bdbfa83e0344d05f", "0bfb2379fed34779807500f0a82bd88c", "8d4c4de19ead40f883d97a51861175ec", "e62420499402411ea6146890ab429fbc", "aa812a9cd8a746308b035bf82bf0c16e", "bde53ef374ce4f74a745f69af6154eca", "8ba2d8d18ce1423cb7b76a39b6ad3546", "f5b90c96d94f4f2ea7f0015645f9ed52", "6d150846c07f4768898a94cf91c6f290", "1fd2890752c04801ac1c18dfa4025b5b", "8ecbfd6b13bf4a898aabf511b4780e24", "3b2cc4650c44418fbae7f5ef21b83131", "1ef554a7cd984085b4b8b14a5cb4cae3", "258cac78c8f6409ba176ecb8c6675c0a", "5452539416fc4f8ea529860712440410", "e38d9e14fbd64f958553a5477ff2ffbd", "0ae26697bc034569b4c613c5121c5dc9", "8d0dd88721734f17aea4d33409885afb", "ea8439c781b54b0ebf9f8667529b9554", "00340b865499426a8b6bbb56de8062d2", "5046f05716484443b3164fe8a75cf6eb", "b4b0bdcbf31c4b8c8a45eeb0bfe59dfb", "861140b2c5ff4ba7a3108345e18d3707", "05395b4807fd4d51b258e062d0ee75de", "9141e150a5fa44d198eabe35fe6782f3", "b3712136160f4413a7e23a6b155adf04", "4a71faafccc4455aa13790872e421a12", "d18b3e9f5e0e43e4a288d43726d43288", "8f437dc404604157a0a6716089cb6c14"]}, "id": "luWZoc3ze3U7", "outputId": "fe354171-a2b4-41eb-d8fc-5009f4cdd5f0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of parameters =  434760\n"]}], "source": ["from transformers import FlaxAutoModel\n", "\n", "wf = FlaxAutoModel.from_pretrained(\n", "    \"nqs-models/j1j2_square_10x10\", trust_remote_code=True\n", ")\n", "N_params = nk.jax.tree_size(wf.params)\n", "print(\"Number of parameters = \", N_params, flush=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_vajEkBMe9zu", "outputId": "d31f93fd-bbcd-466a-b146-85d6fbea41f7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-199.0031+0.0001j ± 0.0099 [σ²=1.5990]\n"]}], "source": ["sampler = nk.sampler.MetropolisExchange(\n", "    hilbert=hilbert, graph=lattice, d_max=2, n_chains=16384, sweep_size=lattice.n_nodes\n", ")\n", "\n", "vstate = nk.vqs.MCState(\n", "    sampler=sampler,\n", "    apply_fun=wf.__call__,\n", "    sampler_seed=subkey,\n", "    n_samples=16384,\n", "    n_discard_per_chain=0,\n", "    variables=wf.params,\n", "    chunk_size=16384,\n", ")\n", "\n", "# Overwrite samples with already thermalized ones\n", "from huggingface_hub import hf_hub_download\n", "from flax.training import checkpoints\n", "\n", "flax.config.update(\"flax_use_orbax_checkpointing\", False)\n", "\n", "path = hf_hub_download(repo_id=\"nqs-models/j1j2_square_10x10\", filename=\"spins\")\n", "samples = checkpoints.restore_checkpoint(ckpt_dir=path, prefix=\"spins\", target=None)\n", "samples = jnp.array(\n", "    samples, dtype=\"int8\"\n", ")  #! some versions of netket do not require this line\n", "\n", "vstate.sampler_state = vstate.sampler_state.replace(σ=samples)\n", "\n", "E = vstate.expect(hamiltonian)\n", "\n", "print(E)"]}, {"cell_type": "markdown", "metadata": {"id": "KtYQifV3goHY"}, "source": ["Using a single A100 GPU, the previous cell should run in less than one minute and produce a mean variational energy of approximately $E_{\\text{var}} \\approx -0.497508$. This energy can be further improved by enforcing lattice symmetries in the variational wavefunction, reaching $E_{\\text{var}} \\approx -0.497676335$ when restoring translational, point group, and parity symmetries (see [Hugging Face ViT](https://huggingface.co/nqs-models/j1j2_square_10x10_05) for more details)."]}, {"cell_type": "markdown", "metadata": {"id": "bh5-oPREBh_8"}, "source": ["---\n", "\n", "### References\n", "\n", "+ [CS17] <PERSON><PERSON>, <PERSON>, and <PERSON>. \"Solving the quantum many-body problem with artificial neural networks.\" Science 355, no. 6325 (2017): 602-606.\n", "+ [VT23] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. \"Transformer variational wave functions for frustrated quantum spin systems.\" Physical Review Letters 130, no. 23 (2023): 236401.\n", "+ [VIT23] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. \"Transformer wave function for the shastry-sutherland model: emergence of a spin-liquid phase.\" arXiv preprint arXiv:2311.16889 (2023).\n", "+ [VA17] <PERSON><PERSON><PERSON><PERSON>, <PERSON>. \"Attention is all you need.\" Advances in Neural Information Processing Systems (2017).\n", "+ [<PERSON><PERSON>24] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. \"Mapping of attention mechanisms to a generalized Potts model.\" Physical Review Research 6, no. 2 (2024): 023057.\n", "+ [SV22] <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON>, \"Vision transformers provably learn spatial structure\", in Advances in neural information processing systems (2022)\n", "+ [RA25] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. \"Are queries and keys always relevant? A case study on transformer wave functions.\" Machine Learning: Science and Technology 6, no. 1 (2025): 010501.\n", "+ [RAS24] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. \"A simple linear algebra identity to optimize large-scale neural network quantum states.\" Communications Physics 7, no. 1 (2024): 260."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "mynetket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00340b865499426a8b6bbb56de8062d2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_05395b4807fd4d51b258e062d0ee75de", "placeholder": "​", "style": "IPY_MODEL_9141e150a5fa44d198eabe35fe6782f3", "value": "model.safetensors: 100%"}}, "039a69c2c5dc45cdb41f20a370fe691a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "05395b4807fd4d51b258e062d0ee75de": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "05f3fe3cf76a4b208cb61cd313a81d84": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0ae26697bc034569b4c613c5121c5dc9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bfb2379fed34779807500f0a82bd88c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0e66e84b9a964a5ca0816b0b2c14e20b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12d075e39a9e40b7996c0038161b4169": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5cc80eb516e44849957adf7eaaed0239", "IPY_MODEL_61481d82173a474f98b609be520c5885", "IPY_MODEL_e4187851ae4b4a988f51609941f5772c"], "layout": "IPY_MODEL_6ef382e6ac034d39bdbfa83e0344d05f"}}, "14f5b7cef4534f6d9e53d9cd29882120": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "175b3b3073bb46a0b90867e1b833e49f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "198659bb5be343388bb96525a1d2a716": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1b191cfa8c23430cb5fdf2c3bc7c4f3f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4a1dfd6b5dc24ea18d764336ea22c73b", "IPY_MODEL_d12a4d807c084a2e8ada609222451593", "IPY_MODEL_35b177119bd649ff9c2eb4e7b7d16006"], "layout": "IPY_MODEL_05f3fe3cf76a4b208cb61cd313a81d84"}}, "1ef554a7cd984085b4b8b14a5cb4cae3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1fd2890752c04801ac1c18dfa4025b5b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5452539416fc4f8ea529860712440410", "max": 2079, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e38d9e14fbd64f958553a5477ff2ffbd", "value": 2079}}, "2345d611347c4c239a92a245589ac1a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "258cac78c8f6409ba176ecb8c6675c0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25f3f464d5a24e9284ba5ea140f4944a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2345d611347c4c239a92a245589ac1a8", "placeholder": "​", "style": "IPY_MODEL_550f50e1dd7f4cdc8131ee99638cfdc7", "value": "100%"}}, "26299012fe9948fd84e494ab2cf6a5f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f7eceef080aa49b393658306e7b1eeb6", "IPY_MODEL_540be49a24a642d58cc194a4a7513ede", "IPY_MODEL_4d3b112f42534b79be249a793a7a218a"], "layout": "IPY_MODEL_d1e381dcef4448608c3a9ff9528994af"}}, "2aa8f77212b84a559b437840904d7404": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "35b177119bd649ff9c2eb4e7b7d16006": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c85dddaa458418a95e0a0a0e8a16848", "placeholder": "​", "style": "IPY_MODEL_a0b8d94847fc412394d600a71806fbd3", "value": " 338/338 [00:00&lt;00:00, 26.6kB/s]"}}, "369a12d13e914409869048258a20d3a5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60b798acf66b4a7596c9b071cac1560e", "IPY_MODEL_be49f75ea338407bbadbf5cd38c9f48d", "IPY_MODEL_b932fa821a8544a4a5b6ff0504914ca9"], "layout": "IPY_MODEL_491cfb2653e84bc9a37ce01099350a85"}}, "3b2cc4650c44418fbae7f5ef21b83131": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ceb6d5780a24fd2bb8f203bca36b661": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f3970deece314655b106677a604e82ad", "max": 200, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_175b3b3073bb46a0b90867e1b833e49f", "value": 200}}, "46680451d39b4a17969585065685d5f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "491cfb2653e84bc9a37ce01099350a85": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a1dfd6b5dc24ea18d764336ea22c73b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d3bd0f3007a74db49aa9871f27318403", "placeholder": "​", "style": "IPY_MODEL_c0547a61bab744dc8bdd1961392db1a4", "value": "config.json: 100%"}}, "4a71faafccc4455aa13790872e421a12": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4d3b112f42534b79be249a793a7a218a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_870b858a65fb4ffcab5640381daa4843", "placeholder": "​", "style": "IPY_MODEL_2aa8f77212b84a559b437840904d7404", "value": " 578/578 [00:00&lt;00:00, 54.8kB/s]"}}, "5046f05716484443b3164fe8a75cf6eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b3712136160f4413a7e23a6b155adf04", "max": 3490136, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4a71faafccc4455aa13790872e421a12", "value": 3490136}}, "540be49a24a642d58cc194a4a7513ede": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a871b6a985d40979a7c3fa862284c3c", "max": 578, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d7225a56d9bd46bcb5d7c1cc098ac43a", "value": 578}}, "5452539416fc4f8ea529860712440410": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "550f50e1dd7f4cdc8131ee99638cfdc7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5c5db6dc04df4d6db8a81514e637f1e2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5cc80eb516e44849957adf7eaaed0239": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0bfb2379fed34779807500f0a82bd88c", "placeholder": "​", "style": "IPY_MODEL_8d4c4de19ead40f883d97a51861175ec", "value": "transformer.py: 100%"}}, "60b798acf66b4a7596c9b071cac1560e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0e66e84b9a964a5ca0816b0b2c14e20b", "placeholder": "​", "style": "IPY_MODEL_46680451d39b4a17969585065685d5f0", "value": "vitnqs_model.py: 100%"}}, "61481d82173a474f98b609be520c5885": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e62420499402411ea6146890ab429fbc", "max": 4558, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_aa812a9cd8a746308b035bf82bf0c16e", "value": 4558}}, "6c85dddaa458418a95e0a0a0e8a16848": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d150846c07f4768898a94cf91c6f290": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ef554a7cd984085b4b8b14a5cb4cae3", "placeholder": "​", "style": "IPY_MODEL_258cac78c8f6409ba176ecb8c6675c0a", "value": "attentions.py: 100%"}}, "6ef382e6ac034d39bdbfa83e0344d05f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7a871b6a985d40979a7c3fa862284c3c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "861140b2c5ff4ba7a3108345e18d3707": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "870b858a65fb4ffcab5640381daa4843": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ba2d8d18ce1423cb7b76a39b6ad3546": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d0dd88721734f17aea4d33409885afb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d4c4de19ead40f883d97a51861175ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8ecbfd6b13bf4a898aabf511b4780e24": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0ae26697bc034569b4c613c5121c5dc9", "placeholder": "​", "style": "IPY_MODEL_8d0dd88721734f17aea4d33409885afb", "value": " 2.08k/2.08k [00:00&lt;00:00, 161kB/s]"}}, "8f437dc404604157a0a6716089cb6c14": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9141e150a5fa44d198eabe35fe6782f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9d68301885f0446db22eb115d8ff948b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a0b8d94847fc412394d600a71806fbd3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a2b99659c19049c3a20236ee35f2bcc9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a378ba5272c04401b53dffdd2d8ad4ed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a8dbb3f874ac4f8baac39b8ebe18fd9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_25f3f464d5a24e9284ba5ea140f4944a", "IPY_MODEL_3ceb6d5780a24fd2bb8f203bca36b661", "IPY_MODEL_feb52ce513814bbd8ada812695fc1718"], "layout": "IPY_MODEL_d72add58f069432a898851ed1ad825da"}}, "aa812a9cd8a746308b035bf82bf0c16e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aa99850e2b814fd2ae0e60be6bc0e384": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3712136160f4413a7e23a6b155adf04": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4b0bdcbf31c4b8c8a45eeb0bfe59dfb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d18b3e9f5e0e43e4a288d43726d43288", "placeholder": "​", "style": "IPY_MODEL_8f437dc404604157a0a6716089cb6c14", "value": " 3.49M/3.49M [00:00&lt;00:00, 33.6MB/s]"}}, "b932fa821a8544a4a5b6ff0504914ca9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa99850e2b814fd2ae0e60be6bc0e384", "placeholder": "​", "style": "IPY_MODEL_5c5db6dc04df4d6db8a81514e637f1e2", "value": " 1.26k/1.26k [00:00&lt;00:00, 107kB/s]"}}, "bde53ef374ce4f74a745f69af6154eca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "be49f75ea338407bbadbf5cd38c9f48d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cedf9000b7284e64a15936e64f836764", "max": 1261, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_14f5b7cef4534f6d9e53d9cd29882120", "value": 1261}}, "c0547a61bab744dc8bdd1961392db1a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cedf9000b7284e64a15936e64f836764": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d12a4d807c084a2e8ada609222451593": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_198659bb5be343388bb96525a1d2a716", "max": 338, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_039a69c2c5dc45cdb41f20a370fe691a", "value": 338}}, "d18b3e9f5e0e43e4a288d43726d43288": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d1e381dcef4448608c3a9ff9528994af": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3bd0f3007a74db49aa9871f27318403": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7225a56d9bd46bcb5d7c1cc098ac43a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d72add58f069432a898851ed1ad825da": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "e38d9e14fbd64f958553a5477ff2ffbd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e4187851ae4b4a988f51609941f5772c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bde53ef374ce4f74a745f69af6154eca", "placeholder": "​", "style": "IPY_MODEL_8ba2d8d18ce1423cb7b76a39b6ad3546", "value": " 4.56k/4.56k [00:00&lt;00:00, 240kB/s]"}}, "e62420499402411ea6146890ab429fbc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ea8439c781b54b0ebf9f8667529b9554": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_00340b865499426a8b6bbb56de8062d2", "IPY_MODEL_5046f05716484443b3164fe8a75cf6eb", "IPY_MODEL_b4b0bdcbf31c4b8c8a45eeb0bfe59dfb"], "layout": "IPY_MODEL_861140b2c5ff4ba7a3108345e18d3707"}}, "f3970deece314655b106677a604e82ad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f5b90c96d94f4f2ea7f0015645f9ed52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6d150846c07f4768898a94cf91c6f290", "IPY_MODEL_1fd2890752c04801ac1c18dfa4025b5b", "IPY_MODEL_8ecbfd6b13bf4a898aabf511b4780e24"], "layout": "IPY_MODEL_3b2cc4650c44418fbae7f5ef21b83131"}}, "f7eceef080aa49b393658306e7b1eeb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a2b99659c19049c3a20236ee35f2bcc9", "placeholder": "​", "style": "IPY_MODEL_a378ba5272c04401b53dffdd2d8ad4ed", "value": "vitnqs_config.py: 100%"}}, "fb9ff265fcbd48bbad4931702d59eeb1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "feb52ce513814bbd8ada812695fc1718": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb9ff265fcbd48bbad4931702d59eeb1", "placeholder": "​", "style": "IPY_MODEL_9d68301885f0446db22eb115d8ff948b", "value": " 200/200 [05:38&lt;00:00,  1.70s/it, Energy=-180.48+0.19j ± 0.90 [σ²=407.82]]"}}}}}, "nbformat": 4, "nbformat_minor": 0}