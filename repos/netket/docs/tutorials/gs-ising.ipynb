{"cells": [{"cell_type": "markdown", "id": "bcc1a161", "metadata": {"tags": []}, "source": ["# Ground-State: Ising model\n", "\n", "Author: <PERSON><PERSON><PERSON> (EPFL-CQSL)\n", "\n", "17 November, 2021\n", "\n", "[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/PhilipVinc/NetKet-lectures/blob/master/01_intro.ipynb)"]}, {"cell_type": "markdown", "id": "ae721781-2989-46c5-a92b-ca528b145e6c", "metadata": {}, "source": ["In this Tutorial we will introduce the open-source package [NetKet](https://www.netket.org/), and show some of its functionalities. We will guide you through a relatively simple quantum problem, that however will be a good guide also to address more complex situations.\n", "\n", "Specifically, we will study the transverse-field Ising model in one dimension:\n", "\n", "$$\n", "\\mathcal{H}=\\Gamma\\sum_{i}\\sigma_{i}^{(x)}+V\\sum_{i}\\sigma_{i}^{(z)}\\sigma_{i+1}^{(z)}.\n", "$$\n", "\n", "In the following we assume periodic boundary conditions and we will count lattice sites starting from $ 0 $, such that $ i=0,1\\dots L-1 $ and $i=L=0$."]}, {"cell_type": "markdown", "id": "39bddf19", "metadata": {"tags": []}, "source": ["## 0. Installing Netket\n", "\n", "If you are executing this notebook on Colab, you will need to install netket. You can do so by running the following cell:"]}, {"cell_type": "code", "execution_count": 1, "id": "43e97750", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/Documents/pythonenvs/netket_pro/bin/python: No module named pip\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --quiet netket"]}, {"cell_type": "markdown", "id": "f15de72d-9454-4873-a88f-a02ec10459ed", "metadata": {}, "source": ["We also want make to sure that this notebook is running on the cpu.\n", "You can edit the field by changing \"cpu\" to \"gpu\" to make it run on the GPU if you want.\n", "But you'll need to use much larger systems to see a benefit in the runtime.\n", "For systems with less than 40 spins GPUs slow you down remarkably."]}, {"cell_type": "code", "execution_count": 2, "id": "f752288d-caf4-4f53-a104-d4b2efe6e0fd", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\""]}, {"cell_type": "markdown", "id": "7f424667", "metadata": {}, "source": ["You can check that the installation was successful doing"]}, {"cell_type": "markdown", "id": "d8b7effe", "metadata": {}, "source": ["## 1. Defining The Hamiltonian\n", "\n", "The first step in our journey consists in defining the Hamiltonian we are interested in.\n", "For this purpose, we first need to define the kind of degrees of freedom we are dealing with (i.e. if we have spins, bosons, fermions etc).\n", "This is done specifying the <PERSON>lbert space of the problem. For example, let us concentrate on a problem with 20 spins-1/2.\n", "\n", "When building hilbert spaces, in general, the first argument determines the size of the local basis and the latter defines how many modes you have."]}, {"cell_type": "code", "execution_count": 3, "id": "65e5273d", "metadata": {}, "outputs": [], "source": ["import netket as nk\n", "\n", "N = 20\n", "hi = nk.hilbert.Spin(s=1 / 2, N=N)"]}, {"cell_type": "markdown", "id": "3d889a00-c71a-450e-8ba5-af5c693706a5", "metadata": {}, "source": ["NetKet's Hilbert spaces define the computational basis of the calculation, and are used to label and generate elements from it.\n", "The standard Spin-basis implicitly selects the `z` basis and elements of that basis will be elements $ v\\in\\{\\pm 1\\}^N $.\n", "\n", "It is possible to generate random basis elements through the function `random_state(rng, shape, dtype)`, where the first argument must be a jax RNG state (usually built with `jax.random.key(seed)`, second is an integer or a tuple giving the shape of the samples and the last is the dtype of the generated states."]}, {"cell_type": "code", "execution_count": 4, "id": "4b2bf39c-ae7f-4cb0-b141-093c448138e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([[-1, -1, -1, -1, -1, -1,  1,  1,  1,  1,  1, -1,  1,  1, -1,  1,\n", "         1, -1, -1,  1],\n", "       [ 1,  1,  1, -1, -1,  1,  1,  1, -1, -1,  1,  1,  1, -1, -1,  1,\n", "        -1,  1,  1,  1],\n", "       [ 1, -1,  1, -1, -1,  1,  1,  1,  1, -1,  1,  1, -1,  1, -1, -1,\n", "         1,  1, -1, -1]], dtype=int8)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import jax\n", "\n", "hi.random_state(jax.random.key(0), 3)"]}, {"cell_type": "markdown", "id": "fc2e67ac", "metadata": {}, "source": ["We now need to specify the Hamiltonian. For this purpose, we will use NetKet's ```LocalOperator``` (see details [here](https://www.netket.org/docs/_generated/operator/netket.operator.LocalOperator.html#netket.operator.LocalOperator)) which is the sum of arbitrary k-local operators.\n", "\n", "In this specific case, we have a 1-local operator, $ \\sigma^{(x)}_i $ and a 2-local operator, $ \\sigma^{(z)}_i \\sigma^{(z)}_j $. We then start importing the pauli operators.\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b018e4d4", "metadata": {}, "outputs": [], "source": ["from netket.operator.spin import sigmax, sigmaz"]}, {"cell_type": "markdown", "id": "89c35665", "metadata": {}, "source": ["We now take $ \\Gamma=-1 $ and start defining the 1-local parts of the Hamiltonian"]}, {"cell_type": "code", "execution_count": 6, "id": "b35bde0c", "metadata": {}, "outputs": [], "source": ["Gamma = -1\n", "H = sum([Gamma * sigmax(hi, i) for i in range(N)])"]}, {"cell_type": "markdown", "id": "5b41d740", "metadata": {}, "source": ["Here we have used a list comprehension to (mildly) show off our ability to write one-liners, however you could have just added the terms one by one in an explicit loop instead (though you'd end up with a whopping 3 lines of code).\n", "\n", "We now also add the interaction terms, using the fact that NetKet automatically recognizes products of local operators as tensor products."]}, {"cell_type": "code", "execution_count": 7, "id": "eb378d9c", "metadata": {}, "outputs": [], "source": ["V = -1\n", "H += sum([V * sigmaz(hi, i) * sigmaz(hi, (i + 1) % N) for i in range(N)])"]}, {"cell_type": "markdown", "id": "59025d8c-dede-496f-b2f0-6ad63966be1d", "metadata": {}, "source": ["In general, when manipulating NetKet objects, you should always assume that you can safely operate on them like\n", "you would in mathematical equations, therefore you can sum and multiply them with ease."]}, {"cell_type": "markdown", "id": "65581d8b", "metadata": {}, "source": ["## 2. Exact Diagonalization\n", "\n", "Now that we have defined the Hamiltonian, we can already start playing with it. For example, since the number of spins is large but still manageable for exact diagonalization, we can give it a try.\n", "\n", "In NetKet this is easily done converting our Hamiltonian operator into a sparse matrix of size $ 2^N \\times 2^ N $."]}, {"cell_type": "code", "execution_count": 8, "id": "d36dd96c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1048576, 1048576)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["sp_h = H.to_sparse()\n", "sp_h.shape"]}, {"cell_type": "markdown", "id": "e0b786e6", "metadata": {}, "source": ["Since this is just a regular scipy sparse matrix, we can just use any sparse diagonalization routine in there to find the eigenstates. For example, this will find the two lowest eigenstates"]}, {"cell_type": "code", "execution_count": 9, "id": "1123bc8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eigenvalues with scipy sparse: [-25.49098969 -25.41240947]\n"]}], "source": ["from scipy.sparse.linalg import eigsh\n", "\n", "eig_vals, eig_vecs = eigsh(sp_h, k=2, which=\"SA\")\n", "\n", "print(\"eigenvalues with scipy sparse:\", eig_vals)\n", "\n", "E_gs = eig_vals[0]"]}, {"cell_type": "markdown", "id": "8e51cc79", "metadata": {}, "source": ["## 3. <PERSON>-<PERSON>\n", "\n", "We now would like to find a variational approximation of the ground state of this Hamiltonian. As a first step, we can try to use a very simple mean field ansatz:\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{mf}} \\rangle = \\Pi_{i=1}^{N} \\Phi(\\sigma^{z}_i), $$\n", "\n", "where the variational parameters are the single-spin wave functions, which we can further take to be normalized:\n", "\n", "$$ |\\Phi(\\uparrow)|^2 + |\\Phi(\\downarrow)|^2 =1, $$\n", "\n", "and we can further write $ \\Phi(\\sigma^z) = \\sqrt{P(\\sigma^z)}e^{i \\phi(\\sigma^z)}$. In order to simplify the presentation, we take here and in the following examples the phase $ \\phi=0 $. In this specific model this is without loss of generality, since it is known that the ground state is real and positive.\n", "\n", "For the normalized single-spin probability we will take a sigmoid form:\n", "\n", "$$ P(\\sigma_z; \\lambda) = 1/(1+\\exp(-\\lambda \\sigma_z)), $$\n", "\n", "thus depending on the real-valued variational parameter $\\lambda$.\n", "In NetKet one has to define a variational function approximating the **logarithm** of the wave-function amplitudes (or density-matrix values).\n", "We call this variational function _the Model_ (yes, caps on the M).\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{mf}} \\rangle = \\exp\\left[\\mathrm{Model}(\\sigma^{z}_1,\\dots \\sigma^{z}_N ; \\theta ) \\right], $$\n", "\n", "where $\\theta$ is a set of parameters.\n", "In this case, the parameter of the model will be just one: $\\gamma$.\n", "\n", "The Model can be defined using one of the several *functional* jax frameworks such as Jax/Stax, Flax or Haiku.\n", "NetKet includes several pre-built models and layers built with [Flax](https://github.com/google/flax), so we will be using it for the rest of the notebook."]}, {"cell_type": "code", "execution_count": 10, "id": "967e1611", "metadata": {}, "outputs": [], "source": ["# numerical operations in the model should always use jax.numpy\n", "# instead of numpy because jax supports computing derivatives.\n", "# If you want to better understand the difference between the two, check\n", "# https://flax.readthedocs.io/en/latest/notebooks/jax_for_the_impatient.html\n", "import jax.numpy as jnp\n", "\n", "# Flax is a framework to define models using jax\n", "\n", "# Flax has two 'neural network' libraries. THe  first one is `flax.linen`\n", "# which has been in use since 2020, and most examples use it. The new one,\n", "# nnx, is somewhat simpler to use, and it's the one we will use here.\n", "from flax import nnx\n", "\n", "\n", "# An NNX model must be a class subclassing `nnx.Module`\n", "class MF(nnx.Module):\n", "    \"\"\"\n", "    A class implementing a uniform mean-field model.\n", "    \"\"\"\n", "\n", "    # The __init__ function is used to define the parameters of the model\n", "    # The RNG argument is used to initialize the parameters of the model.\n", "    def __init__(self, *, rngs: nnx.Rngs):\n", "        # To generate random numbers we need to extract the key from the\n", "        # `rngs` object.\n", "        key = rngs.params()\n", "        # We store the log-wavefunction on a single site, and we call it\n", "        # `log_phi_local`. This is a variational parameter, and it will be\n", "        # optimized during training.\n", "        #\n", "        # We store a single real parameter, as we assume the wavefunction\n", "        # is normalised, and initialise it according to a normal distibution.\n", "        self.log_phi_local = nnx.Param(jax.random.normal(key, (1,)))\n", "\n", "    # The __call__(self, x) function should take as\n", "    # input a batch of states x.shape = (n_samples, L)\n", "    # and should return a vector of n_samples log-amplitudes\n", "    def __call__(self, x: jax.Array):\n", "\n", "        # compute the probabilities\n", "        p = nnx.log_sigmoid(self.log_phi_local * x)\n", "\n", "        # sum the output\n", "        return 0.5 * jnp.sum(p, axis=-1)"]}, {"cell_type": "markdown", "id": "c1ccb969-487e-43e6-ac87-50cde68c68e1", "metadata": {}, "source": ["Here we use ``flax.nnx`` to construct the model, which is a more modern and simpler neural-network library when compared to ``flax.linen``, which is what was used in NetKet for many years. In fact, you can use both those libraries with NetKet! Just be careful that the two are quite different.\n", "\n", "To actually create a variational state the easiest way is to construct a Monte-Carlo-sampled Variational State.\n", "To do this, we first need to define a sampler.\n", "\n", "In `netket.sampler` several samplers are defined, each with its own peculiarities.\n", "In the following example, we will be using a simple sampler that flips the spins in the configurations one by one.\n", "\n", "You can read more about how the sampler works by checking the documentation with `?nk.sampler.MetropolisLocal`"]}, {"cell_type": "code", "execution_count": null, "id": "6fe71478", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "c2cb0ac3-bc4c-4743-adec-03bb4e2d7c55", "metadata": {}, "outputs": [], "source": ["# Create an instance of the model, using the seed 0\n", "mf_model = MF(rngs=nnx.Rngs(0))\n", "# If you want to learn more about how to use this model, check the nnx tutorial\n", "# https://flax.readthedocs.io/en/latest/nnx_basics.html\n", "\n", "# Create the local sampler on the hilbert space\n", "sampler = nk.sampler.MetropolisLocal(hi)\n", "\n", "# Construct the variational state using the model and the sampler above.\n", "# n_samples specifies how many samples should be used to compute expectation\n", "# values.\n", "vstate = nk.vqs.MCState(sampler, mf_model, n_samples=512)"]}, {"cell_type": "markdown", "id": "2e9925a2-fbfb-4580-bba9-d8e233ab798c", "metadata": {}, "source": ["You can play around with the variational state: for example, you can compute expectation values yourself or inspect it's parameters.\n", "The parameters are stored as a set of nested dictionaries. In this case, the single parameter $\\lambda$ is stored inside a (frozen) dictionary.\n", "(The reason why the dictionary is frozen is a detail of Flax)."]}, {"cell_type": "code", "execution_count": 12, "id": "578fae25-6b39-41c1-a25e-51de791b7aa3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'log_phi_local': Array([1.88002989], dtype=float64)}\n"]}], "source": ["print(vstate.parameters)"]}, {"cell_type": "markdown", "id": "e6f23399-7a3c-43c1-8d1c-14f8d5f13ea3", "metadata": {}, "source": ["With a variational state, you can compute expectation values of operators.\n", "Notice that it also provides an error estimate and the variance of this estimator.\n", "If you are close to an eigenstate of the operators, the variance should be 0 or close to 0.\n", "\n", "The $\\hat{R}$ value is a Monte-Carlo convergence estimator. It will be $\\hat{R}\\approx 1$ if the Markov Chain is converged, while it will be far from $1$ if your sampling has not converged.\n", "As a rule of thumb, look out for $|\\hat{R}| > 1.1$, and check if your sampling scheme or sampler is consistent with your system specification.\n", "\n", "You can also investigate the correlation time of your estimator, $\\tau$. If $\\tau\\gg1$ then your samples are very correlated and you most likely have some issues with your sampling scheme."]}, {"cell_type": "code", "execution_count": 13, "id": "0ea20c03-dcfa-4cda-8f2f-b1d86a95872b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-24.30 ± 0.10 [σ²=5.43, R̂=1.0393]\n"]}], "source": ["E = vstate.expect(H)\n", "print(E)"]}, {"cell_type": "markdown", "id": "d393a326-8afb-47d5-ac46-9c6eaaaf1963", "metadata": {}, "source": ["You can also access the fields individually:\n", "Note that if you run your calculation using MPI on different processes/machines, those estimators will return the mean, error and estimators of all the samples across all the processes."]}, {"cell_type": "code", "execution_count": 14, "id": "df25e787-deb1-4c3c-a6a8-5150948a6f3f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean                  : -24.30451755271647\n", "Error                 : 0.10301052633799812\n", "Variance              : 5.432918290652879\n", "Convergence indicator : 1.0392993804275164\n", "Correlation time      : 0.0\n"]}], "source": ["print(\"Mean                  :\", E.mean)\n", "print(\"Error                 :\", E.error_of_mean)\n", "print(\"Variance              :\", E.variance)\n", "print(\"Convergence indicator :\", E.R_hat)\n", "print(\"Correlation time      :\", E.tau_corr)"]}, {"cell_type": "code", "execution_count": 15, "id": "c161f0a6-3f95-4f6e-9dd2-d2a71527efce", "metadata": {}, "outputs": [{"data": {"text/plain": ["(-24.30 ± 0.10 [σ²=5.43, R̂=1.0393],\n", " {'log_phi_local': Array([-1.94709886], dtype=float64)})"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["vstate.expect_and_grad(H)"]}, {"cell_type": "markdown", "id": "1b5b6302", "metadata": {"tags": []}, "source": ["## 4. Variational Monte Carlo"]}, {"cell_type": "markdown", "id": "8ab77267-892e-48d2-97e2-eee6c8eae796", "metadata": {}, "source": ["We will now try to optimise $ \\lambda $ in order to best approximate the ground state of the hamiltonian.\n", "\n", "At first, we'll try to do this by ourselves by writing the training loop, but then we'll switch to using a pre-made\n", "solution provided by netket for simplicity."]}, {"cell_type": "markdown", "id": "d103e357-f079-4816-8323-d9a606748a04", "metadata": {}, "source": ["### 4a. DIY Optimisation loop\n", "\n", "The optimisation (or training) loop must do a very simple thing: at every iteration it must compute the energy and it's gradient, then multiply the gradient by a certain learning rate $\\lambda = 0.05$ and lastly it must update the parameters with this rescaled gradient.\n", "You can do so as follows:"]}, {"cell_type": "code", "execution_count": 16, "id": "ef4b0687-1f78-4c2e-9f17-16c3fac2f064", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [00:01<00:00, 88.44it/s]\n"]}], "source": ["from tqdm import tqdm\n", "\n", "energy_history = []\n", "n_steps = 100\n", "\n", "# For every iteration (tqdm is just a progress bar)\n", "for i in tqdm(range(n_steps)):\n", "    # compute energy and gradient of the energy\n", "    E, E_grad = vstate.expect_and_grad(H)\n", "    # log the energy to a list\n", "    energy_history.append(E.mean.real)\n", "    # equivalent to vstate.parameters - 0.05*E_grad , but it performs this\n", "    # function on every leaf of the dictionaries containing the set of parameters\n", "    new_pars = jax.tree_util.tree_map(\n", "        lambda x, y: x - 0.05 * y, vstate.parameters, E_grad\n", "    )\n", "    # actually update the parameters\n", "    vstate.parameters = new_pars"]}, {"cell_type": "markdown", "id": "7d22ec7f-4479-47d4-a921-b568a4280c4f", "metadata": {}, "source": ["We now can plot the energy during those optimisation steps:\n"]}, {"cell_type": "code", "execution_count": 17, "id": "747f2a0e-3408-443d-8f9b-13f70db15231", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x121cb4cd0>]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAAGdCAYAAAAfTAk2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAZepJREFUeJztnQd4HOW1/j/JsuSi5t57wcbG2BhwodkBgsGhJMQkELi0PyGJkxAgJEAgXMgNhkAggXApuQGSSwsmGAOBgK9tWjAugHvBxhXLvUhykyxp/8/77ZzZ2dHs7qy0M7Pl/T2PrGJpNZrd+eb9znnPOXmhUCikCCGEEEKyiPygD4AQQgghJNVQ4BBCCCEk66DAIYQQQkjWQYFDCCGEkKyDAocQQgghWQcFDiGEEEKyDgocQgghhGQdFDiEEEIIyToKVA7S0NCgKioqVElJicrLywv6cAghhBDiAvQmrq6uVt27d1f5+fFjNDkpcCBuevXqFfRhEEIIIaQJbNmyRfXs2TPu9+SkwEHkRk5QaWlp0IdDCCGEEBdUVVXpAIXcx+ORkwJH0lIQNxQ4hBBCSGbhxl5CkzEhhBBCsg4KHEIIIYRkHRQ4hBBCCMk6KHAIIYQQknVQ4BBCCCEk66DAIYQQQkjWQYFDCCGEkKyDAocQQgghWQcFDiGEEEKyDgocQgghhGQdFDiEEEIIyToocAghhBCSdeTksE2vWLRxr3pr2XY1uEux+u7JvYM+HEIIISRnYQQnhazZUa2e/vcGNXv1zqAPhRBCCMlpKHBSSHFROCB24Ehd0IdCCCGE5DQUOCmktFVL/b665mjQh0IIIYTkNBQ4KaS4FSM4hBBCSDpAgeNFiqqGAocQQggJEgqcFFJiRHCqGMEhhBBCAoUCJ4WUFIU9OLV1Daqmrj7owyGEEEJyFgqcFNK2qIX5MX04hBBCSHBQ4KSQghb5qk1hWOTQh0MIIYQEBwWOR0bjakZwCCGEkMCgwPHIaEyBQwghhAQHBU6KKTaa/TFFRQghhAQHBU6KKTF74bCbMSGEEBIUFDgphikqQgghJHgocFIMTcaEEEJI8FDgpJgSGbhJgUMIIYQEBgWOVwM36cEhhBBCAoMCJ8WYJmNGcAghhJDAoMBJMTQZE0IIIcFDgeNRiqqafXAIIYSQwKDASTGsoiKEEEKChwLHoyoqmowJIYSQ4KDA8ciDQ5MxIYQQEhwUOB6ajEOhUNCHQwghhOQkFDgeeXDqGkKqpq4h6MMhhBBCchIKnBTTtrBA5eWFP646Qh8OIYQQEgQUOCkmPz9PFRfSh0MIIYQECQWOp+MaKHAIIYSQIKDA8QB2MyaEEEKChQLHA9jsjxBCCAkWChwPKDaa/VXTZEwIIYQEAgWOl83+6MEhhBBCAoECxwNKjBQVq6gIIYSQYKDA8dJkzAgOIYQQEggUOB5QXCQeHAocQgghJKsEzsaNG9W1116r+vXrp1q3bq0GDBig7rrrLlVbW+v4/evWrVMlJSWqvLzc9e/Ys2eP6tmzp8rLy1P79+9X6QL74BBCCCFZKnBWr16tGhoa1JNPPqlWrFihHn74YfXEE0+o22+/vdH3Hj16VF166aXqtNNOS+p3QECNGDFCpW8fHFZREUIIIVklcCZNmqSeeeYZ9fWvf131799fXXDBBernP/+5evXVVxt97x133KGGDBmiLrnkEteP//jjj+uoDR4z3aDJmBBCCAmW8J3YJyorK1X79u2jvjZnzhw1ffp0tXjxYkfx48TKlSvVPffco+bPn6/Wr1+f8Ptramr0m1BVVaX8SFHRg0MIIYRkuckYHptHH31UXX/99VEemquuuko9++yzqrS01NXjQKggnfXAAw+o3r17u/qZadOmqbKyMvOtV69eyktKjEZ/9OAQQgghGSJwbr31Vm3qjfcG/42VrVu36pTVlClT1HXXXWd+HR9fdtll6vTTT3f9+2+77TY1dOhQdfnllyf1M4geyduWLVuUP6Ma6MEhhBBCgiAvFAqFkvmBXbt26chLPOC5KSws1B9XVFSoCRMmqLFjx+pITX5+RFOhYurAgQPm5zgUGJNbtGihnnrqKXXNNdc0euyRI0eqZcuWaSFl/5lf/epX6u677074NyBFhUgOxI7byFEy7Kw6ok6+d7bKz1Pqy3vPM4+VEEIIIU0nmft30h6cTp066Tc3IHIzceJENXr0aG04toobMG/ePFVfX29+PnPmTHX//ferjz/+WPXo0cPxMf/xj3+ow4cPm58vXLhQC6EPP/xQl6KnA+LBaQgpdai2XrU1IjqEEEII8QfP7rwQN4jc9OnTRz344IM68iN07dpVv0eqycqiRYu0CBo+fLj5tRkzZugUk6S97CJm9+7d5mMl00PHS1q3bKFa5Oep+oaQNhpT4BBCCCH+4tmdd9asWdpYjDc047OSTFYMYag1a9aoTAIpKfhwKg8fVQdq4MNpFfQhEUIIITlF0h6cbMBrDw445b45auv+w2rGj8arUb3befI7CCGEkFwimfs3Z1F53s2YpeKEEEKI31DgeCxw2AuHEEII8R8KHI9gLxxCCCEkOChwPO5mzBQVIYQQ4j8UOB73wmGKihBCCPEfChyPoMmYEEIICQ4KHI8oMTw4ByhwCCGEEN+hwPHYZMwUFSGEEOI/FDgem4yrWEVFCCGE+A4FjkfQZEwIIYQEBwWOxx4cmowJIYQQ/6HA8ThFRZMxIYQQ4j8UOB7BFBUhhBASHBQ4Psyiqm/IuYHthBBCSKBQ4HhcJg4O1jKKQwghhPgJBY5HFBXkq5Yt8vTHNBoTQggh/kKB4xF5eXk0GhNCCCEBQYHjSzdjNvsjhBBC/IQCxwejcRUjOIQQQoivUOD4EcGhwCGEEEJ8hQLHhwgOTcaEEEKIv1DgeIhpMqYHhxBCCPEVChwPYYqKEEIICQYKHB/GNdBkTAghhPgLBY5P4xoIIYQQ4h8UOB5SwhQVIYQQEggUOD6YjKtpMiaEEEJ8hQLHQ2gyJoQQQoKBAscHkzH74BBCCCH+QoHjR6M/mowJIYQQX6HA8ZCSIk4TJ4QQQoKAAseHCM7ho/XqaH1D0IdDCCGE5AwUOB7S1jAZg4NMUxFCCCG+QYHjIYUF+aqoIHyKaTQmhBBC/IMCx69eOBQ4hBBCiG9Q4PhVSXWEzf4IIYQQv6DA8ZjS1uEITuVhChxCCCHELyhwPKacAocQQgjxHQocjymjwCGEEEJ8hwLHYyhwCCGEEP+hwPGY8jYUOIQQQojfUOD4FMHZf4gChxBCCPELChyPYRUVIYQQ4j8UOD5VUe2nwCGEEEJ8gwLHpxRVFQUOIYQQ4hsUOB5T3qZQv2eKihBCCPEPChwfy8RDoVDQh0MIIYTkBBQ4Pgmc+oaQOlDDgZuEEEKIH1DgeEyrlvmqsCB8mpmmIoQQQvyBAsdj8vLy2AuHEEII8RkKHB9LxVlJRQghhPgDBY4PcB4VIYQQ4i8UOD5gpqgocAghhBBfoMDxgTIO3CSEEEJ8hQLHB2gyJoQQQvyFAscH6MEhhBBC/IUCxwdYRUUIIYT4CwWOjx6c/Ydrgz4UQgghJCegwPGB8tYcuEkIIYT4CQWOD5TSg0MIIYT4CgWOD7CKihBCCPEXChwfKDc8ONVH6vRUcUIIIYR4CwWOjxEcwEoqQgghxHsocHygZYt81aawhf6YPhxCCCHEeyhwfO6FQ4FDCCGEeA8Fjs+VVBy4SQghhHgPBY7PRmNGcAghhBDvocDxCc6jIoQQQvyDAsdvgXOI4xoIIYQQr6HA8YnyNhzXQAghhGS0wNm4caO69tprVb9+/VTr1q3VgAED1F133aVqa52jF+vWrVMlJSWqvLzc1eM/++yzasSIEapVq1aqc+fOaurUqSrdYYqKEEII8Y8CLx509erVqqGhQT355JNq4MCBavny5eq6665TBw8eVA8++GDU9x49elRdeuml6rTTTlMff/xxwsd+6KGH1O9//3v1wAMPqDFjxujHhKBKdziugRBCCMlwgTNp0iT9JvTv31+tWbNGPf74440Ezh133KGGDBmizjzzzIQCZ9++ffr733jjDf39AqI56Q4jOIQQQkgWenAqKytV+/bto742Z84cNX36dPXYY4+5eoxZs2bpyNDWrVvV0KFDVc+ePdUll1yitmzZEvfnampqVFVVVdSb31DgEEIIIVkmcOCxefTRR9X1119vfm3Pnj3qqquu0n6a0tJSV4+zfv16LXDuvfde9Yc//EG98sorau/everss8+O6e8B06ZNU2VlZeZbr169lN+wDw4hhBCSpgLn1ltvVXl5eXHf4L+xgmgL0lVTpkzRPhwBH1922WXq9NNPd/37IW7g2XnkkUfUOeeco8aOHatefPFFtXbtWjV37tyYP3fbbbfpCJK8JYr4eAEjOIQQQkiaenBuvvlmHXWJB/w2QkVFhZo4caIaP368euqppxqlp15//XXTkxMKhbSAKSgo0N97zTXXNHrsbt266ffHHnus+bVOnTqpjh07qs2bN8c8pqKiIv0WJCJwDtXWq9q6BlVYwAp9QgghJC0EDsQE3tyAyA3EzejRo9Uzzzyj8vOjb+jz5s1T9fX15uczZ85U999/vzYa9+jRw/ExTznlFP0ehmX4bwBSVLt371Z9+vRR6UxJq5YqLw9CLhzF6VQSrOAihBBCshlPwggQNxMmTFC9e/fWEZpdu3ap7du36zcBJuHhw4ebbxA1EEH4uF27dvp7ZsyYoSushMGDB6sLL7xQ3XDDDVoIofz8yiuv1N8DMZXOtMjPUyVFYT3JNBUhhBCSgWXiqHaCsRhvEmkRkIpyC/wyiNZY+dvf/qZuvPFGNXnyZC2IzjjjDPWvf/1LtWwZTgGlezfjqiN1qvIwxzUQQgghXpIXSkZxZAkoE0c1FQSU2wquVHD+ox+pZVsr1dNXnai+NqSLb7+XEEIIybX7N52uPsJuxoQQQog/UOD4SBl74RBCCCG+QIHjI+yFQwghhPgDBY6PlDNFRQghhPgCBU4AEZwqRnAIIYQQT6HA8RGmqAghhBB/oMAJYODmfgocQgghxFMocHyklBEcQgghxBcocHyEKSpCCCHEHyhwfB7VACoPHU1qZAUhhBBCkoMCJ4AITm19gzpytCHowyGEEEKyFgocH2lb2EIV5Ofpj/dz4CYhhBDiGRQ4PpKXl0cfDiGEEOIDFDg+YwocdjMmhBBCPIMCJ6CBm+yFQwghhHgHBY7PMEVFCCGEeA8Fjs9wHhUhhBDiPRQ4PsOJ4oQQQoj3UOD4DFNUhBBCiPdQ4PhMmXQzpsAhhBBCPIMCJ6AIDquoCCGEEO+gwAnMg8NOxoQQQohXUOD4TOfSIv1+R9WRoA+FEEIIyVoocHymW1lr/X5ndY06Ws+Bm4QQQogXUOD4TIe2haplizwVCim1q7om6MMhhBBCshIKHJ/Jz89TXUpb6Y+3VTJNRQghhHgBBU4AdCsTgXM46EMhhBBCshIKnADoavhwtjOCQwghhHgCBU4AdDcjOBQ4hBBCiBdQ4ARAV0PgMIJDCCGEeAMFToAenAp6cAghhBBPoMAJAHpwCCGEEG+hwAkwgoNmf3Vs9kcIIYSkHAqcAOhYXKQK8vNUfUNI7T7AmVSEEEJIqqHACYAWUc3+6MMhhBBCUg0FTsCVVCwVJ4QQQlIPBU5AUOAQQggh3kGBExDdjBTVdqaoCCGEkJRDgRMQ3crDpeKM4BBCCCGphwIn4FJx9sIhhBBCUg8FTkDQg0MIIYR4BwVOwBGcHVVHdD8cQgghhKQOCpyA6FRcpPLzlKprCKk9B2qCPhxCCCEkq6DACYiCFvmWZn9MUxFCCCGphAInLXw4LBUnhBBCUgkFThr4cBjBIYQQQlILBU6AdC0N98JhqTghhBCSWihwAoQRHEIIIcQbKHDSwIPDCA4hhBCSWihwAqR7eVjgVNBkTAghhKQUCpwA6VrW2mz218Bmf4QQQkjKoMAJkM4lRSovT6mj9SG152Bt0IdDCCGEZA0UOAHSskW+7mgM6MMhhBBCUgcFTtpUUtGHQwghhKQKCpyA6Wb4cFgqTgghhKQOCpy0GdeQnMDBBPIrn16gpj7/mUdHRgghhGQuBUEfQK4jKartSaaoNuw+oN7/Ypf++PdH61Wrli08OT5CCCEkE2EEJ0MjOF/sOGB+XHO0IeXHRQghhGQyFDhp6sFZs71a7aquiflzX+yoNj8+Ulfv4RESQgghmQdTVGmTojqiQqGQqqlrUPe+tUr9bd4mNaRrifrXz053/Lm1lgjO4VoKHEIIIcQKBU7AdCkNC5za+gb10brd6u43Vqp1O8PiZfX2arX/UK0qb1MYN4Jz+CgFDiGEEGKFKaqAKSzIVx2NZn9X/GWBFjfocNy+bVjUrKioavQzR+sb1IbdB83Pj1DgEEIIIVFQ4KRRmgqcM6yLTkuN7d9ef758a2Wj79+4+6Cqs8yuYgSHEEIIiYYCJw34xohuqlNJkbr/4uPUE5eP1tGbYd3LYkZwrBVUgBEcQgghJBp6cNKA688YoN+sDOteqt8vr6iM678BR1gmTgghhETBCE6aIhEceG0O1tRF/d/andECh1VUhBBCSDQUOGkKUlZdSotUKKTUqm1VjimqNoXh7sXsgxM9woIQQgihwEljhhtRHKvRuLauQZuMrf/PCE6YB99Zo0be/a5avyvao0QIIST3oMBJY4b1aGw03mBUUJUUFah+Hdvqr9FkHGbump2quqZOLdy4N+hDIYQQko0CZ+PGjeraa69V/fr1U61bt1YDBgxQd911l6qtrXX8/nXr1qmSkhJVXl6e8LEXLlyozjzzTP297dq1U+ecc45asmSJykYiRuOqRgbjgV2KVWtJUdFkrNl7MPz6ijfighBCSG7gicBZvXq1amhoUE8++aRasWKFevjhh9UTTzyhbr/99kbfe/ToUXXppZeq0047LeHjHjhwQE2aNEn17t1bzZ8/X3300UdaGEHk4HGyjeFGBGftjmpVY/hs1hpdjgd3LjEniLMPjtJjLvZQ4BBCCPGyTBwiBG9C//791Zo1a9Tjjz+uHnzwwajvveOOO9SQIUN0VObjjz9OKJz27t2r7rnnHtWrVy/9NUSGRowYoTZt2qQGDhyosonuZa1UuzYt1b5DR9UX2w+o43qWabEDBnUpVgeM6ioKHKUO1tZrfxLYdYAChxBCch3fPDiVlZWqfftwd15hzpw5avr06eqxxx5z9RjHHHOM6tChg/rLX/6i012HDx/WHw8dOlT17dtXZRt5eXlmubj0w5EU1eAuJaq1EcGhB0epPRZRs7OKAocQQnIdXwQOPDaPPvqouv76682v7dmzR1111VXq2WefVaWlYa9JIpCOeu+999Rzzz2nvT3FxcXqX//6l3r77bdVQUHsYFRNTY2qqqqKessUhvUwfDhbK3WaauOeQxGBY3pwKHAkPQUYwSGEEJKUwLn11lt1VCHeG9JIVrZu3arTVVOmTFHXXXed+XV8fNlll6nTTz/d9e9HxAbm5VNOOUV98skn6t///rcaPny4mjx5sv6/WEybNk2VlZWZb5LeygSsIxtQQYU+LyWtCnSPnFYFhgeHZeJq7wGLwKEHhxBCcp6kPDg333yzjrrEA34boaKiQk2cOFGNHz9ePfXUU43SU6+//rrpyYFJFMZkRGLwvddcc02jx37hhRd0hda8efNUfn6++TVUU82cOVN997vfdTym2267Td10003m54jgZIrIGW5UUqHZ3+ptkfQUxGQrVlE1qqACh2rrtT+puIiTSAghJFdJ6g7QqVMn/eYGRG4gbkaPHq2eeeYZU5AIECn19ZHIAwTK/fffr43GPXr0cHzMQ4cO6cfBzV2QzyGOYlFUVKTfMpG+HdqqtoUttIn27eXb9NcGdynW78WDQ5NxdIpKojgUOIQQkrt44sGBuJkwYYIu50aEZteuXWr79u36TYAxGOkleYOogVjBx4jIgBkzZugKK+Hss89W+/btU1OnTlWrVq3SJehXX321jvpATGUj+fl56lgjijN39S79flDnEv2+Vcvw00cPTrTJGDBNRQghuY0nW9xZs2ZpYzHeevbsGfV/SEUlU3mF8nIBYueNN95Qd999txo3bpwWRKNGjdJG427duqlsBT6chRv3qdr6BjNFBVhF5ZyiAhQ4hBCS23gSwYFPB0LG6S3ez+zfv9/xcawgioMGf/he9MSZPXu2Gjt2rMpmpOGfICkqNvqLnaLaWX0ksGMhhBASPJxFlUEjG0BZ65Z60niUwGEVlRnB6dW+tX7PCA4hhOQ2FDgZwMDOxaqwIN+M3ojJ2uyDY3TwzWVE4BzTJSwGKXAIISS3ocDJAFq2yFdDuoZ9N4MM/43Vg4MRBeiPk6sgjbnbMBnLedpJgUMIITkNBU6GcMbgcHn+KQM6ml+TKiogwzhzEfS9qTGiWEO6hQUOIziEEJLbsFFIhvCTrw1S5x/fXQ3qHDYYA+lkLD6cNoUFOZ2eKirI132DAMc1EEJIbsMIToYAD450MLb2yMFNPdcrqaSCqkPbQtXZMGCjL04up+0IISTXocDJcKSSKpfHNew9GI7WtC8uVO3bFipoQGibPcbXCSGE5B4UOBkOm/0ptdsYtNmhbZEqaJGv3wP6cAghJHehwMlwpFQ8l1NUey0pKiB9gihwCCEkd6HAyXDEg5PLERwROEhPWQUOS8UJISR3ocDJlghODncz3mOkqODBAZ2KGcEhhJBchwInw5FS8XRNUW3Ze0jtqDrii8lYUlSdSylwCCEk16HAyZIITk0aVlEdqq1T5z3yobrgTx8lNUW+6WXiRdERHPbCIYSQnIUCJ0uqqNIxgrN57yFVfaRO7aiq0d2GfUtRicm4igKHEEJyFQqcDKfIGNeQjgJn677D5scHaup8q6KSZn+M4BBCSO5CgZPhpHMfnIr9EYGDSI4XwFwt4s5eRUUPDiGE5C4UOBlOOqeovtrvfQRHuhUXtshXxUUFUQIHvxM+IEIIIbkHBU6WjGpIR5Nxxf5I9dQBjyI44r/pUFxozumC0BHhxygOIYTkJhQ4GU4698HZuu+Q+fGBmqO+NPkDEDpMUxFCSG5DgZMlEZx0TFFFRXBq6j0tEbcKHECBQwghuQ0FTobTyqiiSjeTcW1dg9pRbU1RHfWlyZ8gvXA4roEQQnITCpwMJ11NxuhebO3t553JWDw4YUEjsJsxIYTkNhQ4GU66lol/ZemBA6q9EjjS5C9GBIcChxBCchMKnCzx4BxJsyoqaw8cL6uo7E3+Gnlw2OyPEEJyEgqcDCddTcZb7QLH4xSVPYIjKaqdFh8QIYSQ3IECJ8NJ1zJxieD0KG/tcQTHMBkbc6iETsWt9HumqAghJDehwMmSKqqauvSM4AzuUuypB2fvgehJ4vYU1e4DtaqhwbtJ5oQQQtITCpxsqaJKcQQHkY+6+obmC5yuJZ5FcGCsPmj83TJJXAh3NlaqviGk9h4KiyBCCCG5AwVOFpWJh6x12c1g7Y5qNebe/1M3vbykST+P45AU1RAROB5EcMR/07JFniox5lAJLVvkq/ZtwqKHaSpCCMk9KHAynCJD4CALc7Q+NQJnRUWVfrz3v9jVJNGEyiZUdSGCMqizdwJH0lMwGMscKivsZkwIIbkLBU6WRHBSWUklkZHKw0cb9bNJJj3VuaRItTOqm7xIUckk8fY2/41AgUMIIbkLBU6Gg/RMi/xw9KImRQJnnyFwwIqKyqR/XtJT3ctb68neoLa+IeVGaOmB09Hmv7ELHI5rIISQ3IMCJ8NBaqZVQb4nERywbGvyAkeiPj0sAseLKE6sLsYCIziEEJK7UOBkUy8cDyI4y7dWNXmKOAQOokttjONLtQ8nVpO/RuMa2M2YEEJyDgqcLKCoILXjGiT1A5ZvrUzaaLx1/yH9vke7cJM/ieJUpziCE2uSeOMIDrsZE0JIrkGBkwWkupuxmHfDH9eqHVU1TYrgdC8zBE6rAk8iOHtjTBIXOpeEuxnTg0MIIbkHBU4WkOqJ4vsOHdXviwxvD6I4TamikgiO9KhJuQcnUYqKHhxCCMlZKHCyaFxDKgQOOv/uMzr/junfQb9fnkQlFaJIEllBFZU1gnOw1huTccwUlRHZQWosVeKPEEJIZkCBkwWkcqL4/kO1Siw3pw3smLTRWKI3iNqUtW7psQcnfgSntHWBKmwRfonvptGYEEJyCgqcLBvX0FwkegNxMrJ3edK9cKw9cITiopYp9+Cgp448nn3QprWEXnrkYOgmIYSQ3IECJ4siOKmoorL2lhnarVSPW9hWecR1BEQiON3LwwZfUCIm4xRGcCR6U5CfpyM1saAPhxBCchMKnCwglSZjieBA4CC11K9j26SMxhU2g7E1RZXKCM6eBHOohI6GD4cpKkIIyS0ocLKAVJaJS2VSO2MS93E9yswBnG7Yus8hRdUq9R6cRBVUAiM4hBCSOhZu3Ksu/5/5at3OapXuUOBkAUUprKLaa6tMGt69LKkIjlkibhE4bc0ITrj8PBVs2HVAv+9piRQ5QYFDCCGp48UFm9VH63arVz7dqtIdCpwsNxkfqq2LGr2QiL2SojLMucN6lCZVKu4kcEo8SFGt3h7ePcAnFA+mqAgJBnRAT7YLOgmW/Ydq1ftf7FINDbGfN9ksbtpzUKU7FDhZLnC++djH6vQH5qqDLsWFWXptpKiGGRGcLXsPq0qjAWC8HjrbK4/E9uCkMEW1als4ZTaka3yBwwgOIf4DYXPJk/PU+X/6SNXVp2aEDPGeu99Yqa58eoGavXpnzO+RtXTjnvBInnSGAieLqqhqbFVUSFmt2VGtvS/bKsORlWR7y6BcvHf7Nq7KxXdWH1F1DSFd2SRjEqI8OCmK4EBI4e8CQ7uVxP1eRnD846FZX6j/nbcx6MMgaQCitQs37tM9tDbtTf8bIQmz1vDVrNsZtgA4IWspIjjpHqGjwMniCI4YcZMx+JoCx0hRgeEu01RSQdW1rJWeIu5VBAcXFkri0cG5T4dwlZfXERxcyPHCtrkOUpOPzF6r/vONlSmbiUYyF2s6+ss4N0uSXuww5g7GWi8RjZP7yqHaerUrzTeOFDhZQCujispuMt5teZEeaGKKypqmStTR+CuHCqqoPjgpiuCI/+aYLiVRQsoJafR3sLZe+5GayhV/WaC+/ocPdIPBWLz62Vdq6vOfqf/9ZJPriFm2sN34exFd+8KIrpHcxbqZ+XKXP14NCuvmUVffYEZnEI2P5dG0Bm02pXmaigInC2hlDMW0R3CsaRk3HhxEKZzGH0ipeKJKKpki3tMmcCSCA8WPG6Bf/hv53TKra3d107oZH61v0FUDCNvGC93+9p+r1D+XbVN3vrZcjZs2R01+5EOdthFfUjaz0zJxfvV296M9SHZiTUfHu2ZSxUPvrlEj7n5HfbZ5n+e/K1vZfSAiXnbGiODYIzsbd6e30ZgCJ4v74FgFjpsUFQRITV1DI4EzrHtYSKzffVBVH4ltNN66/5BjBEc8OKmK4qza5s5/A9AE0ExTNTGcat2NbtztvGOpPHzUDN2e0Ltcd4BG7yCkbX7y4mcq27EuiPL8kOwAG4rfvLkyqQhodATHe4Hz5rJt6mh9SC3Zst/z35UJbN5zSJ16/xz1tyQ8cTuqIhuxWCkq+9cZwSG+eXBEnAjW+UtuhIVEb4oK8lUbQzSBDsVFqntZ2DS8Mk7DP3mx92ofLXCKClqYQy9TIXAkQjAkQYm43WjcVB+OVRxujFEaKTuZziVF6tUfnaIW/eos9buLRyhk0GC23JKk0TKekExHrCFtibCR7AARyb98tEG9+tnWpnlwdh3w1IyK6s71Rhos1QN9M5W5a3Zqy8CT7693fe6tAmen5WMr9pl+sdbDdIECJ5umiceJ4Lgx+IrA6eAw/kD6zayNE26WF3tfB+OvRHGaazSuOnLU9PoMdZGiAp2KmxfBwe8UNsQIycrX+xqjLSAKLzmplxrbv4P+/I2lFa5/32Nz16kRd7+rPly7S2Vmiqo67asriPu+KJL2kR5XbrBe5xAdXppRF38VidpUHc6sjYFXyGZu6/7DriNoOywbQHgWnWwN8riorgWM4BD/BE4jD07TIjjtHMYf9O8UvnHLTslObV2DOaZB5ldZicyjat4CtMYwGCOiVNYmfJEloqORorKarpscwUkgcPrZxN0Fx3fX719f7F7gzPtyj86Fz14VuxdFOqeokK7bHmMHmEtgw5HpZvMP1u5WYpvbkYSXzLopAF/u9G6n/7nFd2P/vZkGKkTvfWtVVDSluRHV99a42yjZozZOPhwROCf2aWduatN5M0OBkwWIiTZeFZWbHjTx5jv161is32/Y7bwb2LLvkF4I2xa2MD0vzgKneZUOq7cll55KRQTHmi6KmaKS6JVN3J07vJtq2SJPRzXcVhdVGDfFeOnAdMO+GK6mD0fd8soSddr9czPqebQz19LwbVsSAse+ofLSh7PY4rupOpy5KSoIhRteWqye+mC9+uFznzarQaI1Hf/B2t2ufsZeDOGUppI19IQ+7bTPEJu/fQkawAYJBU6WeXCsvVqSTVHJSAeZQ2VFojKxUjQS2UBfGqfp3qlKUa0yIjhDuiY2GLuN4KCyK151lzWCg6iYkz9G/n579ApRpjMGd3IdxcEit82oRkNjxUzpvSOLIUr3wcoc9+Hg5oQIHBpfwg+RieCaQNt+IZmogv0690rg4Hr5fPP+rIjgoAJTxNpnm/erP81d1+THsm7m5q/f42pOoTVFFSuCI2soZgB2K20V956QDlDgZFEVFThi6dMSJXCSiOA4pagGGCmqLfsO63RUzBSNQ3oqeh5V8xYgMbAmmkHlNoKDvjZnPfS+mvLExzF/3i5o7JVUWGRRYRbr7z9f0lRLKhKGc5HekVQj8uCZ0AUWZfTy2jl9cMeoXkW5Cjpty/NovQFnEku/2q/T1tJqChEct+kIWW9kGK5XvXCw7uCayXSBg3Xod/9aoz8+uV97/R4VmJ9u2tusCE5+Xnjj+8n6Pa43KaXGZtQxRWWsoVhTpclqOs+kosDJAloVWASOMa4BNx1r6LC5ERyknZB+wq5us8NNV1I0fTqExzrYMcc1NCOCg2iGeHDclIgLkjJzGtewdscBvUhixxRrl2M/5g22Cxo3Afkep7//7GO76CgbztuSr9z1EhLcTnEPEjmvGNExfoAhcHI8gmMVNYu37Etrn0Is5hrejYnHdNbvIdiqXF6/khI/vle5p92MJeKBys9MrqJ67pPNen3AWvXs1Sepb47qoVP+SFklW1GJdVL8l2cY0WNrJC4WEqEbbvQ9c2r2J8IJx9m3Y5u0n0lFgZMF5OfnqUJbsz/7BPHqZkZwkHbqZ0RxnEKSEtWwe1Aae3CavgDB54NePfhbnSq1EkZwqmsa3WisofNYFRj2c2c3Gou4g/FZDN9W2hQWqLOO7eIqTWU3paKXjlte+3xrSmZB4Rwl0/NEKqhQjn+spWeSm7B4tmJtOIebjVT+ZaL/ZtLwrqrcMPS7bVopG6qRPcvNah4vOg2LkBxjVCtmYhUVIlCPzlmrP77p7MF6vbjnwmG63QZeN7+euSKpx9t3qFZvRPPylPrWCT1dCRxEkGRDLI1dd1kqI+V7JFoGgcMIDvF/HpWxiNjTMW5SQ3sP1sSM4FiNxusd8umJUlSp8OBIA7nBXYpVgdFXxw0dSwrN6BbSPlasXVatoW4rsoOS3kB2gbPBEHciAJ2Qaqo3l1bE9ftUGDcQsTElGnAqIGL3i1eWqjtnrmjWggNx85MXP1fH3/2u4/PshISyO5cW6T5AMKnjb3TqYIt5Zem0IKIh2m2vLk15tdNi48YLgznItA672L0vM6KHE47prLoafgu31XFyzfTu0Ea1M8TR+hgFCqmI4Jw+KBw5RIQpVrQM19LUFz5Lu+67/z13ndp/6Kga1LlYTRkdFiQlrVqqP3xnpB5FM+PzrWrmYvc9iOR6bN+mUJ1xTCf9GKh+jdeLSzYp6Fc2yPDR2VNUe4yoEF7TKBPva0SrGcEhvldSSYjSbLDnJkVlKPj2bRtXQYH+MYzG+J1S+RMrshLx4NQ133/jsv+NgB0R0mtOzf6sN+FY+XsJyw83ZnLZK6mksixeVAneFOS2sWjM3xA7H77N6DUy0gjtI4LjJr2B8HKtUXXRnJvpm0u36TfdFdbSXyQeEsqGuEGkTwzg9oZ/iApd8KeP1Hl//DBmIzG/efKDL9WLC7aoP81puqHTDqKn4smafFy3jPThvG+kp0b0LNO7dQzQtc4cS4Rc57juB3Qq9mRkAzZz8hqbcEw4FQNhjSivE8/P36z+uXSb+tu8TSpdgOh45uNw1PW284ZEbdxG92mvfvK1gfpjRHHizcGLlUYqbdVSd1YHH8Tpq2Vew6VFqktpkWOKSh4XkVpc5xKtT6cNix0KnCyL4JgCx3gxSldhLDiJbpR7jKhP+7bO/WXMXjg2gYOLFA+NNJQMt4yVonKTKktVB2M3Phx3EZzwMR/Xs8xxxyLpuVjRK+nmjJJx8MaS2GkqKcWdMLiz3nnB3+OmPNfq3WnqzRRN3e5+IxIOx67SDbL761TSKmpGmH1kA24uEN6Iov0jia64XiIpykUb96W88Rw2BBOHhP0rn2fYCAHpnYLoDTAjOJXuWi3IhgqRWxE4qTYaL6+o1FVqENb4HfCAxduoSNoeP5cuPPjuGl20Ma5/B9PrZOXHEwdqkYi1yW1TPavAifLhxOmHI1PEu5S2Up2N69gewbE/bu/2bcx1AmtHOkKBkyWI90NMxnIjl6gCduT2UQ72FIdEKmJFcOQGbm/2F+ni28axRBwUt2rZ7BSVVOYMTaJEPN64BpTyWqMxiVJUkpuG6LB+r/n3J/AFXTAynKZ6a9l2x0o0SeHox+rYRoes3fpwrCmWpgqcaW+tjmoO6VrgSIrKWPjEAG4fuvnyoi3mx9MXbUm58fZfy7cnPclcxCmqntDyPxV8vikslkb2Llcn9A43RFtZUZkxniSsBR8Yno2JRmTEjOC4TVEZGxlsbAYar2M3peJ4/f/4hc/Uuyu2u04DItqJdafU6K4bqxcOvCkAfYnSof0CCiZmGp68288b6rh2IqLTX86fywiYtdIJnDE4LJw+/nJPzHVHDMaI3sh1jOvfGjXabXtcRMYl2pOuHY0pcLK0m7G8GMUIlig9JBc/NkHShjuWwMFjW3dJ8UY0pMpkjLbhchE1J4JjFTioWoDwE2Ld4CSCIx4TIHl83KTl74/nwQEY24DjgDiKVbYp0ZpuZa3VMCMl5qaSyhrBQdg+WUMnuif/3RAgY/u3jyv47OyyhLetJfw4DhExuLlhJhdeX4g2IgqIz1MFztEPnvtUXfbn+a6FBF5T1hv2p5ubVpJrR6I1EDcok0ZUE6+zZAzjQfLppn1aoMBLNcIwCUciOIlTVHjO5TrXEZzObV3doPF8XPvXRTpFetPLSxL23fl8S/j1M8oQkVLeHKvqSAQ7ji0dZiihoR+YNKyrGR12YmCSKT57pGVY91Ltq8TfHSt9LREcRG9gKBdrg3W9tKaoBLm/pMP5dIICJ9tMxsbiLoYw3HTEfxIveiJjGsrbFOrUiBMwvslFYzXqbXCRoilppslYojfYMTh1Wk6EXJTWFJV9waiMsfOTBRP57L62CxrRC+T8ccp6tXMukRdwXqXHhT26AbCrlCqVbmWt1PAeYaHg5sYokR+AsL0YRN0AQfCrGcv0x98b01udOSRc8eU27ByJ4IRvgtix42+Fp0v+T6I3SHmcf3w4VffSws0qVUi3YDy/r3z6laufsS/KqRBceA4lsjBKT5XPUyN7tWs0UiCdkcaEEwaHDarREZzEKSpcDxKcKylqaaaoEOmMZbDH11ESLZ4a3IzveXNl3N8jkUrxq2F9ipeiskYklwcsNhFxFePw9Wf0j/u9pkBM0vTfyVirUWV7eoJycfHEIUWF16z8rDVNZUaGLJ3qxWjMCA7xpdmf7F7lxYgbu1nBVJNY4CQSD05pKhE7XkZwTP9NkgbjeBGcdbYFI9bCKBEciDTp/SBpKXnfs10bs1Q/HqYfwWE2z+6DNdoojEg1bigSwXFTSSUpKolyJ3MzxXBPRFQQnfrluUPMGV/7DyfnwZHoFqKJYkjHDQspj398Gl7MLzmxl/rOSb30x28t25ayxmzWxf/PH66PW6km2M3yizbuTclxIPqBDYd0dYbQsY8USGfeW234bwz/EEjGZCzXC8QRih/0tdEiX6fIrULcyu/+tVr936od+hr67TeH65+FZ+u9GF2gsRFAtBP6C0ZoUNq6IG6Kav/hiGBfEXB/qac/2qA3ImP6tTcjULFI1sMkEdVOFiEiPhxJPdrZYfyMpJxMgWMRtPbIEGAEhwRaRYXwuBtxYQqcNvEFzgAHo3GsOUxW2orJuIk3NJltNCSJBn+JTMYSwZEKL6eUDHw6UpWBHaL8jSLqTHEX5293Pn+Nd2MyogFCoWWLfLOnDBZyMYDHYqvxsyf3bZ+UDwcVEI+/96X+GL03EKUqN1KUbjw4iFiImJYUlTWNiMgb+qngvOO1eObQzjp1gygP/GLxDNdNFTjYTb7jwsOxwbhhnNQ3fINZsqX5PhlJAeCmKxUxInAyoZIKBQPwI0E4SOm1NUWFqFyicyQtKbAhQDQAYkU2RvZNhUT3njTSNQ98e4T63pg+6urxfc3qIaffh+aJ4JiupebagtcucBLNeAzxJwZtNMY688L8cPTyB2cMSPj9Vg+TG++QkxA51XguEQ12auBnNRlbNysilqI8OFERHKmkYgSH+OHBMW7Gu6MiOIkNvslGcGT3G56YfMR9ispFNVe8CM6xTfDfxDIZiycAZtBYAscqCvE3yLTwDcYFLedBIhaJiLcbkygM/DcAwlTOaaI0lfzsN0Z0M2+0bs7zgg179U5ydJ926pxhXaMaPbpJUaE5pDQVs+bmraXikp5C0zEIN9z0vnNiOIrz8sKI8bg5rLeJlSfe/zLh3y/PHdJmOHZEz5rbOVpEDIYRCvCxQDCg2V2i8ngc84PvrFH/82H4hu83//1euFweHamRrhbgy5NNVCJvjERwZGMFYvlw4EWT9OhPzxykLhzZQ3/8s7MH6zQtfHJOJfzic5L0VJTAcbiO7WJ9+VZ37RfwPb98Zam65Il5KasUgrhBJSH6eUl5ezxQrYQKMWy03Ji8ZY3rbBEieH3DiwMWbtgXx2RsCByzVDyRB0dSVIzgEF/KxMMDN0Ww4MXopgeNKXBilHnHavYnYxtg8JOGXk7IYocNiPiE3IJFRjw4xzShgio6glNrPqaIDKl0cRI4slhjccfN2R7BiVRQxfff2Evtcb7lnNuNwt3Lw4sMkChOPIGD/jKygEOkYMeMhclNebl4fmBklCoOM4LjIkUlu0GYGHF+zOM2hOj89XvNlv9ITwnfPKGHXrQxusLJj5QMqAyRmV3/ecEw3bZ/6VeVal6C+TvrLeJUhFFzfTgicEZZbrx47Q820lWJysXxPGPI4n/9c5V6cUHqPEpuwDX98qKwf+lnZw2K+j+8NiJG4/ivK9NgbBU4DsIer52pz3+mDdiTR3RTPzsz8jvxs3edP8zsVbRuZ7XzeTY2J1EpKoeNnKSnsE6hUR2udTfdpSHOYb5fsHGv+s/Xk+so7ASqkp7+9wb98fdPHxCz6tQKrisREol8OIhUyd/fqTiyjgAxjNtT3lg/ZJ2TFJVZKp4wRdUm7hDioKHAycIqKmnVDTpYUlTVKUhRWSM4EAnWDsbxLlZ0AZb/TtZojPApLkBrqDtZpD+PjGvATggLMR7z+F5lMXd+Eu4WA6OEZLFAoq+Gm/ScFZRW9igPR2jsnYLtERxrc8F4IXURRhCynUtbmWXablIi24ydm3gsgHhw8DcmColHDI3Ri6mkEnGe640IkYTaRXifNTRsZv57M6M4ENn4HTDTQ1iJkHri/dhRED0g1Tj/qH470UjtNceHgwX+C+NGbPdVuE1TWX//XTNX+NoB+fezvtDn8cwhnc3zYcVtqbhc3xK1jRY44XOO19Ut05fqCCCifb+fcrw2w1o5Z1gXfSwQQOjSDU8OTLIYQLnMmOkmTezCvy/8unW60e47GBkxIJukRNE6+IX+681V5uevLa5Qby/bppoDxqlgDYJYlO7mbnDbLFFECLxMpYbgE6RowV6AICIGa7TcKyT6IxsYiCDpAm8VODjnsramY5rKM4GzceNGde2116p+/fqp1q1bqwEDBqi77rpL1dbWRn0Pbor2t08++STuY2/evFlNnjxZtWnTRnXu3Fndcsstqq4uM4esedHoT2ZKodwP6t/NmAS3KSqES1sY4VLc3Nze4PG8NrXZn/Q2QZQEDfOagoRVkYbADkcWCuxAOhh9f5wEjtVgLGZu2ckiAiBN/5IRXhLFse/GZEwDQvOChJWlSsgJUxgZkZ9RSVTtyG48SuAYERxE8BMNL5R5NdZwuH680lZR7QYkJWVFzMZoRe+2S6sTch77G1Go607rr1NCMFTGOm/wkshOt0/7SARn0aZ9Te6RAg8Pzhmaa1pvAsk8JwuNHjoQq3it/vC5Tx09E6kGN3sICGxCfn7OMY7f4zaCY+2BI4i4FVH57McbtVhBtO3RS0c5znDDc3n3hcP02oZhuBizcOXTC9TFj8/TGzlck/2NiLK1TNzJZFxpRHCQdnOzaYAAvvXVZfpvgTgVr8yvXlvuOLTXDXhdSWn4Naf2dVWUILjtJWTtgZNn23BKHy97d3Rrekp+xp6i2l0dPn94LqQqV4jMpMohgbN69WrV0NCgnnzySbVixQr18MMPqyeeeELdfvvtjb73//7v/9S2bdvMt9GjR8d83Pr6ei1uIJQ+/vhj9de//lU9++yz6te//rXKZaxVVNLFWG7qEZPx0YQCBxGfeOCi7NWutXmxuamgEsxUWZIRnLWGGBnUuWnpKYAFVEQKdjniBcDOSG7E8VJUsjsEUkkF/wDSIwh5S1SmOT4cGdPQ3fJYInAQKYsVApbKFIn8mNECF1U7TgIHIlLmblkrTxKNabCChVIiSVgQkYKwg9JV3DSRXnt3xQ7VVGTRFwM35h+dZ4xIQHrDCYk8YkBqayPyg78ZrwF5vQlIj+DG+vGXu+Meh4gXETNW5DlB6gzGdSdw05EIzh++O1Lf1BC9/PHzn+tKNC/53Ttr9PsLj+9u9jGy09V4fSVKfZoeHMs1E+mhVat7Lt339mr9+R2Th5qzj5xABdbD3xmpGw5ChOLYICARNbjmlH5RUR+z0Z/DdSIpXKTRhxk3evhw4qWmIJCx3j3w7ePVjWcP0pEmrJPwDDXFR4gqMVzzWAcvPbl3Uj8br/rSilMaSUCatMChO/oOB8+OvZvxrgNHzLl+duEkaap0rKTyTOBMmjRJPfPMM+rrX/+66t+/v7rgggvUz3/+c/Xqq682+t4OHTqorl27mm8tW8b2crz77rtq5cqV6rnnnlMjR45U5557rvrNb36jHnvssajoUK6BnRDAziZSIl4YLXBcRHDaJUhR2dNUiYZsWpFIEhp6JcNaI4IDU15zsJaKSzUHbiKyMCIEa7+RRHrgRHaj8rfKtOVeMAEmMfxTbsR2w2WkyV9EbHQoLjI/t48+aOzdEYHTzgxFx+pcKki6wfo7gdtKKuugTTuS8z//+O5mpYsVRAK/bQwXlI6uzTEYI4IjyI4bTeOchgyar1vjucDzJyJkoSVNhF33zS8v0dEGlNPHQwSl1RdivUHhxobrE1VKTsATAkGDmxBMvk9eMVr/DPwfv/1nJFWSaiA4cDPH773pbOfoDehqPMc7mpCiwvMPMQmu/99FOjp11tDO6vKxfRIeH6aZP3P1yWr6D8art284TX34i6+pRXecrW48e3DU98UzGcucvbLWiOCUmlErJ6FiTU3dfPZgvUZA9D90yUi9mXlnxY6kX684vz+fvkR//L2xfaI2TG4YYERwnKrQ3AqcVi1bmGLSmqay9sARROygehNpS/NxLQbjxpVUOSRwnKisrFTt2zfO7UL8INV06qmnqtdffz3uY8ybN08dd9xxqkuXcP4enHPOOaqqqkpHipyoqanR/299y9YIDqqaxEiLm6NVWLjy4Lhooic3EpTZJuNBaW6KKt5Ozw1ycSLELCkqmGut4sW+OMrn1sVaLmjxR0hlVbK7MWupPXb1cuOwRnBAoo7GkqKSGwhSedipQtystA28tIJonzzv3Uqjf2eZIXQTGY0jPXCiBRL40YQB6tffOFb9avLQuDcv8O91u5tcoh2J4EQEzvAeZeqUgR304vy6Qym6DEi1CvMT+zT24by0cIs2QkuH31iCETdKieCIad0KIg0jE/hwFm3aax47rmf8PQ99Z6SZ0nFT+p4sOO7fvROOpiCqgOhXLCSCk9CDI2XiNlErN2mkBnEDvv/iEa5Mtm6JRHBim4xxXSAKBHGNVL6UR1vPx21GagoVWv/vtP5Rhv+ffi1shP71zOUJU3XyeKiIu+qZBfq48Jg/dFEaHmtTBKERr8N4PIEDjpPmoZa1xDqmQcC9A8ExZGshcnYZ9xSnx41EcHIoRWVn3bp16tFHH1XXX3+9+bXi4mL1+9//Xk2fPl3985//1ALnoosuiitytm/fHiVugHyO/3Ni2rRpqqyszHzr1auxHyBrPDh1DY1mhiSK4OAilFENbgSO3BSQw5YFws1NvinzqHBsa3eEb0ZSidJUOlojOEaoF7sz7N7lHNkXRwm3y+7QKubEquHWYGxf6GGOFe8JIiF4POyirWWY1jRVrEoqewQHNw2J4sTzfMjChteO3ZAYieA0LUWlH6NNobrm1H5xd6v427CwIrIxf0PyBt+wWfhgVCmyIGXviFDYiUQeI6LoJMNYK5VUEH9y85cKxWVbncUJFndECZDSiJXikcqqmALH+L3iBwJnH9tFXX1KuCfMDA8GlM5auUMfD6oEZXJ1LCLN/pKvorILUJiKZQOWKuQ17JTK3W+YjOFL1JEM4xq0bxoQmUG0Ds/jg1NGNOrq/sMJA9TxPcv0OnHbq0vjpqog2G+evkRXxOHaRrType+PNU38yYBrSASIvTgh3lw4OxDP9k7O0p3aGsHB3y3PDx7TqURcMLu72xpnZqTAufXWWx2NwdY3+G+sbN26VaespkyZoq677jrz6x07dlQ33XSTGjNmjDrppJPUfffdpy6//HL1wAMPqFRy22236eiRvG3ZkpreG2k5bBMRHPPFWNioB40TuFhlJpOrCI5xQ5cFGbsiNxetm3J1O9gtYjfVnAoqQQQfdvwiAkVsxPLhSLTJKYIjJHtcWHyw+CO6sNnY9UgUBjcR+6IaETjOEZwKm8nYzc00/Dsj/hv7Trpd2+anqNyA3ytTlCXllwzYheM5w+Hbn5fxAzqYKSe7idlMa1meO0RYcO7RrwZpivvfXq3/fggWCA3wyXpnEbZgwx7TyBnLPCqiE94tpxujXE+jjUiSIL1h/v3l7pj+naby+PthjxL8LKjAi4ekMfGcx+sUHfHgRAsc9HzBSxtCSkYHpBJzVMPhxr22JIIjkUmJilpTNXiNPGB4kX76tYFqoIPnD5uh319yvO7MjPYHbyx1rqrCa/K7T32iXv1sq35NIZKJJoZOZupUVlIliuAMc/i7ZaNjf/6tlVTxHhcbPDyveF2gh1Oqh+j6KnBuvvlmtWrVqrhv8NwIFRUVauLEiWr8+PHqqaeeSvj4EDuI9sQCHp0dO6INifI5/s+JoqIiVVpaGvWWvRGcSBVVY5NxjCm7xvfDDOrmApQUFRrEJRPBaMq4BoneIO2STNWBE3JxyqBLLNhyTKWxBI6tTNwakm2qwMFN3fThGLsxMwpjKRG377pgfrWncbCYiMnY+rNmBMfo+OqELGxSHWMFXoVEAge/2z6HqilMNEYCzFm9M+nFUXxMGGppf+3ihoDnHCMCrEIPvhqn1CpeC9K/B9UuMnz0vy4aZoqlWENSZ68Ki7PTB3WKO2wVvwMCyt5vB4NepcT8REsER0QTBDiEg6TLUgFuWnJerjQ6B8cD6wlu1hA38SqJYkVw0FBx5T2T1M1fj+3zaQ6Saoa/B8+5kwdHenVF5rxVRjXgw3ODSMm1p8aeDwXhM3ViONp19+srzPVTwPn52Uuf69EceN7+evXJOpLZ3HRcpJLqoOtJ4nbw+oYYwXMv3hvTg2MTL6bAqaqJK3DwN8pzih5OP/v74mZVRaaSpO8YnTp1UkOGDIn7VlhYaEZuJkyYoKuiYDjOz0/86xYvXqy6dWtccSGMGzdOLVu2TO3cGdntzZo1S4uWY489VuUqRUaX0bAHJ0YVVYzUkAgi6WCbCCwAIqiS8aCYXqAkUlTiv2lueio6gnOwUchcFke7wJGUlTWCgxup+F2akqKy/m45FnuptxUIMTTSw8IpwwgFCBBpQW+thBrRq0xHNbbsPRzVvTmRqVlAKD9RFRV2yuJJibVjdMOpAzvqHTFSdm7n7QjiY7I+lwJuKOP6d2iUpkJUEOcM6UAIIysiLuB5AVNG99QRlTH9Opg+HLsRHaLzw7XhCiuMoogFfDXnHRfehP3DNhA03Hk6HFGypwEgKnCOwIdrnWcJNWeoJsZKWNMTscBxyE0vXprKyWQsNCeCkYi2hQX65u3kpYOABOWGcB9uq6SCKJOOyTecOdj0NMYCqSoUPWDt/O1b0Qbwh2at0dEdFH48d+0Yc0xCc7H3EnJCovexrsfWhS1MoQSLATYU9jENTpVUdtuDHQi+3108Ql9TSPP9x18WmOc8Kz04Im569+6tHnzwQbVr1y7tkbH6ZFDi/eKLL+qUFt7uvfde9fTTT6uf/OQn5vfMmDFDiyYBVVkQMldccYVasmSJeuedd9Qdd9yhpk6dqiM1uYp1mriZoipxZzKWHQhuom7AjcMatUg+gnM06QhOcw3GUuJoxdp4TlJU9oXRqUzc+jdjEevm4uZgR1JjEoGQCI61yZ/1fB9nDBS0N+mS9BTSkdabBzxD4jOINeTRqUTc7sGJt0iJ/wbisDk3LlTYjOnfvklpKjl/1n4oViTyYhU44r9BTydr92WrD0deE7eeG157UCKMz9H/ye7bQMdkXHcQipJOjMXFJ4Srxv65bJs5VsVauYWGiE6cZtwkRUilgtmrwpHvr1mGaiZCboLxSsVjXTNeAyN3rIniItRFuCPtiA0AxC42ABh+CbGCSPGUE8PPUTwQTZ72LZiklZ5eD5M8QC+hx+aG034wUct1mwoipeLOAgdiJVGKCkgfoGVfVel7gnSWt6eZI71wIikquac4cclJvdQzV5+krQjw033r8X87VjBmhcBBVAWpptmzZ6uePXvqqIy8WUGJNyI8SE3NnDlT/f3vf1dXX321+f/wzKxZE86LghYtWqg333xTv0c0B56d//iP/1D33HOPymWi+uAciBYsJUXxzb3JVFAJUl6bjMAxvUDJRHB2pqZE3Kl1uYiMuB4cM0UVvRuVvxm+D3sHVjc0TlFJDxxnsTTC2HGij0o8g7EV6ccSqxtupHNyvAhObIEjO79E3g03iA8HaaomRXBsBmMB5daSqkM3VuvPOKUWremhW845xjRa4jk+uV9Y/NjN0FahkCgNAQEFYYWIgbUqCg0G5f+dkCgAxGq8Khq3IIVgRp2GRBdtxENeK/FKxWOlqPxAjMaVlmZ/4SKKiMlYjk2e/4/W7TIb8CHVYhe9sYAY/Q+jzP32Gcu0oV9Kwa87rZ+6aFTYO5Uq5DWOsSRO1Xx4XSA9F8sM3NhoXGmmp7C+oct6zBRVggiOcNqgTmr6D8fp1wmisTe89HmgnhzPBM5VV12l/zCnN+HKK6/UPW0OHjyohcz8+fPVt7/9bcfHsdKnTx/11ltvqUOHDunIECJEBQX+X0zpRCujwy/EirzIO9kiOFDqTibFZFNUYIDl5uA6RZWkBwfP+7oUVVA57WpQIu4+glPg+LPWKFAyiI8JiwD+zki6yLlhYCSkXulapCBNBWJ185UITpe4HpzaJlVQJYtEERDJcGrUlrCLcYwIDprCoQkjTPRi4t0YR+AgLH/jWYPVVeP7NmrGNkYEjsWHg+dujuG/kdET8YBQ+tYJ4Rsfdv4AN6slRpRttM1/Y214hw7YSFPOS9Bw0A2YEYZoFJ67RFGn5CM4Rx1Nxn4gmzlrJRXSkSIIrH2+JJJx9xsr9ZqE8zDZaBDpllsmDdHXHrr4Tnki3GEZ6cRfTopkHVIFvHLwSerihL2NU7kSZQkPRo0dUZW1BKXisdJT1vUS11gyqeghXUvVa1NP0W0aHphyfEpbASQLZ1FlWQRHihsgJuRF3rYo8mKXeSJWpETcbYqqcQSnjSceHKmgQl7XTafkRNi7NFvFSSKTsbVMHFw8uqf68cSBusNpU4BRGYEfLKxYmOIJFWvTPHiSrKkNmCJjCSPp/BwrZx9p8te6SREcMRi78W8kAhEx+E9gXP/IZRoGUQgJgceK4GBxlTTVx0aayt7kz84NZw3SQzvt1WwwCQMYhGWjgD5DGLGBMutxxu9JhKSpUBWFyB120jDFIoIabyq9GJg/SEGaSiJlEJbJRCC7JojgQPDJBsbeB8cPnAZuyvqGJn3Sods6ukCM9L+YNCTpaCzW2f+6aLj+GK9dCGqMnkim8WdSxQnS8M+ho7Gb9JT084HmwOtWPH3WHjiCzJeTiGdJEqlorAnP/7+xjt44P6HAyRLsLzwpEQfowikVSE7Rkz0Hko/gyM0TF4bbXHuyEZwvpIKqY9tmV1ABhJ6ligK7HOs5ip2ico7g4Psxs8eplNQNeE6QqpCbpKQVnVJNcp4RdoaAtTbv22akqJxGRUgaDCLIKooAjLIiUBw9OG1ceHBizKFqbjWVWx8Ods04H3hu4oXORXhI5COZ7ttW4NvA78LrV54DqZ46dWAn14s/Ol8jGoTANOZwSWPBE/u0i7vbPX1wR7MrbnPC/vjZ2avDabUzXUSdrIgAF0FuBxEMc5MVQATHqZuxCBhEJa3nd5hRSQXG9m+vTm+iGRjn8PKxvfU1+NQVJya1jqbSaOw2jVRsSc/J67eLQxWkXNfyUkv0uOkIBU6WgB2kFXsONt4cqL0Ha5KO4CCce+c3jlUPTjne9c8k6sfj1YgGp/OCm791sTNTVJbQNnbpCON7ZZiUxUoMijAsiwCzg2NFtQtY9lXENByv+gr+ETweFqj1RudeITxVPbyrdXreJZSPCE6sm6mkqJpTQeWUpkIFipuBl6bB2BiymUjgwKCNFC6qtZoicBDREY8MUjxW/w3GDiQDIoBSTSUl4/bycDuo5MLzhZEOzRlsiD4qqK7DpgFphGSQaJ29A7Ag6wsCIdZKS79wmkclaVb7tYWeMBKlQ/SmOamU/7roOPXvW78Ws8ljqjBLxR2Mxm4jONb0nJjbnXx09seJZzBOVyhwsgSU2Vqjq/Z0jDlR3KGCaa+xw2lvTNV2AxaDa0/tp01lbilOYHaOOaKhGUM2Y120du+MUwTHKsScSl6bi4SbxeyJ6E28RVZC6kstPpx4JuO4gz0NDwU8J05heTkfyPfHqr6LNPlrfooKQDxgd4mS1HiTnhuXiMcXKkjBIfUDzQRBgb8JN1+nXWsisNMH8zfs0QJP+tIkU4kEMAwUx4C/QSJWJ8YwGFurzaTKqjnl4rON34fUnd1Y6jaCA/+Wk/CV1BCexyC8F5EITuQ1K2lWiUpaX+OPXTZKPXLpKMfxGumIvTjB6Xp0JXB6lEb1MnNKUSEiKeuA28dNNyhwsgQsJtYdkz2CY86BchAXmDWSbBVVU3AzE8spRZUKg7EgfU/sO61I9UVE4Mi5QnTMbWVFUxar1dur41ZQ2QWOGI1xoxYfjVODQKuQs3c/FYNxLM8PFjeJCsZKU8mOMVUpKkQUpN+Lm2oq60T4REgU54UFmyPdV5tQ/Sb9cBZs2GuG99G6P1mRh+vxXGMOF24yiN7Jrjoep6XAhyOm6DOTFGXWCA5SUVYRIZj+G59LxBt7cBqnqDA6xM6k4d3UBcd3V5mCdcNiF5jJXI/DjbVEiOWjsz4WU1QkUFq5EDj29JDuRms66b19AcsxwJGfqNOlrqAybmCDUpii+tlZg9Vd5x+rvnNSL+cIjuVmLoukV4u1/cYcq4JKkJ4aOC8oeUYEASIHJuxYu6tYOXsRRk7+G6E8QTdjKTFNlcCJSlO5ETjmkM3EqSYpFxf/TTwzb6LULF7HiFQ8aYw5SNbHYk9TgeN7lbvymYnRGH197A0H3YCeVzLUUzxPya4xkupxGroZr8mfH8i1Wu1gMpbeTplMnw5tdVoN67g9TZhMimqYTUzHWvutvXEYwSHpI3BsL8ZYPWjQH0LKypvTbt8N1r4YB2viCxykUA6ksIJKQCrn6lP6NQrNS+4e0SXxf8QyGHslcKzdkZ3ALgsLkTYaV1SZ6Sl83V7xY/4Oo7rInrPfnqBqK1E344M1dWZFXqpSVGDCkPANHKmfh2d9YUYX4w7ZdBHBkdSS0NS5ZqiOkTSRTE+O1704HuiyLM+5dcBmIoEFgYFrI1YDx3hgkCReP2hciNLzphApFW9sNJYUeBA9cKwdya0m48oYKapMBCK4j1GcYN+0JCNwylq3NIsc4q391q8zgkMCxdpevJPNg4P8vVMER8o9YTRNRaVSPHATljLNRD4c8d/gRuT1cVlz94j6SgrN646sqLawpgW7xfDROPpwvqo0bzDxUlsDO4XTe/B6WAckRgZtxv6dEtVyiuBIvh/PZypvZlhQpRfJH2evVePvm6ObqDkt5nieoOt622aDOQHDNW7qQnMGt0rXZRGIMr8qWZAiu/W8oVq0XHJiL9c/c6oRxfnwi11N9t80VZQlavYXa9BmkCZj6dTulKLKRKSHlj3tbFZRuYy0HGdJU8UalhuVomIEh6RLJZVbD46EmVO5C4+HCK3qBOMa1nrgv0kU/YIPwrr7i/TA8W6xtqZX4kVThON6lJsVQZHux7FFSo92rbVARFpw677Djcc0xHne4/XC8SI9JfzxuyPVny4bpb0t6A+DIYhnPfS+uvHvi82ZOGKaxi4UJfdusPapidUDxw3SD8dt9+J4wP/xz5+eplMPbpGxDcn6cJDSet+YP/W1JLoX25G0plOzvyC7GMcsE8+iCI41KmuN4OH6lo70biMtwwyjMSKCsa4hq6iJ1x05XaHAySKsJmNpMd+4iqrO8UbV1WP/jb1cPVGKaq0xoqGpnYKbgr2SyusUlb0DbzyhIpil4lrgxO9+LFEz8Zus2xU+p9ERnMQenEqHbsY7UjBFPF4a6BsjuutuqC9fP053CJaeMRA60xdtUetM/43714f4cJLpvh1r5yuRSDfdi1ONCJylXyU3tgGdnOEdQtRwZK+wUG4KXUtbJ4zgBOXBcWr0J746axfjTObrx4Zfc28urTCjuHuMVh9I6bv9O08wKsfiiWvrxpcRHJJGJuNCV31wtlemrhutG+KVq3tdQZUI+7gGcw6VUd7uBdYOvG4iOFL9gJSNhKgTVV9FBnuGox7wGEkPm7genLZxUlTGza2Th8IYkRHMf/qfK0/UYgcpJhzLLa8sVfcZE5wxvsAtiOCgGRtu7s1pxoaKumnfOk5PlD59sPs2CakCghZDIZFxjDVnzImZi7eaPXtiebbc0LWsKGEEJ7AqKocITjaZjEF4un17PX7kzx9s0F8zh2EWF7muDhzTr72OlsbrZWaN0Npbj2QCFDhZKHCQarGHiGNVUe0wbnS+CZw45epOFVSpbPKXjhEciUBAgLq5KWAXBVGCiAZ6scQrEbf/DvGxYPYYFkesg/F2ZRLBkUGFXpaIJwKi5I2fnKpn/OD1LQbnZCI4eO3NvvkM9coPxjX7eC4c2UMfS3OEQnNvckC6ICcCQ3gx6Rp8yxgV0VTEtyVpTkcPTsApqhpLpaakqMqyJEUFpk4cqN+/sGCTNuInYzC2biDwOo4XJYdXDS9xjJbxolWG12TeEZOEKSqoeLsvoFjKJ2OkqPwWOPG6GWNGCv4fXVvdTir3QuBImNvL3eiJfdrrSppzjJ4obpAoDkRKrC7G8XrhyI0Ji2G8Rcsc1+BQRWU2+fO48s4KjhVRk3dvPF2dMbiTNsafYvTNSWYT4MWcIL+RqisZIJqId1fu0Nc+IlgnJ2gomAjxbTmWiQfswbGamyG2sFmKdDLOvAhEvDQl0tUYJPrMvzc2SeC4AfcFpImfvfpklYnk9gjubBU4Di9yU1jYJjXLIuV1DxzzOGKUq8eqoPJz12AfuGmmqDyM4GBX+dEvv5ZU07kRPcrUrJXhEQGx5lDF634qeft4BmNrSN8pRbVpTzjdheGCfgPPwF+vOVnfvIKcVBwkMtZhyVf7tXk40XXy6mfhyeWYZN6UBodWJCWK1wWuEesGQNaXoKqoEFFDNBRiDmkqrImyEcgWkzHA6/5HEwaqHzz3qfrrvI3m5HsvSrlPbKYgDpLM38qQRlVUHR38BbHmQEmzKL8iOCUuIjhy8x7S1du5Lm5TVCJ8vCLZG440/AOtbe3UYxmZoQOQakI4202TP2tI36mKqqkDK1NJroobeU5xw8YOfkVFZPiqE/BbYUBnKtJTAIJG2htgppWVICeJNy4VrzP9N6gkDGI2ltdm40Gdi/U69dwnmzLWCOwlFDhZRCujssOpnC8SwYkIC+z8pOzWL4HTxbipzl2z03GgIiZfo0oGXD62jwqyh4YfEZymYO1fgfRUohs9+iNJlAfl1ZExDfGjL7E6GaOviPhyghQ4uQxEMaaPu/HhzPy8QhuST+hdnrLnCxPRgQwuTZc+ONbrFREcc0xD65ZZJ4jxGvjRxAH6YxkKHKufTa5CgZNFTBjcWc9amuTg53AqE4e4gVkVpYXJTBJvDt85sZcWW8u3VqnXl1Q0+v/H31unQ8ro8ooKmmAiOPZGf+klcNACQARLovSUk9HY7IGTIIJj9eBY597IkEuYnZMd1ki8MBrH9+H8w0hPWUdDNBfpgvvVPmeBE1QVldVojGPJpi7GTpw/ontUmjgTuw17CQVOFoEyWPg5nGbMWFNDcrOSGx0qYZqbl0/m5gyjKHjgnTW6ukOAN+TlheHF+IazBim/kYZ+jVJUAS7WiaI4bkrL7UZjswdOIg+OcVOA4JQdYrqkp0jEh7No0z7Hyd5gRUWlHuaKFM03jkvdUMlextBaewQnaJOxfeCmWSKeRQZjKzDM/+CM8HoKmKKKhgInR5AIDkLVmAQc5b9xeZNMFdee2k/fmJGOevbjjebXH3/vSz0XC/0ZrN1ig/PgpGeKCiBKB0Ol2yqiqAiOSw8OPAuFhnnV6sNZb5iVk+lBQ7wRuXh+EIm1Cw3h1c/CvW/OHtolpWXSEsGx/l6IrEgfnAAFjqUXjjVFla18e3RP/XzgepUxDiQMBU6OgBe/BGnEhyOdSLv4WOorpbo3f/0Y/fFjc9fpFuOIJr20YEtg0RurwKk+fFTV1TeYvVaCDLfH4qJRPdSKu8/RfSySj+AkHrQJ4FmQKI7M84mO4HAxDRJcR2I4X+iQpoLHTpr7oXoqlTgJHBieZd5ZsBGciJcuG0vE7WDMAhphon2CdbYdocDJGXCzMpvs1UQLnEQ7eS/45qgeekgh0kCPzF6rnng/HL1Bjw74b4JAdriI4Fi9SukYwbF3rk6ElIp/te+wvhG5NZZHfDhHGwkcGQFBgkOMxp9uamw0/nDtLrX7QK3216W647KYjL/ae9gsFpD5cvDyyiiLYCeK10UiOFnqwREgbOQ5IREocHIIiURIBCcyaNP/vC3SK7efN1R/jBLHFxZsNqM3QVU7SGgbN3Px36D0PhM7eDotgNZFHp+7EUj2SirczOjBSR9Gm5VUjSM4//g0HL1BlC/Vr2FE/1CcgE2JdEOXdQUbqSArlmSd0xGcLOxiTNyT+Ss3aXIX4Z3iwfE5RSWcOqijmnBMJ1XXENLTcLEbHW+Z+BxUigrHI9GtdExPNQXccAZa8vNu2wJEeuGEQ/0VlYd1G3x0mUbFHkkPgbN25wEzHQMwo+qt5eHRDBePTm16SsytMhxWeuGYFVQBpqesJmMcTy6kqEhsKHByiLZFLaIWoiBTVMJt5w41vUFBRm8AwurYlUoqJ53TU03BOrfJbfWVvZuxRG/gwciGkQeZDqoSxez96aZwFAczmH75ylLdAuLiE3qqYd0jfZO89OEEPWgzV03GJDbZs3qThMg8KlmI/B7T4MQxXUvUo5eeoCMEpyY5VyjVQFwhioNhlNLfI+jFOpVYh+q5FbV2D876XTQYpxuIfOJ5Qbn4mUO7qMfmfqkjOh2LC9Wd3wingb3A3uwvHZr82U3GYnrO1jJxEh9uwXIIsxfOkaPqUG2duSD51cU4FpNHdFPfG9MnLTqNyuKIEnb9eTZFcDpHPDPdXD7ncmOQUL9EcMS0TIIHA1vBpxv3qdXbq9R/z12nP7/nwuGe3tglgrPFFsEJsoIqOoKT/Y3+SHyyZ/UmSXlwpAcO0jJBL0jphAicbE9Rue19JL4kCfVLF2MajNOH0UbDv8Vf7Ve/eGWp9pCdM6yLOjeJCfUpSVEFPGjT7sGBuEGpPKDAyU0YwckhZOFBmbjpvylNPMsolyizC5yi7FkYe7ZrozvaJuPBEXOmVKNs2B1u8keBkz6gXB9VcTDqL/2qUovy31w43PPrWkYENPLgBLxhkrQyGppC7AGajHMTCpwcwjpwUwQOh7M5C5ytWRjBQWn+2cd20X1Rhrs0nsrOFykqjNUQ4ceOqekDhIxUU4E7Jx+rOvuQdpYIzq7qGnW4tj7iwQlc4ET//qKC/KR6RpHsIXtWb+L6wg+nqNzNI8o1yozwNvp7ZJvJGPzp0lF6V+u2L4o1RYWdOipzsEOHgZWkD6cN6qhmrdyhThnYQU05MXVDNRO9NrCmQNjAlC8NRIO+ZvDaRupd5qcxepO7UODkaARne6XRA4cCJwr7YM1siuDIbh89bNxiRnAOHzVnUPXr1JZpzTTjspN762sZlYh+PTf4PYjirKio0uLXbPSXBtcMrmMROPTf5C5MUeWqB8foPupHKDuTkIhFtgqcZJEqHPg7VlZU6Y85oiH9QE+ic4Z1VW19Tg9Zjcbp4sGxGo0BBU7uEvwrkfgewTlYU2fOj2GKKpHAye3Fsa3R/BBprc+37NdfYw8c4tQLJ50iONbrVsaNkNyDEZxc9eBUB9/kLxMETjb1wWkK1oniizcbAoc9cIhN4KAXjnhwgjYZ269bRnByFwqcHKLYKHmGKVD64NCD49wHR8j1CI5V9MkNjCkq0rjZ32FVnSZ9cOzXMbsY5y7BvxKJb8jCs/dgZCgfy8SjoQenMeEqlHCDP8AeOMTJgyM9ltIh6mktFmAEJ3cJ/pVIfMMeOkZzsKIC9oeIm6LikL6oGwRSmn4bWUn60qO8tULRFprq4c0aKU4Xk3E7CpychSmqHBY4nUsYvUmcouLNvMxi0uxPgzGxgKiNfa5ZcZpFcKyvX5JbUODkEOhkiwZYyU6UziVQ4iptRFq1zHfdEC9XIjg0GJNYRmOAa6dNGnQNjqqiYgQnZ+HqncNRnC4lFDh28vPzzD4eNBiHKbdEtWgwJrF8OKC4sEBfQ+mVomIEJ1ehwMllgUODsSNlxo6P6anGO+D+jOCQeAInTa4ZmowJoMDJMawLUBemqOIajRnBCVNm2QGzyR+x07uDReCkiQHd6qWzFw6Q3IECJ8dgiioxsiCmQ7lrOqWo0NG4Z7vWQR8OSTN6tosInHSJenZoW2geDyeJ5y7p8WokgQgcmozjh7fTZbEOGvS9gXl0RM8ymq5JghRVy7QxPt927hDVg4I8p+EKnsMpKjb5S5CiSoN+Hulys5h14+mqYzFfL6QxHYsLVeuWLXQfnHQYtClcf8aAoA+BBAy3YzmGLEAoGe/YljcsJ2TCeif2CTIZ2LmELe9JzHllEsVJFw8OIYCvxhyN4KDJXzqUc6YjV47ro4qLWqhvndAz6EMhJGOifGt2VKdNFRUhgBGcHEPaqHPIZmw6FBep758+gCkZQlwyvEdpIz8OIUFDuZ1jyHgGLkSEkFTxgzMGqDH9OqgT+7YL+lAIMaHAyTEmj+imDtXWqYlDOgd9KISQLAGl2OMGdAj6MAiJggInBxeiK8b1DfowCCGEEE+hB4cQQgghWQcFDiGEEEKyDgocQgghhGQdFDiEEEIIyToocAghhBCSdVDgEEIIISTroMAhhBBCSNZBgUMIIYSQrIMChxBCCCFZBwUOIYQQQrIOChxCCCGEZB0UOIQQQgjJOihwCCGEEJJ15OQ08VAopN9XVVUFfSiEEEIIcYnct+U+Ho+cFDjV1dX6fa9evYI+FEIIIYQ04T5eVlYW93vyQm5kUJbR0NCgKioqVElJicrLy0u5uoRw2rJliyotLU3pY5NoeK79g+faP3iu/YPnOvPONSQLxE337t1Vfn58l01ORnBwUnr27Onp78ATyAvGH3iu/YPn2j94rv2D5zqzznWiyI1AkzEhhBBCsg4KHEIIIYRkHRQ4KaaoqEjddddd+j3xFp5r/+C59g+ea//guc7uc52TJmNCCCGEZDeM4BBCCCEk66DAIYQQQkjWQYFDCCGEkKyDAocQQgghWQcFTgp57LHHVN++fVWrVq3UmDFj1IIFC4I+pIxn2rRp6qSTTtJdpzt37qwuuugitWbNmqjvOXLkiJo6darq0KGDKi4uVhdffLHasWNHYMecLdx333260/fPfvYz82s816lj69at6vLLL9fnsnXr1uq4445TixYtMv8f9R+//vWvVbdu3fT/n3XWWWrt2rWBHnMmUl9fr+68807Vr18/fR4HDBigfvOb30TNMuK5bhoffPCBOv/883VXYawVr732WtT/uzmve/fuVd/73vd087/y8nJ17bXXqgMHDqiUgCoq0nxeeumlUGFhYejpp58OrVixInTdddeFysvLQzt27Aj60DKac845J/TMM8+Eli9fHlq8eHHovPPOC/Xu3Tt04MAB83t+8IMfhHr16hWaPXt2aNGiRaGxY8eGxo8fH+hxZzoLFiwI9e3bNzRixIjQDTfcYH6d5zo17N27N9SnT5/QVVddFZo/f35o/fr1oXfeeSe0bt0683vuu+++UFlZWei1114LLVmyJHTBBReE+vXrFzp8+HCgx55p/Pa3vw116NAh9Oabb4Y2bNgQmj59eqi4uDj0xz/+0fwenuum8dZbb4V+9atfhV599VWoxdCMGTOi/t/NeZ00aVLo+OOPD33yySehDz/8MDRw4MDQpZdeGkoFFDgp4uSTTw5NnTrV/Ly+vj7UvXv30LRp0wI9rmxj586d+kJ6//339ef79+8PtWzZUi9awqpVq/T3zJs3L8AjzVyqq6tDgwYNCs2aNSt0xhlnmAKH5zp1/PKXvwydeuqpMf+/oaEh1LVr19ADDzxgfg3nv6ioKPTiiy/6dJTZweTJk0PXXHNN1Ne+9a1vhb73ve/pj3muU4Nd4Lg5rytXrtQ/t3DhQvN73n777VBeXl5o69atzT4mpqhSQG1trfr00091+M067wqfz5s3L9BjyzYqKyv1+/bt2+v3OO9Hjx6NOvdDhgxRvXv35rlvIkhBTZ48OeqcAp7r1PH666+rE088UU2ZMkWnXkeNGqX+/Oc/m/+/YcMGtX379qhzjfk7SH3zXCfH+PHj1ezZs9UXX3yhP1+yZIn66KOP1Lnnnqs/57n2BjfnFe+RlsK1IOD7cf+cP39+s48hJ4dtpprdu3frPG+XLl2ivo7PV69eHdhxZeMUePhBTjnlFDV8+HD9NVxAhYWF+iKxn3v8H0mOl156SX322Wdq4cKFjf6P5zp1rF+/Xj3++OPqpptuUrfffrs+3z/96U/1+b3yyivN8+m0pvBcJ8ett96qJ1lDjLdo0UKv1b/97W+17wPwXHuDm/OK9xD4VgoKCvQGNhXnngKHZFRkYfny5Xr3RVLPli1b1A033KBmzZqljfLEW7GOXeu9996rP0cEB6/tJ554Qgsckjpefvll9fzzz6sXXnhBDRs2TC1evFhvlGCM5bnObpiiSgEdO3bUOwN7NQk+79q1a2DHlU38+Mc/Vm+++aaaO3eu6tmzp/l1nF+kCPfv3x/1/Tz3yYMU1M6dO9UJJ5ygd1F4e//999UjjzyiP8bOi+c6NaCq5Nhjj4362tChQ9XmzZv1x3I+uaY0n1tuuUVHcb773e/qSrUrrrhC3XjjjbpCE/Bce4Ob84r3WHOs1NXV6cqqVJx7CpwUgLDy6NGjdZ7XukPD5+PGjQv02DIdeNcgbmbMmKHmzJmjSz2t4Ly3bNky6tyjjBw3Cp775DjzzDPVsmXL9A5X3hBlQChfPua5Tg1Is9rbHcAj0qdPH/0xXudY4K3nGmkW+BJ4rpPj0KFD2tNhBRtSrNGA59ob3JxXvMeGCZsrAes8nht4dZpNs23KxCwThzv82Wef1c7w73//+7pMfPv27UEfWkbzwx/+UJcZvvfee6Ft27aZb4cOHYoqXUbp+Jw5c3Tp8rhx4/QbaT7WKirAc526MvyCggJdwrx27drQ888/H2rTpk3oueeeiyqxxRoyc+bM0NKlS0MXXnghS5ebwJVXXhnq0aOHWSaOkuaOHTuGfvGLX5jfw3Pd9IrLzz//XL9BTjz00EP6402bNrk+rygTHzVqlG6X8NFHH+kKTpaJpyGPPvqoXvzRDwdl46jrJ80DF43TG3rjCLhYfvSjH4XatWunbxLf/OY3tQgiqRc4PNep44033ggNHz5cb4yGDBkSeuqpp6L+H2W2d955Z6hLly76e84888zQmjVrAjveTKWqqkq/hrE2t2rVKtS/f3/du6Wmpsb8Hp7rpjF37lzH9Rmi0u153bNnjxY06E1UWloauvrqq7VwSgV5+Kf5cSBCCCGEkPSBHhxCCCGEZB0UOIQQQgjJOihwCCGEEJJ1UOAQQgghJOugwCGEEEJI1kGBQwghhJCsgwKHEEIIIVkHBQ4hhBBCsg4KHEIIIYRkHRQ4hBBCCMk6KHAIIYQQknVQ4BBCCCFEZRv/HwgP0q9+Pp73AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.plot(energy_history)"]}, {"cell_type": "markdown", "id": "a10ade91-7ad7-4a8c-997d-e4ee9098b2d7", "metadata": {}, "source": ["### 4b. Use NetKet's optimisation driver\n", "\n", "As writing the whole optimisation loop by yourself every time is.. boring, we can make use of a coupled of NetKet's built-in utilities."]}, {"cell_type": "code", "execution_count": 18, "id": "4572b5c5", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d2c5abb6e0b04ae78bc1f0f0b76749ac", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/300 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy and relative error:  -24.965 ± 0.043 [σ²=0.966, R̂=1.0302] 0.020648122350504595\n"]}], "source": ["# First we reset the parameters to run the optimisation again\n", "vstate.init_parameters()\n", "\n", "# Then we create an optimiser from the standard library.\n", "# You can also use optax.\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.05)\n", "\n", "# build the optimisation driver\n", "gs = nk.driver.VMC(H, optimizer, variational_state=vstate)\n", "\n", "# run the driver for 300 iterations. This will display a progress bar\n", "# by default.\n", "gs.run(n_iter=300)\n", "\n", "mf_energy = vstate.expect(H)\n", "error = abs((mf_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(\"Optimized energy and relative error: \", mf_energy, error)"]}, {"cell_type": "code", "execution_count": 20, "id": "4f4b1a2d-fcbf-4248-ae18-d0f527ac67e5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final optimized parameter:  [2.65683622]\n"]}], "source": ["# we can also inspect the parameter:\n", "print(\"Final optimized parameter: \", vstate.parameters[\"log_phi_local\"])"]}, {"cell_type": "code", "execution_count": 21, "id": "5bb5976b-da47-45af-88e8-690afb3980b1", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"text/plain": ["-25.490989686364788"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["eig_vals[0]"]}, {"cell_type": "markdown", "id": "d9f0e656", "metadata": {"lines_to_next_cell": 2}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "We have seen that the mean field ansatz yields about 2% error on the ground-state energy. Let's now try to do better, using a more correlated ansatz.\n", "\n", "We will now take a short-range Jastrow ansatz, entangling nearest and next-to nearest neighbors, of the form\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{jas}} \\rangle = \\exp \\left( \\sum_i J_1 \\sigma^{z}_i\\sigma^{z}_{i+1} + J_2 \\sigma^{z}_i\\sigma^{z}_{i+2} \\right),$$\n", "\n", "where the parameters $J_1$ and $J_2$ are to be learned.\n", "\n", "Again we can write the model using flax."]}, {"cell_type": "code", "execution_count": 22, "id": "4b390e0a", "metadata": {}, "outputs": [], "source": ["class JasShort(nnx.Module):\n", "\n", "    def __init__(self, *, rngs: nnx.Rngs):\n", "\n", "        # Define two parameters j1, and j2.\n", "        # Initialise them with a random normal distribution of standard deviation\n", "        # 0.01\n", "        # We must get a different key for each parameter, otherwise they will be\n", "        # initialised with the same value.\n", "        self.j1 = nnx.Param(0.01 * jax.random.normal(rngs.params(), (1,)), dtype=float)\n", "        self.j2 = nnx.Param(0.01 * jax.random.normal(rngs.params(), (1,)), dtype=float)\n", "\n", "    def __call__(self, x: jax.Array):\n", "\n", "        # compute the nearest-neighbor correlations\n", "        corr1 = x * jnp.roll(x, -1, axis=-1)\n", "        corr2 = x * jnp.roll(x, -2, axis=-1)\n", "\n", "        # sum the output\n", "        return jnp.sum(self.j1 * corr1 + self.j2 * corr2, axis=-1)\n", "\n", "\n", "# Initialise the model wtih seed 1\n", "model = JasShort(rngs=nnx.Rngs(1))\n", "\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=1008)"]}, {"cell_type": "markdown", "id": "1616acc8", "metadata": {}, "source": ["We then optimize it, however this time we also introduce a stochastic reconfiguration (natural gradient) preconditioner. Also, we now log the intermediate results of the optimization, so that we can visualize them at a later stage.\n", "\n", "Loggers that work together with optimisation drivers are defined in `nk.logging`. In this example we use `RuntimeLog`, which keeps the metrics in memory. You could also use `JsonLog`, which stores data to a json file which can be later read as a dict or `TensorBoardLog` which connects to [TensorBoard](https://www.tensorflow.org/tensorboard)."]}, {"cell_type": "code", "execution_count": 23, "id": "11db148b", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "27a40e9fda1c44f5a2b7f791fdf6f258", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/300 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Final optimized parameters: j1=[0.23280522], j2=[0.08196385]\n", "Optimized energy : -25.303 ± 0.020 [σ²=0.413, R̂=1.0171]\n", "relative error   : 0.0073876968007242135\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.01)\n", "\n", "gs = nk.driver.VMC(\n", "    H,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.1),\n", ")\n", "\n", "# construct the logger\n", "log = nk.logging.RuntimeLog()\n", "\n", "# One or more logger objects must be passed to the keyword argument `out`.\n", "gs.run(n_iter=300, out=log)\n", "\n", "print(\n", "    f\"Final optimized parameters: j1={vstate.parameters['j1']}, j2={vstate.parameters['j2']}\"\n", ")\n", "\n", "jas_energy = vstate.expect(H)\n", "error = abs((jas_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(f\"Optimized energy : {jas_energy}\")\n", "print(f\"relative error   : {error}\")"]}, {"cell_type": "markdown", "id": "e9b18a6a", "metadata": {}, "source": ["You can now see that this ansatz is almost one order of magnitude more accurate than the mean field!\n", "\n", "In order to visualize what happened during the optimization, we can use the data that has been stored by the logger. There are several available loggers in NetKet, here we have just used a simple one that stores the intermediate results as values in a dictionary."]}, {"cell_type": "code", "execution_count": 24, "id": "4b34166d-92eb-4ad8-a43f-cffc3bb717be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HistoryDict with 2 elements:\n", "\t'Energy' -> History(keys=['Mean', 'Varian<PERSON>', '<PERSON>', 'R_hat', '<PERSON><PERSON>orr'], n_iters=300)\n", "\t'acceptance' -> History(keys=['value'], n_iters=300)\n"]}], "source": ["data_jastrow = log.data\n", "print(data_jastrow)"]}, {"cell_type": "markdown", "id": "00670bd1", "metadata": {}, "source": ["These report several intermediate quantities, that can be easily plotted. For example we can plot the value of the energy (with its error bar) at each optimization step."]}, {"cell_type": "code", "execution_count": 25, "id": "6d5c22fb", "metadata": {"lines_to_next_cell": 2, "scrolled": true}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Energy')"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from matplotlib import pyplot as plt\n", "\n", "plt.errorbar(\n", "    data_jastrow[\"Energy\"].iters,\n", "    data_jastrow[\"Energy\"].Mean,\n", "    yerr=data_jastrow[\"Energy\"].Sigma,\n", ")\n", "plt.xlabel(\"Iterations\")\n", "plt.ylabel(\"Energy\")"]}, {"cell_type": "markdown", "id": "d216a4d6", "metadata": {"lines_to_next_cell": 2}, "source": ["## 6. Neural-Network Quantum State\n", "\n", "We now want to use a more sophisticated ansatz, based on a neural network representation of the wave function. At this point, this is quite straightforward, since we can again take advantage of automatic differentiation.\n", "\n", "Let us define a simple fully-connected feed-forward network with a ReLu activation function and a sum layer."]}, {"cell_type": "code", "execution_count": 26, "id": "2b5c30e7", "metadata": {}, "outputs": [], "source": ["class FFN(nnx.Module):\n", "\n", "    def __init__(self, N: int, alpha: int = 1, *, rngs: nnx.Rngs):\n", "        \"\"\"\n", "        Construct a Feed-Forward Neural Network with a single hidden layer.\n", "\n", "        Args:\n", "            N: The number of input nodes (number of spins in the chain).\n", "            alpha: The density of the hidden layer. The hidden layer will have\n", "                N*alpha nodes.\n", "            rngs: The random number generator seed.\n", "        \"\"\"\n", "        self.alpha = alpha\n", "\n", "        # We define a linear (or dense) layer with `alpha` times the number of input nodes\n", "        # as output nodes.\n", "        # We must pass forward the rngs object to the dense layer.\n", "        self.linear = nnx.Linear(in_features=N, out_features=alpha * N, rngs=rngs)\n", "\n", "    def __call__(self, x: jax.Array):\n", "\n", "        # we apply the linear layer to the input\n", "        y = self.linear(x)\n", "\n", "        # the non-linearity is a simple ReLu\n", "        y = nnx.relu(y)\n", "\n", "        # sum the output\n", "        return jnp.sum(y, axis=-1)\n", "\n", "\n", "model = FFN(N=N, alpha=1, rngs=nnx.Rngs(2))\n", "\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=1008)"]}, {"cell_type": "markdown", "id": "f29016f4", "metadata": {}, "source": ["We then proceed to the optimization as before."]}, {"cell_type": "code", "execution_count": 27, "id": "279c052e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be3a8e8826c84a99b396326a9355e3e8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/300 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy and relative error:  -25.449 ± 0.015 [σ²=0.241, R̂=1.0041] 0.0016279223207961424\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.1)\n", "\n", "# Notice the use, again of Stochastic Reconfiguration, which considerably improves the optimisation\n", "gs = nk.driver.VMC(\n", "    H,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.1),\n", ")\n", "\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=300, out=log)\n", "\n", "ffn_energy = vstate.expect(H)\n", "error = abs((ffn_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(\"Optimized energy and relative error: \", ffn_energy, error)"]}, {"cell_type": "markdown", "id": "26014bdc-f72b-466f-b7d7-761a7499ce25", "metadata": {}, "source": ["And we can compare the results between the two different Ansätze:"]}, {"cell_type": "code", "execution_count": 28, "id": "10580934-c128-47ef-beb4-315420d63b9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Energy')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data_FFN = log.data\n", "\n", "plt.errorbar(\n", "    data_jastrow[\"Energy\"].iters,\n", "    data_jastrow[\"Energy\"].Mean,\n", "    yerr=data_jastrow[\"Energy\"].Sigma,\n", "    label=\"J<PERSON><PERSON>\",\n", ")\n", "plt.errorbar(\n", "    data_FFN[\"Energy\"].iters,\n", "    data_FFN[\"Energy\"].Mean,\n", "    yerr=data_FFN[\"Energy\"].Sigma,\n", "    label=\"FFN\",\n", ")\n", "plt.hlines([E_gs], xmin=0, xmax=300, color=\"black\", label=\"Exact\")\n", "plt.legend()\n", "\n", "plt.xlabel(\"Iterations\")\n", "plt.ylabel(\"Energy\")"]}, {"cell_type": "markdown", "id": "5ff286a6", "metadata": {}, "source": ["## 7. Translation Symmetry\n", "\n", "In order to enforce spatial symmetries we can use some built-in functionalities of NetKet, in conjunction with equivariant layers.\n", "\n", "The first step is to construct explicitly a graph that contains the edges of our interactions, in this case this is a simple chain with periodic boundaries. NetKet has builtin several symmetry groups that can be used to target specific spatial symmetries. In this case for example after constructing the graph we can also print its translation group.\n"]}, {"cell_type": "code", "execution_count": 29, "id": "72f147d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PermutationGroup(elems=[Id(), Translation([1]), Translation([2]), Translation([3]), Translation([4]), Translation([5]), Translation([6]), Translation([7]), Translation([8]), Translation([9]), Translation([10]), Translation([11]), Translation([12]), Translation([13]), Translation([14]), Translation([15]), Translation([16]), Translation([17]), Translation([18]), Translation([19])], degree=20)\n"]}], "source": ["graph = nk.graph.Chain(length=N, pbc=True)\n", "\n", "print(graph.translation_group())"]}, {"cell_type": "markdown", "id": "64cabe68", "metadata": {}, "source": ["Graphs are in general quite handy when defining hamiltonian terms on their edges. For example we can define our Hamiltonian as"]}, {"cell_type": "code", "execution_count": 30, "id": "ae71872a", "metadata": {}, "outputs": [], "source": ["Gamma = -1\n", "H = sum([Gamma * sigmax(hi, i) for i in range(N)])\n", "\n", "V = -1\n", "H += sum([V * sigmaz(hi, i) * sigmaz(hi, j) for (i, j) in graph.edges()])"]}, {"cell_type": "markdown", "id": "e0196f9f", "metadata": {}, "source": ["We now write a model with an invariant transformation given by the translation group. Notice that we will now use NetKet's own ```nn``` module, instead of Flax, since it contains several additions and also an extended and seamless support for complex layers/parameters."]}, {"cell_type": "code", "execution_count": 31, "id": "a0f65b95", "metadata": {}, "outputs": [{"data": {"text/plain": ["84"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["import flax.linen as nn\n", "import netket.nn as nknn\n", "\n", "\n", "class SymmModel(nnx.Module):\n", "\n", "    def __init__(self, N: int, alpha: int = 1, *, rngs: nnx.Rngs):\n", "        \"\"\"\n", "        Construct a Feed-Forward Neural Network with a single hidden layer.\n", "\n", "        Args:\n", "            N: The number of input nodes (number of spins in the chain).\n", "            alpha: The density of the hidden layer. The hidden layer will have\n", "                N*alpha nodes.\n", "            rngs: The random number generator seed.\n", "        \"\"\"\n", "        self.alpha = alpha\n", "\n", "        # We want to use netket's DenseSymm layer, which requires a symmetry group.\n", "        dense_symm_linen = nknn.DenseSymm(\n", "            symmetries=graph.translation_group(),\n", "            features=alpha,\n", "            kernel_init=nn.initializers.normal(stddev=0.01),\n", "        )\n", "        # However, this layer is defined using ``flax.linen``, so to use it with NNX we\n", "        # must resort to the conversion function ``nnx.bridge.ToNNX``.\n", "        # The syntax is a bit weird, as we need to separately pass the random number generator\n", "        # and then initialize the layer with a dummy input of shape (1, 1, Nsites).\n", "        self.linear_symm = nnx.bridge.ToNNX(dense_symm_linen, rngs=rngs).lazy_init(\n", "            jnp.ones((1, 1, N))\n", "        )\n", "\n", "    def __call__(self, x: jax.Array):\n", "        # add an extra dimension with size 1, because DenseSymm requires rank-3 tensors as inputs.\n", "        # the shape will now be (batches, 1, Nsites)\n", "        x = x.reshape(-1, 1, x.shape[-1])\n", "\n", "        x = self.linear_symm(x)\n", "        x = nnx.relu(x)\n", "\n", "        # sum the output\n", "        return jnp.sum(x, axis=(-1, -2))\n", "\n", "\n", "sampler = nk.sampler.MetropolisLocal(hi)\n", "\n", "# Let us define a model with 4 features per symmetry\n", "model = SymmModel(N=N, alpha=4, rngs=nnx.Rngs(2))\n", "\n", "vstate = nk.vqs.MCState(sampler, model, n_samples=1008)\n", "\n", "vstate.n_parameters"]}, {"cell_type": "markdown", "id": "7e0ca35d", "metadata": {}, "source": ["As it can be seen, the number of parameters of this model is greatly reduced, because of the symmetries that impose constraints on the weights of the dense layers. We can now optimize the model, using a few more optimization steps than before."]}, {"cell_type": "code", "execution_count": 32, "id": "16a45cf8", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "adaa8034a6ac42cd8cdc501f022bfc8e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/600 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimized energy and relative error:  -25.4903 ± 0.0020 [σ²=0.0042, R̂=1.0094] 2.6346677498658308e-05\n"]}], "source": ["optimizer = nk.optimizer.Sgd(learning_rate=0.1)\n", "\n", "gs = nk.driver.VMC(\n", "    H,\n", "    optimizer,\n", "    variational_state=vstate,\n", "    preconditioner=nk.optimizer.SR(diag_shift=0.1),\n", ")\n", "\n", "log = nk.logging.RuntimeLog()\n", "gs.run(n_iter=600, out=log)\n", "\n", "symm_energy = vstate.expect(H)\n", "error = abs((symm_energy.mean - eig_vals[0]) / eig_vals[0])\n", "print(\"Optimized energy and relative error: \", symm_energy, error)"]}, {"cell_type": "code", "execution_count": 33, "id": "0e2ed895", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x31c1c6210>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.errorbar(\n", "    log.data[\"Energy\"].iters[50:],\n", "    log.data[\"Energy\"].Mean[50:],\n", "    yerr=log.data[\"Energy\"].Sigma[50:],\n", "    label=\"SymmModel\",\n", ")\n", "\n", "plt.axhline(\n", "    y=eig_vals[0],\n", "    xmin=0,\n", "    xmax=log.data[\"Energy\"].iters[-1],\n", "    linewidth=2,\n", "    color=\"k\",\n", "    label=\"Exact\",\n", ")\n", "plt.xlabel(\"Iterations\")\n", "plt.ylabel(\"Energy\")\n", "plt.legend(frameon=False)"]}, {"cell_type": "markdown", "id": "68f501c7", "metadata": {}, "source": ["## 8. Measuring Other Properties\n", "\n", "Once the model has been optimized, we can of course measure also other observables that are not the energy. For example, we could decide to measure the value of the nearest-neighbor $X-X$ correlator.\n", "Notice that since correlators do not enjoy the zero-variance principle as the Hamiltonian instead does, it is important to use a larger number of samples to have a sufficiently low error bar on their measurement.\n"]}, {"cell_type": "code", "execution_count": 34, "id": "6cf7a569", "metadata": {}, "outputs": [], "source": ["corr = sum([sigmax(hi, i) * sigmax(hi, j) for (i, j) in graph.edges()])"]}, {"cell_type": "code", "execution_count": 35, "id": "10fb783e", "metadata": {}, "outputs": [], "source": ["vstate.n_samples = 400000"]}, {"cell_type": "code", "execution_count": 36, "id": "e3b3b4ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["10.908 ± 0.018 [σ²=31.718, R̂=1.0002]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["vstate.expect(corr)"]}, {"cell_type": "markdown", "id": "1daae42d", "metadata": {}, "source": ["And we can further compare this to the exact ED result."]}, {"cell_type": "code", "execution_count": 37, "id": "c89a3597", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10.852248713128098\n"]}], "source": ["psi = eig_vecs[:, 0]\n", "exact_corr = psi @ (corr @ psi)\n", "print(exact_corr)"]}, {"cell_type": "code", "execution_count": null, "id": "79f5ebd2-5e85-4c44-9948-9ee54ffcafda", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "netket_pro", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}