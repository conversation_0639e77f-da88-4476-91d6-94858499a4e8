{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground-State: J1-J2 model\n", "\n", "In this tutorial we will use NetKet to obtain the ground state of the J1-J2 model in one-dimension with periodic boundary conditions, using a Neural Network variational wave-function. The Hamiltonian of the model is given by:\n", "\n", "$$ H = \\sum_{i=1}^{L} J_{1}\\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{i+1} + J_{2} \\vec{\\sigma}_{i} \\cdot \\vec{\\sigma}_{i+2} $$\n", "\n", "where the sum is over sites of the 1-D chain. Here $\\vec{\\sigma}=(\\sigma^x,\\sigma^y,\\sigma^z)$ is the vector of Pauli matrices.\n", "\n", "We will also explore some useful functionalities provided by the package.\n", "\n", "## Objectives:\n", "    1. Defining custom Hamiltonians\n", "    2. Defining the machine (variational ansatz)\n", "    3. Variational Monte Carlo Optimisation\n", "    4. Measuring observables\n", "    5. Data Visualisation\n", "    6. Sanity Check: Exact Diagonalisation\n", "\n", "Let's start."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# ensure we run on the CPU\n", "import os\n", "\n", "os.environ[\"JAX_PLATFORM_NAME\"] = \"cpu\"\n", "\n", "# Import netket library\n", "import netket as nk\n", "\n", "# Helper libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1) Defining a Custom Hamiltonian\n", "\n", "The first thing to do is to define the graph (lattice) on which to specify the Hamiltonian. Here we would like to build a one-dimensional graph with both nearest and next nearest neighbour bonds. The graph is created in the ``nk.graph.CustomGraph`` class. To initialise the class we simply provide a list of edges in the ``[[site_i, site_j, edge_color], ...]``"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Couplings J1 and J2\n", "J = [1, 0.2]\n", "L = 14"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Define custom graph\n", "edge_colors = []\n", "for i in range(L):\n", "    edge_colors.append([i, (i + 1) % L, 1])\n", "    edge_colors.append([i, (i + 2) % L, 2])\n", "\n", "# Define the netket graph object\n", "g = nk.graph.Graph(edges=edge_colors)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We specify a different ``color`` for each type of bond so as to define a different operator for each of them.\n", "Next, we define the relevant bond operators."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Sigma^z*Sigma^z interactions\n", "sigmaz = [[1, 0], [0, -1]]\n", "mszsz = np.kron(sigmaz, sigmaz)\n", "\n", "# Exchange interactions\n", "exchange = np.asarray([[0, 0, 0, 0], [0, 0, 2, 0], [0, 2, 0, 0], [0, 0, 0, 0]])\n", "\n", "bond_operator = [\n", "    (J[0] * mszsz).tolist(),\n", "    (J[1] * mszsz).tolist(),\n", "    (-J[0] * exchange).tolist(),\n", "    (J[1] * exchange).tolist(),\n", "]\n", "\n", "bond_color = [1, 2, 1, 2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Side Remark**: Notice the minus sign in front of the exchange. This is simply a basis rotation corresponding to the Marshall sign rule (as an exercise, change the sign of this exchange and observe that the exact diagonalization results in Section 6 do not change). The goal of this basis rotation is to speed up the convergence of the Monte Carlo simulations of the wave-function (by providing a good variational sign structure to start with), but in principle the converged results should be identical in both bases. Note further that this sign change is useful at low frustration (such as here $J_2=0.2$), but may actually be not optimal at stronger frustration. As a bonus exercise, repeat the calculation with $J_2=0.8$, and see which basis (*i.e.* which sign in front of the exchange) leads to faster convergence."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before defining the Hamiltonian, we also need to specify the <PERSON>lbert space. For our case, this would be the chain spin-half degrees of freedom."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Spin based Hilbert Space\n", "hi = nk.hilbert.Spin(s=0.5, total_sz=0.0, N=g.n_nodes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that we impose here the total magnetization to be zero (it turns out to be the correct magnetization for the ground-state). As an exercise, check that the energy of the lowest state in other magnetization sectors is larger.\n", "\n", "Next, we define the custom graph Hamiltonian using the ``nk.operator.GraphOperator`` class, by providing the hilbert space ``hi``, the bond operators ``bondops=bond_operator`` and the corresponding bond color ``bondops_colors=bond_color``. The information about the graph (bonds and bond colors) are contained within the ``nk.hilbert.Spin`` object ``hi``."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Custom Hamiltonian operator\n", "op = nk.operator.GraphOperator(\n", "    hi, graph=g, bond_ops=bond_operator, bond_ops_colors=bond_color\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2) Defining the Machine\n", "\n", "For this tutorial, we shall use the most common type of neural network: fully connected feedforward neural network ``nk.machine.FFNN``. Other types of neural networks available will be discussed in other tutorials."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import netket.nn as nknn\n", "import flax.linen as nn\n", "\n", "import jax.numpy as jnp\n", "\n", "\n", "class FFNN(nn.Module):\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        x = nn.Den<PERSON>(\n", "            features=2 * x.shape[-1],\n", "            use_bias=True,\n", "            param_dtype=np.complex128,\n", "            kernel_init=nn.initializers.normal(stddev=0.01),\n", "            bias_init=nn.initializers.normal(stddev=0.01),\n", "        )(x)\n", "        x = nknn.log_cosh(x)\n", "        x = jnp.sum(x, axis=-1)\n", "        return x\n", "\n", "\n", "model = FFNN()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3) Variational Monte Carlo Optimisation\n", "\n", "We have now set up our model (<PERSON>, <PERSON>, <PERSON>lbert Space) and can proceed to optimise the variational ansatz we chose, namely the ``ffnn`` machine.\n", "\n", "To setup the variational Monte Carlo optimisation tool, we have to provide a sampler ``nk.sampler`` and an optimizer ``nk.optimizer``."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# We shall use an exchange Sampler which preserves the global magnetization (as this is a conserved quantity in the model)\n", "sa = nk.sampler.MetropolisExchange(hilbert=hi, graph=g, d_max=2)\n", "\n", "# Construct the variational state\n", "vs = nk.vqs.MCState(sa, model, n_samples=1008)\n", "\n", "# We choose a basic, albeit important, Optimizer: the Stochastic Gradient Descent\n", "opt = nk.optimizer.Sgd(learning_rate=0.01)\n", "\n", "# Stochastic Reconfiguration\n", "sr = nk.optimizer.SR(diag_shift=0.01)\n", "\n", "# We can then specify a Variational Monte Carlo object, using the Hamiltonian, sampler and optimizers chosen.\n", "# Note that we also specify the method to learn the parameters of the wave-function: here we choose the efficient\n", "# Stochastic reconfiguration (Sr), here in an iterative setup\n", "gs = nk.VMC(hamiltonian=op, optimizer=opt, variational_state=vs, preconditioner=sr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4) Measuring Observables\n", "\n", "Before running the optimization, it can be helpful to add some observables to keep track off during the optimization. For our purpose, let us measure the antiferromagnetic structure factor, defined as:\n", "\n", "$$ \\frac{1}{L} \\sum_{ij} \\langle \\hat{\\sigma}_{i}^z \\cdot \\hat{\\sigma}_{j}^z\\rangle e^{i\\pi(i-j)}$$."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["# We need to specify the local operators as a matrix acting on a local Hilbert space\n", "sf = []\n", "sites = []\n", "structure_factor = nk.operator.LocalOperator(hi, dtype=complex)\n", "for i in range(0, L):\n", "    for j in range(0, L):\n", "        structure_factor += (\n", "            (nk.operator.spin.sigmaz(hi, i) * nk.operator.spin.sigmaz(hi, j))\n", "            * ((-1) ** (i - j))\n", "            / L\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once again, notice that we had to multiply the exchange operator (matrix) by some factor. This is to account for the Marshall basis rotation we made in our model."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now optimize our variational ansatz. The optimization data for each iteration will be stored in a log file, which contains the following information:\n", "1. Mean, variance and uncertainty in the Energy $ \\langle \\hat{H} \\rangle $\n", "2. Mean, variance and uncertainty in the Energy Variance, $ \\langle\\hat{H}^{2}\\rangle-\\langle \\hat{H}\\rangle^{2}$.\n", "3. Acceptance rates of the sampler\n", "4. Mean, variance and uncertainty of observables (if specified)\n", "\n", "Now let's learn the ground-state!"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 600/600 [01:40<00:00,  5.99it/s, Energy=-23.0641-0.0055j ± 0.0045 [σ²=0.0207, R̂=0.9965]]\n"]}, {"data": {"text/plain": ["(JsonLog('test', mode=write, autoflush_cost=0.005)\n", "   Runtime cost:\n", "   \tLog:    0.3097798824310303\n", "   \tParams: 0.0022630691528320312,)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Run the optimization protocol\n", "gs.run(out=\"test\", n_iter=600, obs={\"Structure Factor\": structure_factor})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5) Data Visualisation\n", "\n", "Now that we have optimized our machine to find the ground state of the $J_1-J_2$ model, let's look at what we have.\n", "The relevant data are stored in the \".log\" file while the optimized parameters are in the \".wf\" file. The files are all in json format.\n", "\n", "We shall extract the energy as well as specified observables (antiferromagnetic structure factor in our case) from the \".log\" file."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Load the data from the .log file\n", "import json\n", "\n", "data = json.load(open(\"test.log\"))\n", "\n", "iters = data[\"Energy\"][\"iters\"]\n", "energy = data[\"Energy\"][\"Mean\"][\"real\"]\n", "sf = data[\"Structure Factor\"][\"Mean\"][\"real\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax1 = plt.subplots()\n", "ax1.plot(iters, energy, color=\"blue\", label=\"Energy\")\n", "ax1.set_ylabel(\"Energy\")\n", "ax1.set_xlabel(\"Iteration\")\n", "ax2 = ax1.twinx()\n", "ax2.plot(iters, np.array(sf), color=\"green\", label=\"Structure Factor\")\n", "ax2.set_ylabel(\"Structure Factor\")\n", "ax1.legend(loc=2)\n", "ax2.legend(loc=1)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also compute the average of those quantities (energy and neel order) over the last 50 iterations where the optimization seems to have converged."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structure factor = 3.797(0.039)\n", "Energy = -23.062(0.001)\n"]}], "source": ["print(\n", "    rf\"Structure factor = {np.mean(sf[-50:]):.3f}({np.std(np.array(sf[-50:]))/np.sqrt(50):.3f})\"\n", ")\n", "print(\n", "    rf\"Energy = {np.mean(energy[-50:]):.3f}({np.std(energy[-50:])/(np.sqrt(50)):.3f})\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6) Sanity Check: Exact Diagonalisation\n", "\n", "Now that we have obtained some results using VMC, it is a good time to check the quality of our results (at least for small system sizes). For this purpose, Netket provides exact diagonalisation tools."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["E_gs, ket_gs = nk.exact.lanczos_ed(op, compute_eigenvectors=True)\n", "structure_factor_gs = (\n", "    ket_gs.T.conj() @ structure_factor.to_linear_operator() @ ket_gs\n", ").real[0, 0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we have specified that we want the corresponding eigenvector (in order to compute observables)."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact Ground-state Structure Factor: 3.803\n", "Exact ground state energy = -23.064\n"]}], "source": ["print(f\"Exact Ground-state Structure Factor: {structure_factor_gs:.3f}\")\n", "print(f\"Exact ground state energy = {E_gs[0]:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So we see that the both energy and the structure factor we obtained is in agreement with the value obtained via exact diagonalisation."]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python (Netket development)", "language": "python", "name": "dev-netket"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 4}