{"cells": [{"cell_type": "markdown", "id": "96044385", "metadata": {}, "source": ["# Part 3: <PERSON>\n", "\n", "In this final tutorial, we will:\n", "- Implement Monte Carlo sampling for larger systems\n", "- Compute local energies using sparse operator connections\n", "- Estimate energies and gradients from samples\n", "- Build a complete VMC optimization loop\n", "- Explore advanced optimizers and future extensions\n", "\n", "This tutorial builds on the concepts from Parts 1 and 2, extending to the Monte Carlo regime where full summation over the <PERSON>lbert space is no longer feasible."]}, {"cell_type": "markdown", "id": "8650cd1f", "metadata": {}, "source": [":::{note}\n", "If you are executing this notebook on **Colab**, you will need to install NetKet:\n", ":::"]}, {"cell_type": "code", "execution_count": 1, "id": "5d96f12d", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:45.629094Z", "iopub.status.busy": "2025-07-13T14:53:45.628408Z", "iopub.status.idle": "2025-07-13T14:53:45.636215Z", "shell.execute_reply": "2025-07-13T14:53:45.635580Z"}, "mystnb": {"code_prompt_hide": "<PERSON>de", "code_prompt_show": "Show and uncomment to install NetKet on colab"}}, "outputs": [], "source": ["# %pip install --quiet netket"]}, {"cell_type": "code", "execution_count": 2, "id": "78837ba6", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:45.639509Z", "iopub.status.busy": "2025-07-13T14:53:45.639188Z", "iopub.status.idle": "2025-07-13T14:53:46.909208Z", "shell.execute_reply": "2025-07-13T14:53:46.908875Z"}, "tags": ["hide-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version (requires >=3.9) 3.12.11\n", "NetKet version (requires >=3.9.1) 3.19.dev26+gd94cf4597.d20250713\n"]}], "source": ["# Import necessary libraries\n", "import platform\n", "import netket as nk\n", "import numpy as np\n", "from tqdm.auto import tqdm\n", "from functools import partial\n", "\n", "# jax and jax.numpy\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "# Flax for neural network models\n", "import flax.linen as nn\n", "\n", "print(\"Python version (requires >=3.9)\", platform.python_version())\n", "print(\"NetKet version (requires >=3.9.1)\", nk.__version__)"]}, {"cell_type": "markdown", "id": "baefcf95", "metadata": {}, "source": ["## 1. Setup from Previous Tutorials\n", "\n", "Let's recreate the complete system from Parts 1 and 2:"]}, {"cell_type": "code", "execution_count": 3, "id": "235e2879", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:46.911048Z", "iopub.status.busy": "2025-07-13T14:53:46.910769Z", "iopub.status.idle": "2025-07-13T14:53:51.108300Z", "shell.execute_reply": "2025-07-13T14:53:51.108001Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact ground state energy: -34.010598\n"]}], "source": ["# Define the system\n", "L = 4\n", "g = nk.graph.Hypercube(length=L, n_dim=2, pbc=True)\n", "hi = nk.hilbert.Spin(s=1 / 2, N=g.n_nodes)\n", "\n", "# Build the Hamiltonian\n", "hamiltonian = nk.operator.LocalOperator(hi)\n", "\n", "# Add transverse field terms\n", "for site in g.nodes():\n", "    hamiltonian = hamiltonian - 1.0 * nk.operator.spin.sigmax(hi, site)\n", "\n", "# Add Ising interaction terms\n", "for i, j in g.edges():\n", "    hamiltonian = hamiltonian + nk.operator.spin.sigmaz(\n", "        hi, i\n", "    ) @ nk.operator.spin.sigmaz(hi, j)\n", "\n", "# Convert to JAX format\n", "hamiltonian_jax = hamiltonian.to_pauli_strings().to_jax_operator()\n", "\n", "# Compute exact ground state for comparison\n", "from scipy.sparse.linalg import eigsh\n", "\n", "e_gs, psi_gs = eigsh(hamiltonian.to_sparse(), k=1)\n", "e_gs = e_gs[0]\n", "psi_gs = psi_gs.reshape(-1)\n", "\n", "print(f\"Exact ground state energy: {e_gs:.6f}\")"]}, {"cell_type": "markdown", "id": "71f38885", "metadata": {"lines_to_next_cell": 2}, "source": ["## 2. Variational Models from Part 2\n", "\n", "Let's redefine our variational ansätze:"]}, {"cell_type": "code", "execution_count": 4, "id": "a3f67dc5", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:51.109968Z", "iopub.status.busy": "2025-07-13T14:53:51.109822Z", "iopub.status.idle": "2025-07-13T14:53:51.113860Z", "shell.execute_reply": "2025-07-13T14:53:51.113588Z"}}, "outputs": [], "source": ["# Mean Field Ansatz\n", "class MF(nn.Module):\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        lam = self.param(\"lambda\", nn.initializers.normal(), (1,), float)\n", "        p = nn.log_sigmoid(lam * x)\n", "        return 0.5 * jnp.sum(p, axis=-1)\n", "\n", "\n", "# <PERSON><PERSON><PERSON>\n", "class Jastrow(nn.Module):\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        n_sites = x.shape[-1]\n", "        J = self.param(\"J\", nn.initializers.normal(), (n_sites, n_sites), float)\n", "        dtype = jax.numpy.promote_types(J.dtype, x.dtype)\n", "        J = J.astype(dtype)\n", "        x = x.astype(dtype)\n", "        J_symm = J.T + J\n", "        return jnp.einsum(\"...i,ij,...j\", x, J_symm, x)"]}, {"cell_type": "markdown", "id": "1f0c42c9", "metadata": {}, "source": ["## 3. <PERSON>\n", "\n", "For larger problems, we cannot sum over the whole Hilbert space. Instead, we use Monte <PERSON> sampling to generate configurations according to $|\\psi(\\sigma)|^2$."]}, {"cell_type": "markdown", "id": "cb1accfb", "metadata": {}, "source": ["### 3.1 Setting up the Sampler\n", "\n", "We use a Metropolis sampler that proposes new states by flipping individual spins:"]}, {"cell_type": "code", "execution_count": 5, "id": "7ca6c711", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:51.115275Z", "iopub.status.busy": "2025-07-13T14:53:51.115171Z", "iopub.status.idle": "2025-07-13T14:53:51.138329Z", "shell.execute_reply": "2025-07-13T14:53:51.138000Z"}}, "outputs": [], "source": ["sampler = nk.sampler.MetropolisSampler(\n", "    hi,  # the hilbert space to be sampled\n", "    nk.sampler.rules.LocalRule(),  # the transition rule\n", "    n_chains=20,  # number of parallel chains\n", ")"]}, {"cell_type": "markdown", "id": "4575c73b", "metadata": {}, "source": ["### 3.2 Generating Samples\n", "\n", "Samplers are used as follows:\n", "1. Initialize the sampler state\n", "2. Reset when changing parameters\n", "3. Call `sample` to generate new configurations"]}, {"cell_type": "code", "execution_count": 6, "id": "3b5964bc", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:51.139934Z", "iopub.status.busy": "2025-07-13T14:53:51.139819Z", "iopub.status.idle": "2025-07-13T14:53:51.849057Z", "shell.execute_reply": "2025-07-13T14:53:51.848760Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample shape: (5, 100, 16)\n"]}], "source": ["# Example with Mean Field model\n", "model = MF()\n", "parameters = model.init(jax.random.key(0), np.ones((hi.size,)))\n", "\n", "# Initialize sampler state\n", "sampler_state = sampler.init_state(model, parameters, seed=1)\n", "sampler_state = sampler.reset(model, parameters, sampler_state)\n", "\n", "# Generate samples\n", "samples, sampler_state = sampler.sample(\n", "    model, parameters, state=sampler_state, chain_length=100\n", ")\n", "\n", "print(f\"Sample shape: {samples.shape}\")\n", "# Dimensions: (n_chains, chain_length, n_sites)\n", "# Note: chains are sometimes referred to as walkers"]}, {"cell_type": "markdown", "id": "c448e23f", "metadata": {}, "source": ["## 4. Computing Local Energies\n", "\n", "We want to compute the energy as an expectation value:\n", "\n", "$$\n", "   E = \\sum_i^{N_s} \\frac{E_\\text{loc}(\\sigma_i)}{N_s}\n", "$$\n", "\n", "where $\\sigma_i$ are the samples and $E_\\text{loc}$ is the local energy:\n", "\n", "$$\n", "  E_\\text{loc}(\\sigma) = \\frac{\\langle \\sigma |H|\\psi\\rangle}{\\langle \\sigma |\\psi\\rangle} = \\sum_\\eta \\langle\\sigma|H|\\eta\\rangle \\frac{\\psi(\\eta)}{\\psi(\\sigma)}\n", "$$"]}, {"cell_type": "markdown", "id": "70ba37ec", "metadata": {}, "source": ["### 4.1 Understanding Operator Connections\n", "\n", "The sum over $\\eta$ is only over configurations connected to $\\sigma$ by the Hamiltonian (i.e., where $\\langle\\sigma|H|\\eta\\rangle \\neq 0$). NetKet's operators provide this efficiently:"]}, {"cell_type": "code", "execution_count": 7, "id": "313d2856", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:51.850584Z", "iopub.status.busy": "2025-07-13T14:53:51.850445Z", "iopub.status.idle": "2025-07-13T14:53:52.299945Z", "shell.execute_reply": "2025-07-13T14:53:52.299639Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input configuration shape: (16,)\n", "Connected configurations shape: (17, 16)\n", "Matrix elements shape: (17,)\n"]}], "source": ["# Example: get connections for a single configuration\n", "sigma = hi.random_state(jax.random.key(1))\n", "eta, H_sigmaeta = hamiltonian_jax.get_conn_padded(sigma)\n", "\n", "print(f\"Input configuration shape: {sigma.shape}\")\n", "print(f\"Connected configurations shape: {eta.shape}\")\n", "print(f\"Matrix elements shape: {H_sigmaeta.shape}\")\n", "\n", "# For this Hamiltonian, each site connects to itself (diagonal) and its neighbors"]}, {"cell_type": "markdown", "id": "c4a84834", "metadata": {}, "source": ["This also works for batches of configurations:"]}, {"cell_type": "code", "execution_count": 8, "id": "1e8798fd", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.301476Z", "iopub.status.busy": "2025-07-13T14:53:52.301345Z", "iopub.status.idle": "2025-07-13T14:53:52.625197Z", "shell.execute_reply": "2025-07-13T14:53:52.624837Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Batch input shape: (4, 5, 16)\n", "Batch connected configurations shape: (4, 5, 17, 16)\n", "Batch matrix elements shape: (4, 5, 17)\n"]}], "source": ["sigma_batch = hi.random_state(jax.random.key(1), (4, 5))\n", "eta_batch, H_batch = hamiltonian_jax.get_conn_padded(sigma_batch)\n", "\n", "print(f\"Batch input shape: {sigma_batch.shape}\")\n", "print(f\"Batch connected configurations shape: {eta_batch.shape}\")\n", "print(f\"Batch matrix elements shape: {H_batch.shape}\")"]}, {"cell_type": "markdown", "id": "13a210e1", "metadata": {"lines_to_next_cell": 2}, "source": ["## 5. Exercise: Computing Local Energies\n", "\n", "Implement a function to compute local energies using the connection information:\n", "\n", "$$\n", "  E_\\text{loc}(\\sigma) = \\sum_\\eta \\langle\\sigma|H|\\eta\\rangle \\exp[\\log\\psi(\\eta) - \\log\\psi(\\sigma)]\n", "$$"]}, {"cell_type": "code", "execution_count": 9, "id": "a6ddbeb1", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.626665Z", "iopub.status.busy": "2025-07-13T14:53:52.626562Z", "iopub.status.idle": "2025-07-13T14:53:52.628706Z", "shell.execute_reply": "2025-07-13T14:53:52.628460Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["def compute_local_energies(model, parameters, hamiltonian_jax, sigma):\n", "    eta, H_sigmaeta = hamiltonian_jax.get_conn_padded(sigma)\n", "\n", "    # TODO: Compute log-psi for original configurations\n", "    logpsi_sigma = None  # model.apply(parameters, sigma)\n", "\n", "    # TODO: Compute log-psi for connected configurations\n", "    logpsi_eta = None  # model.apply(parameters, eta)\n", "\n", "    # TODO: To match dimensions for broadcasting, expand logpsi_sigma\n", "    # Hint: jnp.expand_dims(logpsi_sigma, -1) might help\n", "\n", "    # TODO: Compute local energies\n", "    # res = jnp.sum(H_sigmaeta * jnp.exp(logpsi_eta - logpsi_sigma), axis=-1)\n", "\n", "    return None  # TODO"]}, {"cell_type": "code", "execution_count": 10, "id": "b7738388", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.629970Z", "iopub.status.busy": "2025-07-13T14:53:52.629857Z", "iopub.status.idle": "2025-07-13T14:53:52.631968Z", "shell.execute_reply": "2025-07-13T14:53:52.631716Z"}, "tags": ["solution"]}, "outputs": [], "source": ["def compute_local_energies(model, parameters, hamiltonian_jax, sigma):\n", "    eta, H_sigmaeta = hamiltonian_jax.get_conn_padded(sigma)\n", "\n", "    logpsi_sigma = model.apply(parameters, sigma)\n", "    logpsi_eta = model.apply(parameters, eta)\n", "    logpsi_sigma = jnp.expand_dims(logpsi_sigma, -1)\n", "\n", "    res = jnp.sum(H_sigmaeta * jnp.exp(logpsi_eta - logpsi_sigma), axis=-1)\n", "\n", "    return res"]}, {"cell_type": "markdown", "id": "1ffdcd8f", "metadata": {}, "source": ["Test your implementation:"]}, {"cell_type": "code", "execution_count": 11, "id": "c13c353f", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.633273Z", "iopub.status.busy": "2025-07-13T14:53:52.633167Z", "iopub.status.idle": "2025-07-13T14:53:52.634809Z", "shell.execute_reply": "2025-07-13T14:53:52.634557Z"}}, "outputs": [], "source": ["# Uncomment after implementing compute_local_energies\n", "# assert compute_local_energies(model, parameters, hamiltonian_jax, samples[0]).shape == samples.shape[1:-1]\n", "# assert compute_local_energies(model, parameters, hamiltonian_jax, samples).shape == samples.shape[:-1]\n", "\n", "# Check that it JIT compiles\n", "# jax.jit(compute_local_energies, static_argnames='model')(model, parameters, hamiltonian_jax, sigma)\n", "# print(\"compute_local_energies implementation is correct!\")"]}, {"cell_type": "markdown", "id": "494b9dd6", "metadata": {"lines_to_next_cell": 2}, "source": ["## 6. Exercise: Estimating Energy from Samples\n", "\n", "Write a function that estimates the energy and its statistical error from samples. The error is given by:\n", "\n", "$$\n", "    \\epsilon_E = \\sqrt{\\frac{\\mathbb{V}\\text{ar}(E_\\text{loc})}{N_\\text{samples}}}\n", "$$"]}, {"cell_type": "code", "execution_count": 12, "id": "f75b159d", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.636149Z", "iopub.status.busy": "2025-07-13T14:53:52.636060Z", "iopub.status.idle": "2025-07-13T14:53:52.638425Z", "shell.execute_reply": "2025-07-13T14:53:52.638167Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["@partial(jax.jit, static_argnames=\"model\")\n", "def estimate_energy(model, parameters, hamiltonian_jax, sigma):\n", "    E_loc = compute_local_energies(model, parameters, hamiltonian_jax, sigma)\n", "\n", "    # TODO: Compute average energy\n", "    E_average = None  # jnp.mean(E_loc)\n", "\n", "    # TODO: Compute variance\n", "    E_variance = None  # jnp.var(E_loc)\n", "\n", "    # TODO: Compute error of the mean\n", "    E_error = None  # jnp.sqrt(E_variance / E_loc.size)\n", "\n", "    # Return a NetKet Stats object\n", "    return nk.stats.Stats(mean=E_average, error_of_mean=E_error, variance=E_variance)"]}, {"cell_type": "code", "execution_count": 13, "id": "008518b4", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.639658Z", "iopub.status.busy": "2025-07-13T14:53:52.639572Z", "iopub.status.idle": "2025-07-13T14:53:52.641820Z", "shell.execute_reply": "2025-07-13T14:53:52.641577Z"}, "lines_to_next_cell": 1, "tags": ["solution"]}, "outputs": [], "source": ["@partial(jax.jit, static_argnames='model')\n", "def estimate_energy(model, parameters, hamiltonian_jax, sigma):\n", "    E_loc = compute_local_energies(model, parameters, hamiltonian_jax, sigma)\n", "\n", "    E_average = jnp.mean(E_loc)\n", "    E_variance = jnp.var(E_loc)\n", "    E_error = jnp.sqrt(E_variance / E_loc.size)\n", "\n", "    return nk.stats.Stats(mean=E_average, error_of_mean=E_error, variance=E_variance)"]}, {"cell_type": "markdown", "id": "47689f26", "metadata": {}, "source": ["Test the energy estimation:"]}, {"cell_type": "code", "execution_count": 14, "id": "d3663f9e", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.643032Z", "iopub.status.busy": "2025-07-13T14:53:52.642950Z", "iopub.status.idle": "2025-07-13T14:53:52.644751Z", "shell.execute_reply": "2025-07-13T14:53:52.644477Z"}}, "outputs": [], "source": ["# Uncomment after implementing estimate_energy\n", "# energy_estimate = estimate_energy(model, parameters, hamiltonian_jax, samples)\n", "# print(\"Energy estimate:\", energy_estimate)"]}, {"cell_type": "markdown", "id": "c764c9c2", "metadata": {}, "source": ["Let's verify our Monte Carlo estimate against the exact calculation by generating more samples:"]}, {"cell_type": "code", "execution_count": 15, "id": "b228e714", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.645948Z", "iopub.status.busy": "2025-07-13T14:53:52.645872Z", "iopub.status.idle": "2025-07-13T14:53:52.647682Z", "shell.execute_reply": "2025-07-13T14:53:52.647420Z"}}, "outputs": [], "source": ["# Uncomment after implementing functions\n", "# samples_many, sampler_state = sampler.sample(model, parameters, state=sampler_state, chain_length=5000)\n", "\n", "# Compare with full summation from Part 2\n", "# def compute_energy_exact(model, parameters, hamiltonian_sparse):\n", "#     all_configurations = hi.all_states()\n", "#     logpsi = model.apply(parameters, all_configurations)\n", "#     psi = jnp.exp(logpsi)\n", "#     psi = psi / jnp.linalg.norm(psi)\n", "#     return psi.conj().T @ (hamiltonian_sparse @ psi)\n", "\n", "# hamiltonian_sparse = hamiltonian.to_sparse()\n", "# exact_energy = compute_energy_exact(model, parameters, hamiltonian_sparse)\n", "# mc_estimate = estimate_energy(model, parameters, hamiltonian_jax, samples_many)\n", "\n", "# print(f\"Exact calculation: {exact_energy:.6f}\")\n", "# print(f\"MC estimate: {mc_estimate}\")"]}, {"cell_type": "markdown", "id": "9c4360c1", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> Estimation with <PERSON>\n", "\n", "The gradient of the energy can be estimated using:\n", "\n", "$$\n", "    \\nabla_k E = \\mathbb{E}_{\\sigma\\sim|\\psi(\\sigma)|^2} \\left[ (\\nabla_k \\log\\psi(\\sigma))^* \\left( E_\\text{loc}(\\sigma) - \\langle E \\rangle\\right)\\right]\n", "$$\n", "\n", "We can compute this efficiently using JAX's vector-Jacobian product (VJP)."]}, {"cell_type": "markdown", "id": "3db5e6b1", "metadata": {}, "source": ["### 7.1 Understanding the Jacobian\n", "\n", "Think of $\\nabla_k \\log\\psi(\\sigma_i)$ as the JACOBIAN of the function $\\log\\psi_\\sigma : \\mathbb{R}^{N_\\text{pars}} \\rightarrow \\mathbb{R}^{N_\\text{samples}}$:"]}, {"cell_type": "code", "execution_count": 16, "id": "f3ee604e", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:52.648959Z", "iopub.status.busy": "2025-07-13T14:53:52.648877Z", "iopub.status.idle": "2025-07-13T14:53:53.318967Z", "shell.execute_reply": "2025-07-13T14:53:53.318661Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input parameters shape: {'params': {'J': (16, 16)}}\n", "Output shape: (500,)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Jacobian shape: {'params': {'J': (500, 16, 16)}}\n"]}], "source": ["# Example with <PERSON><PERSON><PERSON> model\n", "model_jastrow = Jastrow()\n", "parameters_jastrow = model_jastrow.init(\n", "    jax.random.key(0), hi.random_state(jax.random.key(0))\n", ")\n", "\n", "# Reshape samples to a vector\n", "sigma_vector = samples.reshape(-1, hi.size)\n", "\n", "# Define the function to differentiate\n", "logpsi_sigma_fun = lambda pars: model_jastrow.apply(pars, sigma_vector)\n", "\n", "print(f\"Input parameters shape: {jax.tree.map(lambda x: x.shape, parameters_jastrow)}\")\n", "print(f\"Output shape: {logpsi_sigma_fun(parameters_jastrow).shape}\")\n", "\n", "# We can compute the Jacobian\n", "jacobian = jax.jacrev(logpsi_sigma_fun)(parameters_jastrow)\n", "print(f\"Jacobian shape: {jax.tree.map(lambda x: x.shape, jacobian)}\")"]}, {"cell_type": "markdown", "id": "71b0d9da", "metadata": {"lines_to_next_cell": 2}, "source": ["## 8. Exercise: Energy and Gradient Estimation\n", "\n", "Implement a function that computes both energy and gradient estimates using VJP:"]}, {"cell_type": "code", "execution_count": 17, "id": "0e840344", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:53.320630Z", "iopub.status.busy": "2025-07-13T14:53:53.320493Z", "iopub.status.idle": "2025-07-13T14:53:53.323115Z", "shell.execute_reply": "2025-07-13T14:53:53.322863Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["@partial(jax.jit, static_argnames=\"model\")\n", "def estimate_energy_and_gradient(model, parameters, hamiltonian_jax, sigma):\n", "    # Reshape samples to remove extra batch dimensions\n", "    sigma = sigma.reshape(-1, sigma.shape[-1])\n", "\n", "    E_loc = compute_local_energies(model, parameters, hamiltonian_jax, sigma)\n", "\n", "    # TODO: Compute energy statistics\n", "    E_average = None  # jnp.mean(E_loc)\n", "    E_variance = None  # jnp.var(E_loc)\n", "    E_error = None  # jnp.sqrt(E_variance/E_loc.size)\n", "    E = nk.stats.Stats(mean=E_average, error_of_mean=E_error, variance=E_variance)\n", "\n", "    # TODO: Compute gradient using VJP\n", "    # Define function to differentiate\n", "    logpsi_sigma_fun = lambda pars: model.apply(pars, sigma)\n", "\n", "    # Use VJP to compute gradient efficiently\n", "    # _, vjpfun = jax.vjp(logpsi_sigma_fun, parameters)\n", "    # E_grad = vjpfun((E_loc - E_average)/E_loc.size)[0]\n", "\n", "    return E, None  # E_grad"]}, {"cell_type": "code", "execution_count": 18, "id": "7805821f", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:53.324456Z", "iopub.status.busy": "2025-07-13T14:53:53.324348Z", "iopub.status.idle": "2025-07-13T14:53:53.327013Z", "shell.execute_reply": "2025-07-13T14:53:53.326737Z"}, "lines_to_next_cell": 1, "tags": ["solution"]}, "outputs": [], "source": ["@partial(jax.jit, static_argnames='model')\n", "def estimate_energy_and_gradient(model, parameters, hamiltonian_jax, sigma):\n", "    # reshape the samples to a vector of samples with no extra batch dimensions\n", "    sigma = sigma.reshape(-1, sigma.shape[-1])\n", "\n", "    E_loc = compute_local_energies(model, parameters, hamiltonian_jax, sigma)\n", "\n", "    # compute the energy as well\n", "    E_average = jnp.mean(E_loc)\n", "    E_variance = jnp.var(E_loc)\n", "    E_error = jnp.sqrt(E_variance/E_loc.size)\n", "    E = nk.stats.Stats(mean=E_average, error_of_mean=E_error, variance=E_variance)\n", "\n", "    # compute the gradient using VJP\n", "    logpsi_sigma_fun = lambda pars : model.apply(pars, sigma)\n", "    _, vjpfun = jax.vjp(logpsi_sigma_fun, parameters)\n", "    E_grad = vjpfun((E_loc - E_average)/E_loc.size)[0]\n", "\n", "    return E, E_grad"]}, {"cell_type": "markdown", "id": "3<PERSON><PERSON><PERSON><PERSON>", "metadata": {}, "source": ["## 9. Exercise: Complete VMC Optimization Loop\n", "\n", "Now implement a complete VMC optimization using <PERSON> Carlo sampling:"]}, {"cell_type": "code", "execution_count": 19, "id": "2449e9e6", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:53.328330Z", "iopub.status.busy": "2025-07-13T14:53:53.328219Z", "iopub.status.idle": "2025-07-13T14:53:53.351715Z", "shell.execute_reply": "2025-07-13T14:53:53.351293Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\r", "  0%|                                                                                                                                                         | 0/300 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 300/300 [00:00<00:00, 2833989.19it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Settings\n", "model = MF()  # Try both MF() and Jastrow()\n", "sampler = nk.sampler.MetropolisSampler(hi, nk.sampler.rules.LocalRule(), n_chains=20)\n", "n_iters = 300\n", "chain_length = 1000 // sampler.n_chains\n", "\n", "# Initialize\n", "parameters = model.init(jax.random.key(0), np.ones((hi.size,)))\n", "sampler_state = sampler.init_state(model, parameters, seed=1)\n", "\n", "# Logging\n", "logger = nk.logging.RuntimeLog()\n", "\n", "for i in tqdm(range(n_iters)):\n", "    # TODO: Sample configurations\n", "    # sampler_state = sampler.reset(model, parameters, state=sampler_state)\n", "    # samples, sampler_state = sampler.sample(model, parameters, state=sampler_state, chain_length=chain_length)\n", "\n", "    # TODO: Compute energy and gradient\n", "    # E, E_grad = estimate_energy_and_gradient(model, parameters, hamiltonian_jax, samples)\n", "\n", "    # TODO: Update parameters using gradient descent (learning rate ~0.005)\n", "    # parameters = jax.tree.map(lambda x,y: x-0.005*y, parameters, E_grad)\n", "\n", "    # TODO: Log energy\n", "    # logger(step=i, item={'Energy': E})\n", "    pass"]}, {"cell_type": "code", "execution_count": 20, "id": "77e8db06", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:53.353162Z", "iopub.status.busy": "2025-07-13T14:53:53.353055Z", "iopub.status.idle": "2025-07-13T14:53:55.784829Z", "shell.execute_reply": "2025-07-13T14:53:55.784525Z"}, "tags": ["solution"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\r", "  0%|                                                                                                                                                         | 0/300 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "  0%|▍                                                                                                                                                | 1/300 [00:00<02:03,  2.43it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "  6%|████████▋                                                                                                                                       | 18/300 [00:00<00:06, 45.33it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 11%|████████████████▎                                                                                                                               | 34/300 [00:00<00:03, 75.14it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 17%|████████████████████████▎                                                                                                                      | 51/300 [00:00<00:02, 100.42it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 22%|███████████████████████████████▉                                                                                                               | 67/300 [00:00<00:01, 116.96it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 28%|███████████████████████████████████████▌                                                                                                       | 83/300 [00:00<00:01, 128.88it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 33%|███████████████████████████████████████████████▎                                                                                              | 100/300 [00:01<00:01, 139.70it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 39%|██████████████████████████████████████████████████████▉                                                                                       | 116/300 [00:01<00:01, 143.07it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 44%|██████████████████████████████████████████████████████████████▉                                                                               | 133/300 [00:01<00:01, 149.83it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 50%|██████████████████████████████████████████████████████████████████████▌                                                                       | 149/300 [00:01<00:01, 149.66it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 55%|██████████████████████████████████████████████████████████████████████████████▌                                                               | 166/300 [00:01<00:00, 152.96it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 61%|██████████████████████████████████████████████████████████████████████████████████████▌                                                       | 183/300 [00:01<00:00, 156.84it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 66%|██████████████████████████████████████████████████████████████████████████████████████████████▏                                               | 199/300 [00:01<00:00, 156.05it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 72%|█████████████████████████████████████████████████████████████████████████████████████████████████████▊                                        | 215/300 [00:01<00:00, 154.68it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 77%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████▊                                | 232/300 [00:01<00:00, 157.04it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 83%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▊                        | 249/300 [00:01<00:00, 159.50it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 89%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉                | 266/300 [00:02<00:00, 159.70it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", " 94%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉        | 283/300 [00:02<00:00, 161.86it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 300/300 [00:02<00:00, 164.15it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 300/300 [00:02<00:00, 131.90it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Settings\n", "model = J<PERSON>row()  # Try both MF() and J<PERSON>row()\n", "sampler = nk.sampler.MetropolisSampler(\n", "    hi,\n", "    nk.sampler.rules.LocalRule(),\n", "    n_chains=20\n", ")\n", "n_iters = 300\n", "chain_length = 1000 // sampler.n_chains\n", "\n", "# Initialize\n", "parameters = model.init(jax.random.key(0), np.ones((hi.size,)))\n", "sampler_state = sampler.init_state(model, parameters, seed=1)\n", "\n", "# Logging\n", "logger = nk.logging.RuntimeLog()\n", "\n", "for i in tqdm(range(n_iters)):\n", "    # sample\n", "    sampler_state = sampler.reset(model, parameters, state=sampler_state)\n", "    samples, sampler_state = sampler.sample(model, parameters, state=sampler_state, chain_length=chain_length)\n", "\n", "    # compute energy and gradient\n", "    E, E_grad = estimate_energy_and_gradient(model, parameters, hamiltonian_jax, samples)\n", "\n", "    # update parameters\n", "    parameters = jax.tree.map(lambda x,y: x-0.005*y, parameters, E_grad)\n", "\n", "    # log energy\n", "    logger(step=i, item={'Energy':E})"]}, {"cell_type": "markdown", "id": "ca4360b3", "metadata": {}, "source": ["Plot the optimization results:"]}, {"cell_type": "code", "execution_count": 21, "id": "1c3e01cb", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:55.786274Z", "iopub.status.busy": "2025-07-13T14:53:55.786150Z", "iopub.status.idle": "2025-07-13T14:53:55.788176Z", "shell.execute_reply": "2025-07-13T14:53:55.787896Z"}}, "outputs": [], "source": ["# Uncomment after running optimization\n", "# plt.figure(figsize=(12, 5))\n", "# plt.subplot(1, 2, 1)\n", "# plt.plot(logger.data['Energy']['iters'], logger.data['Energy']['Mean'])\n", "# plt.axhline(y=e_gs, color='r', linestyle='--', label='Exact ground state')\n", "# plt.xlabel('Iteration')\n", "# plt.ylabel('Energy')\n", "# plt.title('VMC Energy vs Iteration')\n", "# plt.legend()\n", "\n", "# plt.subplot(1, 2, 2)\n", "# plt.semilogy(logger.data['Energy']['iters'], np.abs(logger.data['Energy']['Mean'] - e_gs))\n", "# plt.xlabel('Iteration')\n", "# plt.ylabel('|Energy - Exact|')\n", "# plt.title('Error vs Iteration (log scale)')\n", "# plt.tight_layout()"]}, {"cell_type": "markdown", "id": "7d2b7cbc", "metadata": {}, "source": ["## 10. Advanced Topics and Extensions"]}, {"cell_type": "markdown", "id": "e6b101fd", "metadata": {}, "source": ["### 10.1 Better Optimizers with Optax\n", "\n", "You can use more sophisticated optimizers from the [optax](https://optax.readthedocs.io/en/latest/) library:"]}, {"cell_type": "code", "execution_count": 22, "id": "45a8f805", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:55.789745Z", "iopub.status.busy": "2025-07-13T14:53:55.789602Z", "iopub.status.idle": "2025-07-13T14:53:55.792600Z", "shell.execute_reply": "2025-07-13T14:53:55.792314Z"}, "lines_to_next_cell": 2}, "outputs": [], "source": ["import optax\n", "\n", "\n", "# Example optimization loop with <PERSON>\n", "def optimize_with_adam():\n", "    # Define optimizer\n", "    optimizer = optax.adam(learning_rate=0.01)\n", "\n", "    # Initialize\n", "    model = J<PERSON>row()\n", "    parameters = model.init(jax.random.key(0), np.ones((hi.size,)))\n", "    optimizer_state = optimizer.init(parameters)\n", "\n", "    logger = nk.logging.RuntimeLog()\n", "\n", "    for i in tqdm(range(100)):\n", "        # Sample and compute gradients (same as before)\n", "        # samples, sampler_state = ...\n", "        # E, E_grad = estimate_energy_and_gradient(...)\n", "\n", "        # Update with <PERSON>\n", "        # updates, optimizer_state = optimizer.update(E_grad, optimizer_state, parameters)\n", "        # parameters = optax.apply_updates(parameters, updates)\n", "\n", "        # logger(step=i, item={'Energy': E})\n", "        pass"]}, {"cell_type": "markdown", "id": "3d0baf36", "metadata": {"lines_to_next_cell": 2}, "source": ["### 10.2 Feed-Forward Neural Networks\n", "\n", "Try implementing a more complex ansatz using feed-forward networks:"]}, {"cell_type": "code", "execution_count": 23, "id": "59f14480", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:55.794296Z", "iopub.status.busy": "2025-07-13T14:53:55.794162Z", "iopub.status.idle": "2025-07-13T14:53:55.796954Z", "shell.execute_reply": "2025-07-13T14:53:55.796591Z"}, "lines_to_next_cell": 1, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["class FeedForward(nn.Module):\n", "    hidden_size: int = 32\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        # TODO: Implement a 2-layer feed-forward network\n", "        # Use nn.Dense layers with relu activation\n", "        # Example structure:\n", "        # x = nn.<PERSON><PERSON>(self.hidden_size)#...\n", "        # x = nn.relu(x)\n", "        # x = nn.<PERSON><PERSON>(self.hidden_size)#...\n", "        # return jnp.sum(x, axis=-1)  # Pool over sites\n", "        pass"]}, {"cell_type": "code", "execution_count": 24, "id": "7762da43", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:55.798369Z", "iopub.status.busy": "2025-07-13T14:53:55.798230Z", "iopub.status.idle": "2025-07-13T14:53:55.800986Z", "shell.execute_reply": "2025-07-13T14:53:55.800673Z"}, "lines_to_next_cell": 2, "tags": ["solution"]}, "outputs": [], "source": ["class FeedForward(nn.Module):\n", "    hidden_size: int = 32\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        # Use nn.Dense layers with relu activation\n", "        x = nn.Dense(self.hidden_size)(x)\n", "        x = nn.relu(x)\n", "        x = nn.Dense(self.hidden_size)(x)\n", "        return jnp.sum(x, axis=-1)  # Pool over sites"]}, {"cell_type": "markdown", "id": "87319fc4", "metadata": {"lines_to_next_cell": 2}, "source": ["### 10.3 Comp<PERSON>on of Different Ansätze\n", "\n", "Compare the performance of different variational ansätze:"]}, {"cell_type": "code", "execution_count": 25, "id": "501a9211", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:53:55.802513Z", "iopub.status.busy": "2025-07-13T14:53:55.802382Z", "iopub.status.idle": "2025-07-13T14:53:55.804678Z", "shell.execute_reply": "2025-07-13T14:53:55.804401Z"}, "lines_to_next_cell": 2, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["def compare_ansatze():\n", "    \"\"\"Compare Mean Field, Jastrow, and Feed-Forward ansätze\"\"\"\n", "    results = {}\n", "\n", "    for name, model_class in [(\"<PERSON><PERSON><PERSON>\", MF), (\"Jastrow\", Jastrow)]:\n", "        print(f\"Optimizing {name} ansatz...\")\n", "\n", "        # Run optimization (implement the loop)\n", "        # Store final energy in results[name]\n", "\n", "    # Plot comparison\n", "    # plt.figure()\n", "    # for name, energy_history in results.items():\n", "    #     plt.semilogy(energy_history - e_gs, label=name)\n", "    # plt.xlabel('Iteration')\n", "    # plt.ylabel('Energy Error')\n", "    # plt.legend()\n", "    # plt.title('Comparison of Variational Ansätze')"]}, {"cell_type": "markdown", "id": "0185b43f", "metadata": {}, "source": ["## Summary\n", "\n", "In this tutorial, you learned:\n", "- How to implement Monte Carlo sampling for VMC calculations\n", "- How to compute local energies using operator connections\n", "- How to estimate energies and gradients from samples\n", "- How to build complete VMC optimization loops\n", "- How to use advanced optimizers and neural network architectures\n", "\n", "You now have the tools to quickly get started in running VMC calculations without worrying about the implementation details of sampling and operators. This provides a foundation for implementing more advanced techniques like:\n", "\n", "- Stochastic Reconfiguration (Natural Gradients)\n", "- Time evolution and dynamics\n", "- More sophisticated neural network architectures\n", "- Multi-GPU distributed calculations\n", "\n", "The modular design allows you to focus on the physics and machine learning aspects while NetKet handles the computational infrastructure."]}, {"cell_type": "code", "execution_count": null, "id": "bf39873f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}