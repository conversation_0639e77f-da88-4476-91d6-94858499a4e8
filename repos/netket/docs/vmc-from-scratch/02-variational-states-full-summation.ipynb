{"cells": [{"cell_type": "markdown", "id": "cad9a433", "metadata": {}, "source": ["# Part 2: Variational States with Full Summation\n", "\n", "In this second tutorial, we will:\n", "- Implement variational ansätze using Flax\n", "- Compute energies using full summation over the Hilbert space\n", "- Learn JAX/JIT compilation techniques\n", "- Implement gradient computation and optimization\n", "- Explore different variational ansätze (Mean Field and Jastrow)\n", "\n", "This tutorial builds on the Hamiltonian and operator concepts from Part 1."]}, {"cell_type": "markdown", "id": "9d5ad9cd", "metadata": {}, "source": [":::{note}\n", "If you are executing this notebook on **Colab**, you will need to install NetKet:\n", ":::"]}, {"cell_type": "code", "execution_count": 1, "id": "c2298499", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:44.960118Z", "iopub.status.busy": "2025-07-13T14:55:44.959620Z", "iopub.status.idle": "2025-07-13T14:55:44.965123Z", "shell.execute_reply": "2025-07-13T14:55:44.964517Z"}, "mystnb": {"code_prompt_hide": "<PERSON>de", "code_prompt_show": "Show and uncomment to install NetKet on colab"}}, "outputs": [], "source": ["# %pip install --quiet netket"]}, {"cell_type": "code", "execution_count": 2, "id": "2c50ccdf", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:44.967155Z", "iopub.status.busy": "2025-07-13T14:55:44.966973Z", "iopub.status.idle": "2025-07-13T14:55:46.242385Z", "shell.execute_reply": "2025-07-13T14:55:46.242059Z"}, "tags": ["hide-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version (requires >=3.9) 3.12.11\n", "NetKet version (requires >=3.9.1) 3.19.dev26+gd94cf4597.d20250713\n"]}], "source": ["# Import necessary libraries\n", "import platform\n", "import netket as nk\n", "import numpy as np\n", "\n", "# jax and jax.numpy\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "# Flax for neural network models\n", "import flax.linen as nn\n", "\n", "print(\"Python version (requires >=3.9)\", platform.python_version())\n", "print(\"NetKet version (requires >=3.9.1)\", nk.__version__)"]}, {"cell_type": "markdown", "id": "acbed7b7", "metadata": {}, "source": ["## 1. Setup from Previous Tutorial\n", "\n", "Let's quickly recreate the system from Part 1:"]}, {"cell_type": "code", "execution_count": 3, "id": "c7420350", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:46.243906Z", "iopub.status.busy": "2025-07-13T14:55:46.243729Z", "iopub.status.idle": "2025-07-13T14:55:50.532430Z", "shell.execute_reply": "2025-07-13T14:55:50.532102Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact ground state energy: -34.010598\n"]}], "source": ["# Define the system\n", "L = 4\n", "g = nk.graph.Hypercube(length=L, n_dim=2, pbc=True)\n", "hi = nk.hilbert.Spin(s=1 / 2, N=g.n_nodes)\n", "\n", "# Build the Hamiltonian (solution from Part 1)\n", "hamiltonian = nk.operator.LocalOperator(hi)\n", "\n", "# Add transverse field terms\n", "for site in g.nodes():\n", "    hamiltonian = hamiltonian - 1.0 * nk.operator.spin.sigmax(hi, site)\n", "\n", "# Add Ising interaction terms\n", "for i, j in g.edges():\n", "    hamiltonian = hamiltonian + nk.operator.spin.sigmaz(\n", "        hi, i\n", "    ) @ nk.operator.spin.sigmaz(hi, j)\n", "\n", "# Convert to JAX format\n", "hamiltonian_jax = hamiltonian.to_pauli_strings().to_jax_operator()\n", "\n", "# Compute exact ground state for comparison\n", "from scipy.sparse.linalg import eigsh\n", "\n", "e_gs, psi_gs = eigsh(hamiltonian.to_sparse(), k=1)\n", "e_gs = e_gs[0]\n", "psi_gs = psi_gs.reshape(-1)\n", "\n", "print(f\"Exact ground state energy: {e_gs:.6f}\")"]}, {"cell_type": "markdown", "id": "8b86f222", "metadata": {}, "source": ["## 2. Variational Ansatz & JAX/Flax Fundamentals\n", "\n", "In this section, we'll implement variational ansätze to approximate the ground state. We'll use JAX and Flax to define models that compute the **logarithm** of the wave-function amplitudes.\n", "\n", "For a variational state $|\\Psi\\rangle$, we define:\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi \\rangle = \\exp\\left[\\mathrm{Model}(\\sigma^{z}_1,\\dots \\sigma^{z}_N ; \\theta ) \\right], $$\n", "\n", "where $\\theta$ are the variational parameters."]}, {"cell_type": "markdown", "id": "2cc0df97", "metadata": {"lines_to_next_cell": 2}, "source": ["### 2.1 Mean-<PERSON> Ansatz\n", "\n", "We now would like to find a variational approximation of the ground state of this Hamiltonian. \n", "As a first step, we can try to use a very simple mean field ansatz: \n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{mf}} \\rangle = \\Pi_{i=1}^{N} \\Phi(\\sigma^{z}_i), $$\n", "\n", "where the variational parameters are the single-spin wave functions, which we can further take to be normalized: \n", "\n", "$$ |\\Phi(\\uparrow)|^2 + |\\Phi(\\downarrow)|^2 =1, $$\n", "\n", "and we can further write $ \\Phi(\\sigma^z) = \\sqrt{P(\\sigma^z)}e^{i \\phi(\\sigma^z)}$. In order to simplify the presentation, we take here and in the following examples the phase $ \\phi=0 $. In this specific model this is without loss of generality, since it is known that the ground state is real and positive. \n", "\n", "For the normalized single-spin probability we will take a sigmoid form: \n", "\n", "$$ P(\\sigma_z; \\lambda) = 1/(1+\\exp(-\\lambda \\sigma_z)), $$\n", "\n", "thus depending on the real-valued variational parameter $\\lambda$. \n", "In NetKet one has to define a variational function approximating the **logarithm** of the wave-function amplitudes (or density-matrix values).\n", "We call this variational function _the Model_ (yes, caps on the M).\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{mf}} \\rangle = \\exp\\left[\\mathrm{Model}(\\sigma^{z}_1,\\dots \\sigma^{z}_N ; \\theta ) \\right], $$\n", "\n", "where $\\theta$ is a set of parameters. \n", "In this case, the parameter of the model will be just one: $\\lambda$.  \n", "\n", "The Model can be defined using one of the several *functional* jax frameworks such as Jax/Stax, Flax or Haiku. \n", "NetKet includes several pre-built models and layers built with [Flax](https://github.com/google/flax), so we will be using it for the rest of the notebook. "]}, {"cell_type": "code", "execution_count": 4, "id": "f2453342", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:50.534016Z", "iopub.status.busy": "2025-07-13T14:55:50.533884Z", "iopub.status.idle": "2025-07-13T14:55:50.536958Z", "shell.execute_reply": "2025-07-13T14:55:50.536650Z"}}, "outputs": [], "source": ["# A Flax model must be a class subclassing `nn.Module`\n", "class MF(nn.Module):\n", "\n", "    # The __call__(self, x) function should take as\n", "    # input a batch of states x.shape = (n_samples, N)\n", "    # and should return a vector of n_samples log-amplitudes\n", "    @nn.compact\n", "    def __call__(self, x):\n", "\n", "        # A tensor of variational parameters is defined by calling\n", "        # the method `self.param` where the arguments are:\n", "        # - arbitrary name used to refer to this set of parameters\n", "        # - an initializer used to provide the initial values.\n", "        # - The shape of the tensor\n", "        # - The dtype of the tensor.\n", "        lam = self.param(\"lambda\", nn.initializers.normal(), (1,), float)\n", "\n", "        # compute the probabilities\n", "        p = nn.log_sigmoid(lam * x)\n", "\n", "        # sum the output\n", "        return 0.5 * jnp.sum(p, axis=-1)"]}, {"cell_type": "markdown", "id": "e27942ba", "metadata": {}, "source": ["### 2.2 Working with Flax Models\n", "\n", "The model itself is only a set of instructions. To initialize parameters:"]}, {"cell_type": "code", "execution_count": 5, "id": "37dda1ee", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:50.538226Z", "iopub.status.busy": "2025-07-13T14:55:50.538131Z", "iopub.status.idle": "2025-07-13T14:55:50.814855Z", "shell.execute_reply": "2025-07-13T14:55:50.814536Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters structure:\n", "{'params': {'lambda': A<PERSON>y([-0.01280742], dtype=float64)}}\n"]}], "source": ["# create an instance of the model\n", "model = MF()\n", "\n", "# pick a RNG key to initialise the random parameters\n", "key = jax.random.key(0)\n", "\n", "# initialise the weights\n", "parameters = model.init(key, np.random.rand(hi.size))\n", "\n", "print(\"Parameters structure:\")\n", "print(parameters)"]}, {"cell_type": "markdown", "id": "ca8a4216", "metadata": {}, "source": ["Parameters are stored as 'pytrees' - nested dictionaries whose leaves are numerical arrays. You can apply mathematical operations using `jax.tree.map`:"]}, {"cell_type": "code", "execution_count": 6, "id": "bd9da910", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:50.816339Z", "iopub.status.busy": "2025-07-13T14:55:50.816202Z", "iopub.status.idle": "2025-07-13T14:55:50.818669Z", "shell.execute_reply": "2025-07-13T14:55:50.818414Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["multiply_by_10:              {'a': 10, 'b': 20}\n", "add dict1 and dict2:        {'a': 2, 'b': 0}\n", "subtract dict1 and dict2:   {'a': 0, 'b': 4}\n"]}], "source": ["# Examples of tree operations\n", "dict1 = {\"a\": 1, \"b\": 2}\n", "dict2 = {\"a\": 1, \"b\": -2}\n", "\n", "print(\"multiply_by_10:             \", jax.tree.map(lambda x: 10 * x, dict1))\n", "print(\"add dict1 and dict2:       \", jax.tree.map(lambda x, y: x + y, dict1, dict2))\n", "print(\"subtract dict1 and dict2:  \", jax.tree.map(lambda x, y: x - y, dict1, dict2))"]}, {"cell_type": "markdown", "id": "82469f9d", "metadata": {}, "source": ["To evaluate the model:"]}, {"cell_type": "code", "execution_count": 7, "id": "b62c965d", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:50.819984Z", "iopub.status.busy": "2025-07-13T14:55:50.819871Z", "iopub.status.idle": "2025-07-13T14:55:51.058516Z", "shell.execute_reply": "2025-07-13T14:55:51.058202Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Log-psi shape: (4,)\n", "Log-psi values: [-5.52613034 -5.5581489  -5.54534147 -5.54534147]\n"]}], "source": ["# generate 4 random inputs\n", "inputs = hi.random_state(jax.random.key(1), (4,))\n", "\n", "log_psi = model.apply(parameters, inputs)\n", "print(f\"Log-psi shape: {log_psi.shape}\")\n", "print(f\"Log-psi values: {log_psi}\")"]}, {"cell_type": "markdown", "id": "0d25ca39", "metadata": {"lines_to_next_cell": 2}, "source": ["## 3. Exercise: Converting to Normalized Wavefunction\n", "\n", "Write a function that takes the model and parameters, and returns the exponentiated wavefunction, properly normalized."]}, {"cell_type": "code", "execution_count": 8, "id": "9bd012cc", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.060182Z", "iopub.status.busy": "2025-07-13T14:55:51.060039Z", "iopub.status.idle": "2025-07-13T14:55:51.062221Z", "shell.execute_reply": "2025-07-13T14:55:51.061844Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["def to_array(model, parameters):\n", "    # Begin by generating all configurations in the hilbert space.\n", "    all_configurations = hi.all_states()\n", "\n", "    # TODO: Evaluate the model and convert to a normalised wavefunction.\n", "    # Hint: Use model.apply, jnp.exp, and jnp.linalg.norm\n", "\n", "    return None  # TODO: return normalized wavefunction"]}, {"cell_type": "code", "execution_count": 9, "id": "28d2ed14", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.064277Z", "iopub.status.busy": "2025-07-13T14:55:51.064106Z", "iopub.status.idle": "2025-07-13T14:55:51.066561Z", "shell.execute_reply": "2025-07-13T14:55:51.066197Z"}, "tags": ["solution"]}, "outputs": [], "source": ["def to_array(model, parameters):\n", "    # begin by generating all configurations in the hilbert space.\n", "    all_configurations = hi.all_states()\n", "\n", "    # now evaluate the model, and convert to a normalised wavefunction.\n", "    logpsi = model.apply(parameters, all_configurations)\n", "    psi = jnp.exp(logpsi)\n", "    psi = psi / jnp.linalg.norm(psi)\n", "    return psi"]}, {"cell_type": "markdown", "id": "5f45b3c4", "metadata": {}, "source": ["Test your implementation:"]}, {"cell_type": "code", "execution_count": 10, "id": "b78a62fc", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.068514Z", "iopub.status.busy": "2025-07-13T14:55:51.068260Z", "iopub.status.idle": "2025-07-13T14:55:51.070316Z", "shell.execute_reply": "2025-07-13T14:55:51.069970Z"}}, "outputs": [], "source": ["# Uncomment after implementing to_array\n", "# assert to_array(model, parameters).shape == (hi.n_states, )\n", "# assert np.all(to_array(model, parameters) > 0)\n", "# np.testing.assert_allclose(np.linalg.norm(to_array(model, parameters)), 1.0)\n", "# print(\"to_array implementation is correct!\")"]}, {"cell_type": "markdown", "id": "b1fa1f63", "metadata": {}, "source": ["### 3.1 JAX JIT Compilation\n", "\n", "If you implemented everything using `jnp.` operations, we can JIT-compile for speed:"]}, {"cell_type": "code", "execution_count": 11, "id": "f6ef643e", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.071718Z", "iopub.status.busy": "2025-07-13T14:55:51.071584Z", "iopub.status.idle": "2025-07-13T14:55:51.073368Z", "shell.execute_reply": "2025-07-13T14:55:51.073109Z"}}, "outputs": [], "source": ["# Uncomment after implementing to_array\n", "# static_argnames must be used for any argument that is not a pytree or array\n", "# to_array_jit = jax.jit(to_array, static_argnames=\"model\")\n", "\n", "# Run once to compile\n", "# to_array_jit(model, parameters)\n", "# print(\"JIT compilation successful!\")"]}, {"cell_type": "markdown", "id": "f1e5cd49", "metadata": {"lines_to_next_cell": 2}, "source": ["## 4. Exercise: Computing Energy\n", "\n", "Write a function that computes the energy of the variational state:"]}, {"cell_type": "code", "execution_count": 12, "id": "dafdd06c", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.074633Z", "iopub.status.busy": "2025-07-13T14:55:51.074540Z", "iopub.status.idle": "2025-07-13T14:55:51.076393Z", "shell.execute_reply": "2025-07-13T14:55:51.076136Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["def compute_energy(model, parameters, hamiltonian_sparse):\n", "    # TODO: Get the wavefunction and compute <psi|H|psi>\n", "    # Hint: Use to_array and matrix multiplication\n", "\n", "    return None  # TODO: return energy"]}, {"cell_type": "code", "execution_count": 13, "id": "fdc1f3a5", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.077665Z", "iopub.status.busy": "2025-07-13T14:55:51.077554Z", "iopub.status.idle": "2025-07-13T14:55:51.079802Z", "shell.execute_reply": "2025-07-13T14:55:51.079459Z"}, "lines_to_next_cell": 1, "tags": ["solution"]}, "outputs": [], "source": ["def compute_energy(model, parameters, hamiltonian_sparse):\n", "    psi = to_array(model, parameters)\n", "    return psi.conj().T @ (hamiltonian_sparse @ psi)"]}, {"cell_type": "code", "execution_count": 14, "id": "06edd215", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:51.081392Z", "iopub.status.busy": "2025-07-13T14:55:51.081259Z", "iopub.status.idle": "2025-07-13T14:55:52.595193Z", "shell.execute_reply": "2025-07-13T14:55:52.594877Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["compute_energy implementation is correct!\n"]}], "source": ["# Test your implementation\n", "hamiltonian_sparse = hamiltonian.to_sparse()\n", "hamiltonian_jax_sparse = hamiltonian_jax.to_sparse()\n", "\n", "assert compute_energy(model, parameters, hamiltonian_sparse).shape == ()\n", "assert compute_energy(model, parameters, hamiltonian_sparse) < 0\n", "print(\"compute_energy implementation is correct!\")\n", "\n", "# We can also JIT-compile this\n", "# compute_energy_jit = jax.jit(compute_energy, static_argnames=\"model\")"]}, {"cell_type": "markdown", "id": "5424c356", "metadata": {}, "source": ["## 5. Gradient Computation\n", "\n", "JAX makes computing gradients easy. We can differentiate the energy with respect to parameters:"]}, {"cell_type": "code", "execution_count": 15, "id": "1dabcbc7", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:52.596812Z", "iopub.status.busy": "2025-07-13T14:55:52.596676Z", "iopub.status.idle": "2025-07-13T14:55:52.598865Z", "shell.execute_reply": "2025-07-13T14:55:52.598600Z"}}, "outputs": [], "source": ["from functools import partial\n", "\n", "\n", "# JIT the combined energy and gradient function\n", "@partial(jax.jit, static_argnames=\"model\")\n", "def compute_energy_and_gradient(model, parameters, hamiltonian_sparse):\n", "    grad_fun = jax.value_and_grad(compute_energy, argnums=1)\n", "    return grad_fun(model, parameters, hamiltonian_sparse)\n", "\n", "\n", "# Example usage (uncomment after implementing compute_energy)\n", "# energy, gradient = compute_energy_and_gradient(model, parameters, hamiltonian_jax_sparse)\n", "# print(\"Energy:\", energy)\n", "# print(\"Gradient structure:\", jax.tree.map(lambda x: x.shape, gradient))"]}, {"cell_type": "markdown", "id": "d9d7c729", "metadata": {}, "source": ["## 6. Exercise: Optimization Loop\n", "\n", "Now implement an optimization loop to find the ground state. Use gradient descent with learning rate 0.01 for 100 iterations:"]}, {"cell_type": "code", "execution_count": 16, "id": "630842a7", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:52.600801Z", "iopub.status.busy": "2025-07-13T14:55:52.600661Z", "iopub.status.idle": "2025-07-13T14:55:52.632526Z", "shell.execute_reply": "2025-07-13T14:55:52.631562Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4efe4c52298a46f59fb39768999fbcc1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from tqdm.auto import tqdm\n", "\n", "# Initialize\n", "model = MF()\n", "parameters = model.init(jax.random.key(0), np.ones((hi.size,)))\n", "\n", "# Logging\n", "logger = nk.logging.RuntimeLog()\n", "\n", "iterations = 100\n", "\n", "for i in tqdm(range(iterations)):\n", "    # TODO: compute energy and gradient\n", "    # energy, gradient = ...\n", "\n", "    # TODO: update parameters using gradient descent\n", "    # parameters = jax.tree.map(lambda x,y: x - 0.01*y, parameters, gradient)\n", "\n", "    # Log energy\n", "    # logger(step=i, item={'Energy': energy})\n", "    pass"]}, {"cell_type": "code", "execution_count": 17, "id": "5065e69d", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:52.637089Z", "iopub.status.busy": "2025-07-13T14:55:52.636923Z", "iopub.status.idle": "2025-07-13T14:55:55.254864Z", "shell.execute_reply": "2025-07-13T14:55:55.254445Z"}, "tags": ["solution"]}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4107c4c619604deb9248fac8ff8c52fb", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from tqdm.auto import tqdm\n", "\n", "# Initialize\n", "model = MF()\n", "parameters = model.init(jax.random.key(0), np.ones((hi.size, )))\n", "\n", "# Logging\n", "logger = nk.logging.RuntimeLog()\n", "\n", "for i in tqdm(range(100)):\n", "    # compute energy and gradient\n", "    energy, gradient = compute_energy_and_gradient(model, parameters, hamiltonian_jax_sparse)\n", "\n", "    # update parameters\n", "    parameters = jax.tree.map(lambda x,y:x-0.01*y, parameters, gradient)\n", "\n", "    # log energy\n", "    logger(step=i, item={'Energy':energy})"]}, {"cell_type": "markdown", "id": "7ec76a5a", "metadata": {}, "source": ["Plot the optimization progress:"]}, {"cell_type": "code", "execution_count": 18, "id": "2cf58f46", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:55.256561Z", "iopub.status.busy": "2025-07-13T14:55:55.256389Z", "iopub.status.idle": "2025-07-13T14:55:55.258407Z", "shell.execute_reply": "2025-07-13T14:55:55.258116Z"}}, "outputs": [], "source": ["# Uncomment after running optimization\n", "# plt.figure(figsize=(10, 6))\n", "# plt.subplot(1, 2, 1)\n", "# plt.plot(logger.data['Energy']['iters'], logger.data['Energy']['value'])\n", "# plt.xlabel('Iteration')\n", "# plt.ylabel('Energy')\n", "# plt.title('Energy vs Iteration')\n", "\n", "# plt.subplot(1, 2, 2)\n", "# plt.semilogy(logger.data['Energy']['iters'], np.abs(logger.data['Energy']['value'] - e_gs))\n", "# plt.xlabel('Iteration')\n", "# plt.ylabel('|Energy - Exact|')\n", "# plt.title('Error vs Iteration (log scale)')\n", "# plt.tight_layout()"]}, {"cell_type": "markdown", "id": "29da58bc", "metadata": {"lines_to_next_cell": 2}, "source": ["## 7. Exercise: <PERSON><PERSON><PERSON>\n", "\n", "Now implement a more sophisticated Jastrow ansatz:\n", "\n", "$$ \\langle \\sigma^{z}_1,\\dots \\sigma^{z}_N| \\Psi_{\\mathrm{jas}} \\rangle = \\exp \\left( \\sum_{ij}\\sigma_i J_{ij}\\sigma_j\\right),$$"]}, {"cell_type": "code", "execution_count": 19, "id": "8b6884d0", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:55.259897Z", "iopub.status.busy": "2025-07-13T14:55:55.259735Z", "iopub.status.idle": "2025-07-13T14:55:55.262949Z", "shell.execute_reply": "2025-07-13T14:55:55.262652Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["class Jastrow(nn.Module):\n", "\n", "    @nn.compact\n", "    def __call__(self, input_x):\n", "\n", "        n_sites = input_x.shape[-1]\n", "\n", "        # Define the J matrix parameter\n", "        J = self.param(\"J\", nn.initializers.normal(), (n_sites, n_sites), float)\n", "\n", "        # Ensure same data types\n", "        dtype = jax.numpy.promote_types(J.dtype, input_x.dtype)\n", "        J = J.astype(dtype)\n", "        input_x = input_x.astype(dtype)\n", "\n", "        # Symmetrize J matrix\n", "        J_symm = J.T + J\n", "\n", "        # TODO: Compute the result using vectorized operations\n", "        # Hint: use jnp.einsum(\"...i,ij,...j\", input_x, J_symm, input_x)\n", "        res = None # TODO: implement this\n", "\n", "        return res"]}, {"cell_type": "code", "execution_count": 20, "id": "27356dd9", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:55.264779Z", "iopub.status.busy": "2025-07-13T14:55:55.264668Z", "iopub.status.idle": "2025-07-13T14:55:55.267569Z", "shell.execute_reply": "2025-07-13T14:55:55.267256Z"}, "tags": ["solution"]}, "outputs": [], "source": ["class Jastrow(nn.Module):\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        n_sites = x.shape[-1]\n", "        J = self.param(\n", "            \"J\", nn.initializers.normal(), (n_sites,n_sites), float\n", "        )\n", "        J_symm = J.T + J\n", "        return jnp.einsum(\"...i,ij,...j\", x, J_symm, x)"]}, {"cell_type": "markdown", "id": "00386aff", "metadata": {}, "source": ["Test the Jastrow implementation:"]}, {"cell_type": "code", "execution_count": 21, "id": "33ea2a2f", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:55.269076Z", "iopub.status.busy": "2025-07-13T14:55:55.268956Z", "iopub.status.idle": "2025-07-13T14:55:55.270993Z", "shell.execute_reply": "2025-07-13T14:55:55.270648Z"}}, "outputs": [], "source": ["# Uncomment after implementing J<PERSON>row\n", "# model_jastrow = Jastrow()\n", "\n", "# one_sample = hi.random_state(jax.random.key(0))\n", "# batch_samples = hi.random_state(jax.random.key(0), (5,))\n", "# multibatch_samples = hi.random_state(jax.random.key(0), (5,4,))\n", "\n", "# parameters_jastrow = model_jastrow.init(jax.random.key(0), one_sample)\n", "# assert parameters_jastrow['params']['J'].shape == (hi.size, hi.size)\n", "# assert model_jastrow.apply(parameters_jastrow, one_sample).shape == ()\n", "# assert model_jastrow.apply(parameters_jastrow, batch_samples).shape == batch_samples.shape[:-1]\n", "# assert model_jastrow.apply(parameters_jastrow, multibatch_samples).shape == multibatch_samples.shape[:-1]\n", "# print(\"Jastrow implementation is correct!\")"]}, {"cell_type": "markdown", "id": "92dfbf23", "metadata": {}, "source": ["## 8. Exercise: Optimize the Jastrow Ansatz\n", "\n", "Repeat the optimization analysis with the Jastrow ansatz and compare the results:"]}, {"cell_type": "code", "execution_count": 22, "id": "343<PERSON><PERSON>", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:55:55.272461Z", "iopub.status.busy": "2025-07-13T14:55:55.272342Z", "iopub.status.idle": "2025-07-13T14:55:55.274091Z", "shell.execute_reply": "2025-07-13T14:55:55.273818Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["# TODO: Implement optimization loop for J<PERSON>row ansatz\n", "# Use the same structure as before but with model_jastrow"]}, {"cell_type": "markdown", "id": "51570aa1", "metadata": {}, "source": ["## Summary\n", "\n", "In this tutorial, you learned:\n", "- How to implement variational ansätze using JAX and Flax\n", "- How to compute energies using full summation over the Hilbert space\n", "- How to use JAX for automatic differentiation and JIT compilation\n", "- How to implement optimization loops for variational parameters\n", "- How to compare different ansätze (Mean Field vs Jastrow)\n", "\n", "In the next tutorial, we will extend this to Monte <PERSON> sampling for larger systems where full summation is not feasible."]}, {"cell_type": "code", "execution_count": null, "id": "80856e03", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"10a7dd335ec94177b6f6812e54e63e07": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d3cf13f441e4ec7b0f1dbc54bc14d3b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_10a7dd335ec94177b6f6812e54e63e07", "placeholder": "​", "style": "IPY_MODEL_dd66a7b2f9ca42cdb63e18595faf9c24", "tabbable": null, "tooltip": null, "value": "100%"}}, "232e8a4237aa44fca6df8092af289c3d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "281990c0ef2341a1b312a2c9a8ce7176": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32dd723cd57840eeb61dccd94f798ef1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "4107c4c619604deb9248fac8ff8c52fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e089b188bb98467f910889c2a6ce76ca", "IPY_MODEL_c2b1fb3b059f4d6bb9b3706f05a36c75", "IPY_MODEL_c8e1a273529541678af354d1415f9d12"], "layout": "IPY_MODEL_cac19f956a73406e94ac9323694f8565", "tabbable": null, "tooltip": null}}, "4efe4c52298a46f59fb39768999fbcc1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1d3cf13f441e4ec7b0f1dbc54bc14d3b", "IPY_MODEL_f24c3b79150d4dc4aebce0b31cef1abd", "IPY_MODEL_e84acb77fce14b4aa43e2adb0444e1b4"], "layout": "IPY_MODEL_a5fb06c55d3e44349d7552c753b98fbb", "tabbable": null, "tooltip": null}}, "5e67496c41ee4a20ab6a0ab85f6cf350": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "84638255997c44f6b9198445a6301560": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "a5fb06c55d3e44349d7552c753b98fbb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1dfb2588c004a0fb45f268a13f64ef0": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b661ca8fa5864748a94b1de8c36b36a9": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2b1fb3b059f4d6bb9b3706f05a36c75": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_232e8a4237aa44fca6df8092af289c3d", "max": 100.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_5e67496c41ee4a20ab6a0ab85f6cf350", "tabbable": null, "tooltip": null, "value": 100.0}}, "c8e1a273529541678af354d1415f9d12": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b1dfb2588c004a0fb45f268a13f64ef0", "placeholder": "​", "style": "IPY_MODEL_d17be3649bbb446989d6030c41a06306", "tabbable": null, "tooltip": null, "value": " 100/100 [00:02&lt;00:00, 41.34it/s]"}}, "cac19f956a73406e94ac9323694f8565": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d17be3649bbb446989d6030c41a06306": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "dd66a7b2f9ca42cdb63e18595faf9c24": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "e089b188bb98467f910889c2a6ce76ca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_281990c0ef2341a1b312a2c9a8ce7176", "placeholder": "​", "style": "IPY_MODEL_32dd723cd57840eeb61dccd94f798ef1", "tabbable": null, "tooltip": null, "value": "100%"}}, "e84acb77fce14b4aa43e2adb0444e1b4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b661ca8fa5864748a94b1de8c36b36a9", "placeholder": "​", "style": "IPY_MODEL_84638255997c44f6b9198445a6301560", "tabbable": null, "tooltip": null, "value": " 100/100 [00:00&lt;00:00, 16310.09it/s]"}}, "ed6106606f7f4383a89cfee0c6a70054": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eef08e82342e4e61ba9331b8775eb7c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f24c3b79150d4dc4aebce0b31cef1abd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ed6106606f7f4383a89cfee0c6a70054", "max": 100.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_eef08e82342e4e61ba9331b8775eb7c1", "tabbable": null, "tooltip": null, "value": 100.0}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}