{"cells": [{"cell_type": "markdown", "id": "04ab0f8e", "metadata": {}, "source": ["\n", "# Part 1: Hamiltonians and Operators\n", "\n", "The goal of this tutorial series is to guide you through writing a simple VMC code from (almost) scratch to compute the ground-state of a paradigmatic spin model: the transverse-field Ising model in 2 dimensions.\n", "\n", "In this first tutorial, we will focus on:\n", "- Understanding NetKet's graph and operator system\n", "- Defining Hamiltonians using building blocks\n", "- Working with different operator formats\n", "- Validating our implementation with exact diagonalization\n", "\n", "The hamiltonian we will study is the transverse-field Ising model in 2 dimensions:\n", "\n", "$$\n", "\\mathcal{H}=-h\\sum_{i}\\sigma_{i}^{(x)}+J\\sum_{\\langle i,j \\rangle}\\sigma_{i}^{(z)}\\sigma_{j}^{(z)}.\n", "$$\n", "\n", "In the following we assume periodic boundary conditions with $h=1$ and $J=1$."]}, {"cell_type": "markdown", "id": "4bec40d7", "metadata": {}, "source": [":::{note}\n", "If you are executing this notebook on **Colab**, you will need to install NetKet.\n", "You can do so by uncommenting and running the following cell.\n", "\n", "Keep in mind that this notebook was designed for NetKet version `3.9.1`, which requires Python >=3.8.\n", ":::"]}, {"cell_type": "code", "execution_count": 1, "id": "7a63c4c4", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:30.957994Z", "iopub.status.busy": "2025-07-13T14:51:30.957715Z", "iopub.status.idle": "2025-07-13T14:51:30.963295Z", "shell.execute_reply": "2025-07-13T14:51:30.962834Z"}, "mystnb": {"code_prompt_hide": "<PERSON>de", "code_prompt_show": "Show and uncomment to install NetKet on colab"}}, "outputs": [], "source": ["# %pip install --quiet netket"]}, {"cell_type": "markdown", "id": "81a05bca", "metadata": {}, "source": ["Please verify that you are running on the correct version of NetKet:"]}, {"cell_type": "code", "execution_count": 2, "id": "49284d82", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:30.965482Z", "iopub.status.busy": "2025-07-13T14:51:30.965311Z", "iopub.status.idle": "2025-07-13T14:51:32.199408Z", "shell.execute_reply": "2025-07-13T14:51:32.199096Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version (requires >=3.9) 3.12.11\n", "NetKet version (requires >=3.9.1) 3.19.dev26+gd94cf4597.d20250713\n"]}], "source": ["import platform\n", "import netket as nk\n", "\n", "print(\"Python version (requires >=3.9)\", platform.python_version())\n", "print(\"NetKet version (requires >=3.9.1)\", nk.__version__)"]}, {"cell_type": "code", "execution_count": 3, "id": "ede28c64", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.200869Z", "iopub.status.busy": "2025-07-13T14:51:32.200715Z", "iopub.status.idle": "2025-07-13T14:51:32.202645Z", "shell.execute_reply": "2025-07-13T14:51:32.202347Z"}, "tags": ["hide-cell"]}, "outputs": [], "source": ["# Import necessary libraries\n", "import netket as nk\n", "import numpy as np"]}, {"cell_type": "markdown", "id": "06118047", "metadata": {}, "source": ["## 1. What is NetKet?\n", "\n", "[NetKet](http://www.netket.org) is a comprehensive package developed to perform Variational Monte Carlo calculations, while hiding varying degrees of complexity from researchers who don't want to get their hands too dirty with nitty gritty details.\n", "\n", "NetKet is thoroughly documented in [its publication](https://scipost.org/SciPostPhysCodeb.7/pdf) and partly in its [online documentation](https://netket.readthedocs.io/en/latest/index.html). When in doubt, check those, and if you find no answer, ask on our official [discussion forum](https://github.com/orgs/netket/discussions)."]}, {"cell_type": "markdown", "id": "c512c6b6", "metadata": {}, "source": ["## 2. Defining Graphs and Lattices\n", "\n", "NetKet covers quite a few standard lattices, so let's use this to quickly define a 2D square lattice with periodic boundary conditions.\n", "For the moment we assume $L=4$ ($N=16$ sites total)."]}, {"cell_type": "code", "execution_count": 4, "id": "75c90430", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.204028Z", "iopub.status.busy": "2025-07-13T14:51:32.203927Z", "iopub.status.idle": "2025-07-13T14:51:32.207115Z", "shell.execute_reply": "2025-07-13T14:51:32.206810Z"}}, "outputs": [], "source": ["# Define a 2D square lattice\n", "L = 4\n", "g = nk.graph.Hypercube(length=L, n_dim=2, pbc=True)"]}, {"cell_type": "markdown", "id": "e5ba214d", "metadata": {}, "source": ["The graph object is handy to quickly write the hamiltonian because it gives you easy access to the vertices (called nodes) and edges of the graph:"]}, {"cell_type": "code", "execution_count": 5, "id": "a3bfe098", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.208516Z", "iopub.status.busy": "2025-07-13T14:51:32.208409Z", "iopub.status.idle": "2025-07-13T14:51:32.210645Z", "shell.execute_reply": "2025-07-13T14:51:32.210385Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["g.n_nodes: 16\n", "g.nodes: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\n", "g.n_edges: 32\n", "g.edges: [(3, 7), (12, 13), (8, 9), (8, 12), (2, 14), (13, 14), (4, 5), (5, 6), (4, 8), (12, 15), (5, 9), (14, 15), (3, 15), (8, 11), (0, 1), (9, 10), (1, 2), (0, 4), (9, 13), (10, 11), (1, 5), (10, 14), (6, 7), (6, 10), (4, 7), (0, 3), (0, 12), (2, 3), (1, 13), (2, 6), (11, 15), (7, 11)]\n"]}], "source": ["# The number of sites (called nodes):\n", "print(\"g.n_nodes:\", g.n_nodes)\n", "# You can iterate through the nodes:\n", "print(\"g.nodes:\", [node for node in g.nodes()])\n", "# You can check the number of edges:\n", "print(\"g.n_edges:\", g.n_edges)\n", "# You can iterate through the edges, which are stored as a 2-tuple with the start and end node:\n", "print(\"g.edges:\", list(g.edges()))"]}, {"cell_type": "markdown", "id": "013624fa", "metadata": {}, "source": ["You can also visualize the lattice:"]}, {"cell_type": "code", "execution_count": 6, "id": "755ad54b", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.211845Z", "iopub.status.busy": "2025-07-13T14:51:32.211756Z", "iopub.status.idle": "2025-07-13T14:51:32.368203Z", "shell.execute_reply": "2025-07-13T14:51:32.367909Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Axes: title={'center': '2D Lattice (Distance Order: 1)'}, xlabel='X', ylabel='Y'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["g.draw()"]}, {"cell_type": "markdown", "id": "44de18dc", "metadata": {}, "source": ["## 3. Defining the Hilbert Space\n", "\n", "Next, NetKet asks us to define the computational space (or basis) for this calculation."]}, {"cell_type": "code", "execution_count": 7, "id": "bd9106b0", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.369598Z", "iopub.status.busy": "2025-07-13T14:51:32.369469Z", "iopub.status.idle": "2025-07-13T14:51:32.372902Z", "shell.execute_reply": "2025-07-13T14:51:32.372617Z"}}, "outputs": [], "source": ["# Define the <PERSON>lbert space based on this graph\n", "# We have spin-1/2 particles on each site\n", "hi = nk.hilbert.Spin(s=1 / 2, N=g.n_nodes)"]}, {"cell_type": "markdown", "id": "786c1fdf", "metadata": {}, "source": ["This is a fundamental object that defines how many degrees of freedom you have and how you store configurations. In this case, the hilbert space stores them as local variables +1 or -1."]}, {"cell_type": "markdown", "id": "42fcf319", "metadata": {}, "source": ["## 4. Building Operators\n", "\n", "In NetKet you can build individual operators acting on a site by calling the following functions:"]}, {"cell_type": "code", "execution_count": 8, "id": "cf84cb23", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.374344Z", "iopub.status.busy": "2025-07-13T14:51:32.374251Z", "iopub.status.idle": "2025-07-13T14:51:32.377076Z", "shell.execute_reply": "2025-07-13T14:51:32.376817Z"}}, "outputs": [], "source": ["sx_1 = nk.operator.spin.sigmax(hi, 1)\n", "sy_2 = nk.operator.spin.sigmay(hi, 2)\n", "sz_2 = nk.operator.spin.sigmaz(hi, 2)"]}, {"cell_type": "markdown", "id": "ee5d34dd", "metadata": {}, "source": ["The functions in `nk.operator.spin` and `nk.operator.boson` are used to create the fundamental operators that can be used to build arbitrary observables and Hamiltonians.\n", "Those functions return an object of type [LocalOperator](https://netket.readthedocs.io/en/latest/api/_generated/operator/netket.operator.LocalOperator.html#netket.operator.LocalOperator), which behaves as some sort of sparse matrix optimized for Variational Monte Carlo.\n", "\n", ":::{note}\n", "A `LocalOperator` can efficiently represent only operators that have a small domain in the computational basis: this means that an operator acting on 1 or 2 qubits will be efficiently represented, but one that acts on many qubits at once will not be.\n", ":::\n", "\n", "A LocalOperator can be printed and has the following representation:"]}, {"cell_type": "code", "execution_count": 9, "id": "9cba600a", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.378386Z", "iopub.status.busy": "2025-07-13T14:51:32.378295Z", "iopub.status.idle": "2025-07-13T14:51:32.380271Z", "shell.execute_reply": "2025-07-13T14:51:32.380010Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LocalOperator(dim=16, acting_on=[(1,)], constant=0.0, dtype=float64)\n"]}], "source": ["print(sx_1)"]}, {"cell_type": "markdown", "id": "ef08a5e3", "metadata": {}, "source": ["The information tells you that:\n", " * `dim`: The hilbert space has 16 local degrees of freedom\n", " * `acting_on`: this specific operator is composed by terms that act on the single site 1\n", " * `constant`: this is a diagonal shift proportional to the identity. In this case it is 0\n", " * `dtype`: the numpy data-type of the matrix elements"]}, {"cell_type": "markdown", "id": "2c174f9d", "metadata": {}, "source": ["## 5. Exercise: Building the Transverse Field Ising Hamiltonian\n", "\n", "You can combine operators by multiplying, adding or subtracting them. Let's build the Transverse field Ising hamiltonian:\n", "\n", "$$\n", "\\mathcal{H}=-h\\sum_{i}\\sigma_{i}^{(x)}+J\\sum_{\\langle i,j \\rangle}\\sigma_{i}^{(z)}\\sigma_{j}^{(z)}.\n", "$$\n", "\n", "where $h=1$ and $J=1$.\n", "\n", "Try to convert the equation above to code, using the operators that were discussed before (`nk.operator.spin.sigmax` and `nk.operator.spin.sigmaz`).\n", "\n", "Complete the code below:\n"]}, {"cell_type": "markdown", "id": "20b8c68c", "metadata": {"lines_to_next_cell": 0}, "source": []}, {"cell_type": "code", "execution_count": 10, "id": "46fc8781", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.381643Z", "iopub.status.busy": "2025-07-13T14:51:32.381552Z", "iopub.status.idle": "2025-07-13T14:51:32.383405Z", "shell.execute_reply": "2025-07-13T14:51:32.383148Z"}, "lines_to_next_cell": 2, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["# This creates an empty operator (or zero) to which you can add others.\n", "# hamiltonian = nk.operator.LocalOperator(hi)\n", "#\n", "# now add all terms acting on single sites\n", "# for site in g.nodes():  # every node (take the list of nodes from the graph object g)\n", "#     # TODO: Add the transverse field term -h * sigma_x\n", "#     pass\n", "#\n", "# now add all terms acting on multiple sites\n", "# for i, j in g.edges():  # every edge (take the list of edges from the graph object)\n", "#     # TODO: Add the Ising interaction term J * sigma_z_i * sigma_z_j\n", "#     pass"]}, {"cell_type": "code", "execution_count": 11, "id": "931d5051", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.384598Z", "iopub.status.busy": "2025-07-13T14:51:32.384511Z", "iopub.status.idle": "2025-07-13T14:51:32.398250Z", "shell.execute_reply": "2025-07-13T14:51:32.397965Z"}, "tags": ["solution"]}, "outputs": [], "source": ["# This creates an empty operator to which you can add others.\n", "hamiltonian = nk.operator.LocalOperator(hi)\n", "\n", "# the list of nodes is given by g.nodes()\n", "for site in g.nodes():\n", "    hamiltonian = hamiltonian - 1.0 * nk.operator.spin.sigmax(hi, site)\n", "\n", "for (i,j) in g.edges():\n", "    # you can multiply operators by using the @ operator\n", "    hamiltonian = hamiltonian + nk.operator.spin.sigmaz(hi, i)@nk.operator.spin.sigmaz(hi, j)"]}, {"cell_type": "markdown", "id": "a4949ae6", "metadata": {}, "source": ["To check if your implementation is correct, you can run the following validation.\n", "It will error if your implementation is wrong."]}, {"cell_type": "code", "execution_count": 12, "id": "9f2ffaa3", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:32.399611Z", "iopub.status.busy": "2025-07-13T14:51:32.399507Z", "iopub.status.idle": "2025-07-13T14:51:35.294104Z", "shell.execute_reply": "2025-07-13T14:51:35.293808Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hamiltonian implementation is correct!\n"]}], "source": ["# Validation - test to verify your code is correct\n", "hamiltonian_correct = nk.operator.Ising(hi, g, h=1.0, J=1.0)\n", "assert np.sum(np.abs(hamiltonian_correct.to_sparse() - hamiltonian.to_sparse())**2) < 1e-5\n", "print(\"Hamiltonian implementation is correct!\")"]}, {"cell_type": "markdown", "id": "f12c2dfc", "metadata": {}, "source": ["## 6. Converting Operator Formats\n", "\n", "Most operators in NetKet are implemented in Numba and are not compatible with `jax.jit`. However, some particular operator formats are implemented both in `numba` and in a `jax.jit`-compatible format.\n", "\n", "To make future tutorials easier, we will convert the operator to a JAX-friendly format. First we convert from the `LocalOperator` format to the `PauliStrings` format, then to the jax format:"]}, {"cell_type": "code", "execution_count": 13, "id": "e173d831", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:35.295461Z", "iopub.status.busy": "2025-07-13T14:51:35.295360Z", "iopub.status.idle": "2025-07-13T14:51:36.628593Z", "shell.execute_reply": "2025-07-13T14:51:36.628260Z"}}, "outputs": [], "source": ["hamiltonian_jax = hamiltonian.to_pauli_strings().to_jax_operator()"]}, {"cell_type": "markdown", "id": "b6cb58b2", "metadata": {}, "source": ["## 7. Exact Diagonalization (Validation)\n", "\n", "As a matter of comparison for future tutorials, let's compute the exact ground-state energy using scipy's sparse eigensolver.\n", "\n", "You can convert your operator to a sparse matrix with the method `.to_sparse()`. Use scipy to diagonalize it and extract the lowest eigenvalue."]}, {"cell_type": "code", "execution_count": 14, "id": "6b03cc0c", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:36.630265Z", "iopub.status.busy": "2025-07-13T14:51:36.630150Z", "iopub.status.idle": "2025-07-13T14:51:36.632069Z", "shell.execute_reply": "2025-07-13T14:51:36.631814Z"}, "tags": ["placeholder", "hide-input", "hide-output"]}, "outputs": [], "source": ["# TODO: Import scipy.sparse.linalg.eigsh and use it to find the ground state\n", "# from scipy.sparse.linalg import eigsh\n", "\n", "# Compute the exact ground state energy and wavefunction\n", "# e_gs, psi_gs = ..."]}, {"cell_type": "code", "execution_count": 15, "id": "b740bded", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:36.633557Z", "iopub.status.busy": "2025-07-13T14:51:36.633459Z", "iopub.status.idle": "2025-07-13T14:51:37.029898Z", "shell.execute_reply": "2025-07-13T14:51:37.029326Z"}, "tags": ["solution"]}, "outputs": [], "source": ["# use scipy.sparse.linalg.eigsh instead of numpy.linalg.eigh because\n", "# the sparse version is much faster as it uses sparsity!\n", "from scipy.sparse.linalg import eigsh\n", "\n", "# k=1 to only get one eigenvalue\n", "e_gs, psi_gs = eigsh(hamiltonian.to_sparse(), k=1)\n", "# get ground state energy and\n", "e_gs = e_gs[0]\n", "# ground state\n", "psi_gs = psi_gs.reshape(-1)"]}, {"cell_type": "markdown", "id": "50d3ff93", "metadata": {}, "source": ["## 8. Validation Test\n", "\n", "If you implemented everything correctly, this validation should pass:"]}, {"cell_type": "code", "execution_count": 16, "id": "14fa35a7", "metadata": {"execution": {"iopub.execute_input": "2025-07-13T14:51:37.032870Z", "iopub.status.busy": "2025-07-13T14:51:37.032361Z", "iopub.status.idle": "2025-07-13T14:51:37.035854Z", "shell.execute_reply": "2025-07-13T14:51:37.035335Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exact ground state energy: -34.010598\n"]}], "source": ["assert e_gs.shape == ()\n", "assert psi_gs.shape == (hi.n_states, )\n", "assert -34.01060 < e_gs < -34.01059\n", "print(f\"Exact ground state energy: {e_gs:.6f}\")"]}, {"cell_type": "markdown", "id": "caad3613", "metadata": {}, "source": ["In the next tutorial, we will use these tools to implement variational ansätze and compute energies using full summation over the Hilbert space."]}, {"cell_type": "code", "execution_count": null, "id": "8b7bdee7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}