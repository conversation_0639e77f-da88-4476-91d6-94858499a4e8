# VMC DIY

This is a series of tutorials designed to teach how VMC and NQS work by guiding you to implement your own VMC loop.

It uses some parts of NetKet only to implement the 'most complex' parts, such as operator indexing, but lets you do most things yourself.

This is a great learning resource for those who want to understand how a VMC code works internally, without getting their hands _too_ dirty.
In general, all students joining some research groups have to go those notebooks.

```{toctree}
:maxdepth: 1

01-hamiltonian-and-operators
02-variational-states-full-summation
03-monte-carlo-sampling
```