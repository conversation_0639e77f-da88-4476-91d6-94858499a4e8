/* This block tries to match the widths of the main netket website. 
It's probably a bad idea.
*/
@media (min-width:  768px) {
  .container-navbar {
    max-width: 720px;
  }
}
@media (min-width:  992px) {
  .container-navbar {
    max-width: 960px;
  }
}
@media (min-width:  1200px) {
  .container-navbar {
    max-width: 1140px;
  }
}
@media (min-width:  1400px) {
  .container-navbar {
    max-width: 1320px;
  }
}


@media screen and (max-width: 997px) {
  .btn-bd-download {
    display: none !important;
  }
}

@media screen and (max-width: 767px) {
  .navbar-brand, .navbar-form {
    display:inline;
  }
}

@media screen and (min-width: 768px) {
  .bs-sidenav .nav > .active > ul {
    display: block;
    position: fixed;
  }
}

.navbar {
    background-color: #2c3e50 !important;
    padding-top: .5rem;
    padding-bottom: .25rem;

}

.navbar-brand {
    color: white !important;
    height: 100%;
    padding: 0rem;
}

.navbar-brand {
    color: white !important;
    margin-right: 1rem;
}

.navbar-brand-text {
    font-family: Lato, "Helvetica Neue", Helvetica, Arial, sans-serif !important;
}

.nav-item > .nav-link a {
    font-family: Lato, "Helvetica Neue", Helvetica, Arial, sans-serif !important;
}

img.navbar-brand-logo {
    position: relative;
    top: -3px;
    margin-right: 15px !important;
    height: 2rem;
}

.navbar-custom {
  position: static;
  width:100%;
  background-color: #2c3e50  !important;
  z-index: 9999;
}

.navbar-custom-brand {
  display: block !important;
}

.nav-link-custom {
  color: rgba(255,255,255,.55) !important;
}

.btn-custom {
  color: rgb(255, 228, 132) !important;
}

.sticky-top {
  z-index: 9999;
}

.btn-bd-download {
    font-weight: 600;
    color: #ffe484;
    border-color: #ffe484
}

.btn-bd-download:hover,
.btn-bd-download:active {
    color: #2a2730;
    background-color: #ffe484;
    border-color: #ffe484
}

.btn-bd-download:focus {
    box-shadow: 0 0 0 3px rgba(255, 228, 132, 0.25)
}
