{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Timing and Profiling in NetKet\n", "\n", "NetKet provides a built-in timing system in {ref}`netket_utils_api` that helps profile your code and understand where time is being spent. This is particularly useful when optimizing performance or debugging slow computations.\n", "\n", "The timing system is designed to work seamlessly with JAX and provides hierarchical timing information with a beautiful output format."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Usage\n", "\n", "### Context Managers\n", "\n", "The simplest way to time code is using the {class}`~netket.utils.timing.Timer` class as a context manager:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:11.602786Z", "iopub.status.busy": "2025-07-24T11:58:11.602185Z", "iopub.status.idle": "2025-07-24T11:58:13.097657Z", "shell.execute_reply": "2025-07-24T11:58:13.097368Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.230                                                                                                    │\n", "│ ├── (30.5%) | matrix multiplication : 0.070 s                                                                   │\n", "│ └── (23.9%) | more work : 0.055 s                                                                               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["import netket as nk\n", "from netket.utils import timing\n", "import time\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "# Basic timing with Timer\n", "with timing.Timer() as timer:\n", "    time.sleep(0.1)  # Simulate some work\n", "    \n", "    # Nested timing with timed_scope\n", "    with timing.timed_scope(\"matrix multiplication\"):\n", "        a = jnp.ones((100, 100))\n", "        b = jnp.ones((100, 100))\n", "        result = a @ b\n", "        # Important for JAX: block until computation is done\n", "        timer.block_until_ready(result)\n", "    \n", "    with timing.timed_scope(\"more work\"):\n", "        time.sleep(0.05)\n", "\n", "print(timer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hierarchical Timing\n", "\n", "You can create nested timing structures by combining multiple timing scopes:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:13.117755Z", "iopub.status.busy": "2025-07-24T11:58:13.117488Z", "iopub.status.idle": "2025-07-24T11:58:13.170980Z", "shell.execute_reply": "2025-07-24T11:58:13.170706Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.050                                                                                                    │\n", "│ ├── (50.0%) | setup : 0.025 s                                                                                   │\n", "│ └── (50.0%) | computation : 0.025 s                                                                             │\n", "│     ├── (50.0%) | part 1 : 0.013 s                                                                              │\n", "│     └── (50.0%) | part 2 : 0.013 s                                                                              │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# Nested timing example\n", "with timing.Timer() as timer:\n", "    with timing.timed_scope(\"setup\"):\n", "        time.sleep(0.02)\n", "    \n", "    with timing.timed_scope(\"computation\"):\n", "        with timing.timed_scope(\"part 1\"):\n", "            time.sleep(0.01)\n", "        with timing.timed_scope(\"part 2\"):\n", "            time.sleep(0.01)\n", "\n", "print(timer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using `timed_scope` with Force\n", "\n", "The {func}`~netket.utils.timing.timed_scope` context manager is perfect for timing specific sections of code within a larger timing context:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:13.172422Z", "iopub.status.busy": "2025-07-24T11:58:13.172308Z", "iopub.status.idle": "2025-07-24T11:58:13.713622Z", "shell.execute_reply": "2025-07-24T11:58:13.713335Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.538                                                                                                    │\n", "│ ├── (67.2%) | eigenvalue decomposition : 0.362 s                                                                │\n", "│ └── (11.9%) | statistical analysis : 0.064 s                                                                    │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# Using timed_scope with force=True to enable timing even without a parent timer\n", "with timing.timed_scope(\"main computation\", force=True) as timer:\n", "    # Some initial setup\n", "    key = jax.random.key(42)\n", "    data = jax.random.normal(key, (1000, 1000))\n", "    timer.block_until_ready(data)\n", "    \n", "    with timing.timed_scope(\"eigenvalue decomposition\"):\n", "        eigenvals = jnp.linalg.eigvals(data)\n", "        timer.block_until_ready(eigenvals)\n", "    \n", "    with timing.timed_scope(\"statistical analysis\"):\n", "        mean_val = jnp.mean(eigenvals)\n", "        std_val = jnp.std(eigenvals)\n", "        timer.block_until_ready((mean_val, std_val))\n", "\n", "print(timer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using the `@timed` Decorator\n", "\n", "The {func}`~netket.utils.timing.timed` decorator allows you to automatically time function calls. This is especially useful for timing functions that are called multiple times:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:13.714923Z", "iopub.status.busy": "2025-07-24T11:58:13.714832Z", "iopub.status.idle": "2025-07-24T11:58:13.986947Z", "shell.execute_reply": "2025-07-24T11:58:13.986648Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.269                                                                                                    │\n", "│ ├── (53.7%) | expensive_computation : 0.144 s                                                                   │\n", "│ └── (16.2%) | data_processing : 0.043 s                                                                         │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["@timing.timed(name=\"expensive_computation\")\n", "def expensive_function(x):\n", "    \"\"\"A function that does some expensive computation.\"\"\"\n", "    result = jnp.sin(x) * jnp.cos(x) + jnp.exp(-x**2)\n", "    return jnp.sum(result)\n", "\n", "@timing.timed(name=\"data_processing\")\n", "def process_data(data):\n", "    \"\"\"Process some data.\"\"\"\n", "    return jnp.fft.fft(data)\n", "\n", "# The decorated functions will only be timed when inside a timing context\n", "with timing.Timer() as timer:\n", "    # Call the functions multiple times\n", "    for i in range(3):\n", "        x = jnp.linspace(0, 10, 1000)\n", "        result1 = expensive_function(x)\n", "        \n", "        key = jax.random.key(i)\n", "        data = jax.random.normal(key, (512,))\n", "        result2 = process_data(data)\n", "        \n", "        # Block until JAX computations are complete\n", "        timer.block_until_ready((result1, result2))\n", "\n", "print(timer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## JAX Gotchas: JIT and Block-Until-Ready\n", "\n", "### JIT Compilation\n", "\n", "Timing decorators and context managers will not work inside JIT-compiled functions. JAX compilation strips out Python operations that aren't compatible with compilation, so timing calls inside JIT functions will not report any information and may cause errors."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:13.988174Z", "iopub.status.busy": "2025-07-24T11:58:13.988092Z", "iopub.status.idle": "2025-07-24T11:58:14.073054Z", "shell.execute_reply": "2025-07-24T11:58:14.072799Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bad example (no timing info):\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.030                                                                                                    │\n", "│ └── (1.5%) | inside jit : 0.000 s                                                                               │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n", "\n", "Good example (proper timing):\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.031                                                                                                    │\n", "│ └── (99.9%) | jitted function call : 0.031 s                                                                    │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# DON'T: Use timing inside a JIT function - this won't work\n", "@jax.jit\n", "def bad_jitted_function(x):\n", "    with timing.timed_scope(\"inside jit\"):  # This will be ignored/cause errors\n", "        return jnp.sum(x**2)\n", "\n", "# DO: Time the JIT function from outside\n", "@timing.timed(name=\"jitted_computation\")\n", "@jax.jit\n", "def good_jitted_function(x):\n", "    return jnp.sum(x**2)\n", "\n", "# Or time the call to the JIT function\n", "@jax.jit\n", "def my_jitted_function(x):\n", "    return jnp.sum(x**2)\n", "\n", "x = jnp.ones((1000, 1000))\n", "\n", "# This will show no timing information from inside the JIT function\n", "print(\"Bad example (no timing info):\")\n", "with timing.Timer() as timer_bad:\n", "    result = bad_jitted_function(x)\n", "    timer_bad.block_until_ready(result)\n", "print(timer_bad)\n", "\n", "# This will properly time the JIT function\n", "print(\"\\nGood example (proper timing):\")\n", "with timing.Timer() as timer_good:\n", "    with timing.timed_scope(\"jitted function call\"):\n", "        result = my_jitted_function(x)\n", "        timer_good.block_until_ready(result)\n", "print(timer_good)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Block-Until-Ready\n", "\n", "When timing JAX functions, it's crucial to use `timer.block_until_ready()` to get accurate timing results. JAX uses lazy evaluation, so computations might not happen immediately when the function is called."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:14.074242Z", "iopub.status.busy": "2025-07-24T11:58:14.074159Z", "iopub.status.idle": "2025-07-24T11:58:14.352140Z", "shell.execute_reply": "2025-07-24T11:58:14.351830Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Without block_until_ready (inaccurate timing):\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.068                                                                                                    │\n", "│ └── (100.0%) | jax computation : 0.068 s                                                                        │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n", "\n", "With block_until_ready (accurate timing):\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.205                                                                                                    │\n", "│ └── (100.0%) | jax computation : 0.205 s                                                                        │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# Demonstration of why block_until_ready is important\n", "print(\"Without block_until_ready (inaccurate timing):\")\n", "with timing.Timer() as timer_bad:\n", "    with timing.timed_scope(\"jax computation\"):\n", "        large_matrix = jnp.ones((2000, 2000))\n", "        result = jnp.linalg.inv(large_matrix)\n", "        # Not blocking - timing will be inaccurate!\n", "\n", "print(timer_bad)\n", "print(\"\\nWith block_until_ready (accurate timing):\")\n", "with timing.Timer() as timer_good:\n", "    with timing.timed_scope(\"jax computation\"):\n", "        large_matrix = jnp.ones((2000, 2000))\n", "        result = jnp.linalg.inv(large_matrix)\n", "        # Properly blocking until computation is done\n", "        timer_good.block_until_ready(result)\n", "\n", "print(timer_good)"]}, {"cell_type": "markdown", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Interaction with NetKet Drivers\n", "\n", "### Real-World Example: Timing a NetKet Simulation\n", "\n", "Here's how the timing system is used in actual NetKet simulations:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:14.353396Z", "iopub.status.busy": "2025-07-24T11:58:14.353304Z", "iopub.status.idle": "2025-07-24T11:58:15.960753Z", "shell.execute_reply": "2025-07-24T11:58:15.960482Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Nextcloud/Codes/Python/netket/netket/vqs/mc/mc_state/state.py:299: UserWarning: n_samples=1000 (250 per JAX device) does not divide n_chains=64, increased to 1024 (256 per JAX device)\n", "  self.n_samples = n_samples\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a716848175a4a58aa96dbae0a6c7c35", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.728                                                                                                    │\n", "│ └── (85.1%) | VMC._forward_and_backward : 0.619 s                                                               │\n", "│     └── (97.6%) | MCState.expect_and_grad : 0.604 s                                                             │\n", "│         └── (69.3%) | MCState.sample : 0.419 s                                                                  │\n", "│             └── (46.2%) | sampling n_discarded samples : 0.193 s                                                │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n", "\n", "Total timing breakdown:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.843                                                                                                    │\n", "│ └── (100.0%) | optimization : 0.843 s                                                                           │\n", "│     └── (86.3%) |                                                                                               │\n", "│         /Users/<USER>/Nextcloud/Codes/Python/netket/netket/driver/abstract_variational_driver.py:336 │\n", "│         : 0.728 s                                                                                               │\n", "│         └── (85.1%) | VMC._forward_and_backward : 0.619 s                                                       │\n", "│             └── (97.6%) | MCState.expect_and_grad : 0.604 s                                                     │\n", "│                 └── (69.3%) | MCState.sample : 0.419 s                                                          │\n", "│                     └── (46.2%) | sampling n_discarded samples : 0.193 s                                        │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# Create a simple quantum system\n", "L = 8\n", "g = nk.graph.Chain(length=L, pbc=True)\n", "hi = nk.hilbert.Spin(s=0.5, N=g.n_nodes)\n", "\n", "# Define the Hamiltonian\n", "ha = nk.operator.Ising(hilbert=hi, graph=g, h=1.0)\n", "\n", "# Define the variational ansatz\n", "model = nk.models.RBM(alpha=1)\n", "sampler = nk.sampler.MetropolisLocal(hi)\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.1)\n", "\n", "# Create the variational state\n", "vs = nk.vqs.MCState(sampler, model, n_samples=1000)\n", "\n", "# Time the creation and execution of a VMC driver\n", "with timing.Timer() as timer:\n", "    with timing.timed_scope(\"driver setup\"):\n", "        driver = nk.VMC(ha, optimizer, variational_state=vs)\n", "    \n", "    with timing.timed_scope(\"optimization\"):\n", "        # Run a few optimization steps with timing enabled\n", "        driver.run(n_iter=5, timeit=True)\n", "\n", "print(\"\\nTotal timing breakdown:\")\n", "print(timer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Timing Custom Observable Calculations\n", "\n", "You can also use the timing system to profile custom observable calculations within your NetKet workflows:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:15.962002Z", "iopub.status.busy": "2025-07-24T11:58:15.961912Z", "iopub.status.idle": "2025-07-24T11:58:16.481726Z", "shell.execute_reply": "2025-07-24T11:58:16.481400Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.516                                                                                                    │\n", "│ └── (99.1%) | iteration 0 : 0.511 s                                                                             │\n", "│     └── (100.0%) | estimate observables : 0.511 s                                                               │\n", "│         ├── (19.0%) | observable: energy : 0.097 s                                                              │\n", "│         │   └── (100.0%) | MCState.expect : 0.097 s                                                             │\n", "│         │       └── (3.2%) | MCState.sample : 0.003 s                                                           │\n", "│         │           └── (26.0%) | sampling n_discarded samples : 0.001 s                                        │\n", "│         └── (81.0%) | observable: magnetization : 0.414 s                                                       │\n", "│             └── (100.0%) | MCState.expect : 0.414 s                                                             │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# Example of how timing is used internally (simplified version)\n", "@timing.timed(name=\"estimate observables\")\n", "def estimate_observables(state, observables):\n", "    \"\"\"This mimics how NetKet drivers time observable estimation.\"\"\"\n", "    results = {}\n", "    for name, obs in observables.items():\n", "        with timing.timed_scope(f\"observable: {name}\"):\n", "            results[name] = state.expect(obs)\n", "    return results\n", "\n", "# Demonstrate the pattern\n", "observables = {\"energy\": ha, \"magnetization\": nk.operator.spin.sigmax(hi, 0)}\n", "\n", "with timing.Timer() as timer:\n", "    for i in range(3):\n", "        with timing.timed_scope(f\"iteration {i}\"):\n", "            estimates = estimate_observables(vs, observables)\n", "\n", "print(timer)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:58:16.483038Z", "iopub.status.busy": "2025-07-24T11:58:16.482930Z", "iopub.status.idle": "2025-07-24T11:58:16.495542Z", "shell.execute_reply": "2025-07-24T11:58:16.495263Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["╭────────────────────────────────────────────── Timing Information ───────────────────────────────────────────────╮\n", "│ Total: 0.007                                                                                                    │\n", "│ ├── (39.0%) | iteration 0 : 0.003 s                                                                             │\n", "│ │   └── (97.9%) | estimate observables : 0.003 s                                                                │\n", "│ │       ├── (42.3%) | observable: energy : 0.001 s                                                              │\n", "│ │       │   └── (99.3%) | MCState.expect : 0.001 s                                                              │\n", "│ │       └── (55.7%) | observable: magnetization : 0.002 s                                                       │\n", "│ │           └── (99.4%) | MCState.expect : 0.002 s                                                              │\n", "│ ├── (29.4%) | iteration 1 : 0.002 s                                                                             │\n", "│ │   └── (97.7%) | estimate observables : 0.002 s                                                                │\n", "│ │       ├── (53.0%) | observable: energy : 0.001 s                                                              │\n", "│ │       │   └── (99.4%) | MCState.expect : 0.001 s                                                              │\n", "│ │       └── (44.9%) | observable: magnetization : 0.001 s                                                       │\n", "│ │           └── (99.0%) | MCState.expect : 0.001 s                                                              │\n", "│ └── (31.0%) | iteration 2 : 0.002 s                                                                             │\n", "│     └── (97.5%) | estimate observables : 0.002 s                                                                │\n", "│         ├── (50.7%) | observable: energy : 0.001 s                                                              │\n", "│         │   └── (99.2%) | MCState.expect : 0.001 s                                                              │\n", "│         └── (46.2%) | observable: magnetization : 0.001 s                                                       │\n", "│             └── (98.6%) | MCState.expect : 0.001 s                                                              │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "\n"]}], "source": ["# The driver.run() method with timeit=True will automatically show timing information\n", "# This is implemented using the same timing system we've been exploring\n", "\n", "# Example of how timing is used internally (simplified version)\n", "@timing.timed(name=\"estimate observables\")\n", "def estimate_observables(state, observables):\n", "    \"\"\"This mimics how NetKet drivers time observable estimation.\"\"\"\n", "    results = {}\n", "    for name, obs in observables.items():\n", "        with timing.timed_scope(f\"observable: {name}\"):\n", "            results[name] = state.expect(obs)\n", "    return results\n", "\n", "# Demonstrate the pattern\n", "observables = {\"energy\": ha, \"magnetization\": nk.operator.spin.sigmax(hi, 0)}\n", "\n", "with timing.Timer() as timer:\n", "    for i in range(3):\n", "        with timing.timed_scope(f\"iteration {i}\"):\n", "            estimates = estimate_observables(vs, observables)\n", "\n", "print(timer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"1cef0ab434834918adeddd02993bd76a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "4a716848175a4a58aa96dbae0a6c7c35": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_66e71031c1e5440cb1ff2cf7cf74b443", "IPY_MODEL_567b11439f5e4fdfaf9618ff728636d8", "IPY_MODEL_a44fd17a7a194a2896988271c5f2e5dc"], "layout": "IPY_MODEL_9bdd1776c9d1470e99fae46d386f49f1", "tabbable": null, "tooltip": null}}, "567b11439f5e4fdfaf9618ff728636d8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_fe130bbf6a8647fabb1c9e020238786a", "max": 5.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_92b48346355c406483117c051e2a3167", "tabbable": null, "tooltip": null, "value": 5.0}}, "61f5ee2933cc4e5aa3c7b32bf6fc6daf": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66e71031c1e5440cb1ff2cf7cf74b443": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a29489e7134f4fe2819442abce8e6c96", "placeholder": "​", "style": "IPY_MODEL_1cef0ab434834918adeddd02993bd76a", "tabbable": null, "tooltip": null, "value": "100%"}}, "8ce930a3f3f3400cb7135e6e1c23a273": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "92b48346355c406483117c051e2a3167": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bdd1776c9d1470e99fae46d386f49f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": "inline-flex", "flex": null, "flex_flow": "row wrap", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "100%"}}, "a29489e7134f4fe2819442abce8e6c96": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a44fd17a7a194a2896988271c5f2e5dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_61f5ee2933cc4e5aa3c7b32bf6fc6daf", "placeholder": "​", "style": "IPY_MODEL_8ce930a3f3f3400cb7135e6e1c23a273", "tabbable": null, "tooltip": null, "value": " 5/5 [00:00&lt;00:00, 113.38it/s, Energy=-8.084 ± 0.087 [σ²=7.3e+00, R̂=1.027]]"}}, "fe130bbf6a8647fabb1c9e020238786a": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": "2", "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}