{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Time-series data with History and HistoryDict\n", "\n", "NetKet provides powerful classes for storing and managing time-series data through the {class}`netket.utils.history.History` and {class}`netket.utils.history.HistoryDict` classes. These are particularly useful for tracking optimization progress, storing simulation results, and managing logging data.\n", "\n", "This tutorial will show you how to:\n", "1. Create and manipulate {class}`~netket.utils.history.History` objects\n", "2. Use {class}`~netket.utils.history.HistoryDict` for managing multiple time-series\n", "3. Save and load history data\n", "4. Use logging utilities like {class}`~netket.logging.RuntimeLog` for easy data collection"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:39.662751Z", "iopub.status.busy": "2025-07-24T11:52:39.662261Z", "iopub.status.idle": "2025-07-24T11:52:40.737772Z", "shell.execute_reply": "2025-07-24T11:52:40.737524Z"}}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tempfile\n", "import os\n", "\n", "import netket as nk\n", "from netket.utils import history\n", "from netket.logging import RuntimeLog"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with History Objects\n", "\n", "The {class}`~netket.utils.history.History` class is designed to store time-series data with associated iteration numbers. Let's start with basic usage:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.739228Z", "iopub.status.busy": "2025-07-24T11:52:40.739072Z", "iopub.status.idle": "2025-07-24T11:52:40.741822Z", "shell.execute_reply": "2025-07-24T11:52:40.741615Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Empty history: History(keys=['value'], n_iters=0)\n", "Length: 0\n"]}], "source": ["# Create an empty history object\n", "hist = history.History()\n", "print(f\"Empty history: {hist}\")\n", "print(f\"Length: {len(hist)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Building History Objects in a Loop\n", "\n", "{class}`~netket.utils.history.History` objects are designed to be built incrementally, which is perfect for optimization loops:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.759428Z", "iopub.status.busy": "2025-07-24T11:52:40.759321Z", "iopub.status.idle": "2025-07-24T11:52:40.762381Z", "shell.execute_reply": "2025-07-24T11:52:40.762140Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["History after creation: History(keys=['energy'], n_iters=10)\n", "Iterations: [0 1 2 3 4 5 6 7 8 9]\n", "Energy values: [1.05901467 1.07698539 0.64250923 0.59448121 0.62322185 0.33553292\n", " 0.31330107 0.14264381 0.31478527 0.11293369]\n"]}], "source": ["# Simulate an optimization loop with data\n", "steps = list(range(10))\n", "energies = [1.0 * np.exp(-step * 0.2) + 0.1 * np.random.randn() for step in steps]\n", "\n", "# Create History object with initial data\n", "energy_history = history.History({'energy': energies}, iters=steps)\n", "\n", "print(f\"History after creation: {energy_history}\")\n", "print(f\"Iterations: {energy_history.iters}\")\n", "print(f\"Energy values: {energy_history['energy']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plotting History Data\n", "\n", "{class}`~netket.utils.history.History` objects have built-in plotting capabilities:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.763478Z", "iopub.status.busy": "2025-07-24T11:52:40.763388Z", "iopub.status.idle": "2025-07-24T11:52:40.829253Z", "shell.execute_reply": "2025-07-24T11:52:40.828995Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the energy history manually\n", "fig, ax = plt.subplots(figsize=(8, 5))\n", "ax.plot(energy_history.iters, energy_history['energy'], 'b-o', linewidth=2, markersize=4)\n", "ax.set_xlabel('Iteration')\n", "ax.set_ylabel('Energy')\n", "ax.set_title('Energy vs Iteration')\n", "ax.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating History with <PERSON> Keys\n", "\n", "A single {class}`~netket.utils.history.History` object can store multiple time-series with the same time axis:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.830515Z", "iopub.status.busy": "2025-07-24T11:52:40.830410Z", "iopub.status.idle": "2025-07-24T11:52:40.832944Z", "shell.execute_reply": "2025-07-24T11:52:40.832720Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Multi-metric history: History(keys=['energy', 'variance', 'gradient_norm'], n_iters=15)\n", "Keys: ['energy', 'variance', 'gradient_norm']\n"]}], "source": ["# Create a history with multiple metrics\n", "steps = list(range(15))\n", "energies = [1.0 * np.exp(-step * 0.1) + 0.05 * np.random.randn() for step in steps]\n", "variances = [0.5 * np.exp(-step * 0.05) + 0.02 * np.random.randn() for step in steps]\n", "grad_norms = [2.0 * np.exp(-step * 0.3) + 0.1 * np.random.randn() for step in steps]\n", "\n", "multi_history = history.History({\n", "    'energy': energies,\n", "    'variance': variances,\n", "    'gradient_norm': grad_norms\n", "}, iters=steps)\n", "\n", "print(f\"Multi-metric history: {multi_history}\")\n", "print(f\"Keys: {multi_history.keys()}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.834166Z", "iopub.status.busy": "2025-07-24T11:52:40.834087Z", "iopub.status.idle": "2025-07-24T11:52:40.965529Z", "shell.execute_reply": "2025-07-24T11:52:40.965177Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot all metrics manually\n", "fig, axes = plt.subplots(3, 1, figsize=(10, 10))\n", "\n", "# Plot each metric\n", "axes[0].plot(multi_history.iters, multi_history['energy'], 'b-o', linewidth=2, markersize=3, label='Energy')\n", "axes[0].set_ylabel('Energy')\n", "axes[0].set_title('Energy over Time')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "axes[1].plot(multi_history.iters, multi_history['variance'], 'r-s', linewidth=2, markersize=3, label='Variance')\n", "axes[1].set_ylabel('Variance')\n", "axes[1].set_title('Variance over Time')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "axes[2].plot(multi_history.iters, multi_history['gradient_norm'], 'g-^', linewidth=2, markersize=3, label='Gradient Norm')\n", "axes[2].set_ylabel('Gradient Norm')\n", "axes[2].set_xlabel('Iteration')\n", "axes[2].set_title('Gradient Norm over Time')\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Concatenating History Objects\n", "\n", "You can concatenate {class}`~netket.utils.history.History` objects together:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.966863Z", "iopub.status.busy": "2025-07-24T11:52:40.966769Z", "iopub.status.idle": "2025-07-24T11:52:40.970441Z", "shell.execute_reply": "2025-07-24T11:52:40.970223Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First segment: History(keys=['loss'], n_iters=5)\n", "Second segment: History(keys=['loss'], n_iters=5)\n", "Concatenated: History(keys=['loss'], n_iters=10)\n"]}], "source": ["# Create two separate history segments with working data\n", "steps1 = list(range(5))\n", "loss1 = [2.0 - step * 0.2 for step in steps1]\n", "hist1 = history.History({'loss': loss1}, iters=steps1)\n", "\n", "steps2 = list(range(5, 10))\n", "loss2 = [2.0 - step * 0.2 for step in steps2]\n", "hist2 = history.History({'loss': loss2}, iters=steps2)\n", "\n", "print(f\"First segment: {hist1}\")\n", "print(f\"Second segment: {hist2}\")\n", "\n", "# Concatenate\n", "hist1.append(hist2)\n", "print(f\"Concatenated: {hist1}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with HistoryDict\n", "\n", "{class}`~netket.utils.history.HistoryDict` is a powerful container that can store multiple {class}`~netket.utils.history.History` objects or nested dictionaries. Each value can be logged at independent iterations, making it perfect for complex simulations where different metrics are computed at different frequencies."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using accum_histories_in_tree for HistoryDict Construction\n", "\n", "The {func}`~netket.utils.history.accum_histories_in_tree` function is the recommended way to build complex nested history structures:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.971662Z", "iopub.status.busy": "2025-07-24T11:52:40.971572Z", "iopub.status.idle": "2025-07-24T11:52:40.974713Z", "shell.execute_reply": "2025-07-24T11:52:40.974474Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Complex HistoryDict: HistoryDict with 6 elements:\n", "\t'diagnostics/acceptance_rate' -> History(keys=['value'], n_iters=7)\n", "\t'diagnostics/step_size' -> History(keys=['value'], n_iters=7)\n", "\t'energy' -> History(keys=['value'], n_iters=20)\n", "\t'expensive_metrics/correlation_length' -> History(keys=['value'], n_iters=4)\n", "\t'expensive_metrics/entanglement_entropy' -> History(keys=['value'], n_iters=4)\n", "\t'variance' -> History(keys=['value'], n_iters=20)\n"]}], "source": ["# Start with an empty HistoryDict\n", "history_dict = history.HistoryDict()\n", "\n", "# Simulate a complex optimization with nested metrics\n", "for step in range(20):\n", "    # Core metrics computed every step\n", "    data = {\n", "        'energy': -2.0 + 0.1 * np.random.randn(),\n", "        'variance': 0.5 + 0.05 * np.random.randn()\n", "    }\n", "    \n", "    # Expensive metrics computed every 5 steps\n", "    if step % 5 == 0:\n", "        data['expensive_metrics'] = {\n", "            'correlation_length': 2.0 + 0.2 * np.random.randn(),\n", "            'entanglement_entropy': 1.5 + 0.1 * np.random.randn()\n", "        }\n", "    \n", "    # Diagnostic metrics computed every 3 steps\n", "    if step % 3 == 0:\n", "        data['diagnostics'] = {\n", "            'acceptance_rate': 0.5 + 0.1 * np.random.randn(),\n", "            'step_size': 0.01 + 0.001 * np.random.randn()\n", "        }\n", "    \n", "    # Use accum_histories_in_tree to accumulate the data\n", "    history_dict = history.accum_histories_in_tree(history_dict, data, step=step)\n", "\n", "print(f\"Complex HistoryDict: {history_dict}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accessing Data in HistoryDict\n", "\n", "{class}`~netket.utils.history.HistoryDict` supports various ways to access nested data:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.975735Z", "iopub.status.busy": "2025-07-24T11:52:40.975643Z", "iopub.status.idle": "2025-07-24T11:52:40.977950Z", "shell.execute_reply": "2025-07-24T11:52:40.977749Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-level keys: ['energy', 'variance', 'expensive_metrics', 'diagnostics']\n", "Energy history: History(keys=['value'], n_iters=20)\n", "Expensive metrics: HistoryDict with 2 elements:\n", "\t'correlation_length' -> History(keys=['value'], n_iters=4)\n", "\t'entanglement_entropy' -> History(keys=['value'], n_iters=4)\n", "Correlation length history: History(keys=['value'], n_iters=4)\n", "Energy logged at steps: [ 0  1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19]\n", "Correlation length logged at steps: [ 0  5 10 15]\n", "Acceptance rate logged at steps: [ 0  3  6  9 12 15 18]\n"]}], "source": ["# Access top-level keys\n", "print(f\"Top-level keys: {list(history_dict.keys())}\")\n", "\n", "# Access nested data using dictionary syntax\n", "print(f\"Energy history: {history_dict['energy']}\")\n", "print(f\"Expensive metrics: {history_dict['expensive_metrics']}\")\n", "\n", "# Access deeply nested data using path syntax\n", "correlation_history = history_dict['expensive_metrics/correlation_length']\n", "print(f\"Correlation length history: {correlation_history}\")\n", "\n", "# Show that different metrics were logged at different frequencies\n", "print(f\"Energy logged at steps: {history_dict['energy'].iters}\")\n", "print(f\"Correlation length logged at steps: {correlation_history.iters}\")\n", "print(f\"Acceptance rate logged at steps: {history_dict['diagnostics/acceptance_rate'].iters}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plotting Data from HistoryDict"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:40.979079Z", "iopub.status.busy": "2025-07-24T11:52:40.979009Z", "iopub.status.idle": "2025-07-24T11:52:41.149070Z", "shell.execute_reply": "2025-07-24T11:52:41.148719Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot different metrics with different sampling frequencies\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "\n", "# Energy (every step)\n", "axes[0, 0].plot(history_dict['energy'].iters, history_dict['energy'].values, 'b-o', markersize=3)\n", "axes[0, 0].set_title('Energy (every step)')\n", "axes[0, 0].set_xlabel('Iteration')\n", "\n", "# Correlation length (every 5 steps)\n", "corr_hist = history_dict['expensive_metrics/correlation_length']\n", "axes[0, 1].plot(corr_hist.iters, corr_hist.values, 'r-s', markersize=5)\n", "axes[0, 1].set_title('Correlation Length (every 5 steps)')\n", "axes[0, 1].set_xlabel('Iteration')\n", "\n", "# Acceptance rate (every 3 steps)\n", "acc_hist = history_dict['diagnostics/acceptance_rate']\n", "axes[1, 0].plot(acc_hist.iters, acc_hist.values, 'g-^', markersize=4)\n", "axes[1, 0].set_title('Acceptance Rate (every 3 steps)')\n", "axes[1, 0].set_xlabel('Iteration')\n", "\n", "# Combined plot\n", "axes[1, 1].plot(history_dict['energy'].iters, history_dict['energy'].values, 'b-', alpha=0.7, label='Energy')\n", "axes[1, 1].plot(corr_hist.iters, corr_hist.values, 'r-s', markersize=4, label='Correlation Length')\n", "axes[1, 1].set_title('Combined View')\n", "axes[1, 1].set_xlabel('Iteration')\n", "axes[1, 1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving and Loading HistoryDict\n", "\n", "{class}`~netket.utils.history.HistoryDict` provides convenient methods for saving and loading data:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.150652Z", "iopub.status.busy": "2025-07-24T11:52:41.150535Z", "iopub.status.idle": "2025-07-24T11:52:41.162788Z", "shell.execute_reply": "2025-07-24T11:52:41.162503Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving to temporary file: /var/folders/b_/dvph1c6569155bspz2m2sml40000gp/T/tmpnfskv9xu.json\n", "Data saved successfully!\n"]}], "source": ["# Create a temporary file for demonstration\n", "with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:\n", "    tmp_filename = tmp_file.name\n", "\n", "print(f\"Saving to temporary file: {tmp_filename}\")\n", "\n", "# Save the HistoryDict - we'll use RuntimeLog for this\n", "# (HistoryDict itself doesn't have a direct save method, but RuntimeLog does)\n", "runtime_log = RuntimeLog()\n", "runtime_log._data = history_dict\n", "runtime_log.serialize(tmp_filename)\n", "\n", "print(\"Data saved successfully!\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.164400Z", "iopub.status.busy": "2025-07-24T11:52:41.164300Z", "iopub.status.idle": "2025-07-24T11:52:41.167483Z", "shell.execute_reply": "2025-07-24T11:52:41.167134Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded HistoryDict: HistoryDict with 6 elements:\n", "\t'diagnostics/acceptance_rate' -> History(keys=['value'], n_iters=7)\n", "\t'diagnostics/step_size' -> History(keys=['value'], n_iters=7)\n", "\t'energy' -> History(keys=['value'], n_iters=20)\n", "\t'expensive_metrics/correlation_length' -> History(keys=['value'], n_iters=4)\n", "\t'expensive_metrics/entanglement_entropy' -> History(keys=['value'], n_iters=4)\n", "\t'variance' -> History(keys=['value'], n_iters=20)\n", "Original energy values: [-1.96226202 -1.9480204  -2.05242489 -1.98877341 -1.96826794]\n", "Loaded energy values: [-1.96226202 -1.9480204  -2.05242489 -1.98877341 -1.96826794]\n"]}], "source": ["# Load the data back\n", "loaded_history_dict = history.HistoryDict.from_file(tmp_filename)\n", "\n", "print(f\"Loaded HistoryDict: {loaded_history_dict}\")\n", "\n", "# Verify the data is the same\n", "print(f\"Original energy values: {history_dict['energy'].values[:5]}\")\n", "print(f\"Loaded energy values: {loaded_history_dict['energy'].values[:5]}\")\n", "\n", "# Clean up\n", "os.unlink(tmp_filename)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using RuntimeLog for Easy Data Collection\n", "\n", "The {class}`~netket.logging.RuntimeLog` class provides an even easier way to work with history data. It automatically uses {class}`~netket.utils.history.HistoryDict` internally and provides a simple interface for logging data in loops."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Basic RuntimeLog Usage\n", "\n", "By passing `driver=None` to log in a loop, {class}`~netket.logging.RuntimeLog` becomes a simple and powerful logging tool:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.168921Z", "iopub.status.busy": "2025-07-24T11:52:41.168815Z", "iopub.status.idle": "2025-07-24T11:52:41.172351Z", "shell.execute_reply": "2025-07-24T11:52:41.171937Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RuntimeLog contents: RuntimeLog():\n", " keys = ['train_loss', 'learning_rate', 'validation', 'checkpoint']\n", "Available keys: ['train_loss', 'learning_rate', 'validation', 'checkpoint']\n"]}], "source": ["# Create a RuntimeLog instance\n", "log = RuntimeLog()\n", "\n", "# Simulate a training loop with irregular logging\n", "for epoch in range(25):\n", "    # Basic metrics every epoch\n", "    metrics = {\n", "        'train_loss': 1.0 * np.exp(-epoch * 0.1) + 0.1 * np.random.randn(),\n", "        'learning_rate': 0.01 * np.exp(-epoch * 0.05)\n", "    }\n", "    \n", "    # Validation metrics every 5 epochs\n", "    if epoch % 5 == 0:\n", "        metrics['validation'] = {\n", "            'val_loss': 1.2 * np.exp(-epoch * 0.08) + 0.15 * np.random.randn(),\n", "            'val_accuracy': 0.5 + 0.4 * (1 - np.exp(-epoch * 0.1))\n", "        }\n", "    \n", "    # Model checkpointing info every 10 epochs\n", "    if epoch % 10 == 0:\n", "        metrics['checkpoint'] = {\n", "            'model_size_mb': 150 + 5 * np.random.randn(),\n", "            'save_time_seconds': 2.5 + 0.5 * np.random.randn()\n", "        }\n", "    \n", "    # Log the metrics (note: driver=None is implicit)\n", "    log(step=epoch, item=metrics, variational_state=None)\n", "\n", "print(f\"RuntimeLog contents: {log}\")\n", "print(f\"Available keys: {list(log.data.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accessing RuntimeLog Data\n", "\n", "{class}`~netket.logging.RuntimeLog`'s data attribute is a {class}`~netket.utils.history.HistoryDict`, so you can use all the same access patterns:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.173883Z", "iopub.status.busy": "2025-07-24T11:52:41.173781Z", "iopub.status.idle": "2025-07-24T11:52:41.176471Z", "shell.execute_reply": "2025-07-24T11:52:41.176143Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Type of log.data: <class 'netket.utils.history.history_dict.HistoryDict'>\n", "Train loss history: History(keys=['value'], n_iters=25)\n", "Validation loss history: History(keys=['value'], n_iters=5)\n", "Checkpoint info: HistoryDict with 2 elements:\n", "\t'model_size_mb' -> History(keys=['value'], n_iters=3)\n", "\t'save_time_seconds' -> History(keys=['value'], n_iters=3)\n", "\n", "Logging frequencies:\n", "Train loss logged at: 25 steps\n", "Validation logged at: 5 steps\n", "Checkpoints at: [ 0 10 20]\n"]}], "source": ["# Access the underlying HistoryDict\n", "data = log.data\n", "print(f\"Type of log.data: {type(data)}\")\n", "\n", "# Access different metrics\n", "print(f\"Train loss history: {data['train_loss']}\")\n", "print(f\"Validation loss history: {data['validation/val_loss']}\")\n", "print(f\"Checkpoint info: {data['checkpoint']}\")\n", "\n", "# Show different logging frequencies\n", "print(f\"\\nLogging frequencies:\")\n", "print(f\"Train loss logged at: {len(data['train_loss'].iters)} steps\")\n", "print(f\"Validation logged at: {len(data['validation/val_loss'].iters)} steps\")\n", "print(f\"Checkpoints at: {data['checkpoint/model_size_mb'].iters}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plotting RuntimeLog Data"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.178316Z", "iopub.status.busy": "2025-07-24T11:52:41.178209Z", "iopub.status.idle": "2025-07-24T11:52:41.449695Z", "shell.execute_reply": "2025-07-24T11:52:41.449347Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create a comprehensive plot of the logged data\n", "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "\n", "# Training and validation loss\n", "train_loss = data['train_loss']\n", "val_loss = data['validation/val_loss']\n", "\n", "axes[0, 0].plot(train_loss.iters, train_loss.values, 'b-', label='Train Loss', alpha=0.8)\n", "axes[0, 0].plot(val_loss.iters, val_loss.values, 'r-o', label='Val Loss', markersize=5)\n", "axes[0, 0].set_title('Training Progress')\n", "axes[0, 0].set_xlabel('Epoch')\n", "axes[0, 0].set_ylabel('Loss')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Learning rate\n", "lr_hist = data['learning_rate']\n", "axes[0, 1].plot(lr_hist.iters, lr_hist.values, 'g-', linewidth=2)\n", "axes[0, 1].set_title('Learning Rate Schedule')\n", "axes[0, 1].set_xlabel('Epoch')\n", "axes[0, 1].set_ylabel('Learning Rate')\n", "axes[0, 1].set_yscale('log')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Validation accuracy\n", "val_acc = data['validation/val_accuracy']\n", "axes[1, 0].plot(val_acc.iters, val_acc.values, 'm-s', markersize=6, linewidth=2)\n", "axes[1, 0].set_title('Validation Accuracy')\n", "axes[1, 0].set_xlabel('Epoch')\n", "axes[1, 0].set_ylabel('Accuracy')\n", "axes[1, 0].set_ylim(0, 1)\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Model size at checkpoints\n", "model_size = data['checkpoint/model_size_mb']\n", "axes[1, 1].bar(model_size.iters, model_size.values, alpha=0.7, color='orange')\n", "axes[1, 1].set_title('Model Size at Checkpoints')\n", "axes[1, 1].set_xlabel('Epoch')\n", "axes[1, 1].set_ylabel('<PERSON><PERSON> (MB)')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Saving and Loading RuntimeLog Data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-07-24T11:52:41.451403Z", "iopub.status.busy": "2025-07-24T11:52:41.451150Z", "iopub.status.idle": "2025-07-24T11:52:41.454968Z", "shell.execute_reply": "2025-07-24T11:52:41.454705Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RuntimeLog data saved to: /var/folders/b_/dvph1c6569155bspz2m2sml40000gp/T/tmp7ewmq_q8.json\n", "Loaded data type: <class 'netket.utils.history.history_dict.HistoryDict'>\n", "Loaded keys: ['checkpoint', 'learning_rate', 'train_loss', 'validation']\n", "Data integrity check: True\n"]}], "source": ["# Save the RuntimeLog data\n", "with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:\n", "    tmp_filename = tmp_file.name\n", "\n", "log.serialize(tmp_filename)\n", "print(f\"RuntimeLog data saved to: {tmp_filename}\")\n", "\n", "# Load it back as a HistoryDict\n", "loaded_data = history.HistoryDict.from_file(tmp_filename)\n", "print(f\"Loaded data type: {type(loaded_data)}\")\n", "print(f\"Loaded keys: {list(loaded_data.keys())}\")\n", "\n", "# Verify data integrity\n", "original_train_loss = log.data['train_loss'].values\n", "loaded_train_loss = loaded_data['train_loss'].values\n", "print(f\"Data integrity check: {np.allclose(original_train_loss, loaded_train_loss)}\")\n", "\n", "# Clean up\n", "os.unlink(tmp_filename)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}