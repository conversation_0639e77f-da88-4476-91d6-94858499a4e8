{"cells": [{"cell_type": "code", "execution_count": null, "id": "a1bdf6b8", "metadata": {}, "outputs": [], "source": ["# Try to load netket, and install it if the import fails\n", "try:\n", "    import netket as nk\n", "except ImportError:\n", "    !pip install --quiet --upgrade netket\n", "    \n", "import netket as nk\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 14, "id": "301f68e3-392f-4411-b440-683e86c9926d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "acaf72ba-cc1b-4b9f-b603-e092c539e796", "metadata": {}, "source": ["(<PERSON><PERSON>)=\n", "# The Sampler module\n", "\n", "```{eval-rst}\n", ".. currentmodule:: netket.sampler\n", "```\n", "\n", "The [Sampler](netket_sampler_api) module contains several Monte-Carlo samplers that can be used to generate samples distributed according to a certain $\\log$-probability distribution function. \n", "The samplers defined here can be used together with the [Variational State](varstate) interface or stand-alone in your jax code. They are all fully-`jax.jit` compatible, and they can be differentiated in forward mode.\n", "\n", "The sampler module also contains infrastructure necessary to define your own samplers that are compatible with the existing API, as well as custom transition rules for the Metropolis-Hastings sampler.\n", "\n", "The inheritance diagram of the classes in this module is shown below:\n", "```{eval-rst}\n", ".. inheritance-diagram:: netket.sampler netket.sampler.rules\n", "\t:top-classes: netket.sampler.AbstractSampler\n", "\t:parts: 1\n", "\n", "```\n", "\n", "Following the purely functional design of <PERSON>, we define the sampler to be a stateless collection of settings and parameters inheriting from the abstract base class {class}`Sampler`, while storing all mutable state such as the PRNG key and the statistics of acceptances in an immutable sampler state object inheriting from {class}`SamplerState`.\n", "\n", "In the documentation below we will first discuss how to construct Monte-Carlo samplers and what are their common options, then in ... we will show how to use samplers directly so that you can use them in your own projects. \n", "Note that if you are using the Variational State interface, you only need to know how to define samplers, because the Variational State will take care of sampling. "]}, {"cell_type": "markdown", "id": "424968d9-caef-44aa-9c0c-4356ccd17319", "metadata": {}, "source": ["## Constructing <PERSON><PERSON><PERSON>\n", "\n", "NetKet's samplers will generate a set of samples $\\sigma$ that respects the following condition:\n", "\n", "$$\n", "    \\sigma \\in \\mathcal{H} \\ \\ \\ \\ \\ \\vert\\ \\ \\ \\  p(\\sigma) \\sim \\frac{\\exp[\\alpha\\ \\Re[\\log\\text{pdf}(\\sigma)]]}{\\sum_{\\eta\\in\\mathcal{H}} \\exp[\\alpha\\ \\Re[\\log\\text{pdf}(\\eta)]]}\n", "$$\n", "\n", "All NetKet samplers generate samples from an [Hilbert space](Hilbert) object, which must be passed as the first positional argument.\n", "Other options that can be specified are:\n", "\n", " - The sampler's `dtype`, which is the data type used to store the arrays of samples. When working with discrete hilbert spaces such as \n", "{class}`nk.hilbert.Spin` and {class}`nk.hilbert.Fock` the dtype is not particularly important, and you might reduce your memory consumption by using some short \n", "types such as `np.int8`. By default `dtype=float64`.\n", "When working with discrete spaces samples are usually made up of strings of integers, therefore if you use low-precision types such as `np.int8`\n", "or `jnp.bfloat16` you will not be losing precision in the number you're representing. Moreover, as soon as you will feed the low-precision samples\n", "to your model, it will be promoted to a wider type matching the `dtype` used for the model's parameters.\n", "\n", " - The sampler's `machine_power`, which is the $\\alpha$ in the formula above specifying what power of the probability distribution $\\text{pdf}(\\sigma)$ we are sampling."]}, {"cell_type": "code", "execution_count": 2, "id": "2e47fabb-845e-4261-8941-088e0ddbc301", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ExactSampler(hilbert=Spin(s=1/2, N=5), machine_pow=2, dtype=<class 'numpy.float64'>)\n"]}], "source": ["hilbert = nk.hilbert.Spin(0.5, 5)\n", "\n", "sampler = nk.sampler.ExactSampler(hilbert)\n", "\n", "print(sampler)"]}, {"cell_type": "markdown", "id": "90225849-8cf0-4450-a3e0-cbe4a5eb5ee4", "metadata": {}, "source": ["The sampler itself is a [_frozen dataclass_ ](https://stackoverflow.com/questions/66194804/what-does-frozen-mean-for-dataclasses), meaning that you cannot change it's attributes once it's created.\n", "To change an attribute of the sampler, you must use the function {meth}`~SamplerState.replace` which returns a new sampler object with that setting changed.\n", "The old sampler will be unchanged"]}, {"cell_type": "code", "execution_count": 3, "id": "bec30e45-d7f4-4d21-9e2b-00b9d8fde3d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this is the old one, unchanged:  ExactSampler(hilbert=Spin(s=1/2, N=5), machine_pow=2, dtype=<class 'numpy.float64'>)\n", "this is the new one:             ExactSampler(hilbert=Spin(s=1/2, N=5), machine_pow=3, dtype=<class 'numpy.float64'>)\n"]}], "source": ["new_sampler = sampler.replace(machine_pow=3)\n", "\n", "print(\"this is the old one, unchanged: \", sampler)\n", "print(\"this is the new one:            \", new_sampler)"]}, {"cell_type": "markdown", "id": "0a80b8e1-6e76-46ec-a44d-48d356ae82d7", "metadata": {}, "source": ["This (sometimes annoying) behaviour is needed to make our sampler objects completely jax-compatible. You can freely pass a NetKet sampler to a jax function and jit it without worrying!"]}, {"cell_type": "markdown", "id": "6182794f-b390-47dd-b9d5-a6af10c9387b", "metadata": {}, "outputs": [], "source": ["## Using Samplers \n", "\n", "The core sampler's API interface is made up of 3 functions:\n", " - {meth}`Sampler.init_state(log_pdf, parameters, seed) <Sampler.init_state>`, which constructs a structure holding the state of the sampler with the provided seed. The seed can either be an integer or a {func}`jax.random.PRNGKey` object. When running distributed computations across multiple devices using JAX sharding, this function automatically generates a different seed on every device, so you do not need to take care of that (only the seed on the main device will be considered).\n", " - {meth}`Sampler.reset(log_pdf, parameters, state) <Sampler.reset>`, is a function that should be called every time the variational parameters of the log_pdf have changed, and in some cases to reset the chain. _NOTE: The state is not valid unless you call reset at least once_\n", " - {meth}`Sampler.sample(log_pdf, parameters, state, chain_length=1) <Sampler.sample>`, which samples a sequence of samples with `chain_length` samples. If you are using a direct sampler like {class}`ExactSampler` or {class}`ARDirectSampler` this determines how many samples you generate. If you are using a Markov-Chain sampler you will get `Sampler.n_chains * chain_length` total samples.\n", "\n", "In general, the `log_pdf` must be a function with the signature `log_pdf(<PERSON>y<PERSON><PERSON>, jnp.Array[...,Sampler.hilbert.size]) -> jnp.Array[...]` or a flax Module. (Note: Some samplers such as {class}`ARDirectSampler` only work with a flax Module.)\n", "\n", "To build an example, first we must define a log-pdf from which we want to sample. \n", "In this case we construct an RBM with real parameters, and initialise some random parameters which will give a roughly flat distribution:"]}, {"cell_type": "code", "execution_count": 4, "id": "b0d60111-8f59-46d7-a624-13050b94987c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:No GPU/TPU found, falling back to CPU. (Set TF_CPP_MIN_LOG_LEVEL=0 and rerun for more info.)\n"]}], "source": ["# We define an Hilbert space of 3 Spin-1/2\n", "hilbert = nk.hilbert.Spin(0.5, 4)\n", "\n", "# We define our variational ansatz\n", "log_pdf = nk.models.RBM(param_dtype=float)\n", "\n", "# and we initialize it's parameters\n", "param_seed = jax.random.key(0)\n", "\n", "pars = log_pdf.init(param_seed, hilbert.random_state(param_seed, 3))"]}, {"cell_type": "markdown", "id": "d1cbac34-ac46-47fc-9cdf-7d834f3ebc40", "metadata": {}, "source": ["Now we can plot the probability distribution by computing it over the whole Hilbert space. Of course this is expensive, but if we have a small set of variables it's possible to do it."]}, {"cell_type": "code", "execution_count": 5, "id": "c41b45e4-3354-4a2b-9cdc-0edbdbf1088a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'pdf')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["pdf_unnormalized = jnp.exp(2*log_pdf.apply(pars, hilbert.all_states()))\n", "pdf = pdf_unnormalized / jnp.sum(pdf_unnormalized)\n", "\n", "plt.plot(pdf)\n", "plt.ylim(0,0.1)\n", "plt.xlabel(\"hilbert space index\")\n", "plt.ylabel(\"pdf\")"]}, {"cell_type": "markdown", "id": "ed684118-9783-4b5c-a7a5-82a6bcc6ff15", "metadata": {}, "source": ["{class}`Exact<PERSON>ample<PERSON>` builds exactly this same probability distribution (which has an exponentially large cost in the number of variables) and generates samples from it. \n", "To use it we can do:"]}, {"cell_type": "code", "execution_count": 6, "id": "e5d15127-1c38-43e1-ac1f-edb4b1551e02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The shape of the samples is: (10, 1, 4), and the dtype is int8\n", "[-1 -1  1  1]\n"]}], "source": ["# We construct an Exact Sampler\n", "sampler = nk.sampler.ExactSampler(hilbert, dtype=jnp.int8)\n", "\n", "# We create the state of the sampler\n", "sampler_state = sampler.init_state(log_pdf, pars, jax.random.key(1))\n", "\n", "# We call reset (this will pre-compute the log_pdf on the whole hilbert space)\n", "sampler_state = sampler.reset(log_pdf, pars, sampler_state)\n", "\n", "# generate samples\n", "samples, sampler_state = sampler.sample(log_pdf, pars, state=sampler_state, chain_length=10)\n", "\n", "print(f\"The shape of the samples is: {samples.shape}, and the dtype is {samples.dtype}\")\n", "print(samples[0,0])"]}, {"cell_type": "markdown", "id": "6aa28e1c-31c7-4497-a04e-1a21a8e798c3", "metadata": {}, "source": ["Notice that samplers return a 3-tensor of samples where the first dimension is `chain_length`, the second is the number of parallel chains (or 1 in non Markov-Chain samplers such as ExactSampler) and the last dimension is the hilbert space size.\n", "\n", "We could verify that those samples are distributed according to the correct distribution by running the following code:"]}, {"cell_type": "code", "execution_count": 7, "id": "25efb614-b56f-4fa6-bfee-bf805070794d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def estimate_pdf(n_samples):\n", "    samples, _ = sampler.sample(log_pdf, pars, state=sampler_state, chain_length=n_samples)\n", "\n", "    # Convert the samples to indices in the space\n", "    idxs = hilbert.states_to_numbers(samples)\n", "\n", "    # count the occurrences of all indices\n", "    return jnp.sum(idxs == jnp.arange(hilbert.n_states), axis=0) / idxs.shape[0]\n", "\n", "plt.plot(pdf, label=\"exact\")\n", "plt.plot(estimate_pdf(2**10), label=\"2^10 samples\")\n", "plt.plot(estimate_pdf(2**14), '--', label=\"2^14 samples\")\n", "\n", "plt.ylim(0,0.1)\n", "plt.xlabel(\"hilbert space index\")\n", "plt.ylabel(\"pdf\")\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "9381b83d-c827-436e-a2eb-7e88341830f8", "metadata": {}, "source": ["\n", "## Metropolis-<PERSON>-Chain Samplers\n", "\n", "NetKet also implements a very flexible class of Markov Chain samplers that use the Metropolis-Hastings algorithm and is called {class}`MetropolisSampler`.\n", "The Metropolis--Hastings algorithm is used to generate samples from an arbitrary probability distribution. \n", "In each step, it suggests a transition from the current configuration $s$ to a proposed configuration $s'$.\n", "The proposal is accepted with probability\n", "\n", "$$\n", "    P_\\text{acc}(s \\rightarrow s') = \\min\\left( 1, \\frac{P(s')}{P(s)} \\frac{g(s \\mid s')}{g(s' \\mid s)} \\right),\n", "$$\n", "\n", "where $P$ is the distribution being sampled from and $g(s' \\mid s)$ is the conditional probability of proposing $s'$ given the current $s$. \n", "\n", "We call $L(s, s') = \\log [g(s \\mid s')/g(s' \\mid s)]$ to denote the correcting factor to the log probability due to the transition kernel. \n", "This factor is needed for asymmetric kernels that might propose one move with higher probability than its reverse.\n", "Simple kernels, such as a local spin flip or exchange, are symmetric, therefore $L(s,s') = L(s', s) = 1$, but other proposals, such as Hamiltonian sampling, are not necessarily symmetric and need this factor.\n", "\n", "The transition rules (or kernels) that NetKet implements for discrete hilbert spaces are the following:\n", " - {class}`rules.LocalRule`: A transition rule acting on the local degree of freedom. This selects a random degree of freedom `i` and then proposes a different local configuration with uniform probability. For example, for Spin-1/2, this will flip a random spin. **Note: This transition rule ignores constraints on the hilbert space, such as total magnetization, and might produce states that do not respect it**\n", " - {class}`rules.ExchangeRule`: A Rule exchanging the value of the local degree of freedom between two sites $i$ and $j$, chosen from a list of\n", "    possible couples (clusters). To construct it, the user must provide a graph object or a list of edges. **This sampler does respect constraints on Hilbert spaces, but might not explore the whole hilbert space because it preserves the total number of excitations/magnetization.**\n", " - {class}`rules.HamiltonianRule`: which transitions the configuration according to the off-diagonal elements of the Hamiltonian. As this rule might not be symmetric, this uses the factor $L(s,s')$ to reweight the transition probability.\n", " \n", "For Continuous Hilbert spaces, the only rule that we implement (at the moment is the following:\n", " - {class}`rules.GaussianRule` which proposes a move by adding a normally-distributed random number to a single degree of freedom. The standard deviation of the Gaussian distribution can be modified.\n", "\n", "The standard syntax to build a MCMC sampler with a certain rule is to pass an instance of the rule as the second argument of the Metropolis Sampler: \n"]}, {"cell_type": "code", "execution_count": 8, "id": "e620e865-0d2f-4f74-993d-807fac593025", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MetropolisSampler(rule = LocalRule(), n_chains = 10, n_sweeps = 4, reset_chains = False, machine_power = 2, dtype = <class 'numpy.float64'>)\n"]}], "source": ["rule = nk.sampler.rules.LocalRule()\n", "sampler = nk.sampler.MetropolisSampler(hilbert, rule, n_chains = 10)\n", "print(sampler)"]}, {"cell_type": "markdown", "id": "794fd8e9-194e-41a9-a52f-f3288aebe568", "metadata": {}, "source": ["The Metropolis Sampler takes as the first two positional arguments the hilbert space to sample and the transition rule. \n", "Additional options that can be specified are the `dtype` and `machine_power`, like all other samplers, but also:\n", " - `n_chains: int` or `n_chains_per_rank:int `: The number of parallel Markov Chains to sample from. Depending on the complexity of the log-pdf and the size of the system, sampling from 1 chain or 10 might take the same time on CPU. Usually on GPUs you can sample from ~1000 of chains at the same cost of sampling from 1 chain. Only one of those 2 options can be specified at a given time. Rank is meant to mean different number of jax devices. Defaults to `n_chains_per_rank=16`\n", " - `sweep_size: int`: The number of sub-sampling sweeps per sample. This integer controls how many Metropolis-Hastings steps are taken before returning a valid sampling. Effectively, this means that when sampling a chain of length `chain_length`, the effective chain length is `chain_length*sweep_size`, but only $1$ sample every `sweep_size` is returned. The default value is the number of degrees of freedom in the hilbert space ({attr}`AbstractHilbert.size <netket.hilbert.AbstractHilbert.size>`).\n", " -`reset_chains : bool`: If True, resets the chain state when `Sampler.reset` is called. By default this is `False`, meaning that during a run the chain is never completely reset (when you reset the chain, a configuration is drawn with uniform probability from the hilbert space unless a special rule is used). \n", "\n", "Other than those additional settings at constructions, Metropolis Samplers follow the same API as all other samplers:"]}, {"cell_type": "code", "execution_count": 9, "id": "d39c18a1-904e-4399-ad00-0f0f24e44ddb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The shape of the samples is: (100, 10, 4), and the dtype is float64\n", "[-1.  1. -1.  1.]\n"]}], "source": ["# We create the state of the sampler\n", "sampler_state = sampler.init_state(log_pdf, pars, jax.random.key(1))\n", "\n", "# We call reset (this will pre-compute the log_pdf on the whole hilbert space)\n", "sampler_state = sampler.reset(log_pdf, pars, sampler_state)\n", "\n", "# generate samples\n", "samples, sampler_state = sampler.sample(log_pdf, pars, state=sampler_state, chain_length=100)\n", "\n", "print(f\"The shape of the samples is: {samples.shape}, and the dtype is {samples.dtype}\")\n", "print(samples[0,0])"]}, {"cell_type": "markdown", "id": "c854424b-be86-4a50-925e-c4857e234414", "metadata": {}, "source": ["However, notice that this time the samples returned are a tensor of shape `(chain_length, n_chains_per_rank, hilbert.size)`. \n", "\n", "For ease of use, we provide some shorthands such as {func}`MetropolisLocal`, {func}`MetropolisHamiltonian` and so on, which automatically build the relevant rule."]}, {"cell_type": "markdown", "id": "2727bece-30e4-4033-87c4-a60bf92afe21", "metadata": {}, "source": ["## Defining custom Transition Rules for Metropolis Sampler\n", "\n", "A transition kernel is a NetKet dataclass inheriting from {class}`rules.MetropolisRule` that must define the following two methods:\n", "\n", " - {meth}`MetropolisRule.init_state(self, sampler, log_pdf, params, rng) -> Optional[Any] <rules.MetropolisRule.init_state>`: which can be used to initialise some arbitrary state or cache. This will be stored inline inside of `MetropolisState`.\n", " - {meth}`MetropolisRule.random_state(rule, sampler, log_pdf, parameters, state, rng, σ: jnp.n<PERSON>ray[n_chains, hilbert.size]) -> jnp.Array[n_chains, hilbert.size] <rules.MetropolisRule.random_state>`: this function is called when the chain is initialised or resetted, and should return a valid configuration for the Markov Chain state. The default implementation returns a state sampled with `hilbert.random_state`. \n", "  - {meth}`MetropolisRule.transition(rule, sampler, log_pdf, parameters, state, rng: PRNGKeyT) -> jnp.Array[n_chains, hilbert.size] <rules.MetropolisRule.transition>`: this function is called when the chain is initialised or resetted, and should return a valid configuration for the Markov Chain state. The default implementation returns a state sampled with {meth}`AbstractHilbert.random_state <netket.hilbert.AbstractHilbert.random_state>`. \n", "  \n", "As an example, I will define below a custom Metropolis Transition rule that flips not one, but two spins at a time"]}, {"cell_type": "code", "execution_count": 10, "id": "6a6c5124-5cc2-4335-8c1d-471041ed0d5c", "metadata": {}, "outputs": [], "source": ["@nk.utils.struct.dataclass\n", "class TwoLocalRule(nk.sampler.rules.MetropolisRule):\n", "    \n", "    def transition(self, sampler, machine, parameters, state, key, σ):\n", "        # Deduce the number of MCMC chains from input shape\n", "        n_chains = σ.shape[0]\n", "        \n", "        # Load the <PERSON>lbert space of the sampler\n", "        hilb = sampler.hilbert\n", "        \n", "        # Split the rng key into 2: one for each random operation\n", "        key_indx, key_flip = jax.random.split(key, 2)\n", "        \n", "        # Pick two random sites on every chain\n", "        indxs = jax.random.randint(\n", "            key_indx, shape=(n_chains, 2), minval=0, maxval=hilb.size\n", "        )\n", "        \n", "        # flip those sites\n", "        σp, _ = nk.hilbert.random.flip_state(hilb, key_flip, σ, indxs)\n", "        \n", "        # If this transition had a correcting factor L, it's possible\n", "        # to return it as a vector in the second value\n", "        return σp, None\n", "\n", "    #\n", "    # random_state is inherited from the base class, so no need to define it\n", "    #\n", "    #def random_state(self, sampler, machine, pars, state, key):\n", "    #    return sampler.hilbert.random_state(\n", "    #        key, size=sampler.n_batches, dtype=sampler.dtype\n", "    #    )\n", "\n"]}, {"cell_type": "markdown", "id": "e6f449d5-5fee-43d7-ad0a-995bd64e8380", "metadata": {}, "source": ["And then, in order to use this transition kernel we can follow the same procedure as before:"]}, {"cell_type": "code", "execution_count": 11, "id": "dd055941-54ad-445b-9f0f-bde76a27fdfb", "metadata": {}, "outputs": [], "source": ["sampler = nk.sampler.MetropolisSampler(hilbert, TwoLocalRule())"]}, {"cell_type": "code", "execution_count": 13, "id": "f4e1c353-4ad7-4f3e-b01d-ad8bd53f68ab", "metadata": {}, "outputs": [], "source": ["# We create the state of the sampler\n", "sampler_state = sampler.init_state(log_pdf, pars, jax.random.key(1))\n", "\n", "# We call reset (this will pre-compute the log_pdf on the whole hilbert space)\n", "sampler_state = sampler.reset(log_pdf, pars, sampler_state)\n", "\n", "# generate samples\n", "samples, sampler_state = sampler.sample(log_pdf, pars, state=sampler_state, chain_length=100)"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python (Netket development)", "language": "python", "name": "dev-netket"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}