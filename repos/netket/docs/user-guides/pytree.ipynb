{"cells": [{"cell_type": "code", "execution_count": null, "id": "7a30ee71", "metadata": {"lines_to_next_cell": 0}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "e2bcc63c", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:25.041035Z", "iopub.status.busy": "2025-07-14T14:04:25.040725Z", "iopub.status.idle": "2025-07-14T14:04:26.539985Z", "shell.execute_reply": "2025-07-14T14:04:26.539642Z"}, "tags": ["hide-input", "hide-output"]}, "outputs": [], "source": ["# Try to load netket, and install it if the import fails\n", "try:\n", "    import netket as nk\n", "except ImportError:\n", "    !pip install --quiet --upgrade netket\n", "    \n", "import netket as nk\n", "import jax\n", "import jax.numpy as jnp\n", "\n", "from netket.utils.struct import Pytree, field, static_field\n", "from flax import serialization\n", "import dataclasses"]}, {"cell_type": "code", "execution_count": null, "id": "84ff3388", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "220657ba", "metadata": {}, "source": ["(<PERSON><PERSON><PERSON>)=\n", "# NetKet Pytrees\n", "\n", "```{eval-rst}\n", ".. currentmodule:: netket.utils.struct\n", "```\n", "\n", "The `Pytree` class provides the foundation for creating JAX-compatible \n", "data structures that can be seamlessly used with JAX transformations \n", "like `jax.jit`, `jax.vmap`, and `jax.grad`. \n", "\n", "A PyTree in JAX is a tree-like data structure composed of containers (like tuples, lists, or dictionaries) with leaves that are arrays or scalars. The `Pytree` class in NetKet extends this concept to custom classes, allowing them to be transparently used with JAX transformations while maintaining object-oriented design principles.\n", "\n", "The `Pytree` class distinguishes between two types of fields:\n", "\n", "- **Dynamic fields (PyTree nodes)**: These are part of the computational graph and can be transformed by JAX. They typically contain arrays, parameters, or other data that changes during computation.\n", "- **Static fields**: These are metadata or configuration parameters that remain constant during JAX transformations. They must be hashable and are excluded from differentiation.\n", "\n", "## Basic Usage\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5be4180a", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.541802Z", "iopub.status.busy": "2025-07-14T14:04:26.541602Z", "iopub.status.idle": "2025-07-14T14:04:26.602262Z", "shell.execute_reply": "2025-07-14T14:04:26.601967Z"}, "lines_to_next_cell": 1}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data values: [1. 2. 3.]\n", "Data size: 3\n", "Sum: 6.0\n"]}], "source": ["from netket.utils import struct\n", "\n", "class SimpleData(struct.P<PERSON>ree):\n", "    \"\"\"A simple data container demonstrating basic Pytree usage.\"\"\"\n", "    \n", "    # Dynamic field - will be part of JAX transformations\n", "    values: jax.<PERSON>y\n", "    \n", "    # Static field - configuration that doesn't change during computation\n", "    size: int = struct.static_field()\n", "    \n", "    def __init__(self, values, size):\n", "        self.values = values\n", "        self.size = size\n", "    \n", "    def sum(self):\n", "        return jnp.sum(self.values)\n", "\n", "# Create an instance\n", "data = SimpleData(\n", "    values=jnp.array([1.0, 2.0, 3.0]),\n", "    size=3\n", ")\n", "\n", "print(f\"Data values: {data.values}\")\n", "print(f\"Data size: {data.size}\")\n", "print(f\"Sum: {data.sum()}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "20ea38aa", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.603724Z", "iopub.status.busy": "2025-07-14T14:04:26.603618Z", "iopub.status.idle": "2025-07-14T14:04:26.629039Z", "shell.execute_reply": "2025-07-14T14:04:26.628739Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean: 2.0\n"]}], "source": ["# The data can be used directly with JAX transformations\n", "@jax.jit\n", "def compute_mean(data):\n", "    return jnp.mean(data.values)\n", "\n", "result = compute_mean(data)\n", "print(f\"Mean: {result}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "3f60567c", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.630526Z", "iopub.status.busy": "2025-07-14T14:04:26.630426Z", "iopub.status.idle": "2025-07-14T14:04:26.656794Z", "shell.execute_reply": "2025-07-14T14:04:26.656501Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTree leaves: [Array([1., 2., 3.], dtype=float64)]\n", "Transformed values: [2. 4. 6.]\n", "Preserved size: 3\n"]}], "source": ["# When we inspect the PyTree structure, we see only dynamic fields\n", "leaves = jax.tree.leaves(data)\n", "print(f\"PyTree leaves: {leaves}\")\n", "\n", "# Static fields are preserved during transformations\n", "transformed_data = jax.tree.map(lambda x: x * 2, data)\n", "print(f\"Transformed values: {transformed_data.values}\")\n", "print(f\"Preserved size: {transformed_data.size}\")"]}, {"cell_type": "markdown", "id": "d52ef534", "metadata": {}, "source": ["## Features\n", "\n", "### Immutability and the `replace` Method\n", "\n", "By default, `Pytree` objects are immutable, similar to frozen dataclasses. This immutability is crucial for JAX's functional programming paradigm and ensures that transformations don't have unexpected side effects."]}, {"cell_type": "code", "execution_count": 5, "id": "88d0193a", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.658204Z", "iopub.status.busy": "2025-07-14T14:04:26.658107Z", "iopub.status.idle": "2025-07-14T14:04:26.660471Z", "shell.execute_reply": "2025-07-14T14:04:26.660200Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: <class '__main__.SimpleData'> is immutable, trying to update field values\n"]}], "source": ["# Trying to modify a field directly will raise an error\n", "try:\n", "    data.values = jnp.array([4.0, 5.0, 6.0])\n", "except AttributeError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "84e96213", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.661723Z", "iopub.status.busy": "2025-07-14T14:04:26.661606Z", "iopub.status.idle": "2025-07-14T14:04:26.664149Z", "shell.execute_reply": "2025-07-14T14:04:26.663888Z"}, "lines_to_next_cell": 1}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original values: [1. 2. 3.]\n", "New values: [4. 5. 6.]\n", "Size unchanged: 3\n"]}], "source": ["# Instead, use the replace method to create a new instance with modified values\n", "new_data = data.replace(values=jnp.array([4.0, 5.0, 6.0]))\n", "print(f\"Original values: {data.values}\")\n", "print(f\"New values: {new_data.values}\")\n", "print(f\"Size unchanged: {new_data.size}\")"]}, {"cell_type": "markdown", "id": "d6c083fb", "metadata": {}, "source": ["### Mutable PyTrees\n", "\n", "While immutability is the default and recommended approach, you can create mutable PyTrees when needed. This is particularly useful during development or when working with algorithms that require in-place modifications."]}, {"cell_type": "code", "execution_count": null, "id": "d5733176", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.665412Z", "iopub.status.busy": "2025-07-14T14:04:26.665330Z", "iopub.status.idle": "2025-07-14T14:04:26.705842Z", "shell.execute_reply": "2025-07-14T14:04:26.705508Z"}, "lines_to_next_cell": 1}, "outputs": [], "source": "class Counter(struct.Pytree, mutable=True):\n    \"\"\"A simple mutable counter.\"\"\"\n    \n    count: jax.Array\n    step_size: int = struct.static_field(default=1)\n    \n    def __init__(self, count, step_size=1):\n        self.count = count\n        self.step_size = step_size\n    \n    def increment(self):\n        \"\"\"Increment counter in-place.\"\"\"\n        self.count = self.count + self.step_size\n\n# Create a mutable counter\ncounter = Counter(\n    count=jnp.array(0),\n    step_size=2\n)\n\nprint(f\"Initial count: {counter.count}\")\n\n# Update counter in-place\ncounter.increment()\nprint(f\"After increment: {counter.count}\")"}, {"cell_type": "markdown", "id": "52876362", "metadata": {}, "source": ["### Field Types and Metadata\n", "\n", "The `field` function provides fine-grained control over how fields are handled in PyTrees. It supports various options for serialization, caching, and distributed computing."]}, {"cell_type": "markdown", "id": "jnerjucsxy", "source": "### Excluding Fields from PyTree Structure\n\nSometimes you need to store data that should be completely excluded from JAX transformations, serialization, and hashing. The `pytree_ignore=True` option excludes fields from the PyTree structure, serialization, and static metadata used for hashing.\n\nThis is useful for caches, temporary data, or debugging information that shouldn't affect the computational graph or object identity.", "metadata": {}}, {"cell_type": "code", "id": "6dsdc9j73sp", "source": "class SimpleOperator(struct.Pytree):\n    \"\"\"Simple operator with cache that doesn't affect PyTree operations.\"\"\"\n    \n    # Core data (included in PyTree)\n    matrix: jax.Array\n    \n    # Cache completely excluded from PyTree, serialization, and hashing\n    _cache: dict = struct.field(pytree_node=False, pytree_ignore=True, default_factory=dict)\n    \n    def __init__(self, matrix):\n        self.matrix = matrix\n        self._cache = {}\n\n# Create two operators with same matrix but different cache\nop1 = SimpleOperator(jnp.array([[1.0, 2.0]]))\nop2 = SimpleOperator(jnp.array([[1.0, 2.0]]))\nop1._cache[\"key\"] = \"different\"\nop2._cache[\"key\"] = \"values\"\n\nprint(f\"PyTree leaves: {len(jax.tree_util.tree_leaves(op1))}\")  # Only matrix\nprint(f\"Objects are equal: {op1 == op2}\")  # True - cache doesn't affect equality", "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "id": "z4ad5td8mrg", "source": "This feature is used in NetKet's [ContinuousOperator](https://github.com/netket/netket/blob/master/netket/operator/_continuous_operator.py#L84) where the `_hash` cache is excluded from PyTree operations:\n\n```python\n@struct.property_cached(pytree_ignore=True)\ndef _hash(self) -> int:\n    return hash((type(self), self._attrs))\n```\n\n**Key differences between field types:**\n- **Dynamic fields**: Part of PyTree structure, included in transformations, serialization, and hashing\n- **Static fields**: Not part of PyTree structure, but included in serialization and hashing  \n- **Excluded fields** (`pytree_ignore=True`): Completely excluded from PyTree structure, serialization, and hashing\n\n**Note**: When using `pytree_ignore=True`, you must also set `pytree_node=False`.", "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "4c2bc187", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.707259Z", "iopub.status.busy": "2025-07-14T14:04:26.707162Z", "iopub.status.idle": "2025-07-14T14:04:26.763439Z", "shell.execute_reply": "2025-07-14T14:04:26.763143Z"}}, "outputs": [], "source": "class Record(struct.Pytree):\n    \"\"\"Demonstrates advanced field configurations.\"\"\"\n    \n    # Standard dynamic field\n    data: jax.Array\n    \n    # Static field with default value\n    name: str = struct.static_field(default=\"default\")\n    \n    # Field with custom serialization name\n    info: jax.Array = struct.field(serialize_name=\"information\")\n    \n    # Field that won't be serialized\n    temp: jax.Array = struct.field(serialize=False)\n    \n    # Field with default factory\n    metadata: dict = struct.field(default_factory=dict, pytree_node=False)\n    \n    def __init__(self, data, info, temp=None, name=\"default\"):\n        self.data = data\n        self.info = info\n        self.temp = temp if temp is not None else jnp.zeros(2)\n        self.name = name\n        self.metadata = {\"created\": True}\n\nrecord = Record(\n    data=jnp.array([1.0, 2.0, 3.0]),\n    info=jnp.array([0.1, 0.2]),\n    name=\"example\"\n)\n\nprint(f\"Name: {record.name}\")\nprint(f\"Metadata: {record.metadata}\")"}, {"cell_type": "markdown", "id": "6f679a90", "metadata": {}, "source": ["### Serialization with Flax\n", "\n", "`Pytree` objects integrate seamlessly with Flax's serialization system, allowing you to save and load object states efficiently. This is particularly important for checkpointing and data persistence."]}, {"cell_type": "code", "execution_count": 9, "id": "d7201c2c", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.764966Z", "iopub.status.busy": "2025-07-14T14:04:26.764870Z", "iopub.status.idle": "2025-07-14T14:04:26.767332Z", "shell.execute_reply": "2025-07-14T14:04:26.767043Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Serialized state dictionary:\n", "  data: [1. 2. 3.]\n", "  information: [0.1 0.2]\n"]}], "source": ["# Serialize the record to a state dictionary\n", "state_dict = serialization.to_state_dict(record)\n", "print(\"Serialized state dictionary:\")\n", "for key, value in state_dict.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "e93365e2", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.768717Z", "iopub.status.busy": "2025-07-14T14:04:26.768599Z", "iopub.status.idle": "2025-07-14T14:04:26.791460Z", "shell.execute_reply": "2025-07-14T14:04:26.791176Z"}, "lines_to_next_cell": 1}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Restored data: [1. 2. 3.]\n", "Restored info: [0.1 0.2]\n", "Temp (not restored): [1. 1.]\n"]}], "source": ["# Notice that temp is not serialized (serialize=False)\n", "# and info is stored under \"information\" (serialize_name=\"information\")\n", "\n", "# Create a new record instance with different values\n", "new_record = Record(\n", "    data=jnp.zeros(3),\n", "    info=jnp.zeros(2),\n", "    temp=jnp.ones(2)\n", ")\n", "\n", "# Restore from the state dictionary\n", "restored_record = serialization.from_state_dict(new_record, state_dict)\n", "\n", "print(f\"Restored data: {restored_record.data}\")\n", "print(f\"Restored info: {restored_record.info}\")\n", "print(f\"Temp (not restored): {restored_record.temp}\")"]}, {"cell_type": "markdown", "id": "7e19e772", "metadata": {}, "source": ["### Dynamic Node Discovery\n", "\n", "For maximum flexibility, you can enable dynamic node discovery, which allows fields to be added at runtime. This is useful when the structure of your PyTree depends on runtime conditions."]}, {"cell_type": "code", "execution_count": null, "id": "1c8d1836", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.792780Z", "iopub.status.busy": "2025-07-14T14:04:26.792676Z", "iopub.status.idle": "2025-07-14T14:04:26.796418Z", "shell.execute_reply": "2025-07-14T14:04:26.796134Z"}, "lines_to_next_cell": 1}, "outputs": [], "source": "class FlexibleData(struct.Pytree, dynamic_nodes=True):\n    \"\"\"A data structure that can have fields added dynamically.\"\"\"\n    \n    base: jax.Array\n    config: str = struct.static_field(default=\"default\")\n    \n    def __init__(self, base, config=\"default\", **kwargs):\n        self.base = base\n        self.config = config\n        \n        # Add additional fields dynamically\n        for key, value in kwargs.items():\n            setattr(self, key, value)\n\n# Create data with dynamic fields\nflex_data = FlexibleData(\n    base=jnp.array([1.0, 2.0]),\n    config=\"custom\",\n    extra1=jnp.array([3.0, 4.0, 5.0]),\n    extra2=jnp.array([6.0, 7.0])\n)\n\nprint(f\"Base: {flex_data.base}\")\nprint(f\"Extra1: {flex_data.extra1}\")\nprint(f\"Extra2: {flex_data.extra2}\")\n\n# All dynamic fields become part of the PyTree\nleaves = jax.tree_util.tree_leaves(flex_data)\nprint(f\"Number of PyTree leaves: {len(leaves)}\")"}, {"cell_type": "markdown", "id": "9a5993a8", "metadata": {}, "source": ["### Inheritance and Composition\n", "\n", "`Pytree` classes can be inherited and composed to build complex hierarchical structures. This is useful for creating modular, reusable components."]}, {"cell_type": "code", "execution_count": null, "id": "7202660d", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.797791Z", "iopub.status.busy": "2025-07-14T14:04:26.797699Z", "iopub.status.idle": "2025-07-14T14:04:26.821056Z", "shell.execute_reply": "2025-07-14T14:04:26.820760Z"}, "lines_to_next_cell": 1}, "outputs": [], "source": "class BaseContainer(struct.Pytree):\n    \"\"\"Base class for data containers.\"\"\"\n    \n    data: jax.Array\n    label: str = struct.static_field(default=\"base\")\n    \n    def __init__(self, data, label=\"base\"):\n        self.data = data\n        self.label = label\n\nclass NumberContainer(BaseContainer):\n    \"\"\"Container for numbers with additional operations.\"\"\"\n    \n    scale: float = struct.static_field(default=1.0)\n    \n    def __init__(self, data, label=\"numbers\", scale=1.0):\n        super().__init__(data, label)\n        self.scale = scale\n    \n    def scaled_sum(self):\n        return jnp.sum(self.data) * self.scale\n\n# Create a number container\nnumbers = NumberContainer(\n    data=jnp.array([1.0, 2.0, 3.0]),\n    label=\"test\",\n    scale=2.0\n)\n\nprint(f\"Data: {numbers.data}\")\nprint(f\"Label: {numbers.label}\")\nprint(f\"Scale: {numbers.scale}\")\nprint(f\"Scaled sum: {numbers.scaled_sum()}\")"}, {"cell_type": "markdown", "id": "d7d67d59", "metadata": {}, "source": ["## Working with JAX Transformations\n", "\n", "The real power of `Pytree` becomes apparent when working with JAX transformations. Let's explore how PyTrees behave under various JAX operations."]}, {"cell_type": "code", "execution_count": null, "id": "967be7c7", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.822505Z", "iopub.status.busy": "2025-07-14T14:04:26.822407Z", "iopub.status.idle": "2025-07-14T14:04:26.887281Z", "shell.execute_reply": "2025-07-14T14:04:26.886968Z"}, "lines_to_next_cell": 1}, "outputs": [], "source": "class OptimizableData(struct.Pytree):\n    \"\"\"A data structure suitable for optimization.\"\"\"\n    \n    values: jax.Array\n    target: jax.Array\n    learning_rate: float = struct.static_field(default=0.01)\n    \n    def __init__(self, values, target, learning_rate=0.01):\n        self.values = values\n        self.target = target\n        self.learning_rate = learning_rate\n    \n    def loss(self):\n        return jnp.mean((self.values - self.target) ** 2)\n\n# Create optimizable data\nopt_data = OptimizableData(\n    values=jnp.array([1.0, 2.0, 3.0]),\n    target=jnp.array([1.5, 2.5, 3.5])\n)\n\nprint(f\"Initial loss: {opt_data.loss()}\")"}, {"cell_type": "code", "execution_count": 14, "id": "408aa558", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.888807Z", "iopub.status.busy": "2025-07-14T14:04:26.888663Z", "iopub.status.idle": "2025-07-14T14:04:26.918058Z", "shell.execute_reply": "2025-07-14T14:04:26.917735Z"}, "lines_to_next_cell": 1}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gradients: [-0.33333333 -0.33333333 -0.33333333]\n", "Learning rate unchanged: 0.01\n"]}], "source": ["# Compute gradients with respect to the data\n", "@jax.jit\n", "def compute_gradients(data):\n", "    return jax.grad(lambda d: d.loss())(data)\n", "\n", "gradients = compute_gradients(opt_data)\n", "print(f\"Gradients: {gradients.values}\")\n", "\n", "# Note: static fields like learning_rate are not differentiated\n", "print(f\"Learning rate unchanged: {gradients.learning_rate}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "acc9c87d", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.919547Z", "iopub.status.busy": "2025-07-14T14:04:26.919435Z", "iopub.status.idle": "2025-07-14T14:04:26.945579Z", "shell.execute_reply": "2025-07-14T14:04:26.945294Z"}, "lines_to_next_cell": 1}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated values: [1.00333333 2.00333333 3.00333333]\n", "Updated loss: 0.2466777777777777\n"]}], "source": ["# Update the data using gradients\n", "@jax.jit\n", "def update_data(data, gradients):\n", "    return data.replace(\n", "        values=data.values - data.learning_rate * gradients.values\n", "    )\n", "\n", "updated_data = update_data(opt_data, gradients)\n", "print(f\"Updated values: {updated_data.values}\")\n", "print(f\"Updated loss: {updated_data.loss()}\")"]}, {"cell_type": "markdown", "id": "8732e956", "metadata": {}, "source": ["## Real-World Example: Sampler State\n", "\n", "Let's look at how `Pytree` is used in NetKet's sampler module to manage the state of Monte Carlo samplers. This example shows the practical application of PyTrees in a complex quantum simulation context."]}, {"cell_type": "code", "execution_count": null, "id": "66ad9664", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:26.947429Z", "iopub.status.busy": "2025-07-14T14:04:26.947263Z", "iopub.status.idle": "2025-07-14T14:04:27.297839Z", "shell.execute_reply": "2025-07-14T14:04:27.297540Z"}, "lines_to_next_cell": 1}, "outputs": [], "source": "class SimpleSamplerState(struct.Pytree):\n    \"\"\"Simplified version of NetKet's sampler state.\"\"\"\n    \n    # Current configurations (dynamic - part of computation)\n    configurations: jax.Array\n    \n    # Log probabilities (dynamic - computed values)\n    log_probs: jax.Array\n    \n    # Random number generator state (dynamic - changes during sampling)\n    rng_state: jax.Array\n    \n    # Number of accepted moves (dynamic - statistics)\n    n_accepted: jax.Array\n    \n    # Number of steps taken (dynamic - statistics)\n    n_steps: jax.Array\n    \n    # Sampler configuration (static - doesn't change during sampling)\n    n_chains: int = struct.static_field()\n    sweep_size: int = struct.static_field()\n    \n    def __init__(self, configurations, log_probs, rng_state, n_chains, sweep_size):\n        self.configurations = configurations\n        self.log_probs = log_probs\n        self.rng_state = rng_state\n        self.n_accepted = jnp.zeros(n_chains, dtype=int)\n        self.n_steps = jnp.zeros((), dtype=int)\n        self.n_chains = n_chains\n        self.sweep_size = sweep_size\n    \n    @property\n    def acceptance_rate(self):\n        \"\"\"Compute the acceptance rate.\"\"\"\n        return jnp.mean(self.n_accepted) / (self.n_steps + 1e-10)\n\n# Create a sampler state\nkey = jax.random.PRNGKey(42)\nsampler_state = SimpleSamplerState(\n    configurations=jax.random.normal(key, (4, 10)),  # 4 chains, 10 sites\n    log_probs=jnp.array([-1.0, -2.0, -1.5, -1.8]),\n    rng_state=jax.random.PRNGKey(123),\n    n_chains=4,\n    sweep_size=10\n)\n\nprint(f\"Configurations shape: {sampler_state.configurations.shape}\")\nprint(f\"Initial acceptance rate: {sampler_state.acceptance_rate}\")"}, {"cell_type": "code", "execution_count": 17, "id": "4ae9affb", "metadata": {"execution": {"iopub.execute_input": "2025-07-14T14:04:27.299313Z", "iopub.status.busy": "2025-07-14T14:04:27.299208Z", "iopub.status.idle": "2025-07-14T14:04:27.556287Z", "shell.execute_reply": "2025-07-14T14:04:27.555997Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 1: Acceptance rate = 0.250\n", "Step 2: Acceptance rate = 0.125\n", "Step 3: Acceptance rate = 0.333\n"]}], "source": ["# Simulate a sampling step\n", "@jax.jit\n", "def sampling_step(state):\n", "    \"\"\"Simulate a single sampling step.\"\"\"\n", "    # Split the RNG key\n", "    new_key, subkey = jax.random.split(state.rng_state)\n", "    \n", "    # Simulate some accepted moves\n", "    new_accepted = state.n_accepted + jax.random.bernoulli(subkey, 0.3, shape=(state.n_chains,)).astype(int)\n", "    new_steps = state.n_steps + 1\n", "    \n", "    # Update some configurations\n", "    new_configs = state.configurations + jax.random.normal(subkey, state.configurations.shape) * 0.1\n", "    \n", "    return state.replace(\n", "        configurations=new_configs,\n", "        n_accepted=new_accepted,\n", "        n_steps=new_steps,\n", "        rng_state=new_key\n", "    )\n", "\n", "# Run several sampling steps\n", "current_state = sampler_state\n", "for i in range(3):\n", "    current_state = sampling_step(current_state)\n", "    print(f\"Step {i+1}: Acceptance rate = {current_state.acceptance_rate:.3f}\")"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}