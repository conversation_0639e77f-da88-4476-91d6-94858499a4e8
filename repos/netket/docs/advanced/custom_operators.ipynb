{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "44701f85-3e69-4e1c-b017-54a843297568", "metadata": {}, "source": ["# Defining Custom Observables and Operators\n", "\n", "In this page we will show how to define custom operators and the relevant methods used to compute expectation values and their gradients.\n", "This page assumes you have already read the [Hilbert module](Hilbert) and [Operator module](Operator) documentation and have a decent understanding of their class hierarchy."]}, {"cell_type": "code", "execution_count": null, "id": "d08155ce-49fc-41af-9762-75b95304b615", "metadata": {"tags": ["hide-cell"]}, "outputs": [], "source": ["%pip install netket --quiet"]}, {"cell_type": "markdown", "id": "c4393379", "metadata": {}, "source": ["If you want to define new quantities to be computed over variational states, such as the infidelity, a Renyi entropy or very peculiar observables, you can always write a function yourself that uses the attributes of variational states such as `vstate._apply_fun`, which is the function used to evaluate the log-wavefunction, `vstate.samples`, which returns a set of (possibly cached) samples and `vstate.variables` which returns the variational parameters.\n", "\n", "For example, when working with a standard {class}`~netket.vqs.MCState` you might try the following:"]}, {"cell_type": "code", "execution_count": 2, "id": "8573c030", "metadata": {}, "outputs": [], "source": ["import jax.numpy as jnp\n", "import netket as nk\n", "\n", "\n", "def expect_avg_X(vstate):\n", "    \"\"\"Compute average magnetization along X axis.\"\"\"\n", "    # this only works with spins\n", "    assert isinstance(vstate.hilbert, nk.hilbert.Spin)\n", "\n", "    samples = vstate.samples\n", "\n", "    samples_flipped = -samples\n", "\n", "    # compute the local observable\n", "    Oloc = jnp.exp(vstate.log_val(samples_flipped) - vstate.log_val(samples))\n", "\n", "    # compute the expectation value\n", "    return nk.stats.statistics(Oloc)"]}, {"cell_type": "markdown", "id": "f7ece8ac", "metadata": {}, "source": ["However this approach is not ideal. This will not work with existing drivers such as {class}`nk.driver.VMC`, and it will not be consistent with the existing interface. Moreover, if you also want it to work with the Full-Summation variational state {class}`nk.vqs.FullSumState` you will have to implement another function and use it accordingly.\n", "\n", "For this reason, NetKet exposes an extensible interface to support arbitrary Observables and Operators within its {meth}`~nk.vqs.VariationalState.expect` and {meth}`~nk.vqs.VariationalState.expect_and_grad` methods.\n", "\n", "The most general interface is to define a custom {class}`netket.experimental.observable.AbstractObservable` and its expectation and gradient dispatch rules. This gives you total control and is least-likely to cause bugs.\n", "However, if your observable is an operator over a discrete hilbert space and can easily be converted to a density matrix and its expectation value can be written easily, you can also use more advanced base classes such as {class}`netket.operator.DiscreteOperator`."]}, {"attachments": {}, "cell_type": "markdown", "id": "535a7cca-cff2-425a-8b69-4ce44529bf7d", "metadata": {}, "source": ["## Defining a custom _zero_ operator\n", "\n", "Let's assume you want to define an operator that always returns 0.\n", "That's a very useful operator!\n"]}, {"cell_type": "code", "execution_count": 10, "id": "78d3ba54-c703-4ae9-ba6d-5c80791619e9", "metadata": {}, "outputs": [], "source": ["from netket.experimental.observable import AbstractObservable\n", "\n", "\n", "class ZeroOperator(AbstractObservable):\n", "    @property\n", "    def dtype(self):\n", "        return float"]}, {"attachments": {}, "cell_type": "markdown", "id": "ed12709e-2705-4d3e-b4b9-29d583de6fdc", "metadata": {}, "source": ["To define an operator we always need to define the `dtype` property, representing the dtype of the output.\n", "\n", "Moreover, since we did not define the `__init__()` method, we inherit the `AbstractObservable` init method which\n", "requires an hilbert space to be specified.\n", "First we define the hilbert space, then we construct the operator itself."]}, {"cell_type": "code", "execution_count": 11, "id": "6984147f-20c6-4937-8c90-eed9d6fd1d75", "metadata": {}, "outputs": [{"data": {"text/plain": ["Spin(s=1/2, N=4)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["hi = nk.hilbert.Spin(0.5, 4)\n", "hi"]}, {"cell_type": "code", "execution_count": 12, "id": "fdc64db1-99f5-46c7-925d-6e6cfcacec78", "metadata": {}, "outputs": [{"data": {"text/plain": ["ZeroOperator(hilbert=Spin(s=1/2, N=4))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["zero_op = ZeroOperator(hi)\n", "zero_op"]}, {"attachments": {}, "cell_type": "markdown", "id": "d7e2c8dd-0a40-44c2-b085-8ccaff92c35c", "metadata": {}, "source": ["If we define a variational state on the same hilbert space, and we try to compute the expectation value on that space, an error will be thrown:"]}, {"cell_type": "code", "execution_count": 13, "id": "3f3051d8-5e9f-49f2-b4fa-6f9e76fb3b99", "metadata": {}, "outputs": [], "source": ["vs = nk.vqs.MCState(nk.sampler.MetropolisLocal(hi), nk.models.RBM())\n", "\n", "# vs.expect(zero_op)"]}, {"attachments": {}, "cell_type": "markdown", "id": "4a9340e2-e3d8-466b-815a-6f73ad9e6d98", "metadata": {}, "source": ["The error should look roughly like the following:\n", "```python\n", ">>> vs.expect(zero_op)\n", "\n", "NotFoundLookupError: get_local_kernel_arguments(MCState(\n", "  hilbert = Spin(s=1/2, N=4),\n", "  sampler = MetropolisSampler(rule = LocalRule(), n_chains = 16, sweep_size = 4, reset_chains = False, machine_power = 2, dtype = <class 'float'>),\n", "  n_samples = 1008,\n", "  n_discard_per_chain = 100,\n", "  sampler_state = MetropolisSamplerState(rng state=[3154305721 3544831998]),\n", "  n_parameters = 24), ZeroOperator(hilbert=Spin(s=1/2, N=4), dtype=<class 'float'>)) could not be resolved.\n", "\n", "Closest candidates are:\n", "get_local_kernel_arguments(vstate: netket.vqs.MCState, Ô: netket.operator._lazy.Squared)\n", "    <function get_local_kernel_arguments at 0x2830c3a60> @ ~/Dropbox/Ricerca/Codes/Python/netket/netket/vqs/mc/mc_state/expect.py:43\n", "get_local_kernel_arguments(vstate: netket.vqs.MCState, Ô: netket.operator.DiscreteOperator)\n", "    <function get_local_kernel_arguments at 0x28310fb00> @ ~/Dropbox/Ricerca/Codes/Python/netket/netket/vqs/mc/mc_state/expect.py:57\n", "get_local_kernel_arguments(vstate: netket.vqs.MCState, Ô: netket.operator.DiscreteJaxOperator)\n", "    <function get_local_kernel_arguments at 0x28310fc40> @ ~/Dropbox/Ricerca/Codes/Python/netket/netket/vqs/mc/mc_state/expect.py:71\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "eee362e4-92b8-487c-8603-4bd5f38fadbb", "metadata": {}, "source": ["This is because you defined a new operator, but you did not define how to compute an expectation value with it.\n", "The method to use to compute an expectation value is chosen from a list using multiple-dispatch, and it is defined both by the type of the variational state and by the type of the operator.\n", "\n", "To define the expect method you should do the following:\n"]}, {"cell_type": "code", "execution_count": 14, "id": "9144f214-6ed9-4aa4-b238-58b6c57500d4", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "\n", "@nk.vqs.expect.dispatch\n", "def expect_zero_operator(vstate: nk.vqs.MCState, op: ZeroOperator, chunk_size: None):\n", "    return np.array(0, dtype=op.dtype)"]}, {"attachments": {}, "cell_type": "markdown", "id": "3542d1c9-43b5-4761-b19d-6126f5db3035", "metadata": {}, "source": ["Dispatch is based on [plum-dispatch's multiple-dispatch](https://beartype.github.io/plum/intro.html) logic which is inspired by <PERSON>'s function definition.\n", "The underlying idea is that we often want to define functions that work for specific combination of types, such in this case.\n", "\n", "The three arguments to expect must closely match the convention used by NetKet:\n", " - The first argument should be the variational state, and the type annotation `: nk.vqs.MCState` must specify the class of the variational state for which your algorithm works. In this case, we are working with `MCState` so that is the one we will use;\n", " - The second argument must be the operator or observable to be computed, and the type annotation must specify what class your implementation will work with. In this case we use `: ZeroOperator` because we are implementing a function that computes the expectation value of a `ZeroOperator` over a `MCState`;\n", " - The third argument must be the specified `chunk_size` by which we compute the values. This is either a `None`, if no chunking is supported, an integer, if only chunking is supported or `Optional[int]` for optional chunking. In this case we specify `None` because we don't support chunking."]}, {"cell_type": "code", "execution_count": 15, "id": "40498cb5-4ffa-46c9-bf7a-9fdb31abd23e", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(0.)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["vs.expect(zero_op)"]}, {"cell_type": "markdown", "id": "82e7748e", "metadata": {}, "source": ["Now that you defined the dispatch rule for computing the expectation value of this observable, you might also want to define the dispatch rule for the gradient, which is normally accessed by calling `variational_state.expect_and_grad(operator)`.\n", "To define it, you can follow the same procedure as above:"]}, {"cell_type": "code", "execution_count": 22, "id": "e7b920f1", "metadata": {}, "outputs": [], "source": ["import jax\n", "\n", "\n", "@nk.vqs.expect_and_grad.dispatch\n", "def expect_and_grad_zero_operator(\n", "    vstate: nk.vqs.MCState, op: ZeroOperator, chunk_size: None, **kwargs\n", "):\n", "    # this is the expectation value, as before\n", "    expval = np.array(0, dtype=op.dtype)\n", "    # this is the gradient, which of course is zero.\n", "    grad = jax.tree.map(jnp.zeros_like, vstate.parameters)\n", "    return expval, grad"]}, {"cell_type": "markdown", "id": "22069b29", "metadata": {}, "source": ["The syntax to define the `expect_and_grad` rule is the same as before, but you should also accept arbitrary keyword arguments. Those may contain additional options specified by the variational state (such as mutable arguments) that you can use or ignore in your calculation."]}, {"cell_type": "code", "execution_count": 24, "id": "01c7a42c", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"text/plain": ["(array(0.),\n", " {'Dense': {'bias': Array([0., 0., 0., 0.], dtype=float64),\n", "   'kernel': Array([[0., 0., 0., 0.],\n", "          [0., 0., 0., 0.],\n", "          [0., 0., 0., 0.],\n", "          [0., 0., 0., 0.]], dtype=float64)},\n", "  'visible_bias': Array([0., 0., 0., 0.], dtype=float64)})"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["vs.expect_and_grad(zero_op)"]}, {"attachments": {}, "cell_type": "markdown", "id": "fbb3c156-4037-4364-9d80-40f38cc1d47c", "metadata": {"lines_to_next_cell": 2}, "source": ["## Defining an operator from scratch\n", "\n", "Let's try to reimplement an operator from scratch.\n", "I will take the $\\hat{X} = \\sum_i^N \\hat{\\sigma}^{(X)}_i $ operator as a simple example.\n", "\n", "In Variational Monte Carlo, we usually compute expectation values through the following expectation value:\n", "\n", "$$ \\langle \\hat{X} \\rangle = \\langle \\psi | \\hat{X} | \\psi \\rangle = \\sum_\\sigma |\\psi(\\sigma)|^2 \\sum_{\\eta} \\frac{\\langle\\sigma|\\hat{X}|\\eta\\rangle\\langle\\eta|\\psi\\rangle}{\\langle \\sigma | \\psi\\rangle} = \\mathbb{E}_{\\sigma \\approx |\\psi(\\sigma)|^2}\\left[ E^{loc}(\\sigma)\\right]\n", "$$\n", "\n", "where $ E^{loc}(\\sigma) =  \\sum_{\\eta} \\frac{\\langle\\sigma|\\hat{X}|\\eta\\rangle\\langle\\eta|\\psi\\rangle}{\\langle \\sigma | \\psi\\rangle} $ is called the local estimator.\n", "\n", "First we define the operator itself:\n"]}, {"cell_type": "code", "execution_count": 12, "id": "431d22d4-d650-430c-bd61-7556a95a9661", "metadata": {}, "outputs": [], "source": ["class XOperator(AbstractObservable):\n", "    @property\n", "    def dtype(self):\n", "        return float"]}, {"attachments": {}, "cell_type": "markdown", "id": "24bd0ff6-cadd-46f6-8dfd-237cb92bb219", "metadata": {}, "source": ["To then compute expectation values we need the following methods:\n", "\n", " - A method to sample the probability distribution $|\\psi(\\sigma)|^2$. This is already provided by monte carlo variational state interface and samples can be retrieved simply by calling {attr}`~netket.vqs.MCState.samples`.\n", " - A method to take the samples $ \\sigma $ and compute the connected elements $ \\eta $ so that $ \\langle\\sigma|\\hat{X}|\\eta\\rangle \\neq 0 $. This should also return those matrix elements.\n", " - A method to compute the local energy given the matrix elements, the $\\sigma$ and $ \\eta $ and the variational state.\n", " - The statistical average of the local energies.\n", "\n", "First we implement a method returning all the connected elements.\n", "Given a bitstring $ \\sigma $ for $N$ spins, the connected elements are $N$ bitstrings of the same length, where each one has a flipped bit.\n", "The matrix element is always 1\n"]}, {"cell_type": "code", "execution_count": 13, "id": "fbe4e976-2b68-47cd-abc3-f6eabf100127", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["from functools import partial  # partial(sum, axis=1)(x) == sum(x, axis=1)\n", "\n", "\n", "@jax.vmap\n", "def get_conns_and_mels(sigma):\n", "    # this code only works if sigma is a single bitstring\n", "    assert sigma.ndim == 1\n", "\n", "    # get number of spins\n", "    N = sigma.shape[-1]\n", "    # repeat eta N times\n", "    eta = jnp.tile(sigma, (N, 1))\n", "    # diagonal indices\n", "    ids = np.diag_indices(N)\n", "    # flip those indices\n", "    eta = eta.at[ids].set(-eta.at[ids].get())\n", "    return eta, jnp.ones(N)\n", "\n", "\n", "@partial(jax.vmap, in_axes=(None, None, 0, 0, 0))\n", "def e_loc(logpsi, pars, sigma, eta, mels):\n", "    return jnp.sum(mels * jnp.exp(logpsi(pars, eta) - logpsi(pars, sigma)), axis=-1)"]}, {"attachments": {}, "cell_type": "markdown", "id": "00da8f8d-afe6-4e4f-b450-8060a2903d22", "metadata": {"lines_to_next_cell": 2}, "source": ["The first function takes a single bitstring $ \\sigma $, a vector with $N$ entries, and returns a batch of bitstrings $ eta_i $, a matrix $ N \\times N $ where every element in the diagonal is flipped.\n", "We then used `jax.vmap` to make this function work with batches of inputs $\\sigma$ (we could have written it from the beginning to work on batches, but this way the meaning is very clear).\n", "\n", "The other function also takes a single $ \\sigma $ and a batch of $ \\eta $ and their matrix elements, and uses the formula above to compute the local energy.\n", "This function is also `jax.vmap`ped in order to work with batches of inputs. The argument `in_axes=(None, None, 0,0,0)` means that the first 2 arguments do not change among batches, while the other three are batched along the first dimension.\n", "\n", "With those two functions written, we can write the expect method\n"]}, {"cell_type": "code", "execution_count": 17, "id": "1f87909b-8438-4143-8673-11f5d7031d64", "metadata": {}, "outputs": [], "source": ["@nk.vqs.expect.dispatch\n", "def expect(vstate: nk.vqs.MCState, op: XOperator, chunk_size: None):\n", "    return _expect(vstate._apply_fun, vstate.variables, vstate.samples)\n", "\n", "\n", "@partial(jax.jit, static_argnums=0)\n", "def _expect(logpsi, variables, sigma):\n", "    n_chains = sigma.shape[-2]\n", "    N = sigma.shape[-1]\n", "    # flatten all batches\n", "    sigma = sigma.reshape(-1, N)\n", "\n", "    eta, mels = get_conns_and_mels(sigma)\n", "\n", "    E_loc = e_loc(logpsi, variables, sigma, eta, mels)\n", "\n", "    # reshape back into chains to compute statistical information\n", "    E_loc = E_loc.reshape(-1, n_chains)\n", "\n", "    # this function computes things like variance and convergence information.\n", "    return nk.stats.statistics(E_loc)"]}, {"attachments": {}, "cell_type": "markdown", "id": "d46d9d90-b893-4fc8-88c9-108738a39dfb", "metadata": {}, "source": ["The dispatch rule is a thin layer that calls a jitted function, in order to have more speed.\n", "We cannot directly jit `expect` itself because it takes as input a `vstate`, which is not directly\n", "jit-compatible.\n", "\n", "\n", "The internal, jitted `_expect` takes as input the `vstate._apply_fun` function which is the one evaluating the neural quantum state, together with it's inputs (`vstate.variables`).\n", "Note that if you want to make the `expect_and_grad` method work with this custom operator, you will also have to define the dispatch rule for `expect_and_grad`.\n", "\n", "We can now test this operator:"]}, {"cell_type": "code", "execution_count": 18, "id": "19305824-73bf-4778-81ff-7b5379f5985d", "metadata": {}, "outputs": [{"data": {"text/plain": ["XOperator(hilbert=Spin(s=1/2, N=4))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["3.9983 ± 0.0021 [σ²=0.0048, R̂=1.0105, τ=1.2<3.1]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["x_op = XOperator(hi)\n", "display(x_op)\n", "\n", "vs.expect(x_op)"]}, {"attachments": {}, "cell_type": "markdown", "id": "049e40fc-d1db-4ddd-ae1a-89c2dc67c3a4", "metadata": {}, "source": ["We can compare it with the built-in LocalOperator implementation in NetKet whose indexing is written in Numba. Of course, as the operators are identical, the expectation values will match."]}, {"cell_type": "code", "execution_count": 21, "id": "c51ea005-2dd2-42c4-8922-d48eaf566e9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["LocalOperator(dim=4, acting_on=[(0,), (1,), (2,), (3,)], constant=0.0, dtype=float64)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["3.9983 ± 0.0021 [σ²=0.0048, R̂=1.0105, τ=1.2<3.1]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["x_localop = sum([nk.operator.spin.sigmax(hi, i) for i in range(hi.size)])\n", "display(x_localop)\n", "\n", "vs.expect(x_localop)"]}, {"attachments": {}, "cell_type": "markdown", "id": "3c5d8e32-3614-4923-93da-a12a47dd2b30", "metadata": {}, "source": ["It might be interesting to investigate the performance difference among the two operators:"]}, {"cell_type": "code", "execution_count": 22, "id": "6cffc92e-0e0e-4415-bd10-4ac321bcedd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["261 µs ± 10.2 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n", "955 µs ± 2.54 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%timeit vs.expect(x_op)\n", "%timeit vs.expect(x_localop)"]}, {"attachments": {}, "cell_type": "markdown", "id": "1ab2567f-bc77-4ecf-8cc9-b85477f74475", "metadata": {}, "source": ["And you can see that our jax-based approach is more efficient than NetKet's built-in operators.\n", "This is for two reasons: first, NetKet's LocalOperator is a general object that can handle completely arbitrary operators, and this flexibility comes at the price of a slightly lower performance.\n", "Usually this is not an issue because if your model is complication enough, the cost of indexing the operator will be negligible with respect to the cost of evaluating the model itself.\n", "\n", "However, writing the operator in Jax has the added benefit that XLA (the Jax compiler) can introspect the code of the operator, and might uber-optimise the evaluation of the expectation value."]}, {"attachments": {}, "cell_type": "markdown", "id": "1264b0d6-c608-42cf-9486-d6ca6cde3e47", "metadata": {}, "source": ["## Defining an operator the easy way\n", "\n", "Most operator that can be used efficiently with VMC approaches have the same structure as above, therefore, when defining new operators you will often find yourself redefining a similar function, where the only thing changing is the code to compute the connected elements, matrix elements, and the local kernel (in the case above computing E_loc).\n", "If you also want to define the gradient, the boilerplate becomes considerable.\n", "\n", "As such, to reduce the amount of boilerplate code that users must write when defining custom operators, NetKet will attempt by default to use a default expectation kernel.\n", "This kernel looks very similar to the `_expect` function above, and will call a function similar to `get_conns_and_mels` and a local kernel, selected using multiple dispatch.\n", "\n", "In the example below we show how to make use of this _lean_ interface.\n"]}, {"cell_type": "code", "execution_count": 24, "id": "68c0e076-02e6-40df-8fc0-d5487a0c6ff7", "metadata": {}, "outputs": [], "source": ["from netket.operator import AbstractOperator\n", "\n", "\n", "class XOperatorLean(AbstractOperator):\n", "    @property\n", "    def dtype(self):\n", "        return float\n", "\n", "    @property\n", "    def is_hermitian(self):\n", "        return True\n", "\n", "\n", "def e_loc(logpsi, pars, sigma, extra_args):\n", "    eta, mels = extra_args\n", "    # check that sigma has been reshaped to 2D, eta is 3D\n", "    # sigma is (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n", "    assert sigma.ndim == 2\n", "    # eta is (Nsamples, Nconnected, Nsites)\n", "    assert eta.ndim == 3\n", "\n", "    # let's write the local energy assuming a single sample, and vmap it\n", "    @partial(jax.vmap, in_axes=(0, 0, 0))\n", "    def _loc_vals(sigma, eta, mels):\n", "        return jnp.sum(mels * jnp.exp(logpsi(pars, eta) - logpsi(pars, sigma)), axis=-1)\n", "\n", "    return _loc_vals(sigma, eta, mels)\n", "\n", "\n", "@nk.vqs.get_local_kernel.dispatch\n", "def get_local_kernel(vstate: nk.vqs.MCState, op: XOperatorLean):\n", "    return e_loc\n", "\n", "\n", "@nk.vqs.get_local_kernel_arguments.dispatch\n", "def get_local_kernel_arguments(vstate: nk.vqs.MCState, op: XOperatorLean):\n", "    sigma = vstate.samples\n", "    # get the connected elements. Reshape the samples because that code only works\n", "    # if the input is a 2D matrix\n", "    extra_args = get_conns_and_mels(sigma.reshape(-1, vstate.hilbert.size))\n", "    return sigma, extra_args"]}, {"attachments": {}, "cell_type": "markdown", "id": "01b03835-c1d6-4708-b4f4-f92c8417936c", "metadata": {}, "source": ["The first function here is very similar to the `e_loc` we defined in the previous section, however instead of taking `eta` and `mels` as two different arguments, they are passed as a tuple named `extra_args`, and must be unpacked by the kernel.\n", "That's because every operator has it's own `get_local_kernel_arguments` which prepares those extra_args, and every different operator might be passing different objects to the kernel.\n", "\n", "Also do note that the kernel is jit-compiled, therefore it must make use of `jax.numpy` functions, while the `get_local_kernel_argument` function *is not jitted*, and it is executed before jitting.\n", "This is in order to allow extra flexibility: sometimes your operators need some pre-processing code that is not jax-jittable, but is only numba-jittable, for example (this is the case of most operators in NetKet).\n", "\n", "All operators and super-operators in netket define those two methods.\n", "If you want to see some examples of how it is used internally, look at the source code found in [this folder](https://github.com/netket/netket/blob/master/netket/vqs/mc/MCState/expect.py).\n", "An additional benefit of using this latter definition, is that is automatically enables `expect_and_grad` for your custom operator.\n", "\n", "We can now test this new implementation:"]}, {"cell_type": "code", "execution_count": 25, "id": "58117d88-2c22-4a72-9f9a-b1db386bee52", "metadata": {}, "outputs": [{"data": {"text/plain": ["3.9983 ± 0.0021 [σ²=0.0048, R̂=1.0105, τ=1.2<3.1]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["x_op_lean = XOperatorLean(hi)\n", "vs.expect(x_op_lean)"]}, {"attachments": {}, "cell_type": "markdown", "id": "8e6be079-97e8-4d0a-b785-5dd4706ab894", "metadata": {}, "source": ["## Comparison of the two approaches\n", "\n", "Above you've seen two different ways to define `expect`, but it might be unclear which one you should use in your code.\n", "In general, you should prefer the second one: the _simple_ interface is easier to use and enables by default gradients too.\n", "\n", "If you can express the expectation value of your operator in such a way that it can be estimated by simply averaging a single local-estimator the simple interface is best.\n", "We expect that you should be able to use this interface in the vast majority of cases.\n", "However in some cases you might want to try something particular, experiment, or your operator cannot be expressed in this form, or you want to try out some optimisations (such as chunking), or compute many operators at once.\n", "That's why we also allow you to override the general `expect` method alltogether: it allows a lot of extra flexibility."]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "python-3.11.2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}