(netket_vqs_api)=
# netket.vqs

```{eval-rst}
.. currentmodule:: netket.vqs

```

This module defines the variational states, the heart of NetKet itself.


```{eval-rst}
.. inheritance-diagram:: netket.vqs
   :top-classes: netket.vqs.VariationalState
   :parts: 1

```

## Abstract Interface

```{eval-rst}
.. autosummary::
  :toctree: _generated/vqs
  :nosignatures:

  VariationalState
  VariationalMixedState
```

## Concrete Variational States

```{eval-rst}
.. autosummary::
  :toctree: _generated/vqs
  :nosignatures:

  FullSumState
  MCState
  MCMixedState
```

## Functions

```{eval-rst}
.. autosummary::
  :toctree: _generated/vqs
  :nosignatures:

  get_local_kernel
  get_local_kernel_arguments
```

