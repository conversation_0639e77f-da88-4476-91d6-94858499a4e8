(netket_geometry_api)=
# netket.experimental.geometry

```{eval-rst}
.. currentmodule:: netket.experimental.geometry
```

The :mod:`netket.experimental.geometry` module contains helper classes to describe the geometry of continuous spaces.
These objects can be passed to {class}`~netket.experimental.hilbert.Particle` and provide utilities such as distance computations.

```{eval-rst}
.. autosummary::
   :toctree: _generated/geometry
   :template: class
   :nosignatures:

   Cell
   FreeSpace
```
