(netket_api)=

# Public API

```{eval-rst}
.. currentmodule:: netket

```

This page lists the documentation of all sub-packages of NetKet.

(netket-subpackages)=

## Subpackages

```{toctree}
:maxdepth: 1

callbacks
drivers
errors
exact
graph
geometry
hilbert
jax
logging
models
nn
operator
optimizer
sampler
stats
utils
vqs
```

## Default drivers

```{eval-rst}
.. currentmodule:: netket.driver

.. autosummary::
   :toctree: _generated/driver
   :nosignatures:

   VMC
   SteadyState

```