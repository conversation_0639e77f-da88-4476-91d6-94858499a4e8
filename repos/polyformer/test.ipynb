{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial Transformer Testing Suite\n", "\n", "This notebook tests various components of the Polynomial Transformer implementation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "import sys\n", "sys.path.append('/home/<USER>/Projects/polyformer')\n", "\n", "from former import (\n", "    TransformerBlock, \n", "    PolyTransformer<PERSON>lock,\n", "    GTrans<PERSON>,\n", "    PolyGTransformer,\n", "    CTransformer,\n", "    PolyCTransformer\n", ")\n", "\n", "from former.util import (\n", "    CP, CP_sparse_LU, CP_sparse_degree, CP_sparse_degree_LU, CP_sparse_LU_sawtooth\n", ")\n", "\n", "from former.util import *\n", "from former.util.poly_utils import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utility Functions for Testing"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def test_layer(layer, input_shape, verbose=True):\n", "    \"\"\"Test if a layer can process input and produce output without errors.\"\"\"\n", "    with torch.no_grad():  # No need for gradient tracking during testing\n", "        x = torch.randn(input_shape)\n", "        try:\n", "            out = layer(x)\n", "            if verbose:\n", "                print(f\"✅ Layer works! Input shape: {input_shape}, Output shape: {out.shape}\")\n", "            return out.shape\n", "        except Exception as e:\n", "            print(f\"❌ Layer failed with input shape {input_shape}. Error: {e}\")\n", "            return None\n", "\n", "def count_parameters(model):\n", "    \"\"\"Count the total number of trainable parameters in a model.\"\"\"\n", "    return sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "def parameter_comparison(model1, model2, names=['Model 1', 'Model 2']):\n", "    \"\"\"Compare parameter counts between two models.\"\"\"\n", "    params1 = non_zero_count(model1)\n", "    params2 = non_zero_count(model2)\n", "    \n", "    print(f\"{names[0]} parameters: {params1:,}\")\n", "    print(f\"{names[1]} parameters: {params2:,}\")\n", "    \n", "    if params1 > params2:\n", "        print(f\"{names[0]} has {params1-params2:,} more parameters ({params1/params2:.3f}x)\")\n", "    else:\n", "        print(f\"{names[1]} has {params2-params1:,} more parameters ({params2/params1:.3f}x)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Testing the PolyTransformerBlock"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing standard TransformerBlock:\n", "✅ Layer works! Input shape: (8, 32, 64), Output shape: torch.<PERSON><PERSON>([8, 32, 64])\n", "\n", "Testing PolyTransformerBlock:\n", "✅ Layer works! Input shape: (8, 32, 64), Output shape: torch.<PERSON><PERSON>([8, 32, 64])\n", "\n", "✅ Both blocks produce outputs of the same shape!\n", "\n", "Parameter comparison:\n", "Standard TransformerBlock parameters: 49,664\n", "PolyTransformerBlock parameters: 27,072\n", "Standard TransformerBlock has 22,592 more parameters (1.000x)\n"]}], "source": ["# Configuration parameters\n", "emb = 64          # Embedding dimension\n", "heads = 4         # Number of attention heads\n", "seq_length = 32   # Sequence length\n", "batch_size = 8    # Batch size\n", "\n", "# Create a standard transformer block\n", "transformer_block = TransformerBlock(\n", "    emb=emb,\n", "    heads=heads,\n", "    mask=True, \n", "    seq_length=seq_length\n", ")\n", "\n", "# Create a polynomial transformer block\n", "poly_block = PolyTransformerBlock(\n", "    emb=emb,\n", "    heads=heads,\n", "    mask=True, \n", "    seq_length=seq_length,\n", "    degree=2,\n", "    poly_class=CP_sparse_degree_LU,\n", "    use_relu=True,\n", "    ff_hidden_mult=2,\n", "    attention_type='sparse_inplace'\n", ")\n", "\n", "# Test both blocks with the same input\n", "input_shape = (batch_size, seq_length, emb)\n", "print(\"Testing standard TransformerBlock:\")\n", "\n", "with torch.no_grad():  # No need for gradient tracking during testing\n", "    x = torch.randn(input_shape)\n", "    out = poly_block(x)\n", "\n", "std_output_shape = test_layer(transformer_block, input_shape)\n", "\n", "print(\"\\nTesting PolyTransformerBlock:\")\n", "poly_output_shape = test_layer(poly_block, input_shape)\n", "\n", "# Verify that both blocks produce the same output shape\n", "if std_output_shape == poly_output_shape:\n", "    print(\"\\n✅ Both blocks produce outputs of the same shape!\")\n", "else:\n", "    print(f\"\\n❌ Output shapes differ! Standard: {std_output_shape}, Poly: {poly_output_shape}\")\n", "\n", "# Compare parameter counts\n", "print(\"\\nParameter comparison:\")\n", "parameter_comparison(transformer_block, poly_block, [\"Standard TransformerBlock\", \"PolyTransformerBlock\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Testing Different Polynomial Classes"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Testing with degree = 1:\n", "\n", "Testing CP:\n", "✅ CP with degree 1 works! Parameters: 49,408\n", "\n", "Testing CP_sparse_LU:\n", "✅ CP_sparse_LU with degree 1 works! Parameters: 47,392\n", "\n", "Testing CP_sparse_degree:\n", "✅ CP_sparse_degree with degree 1 works! Parameters: 49,408\n", "\n", "Testing CP_sparse_degree_LU:\n", "✅ CP_sparse_degree_LU with degree 1 works! Parameters: 47,392\n", "\n", "\n", "Testing with degree = 2:\n", "\n", "Testing CP:\n", "✅ CP with degree 2 works! Parameters: 41,216\n", "\n", "Testing CP_sparse_LU:\n", "✅ CP_sparse_LU with degree 2 works! Parameters: 33,088\n", "\n", "Testing CP_sparse_degree:\n", "✅ CP_sparse_degree with degree 2 works! Parameters: 37,120\n", "\n", "Testing CP_sparse_degree_LU:\n", "✅ CP_sparse_degree_LU with degree 2 works! Parameters: 32,032\n", "\n", "\n", "Testing with degree = 3:\n", "\n", "Testing CP:\n", "✅ CP with degree 3 works! Parameters: 38,400\n", "\n", "Testing CP_sparse_LU:\n", "✅ CP_sparse_LU with degree 3 works! Parameters: 31,008\n", "\n", "Testing CP_sparse_degree:\n", "✅ CP_sparse_degree with degree 3 works! Parameters: 32,896\n", "\n", "Testing CP_sparse_degree_LU:\n", "✅ CP_sparse_degree_LU with degree 3 works! Parameters: 28,010\n", "\n", "\n", "Summary of parameter counts:\n", "------------------------------------------------------------\n", "Degree     Class                Parameters     \n", "------------------------------------------------------------\n", "1          CP                   49,408\n", "1          CP_sparse_LU         47,392\n", "1          CP_sparse_degree     49,408\n", "1          CP_sparse_degree_LU  47,392\n", "2          CP                   41,216\n", "2          CP_sparse_LU         33,088\n", "2          CP_sparse_degree     37,120\n", "2          CP_sparse_degree_LU  32,032\n", "3          CP                   38,400\n", "3          CP_sparse_LU         31,008\n", "3          CP_sparse_degree     32,896\n", "3          CP_sparse_degree_LU  28,010\n"]}], "source": ["# Test various polynomial network implementations\n", "poly_classes = [CP, CP_sparse_LU, CP_sparse_degree, CP_sparse_degree_LU]\n", "poly_class_names = ['CP', 'CP_sparse_LU', 'CP_sparse_degree', 'CP_sparse_degree_LU']\n", "\n", "# Test with different degrees\n", "degrees = [1, 2, 3]\n", "\n", "results = {}\n", "\n", "for degree in degrees:\n", "    print(f\"\\n\\nTesting with degree = {degree}:\")\n", "    \n", "    for cls, name in zip(poly_classes, poly_class_names):\n", "        print(f\"\\nTesting {name}:\")\n", "        \n", "        try:\n", "            block = PolyTransformerBlock(\n", "                emb=emb,\n", "                heads=heads,\n", "                mask=True, \n", "                seq_length=seq_length,\n", "                degree=degree,\n", "                poly_class=cls\n", "            )\n", "            \n", "            _ = test_layer(block, input_shape, verbose=False)\n", "            param_count = non_zero_count(block)\n", "            results[(degree, name)] = param_count\n", "            \n", "            print(f\"✅ {name} with degree {degree} works! Parameters: {param_count:,}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ {name} with degree {degree} failed. Error: {e}\")\n", "            results[(degree, name)] = None\n", "\n", "# Print summary table\n", "print(\"\\n\\nSummary of parameter counts:\")\n", "print(\"-\" * 60)\n", "print(f\"{'Degree':<10} {'Class':<20} {'Parameters':<15}\")\n", "print(\"-\" * 60)\n", "\n", "for (degree, cls_name), params in sorted(results.items()):\n", "    if params is not None:\n", "        print(f\"{degree:<10} {cls_name:<20} {params:,}\")\n", "    else:\n", "        print(f\"{degree:<10} {cls_name:<20} {'Failed'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Testing Full Transformer Models"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. Testing generative models (GTransformer vs PolyGTransformer):\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GTransformer output shape: torch.Size([4, 64, 10000])\n", "PolyGTransformer output shape: torch.Size([4, 64, 10000])\n", "✅ Both generative models produce the same output shape!\n", "\n", "Parameter comparison:\n", "GTransformer parameters: 3,171,088\n", "PolyGTransformer parameters: 2,813,776\n", "GTransformer has 357,312 more parameters (1.000x)\n", "\n", "2. Testing classification models (CTransformer vs PolyCTransformer):\n", "CTransformer output shape: torch.Size([4, 5])\n", "PolyCTransformer output shape: torch.Size([4, 5])\n", "✅ Both classification models produce the same output shape!\n", "\n", "Parameter comparison:\n", "CTransformer parameters: 1,881,733\n", "PolyCTransformer parameters: 1,781,892\n", "CTransformer has 99,841 more parameters (1.000x)\n"]}], "source": ["# Configuration for full transformer models\n", "emb = 128  # Embedding dimension\n", "heads = 8  # Number of attention heads\n", "depth = 3  # Number of transformer blocks\n", "seq_length = 64  # Sequence length\n", "num_tokens = 10000  # Vocabulary size\n", "num_classes = 5  # For classification model\n", "\n", "print(\"1. Testing generative models (GTransformer vs PolyGTransformer):\")\n", "g_transformer = GTransformer(emb, heads, depth, seq_length, num_tokens)\n", "poly_g_transformer = PolyGTransformer(emb, heads, depth, seq_length, num_tokens, degree=2, ff_hidden_mult=2, poly_class=CP_sparse_degree_LU, attention_type='sparse_inplace')\n", "\n", "# Test with a batch of token indices\n", "token_indices = torch.randint(0, num_tokens, (4, seq_length))\n", "\n", "with torch.no_grad():\n", "    g_out = g_transformer(token_indices)\n", "    poly_g_out = poly_g_transformer(token_indices)\n", "    \n", "    print(f\"GTransformer output shape: {g_out.shape}\")\n", "    print(f\"PolyGTransformer output shape: {poly_g_out.shape}\")\n", "    \n", "    if g_out.shape == poly_g_out.shape:\n", "        print(\"✅ Both generative models produce the same output shape!\")\n", "    else:\n", "        print(\"❌ Generative models produce different output shapes!\")\n", "\n", "print(\"\\nParameter comparison:\")\n", "parameter_comparison(g_transformer, poly_g_transformer, [\"GTransformer\", \"PolyGTransformer\"])\n", "\n", "print(\"\\n2. Testing classification models (CTransformer vs PolyCTransformer):\")\n", "c_transformer = CTransformer(emb, heads, depth, seq_length, num_tokens, num_classes)\n", "poly_c_transformer = PolyCTransformer(emb, heads, depth, seq_length, num_tokens, num_classes, degree=2)\n", "\n", "with torch.no_grad():\n", "    c_out = c_transformer(token_indices)\n", "    poly_c_out = poly_c_transformer(token_indices)\n", "    \n", "    print(f\"CTransformer output shape: {c_out.shape}\")\n", "    print(f\"PolyCTransformer output shape: {poly_c_out.shape}\")\n", "    \n", "    if c_out.shape == poly_c_out.shape:\n", "        print(\"✅ Both classification models produce the same output shape!\")\n", "    else:\n", "        print(\"❌ Classification models produce different output shapes!\")\n", "\n", "print(\"\\nParameter comparison:\")\n", "parameter_comparison(c_transformer, poly_c_transformer, [\"CTransformer\", \"PolyCTransformer\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Testing Different Attention Types with Polynomial Networks"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing PolyTransformerBlock with attention_type='default'\n", "❌ Layer failed with input shape (8, 32, 64). Error: Input embedding dim (64) should match layer embedding dim (128)\n", "\n", "Testing PolyTransformerBlock with attention_type='sparse_inplace'\n", "❌ Layer failed with input shape (8, 32, 64). Error: Input embedding dim (64) should match layer embedding dim (128)\n", "\n", "Testing PolyTransformerBlock with attention_type='sparse_graph'\n", "❌ Layer failed with input shape (8, 32, 64). Error: Input embedding dim (64) should match layer embedding dim (128)\n"]}], "source": ["attention_types = [\n", "    'default', 'sparse_inplace', 'sparse_graph'\n", "]\n", "\n", "for att_type in attention_types:\n", "    print(f\"\\nTesting PolyTransformerBlock with attention_type='{att_type}'\")\n", "    \n", "    try:\n", "        # Some attention types might need special handling\n", "        kwargs = {}\n", "        if att_type == 'relative':\n", "            # Relative attention needs position embeddings\n", "            pos_emb = nn.Embedding(seq_length * 2 - 1, emb)\n", "            kwargs['pos_embedding'] = pos_emb\n", "            \n", "        block = PolyTransformerBlock(\n", "            emb=emb,\n", "            heads=heads,\n", "            mask=True, \n", "            seq_length=seq_length,\n", "            degree=2,\n", "            attention_type=att_type,\n", "            **kwargs\n", "        )\n", "        \n", "        out_shape = test_layer(block, input_shape)\n", "        if out_shape is not None:\n", "            print(f\"  ✅ PolyTransformerBlock with {att_type} attention works!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ PolyTransformerBlock with {att_type} attention failed. Error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test Training with Small Synthetic Task"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training GTransformer:\n", "Epoch 1, Loss: 4.7181\n", "Epoch 2, Loss: 4.5220\n", "Epoch 3, Loss: 4.3277\n", "Epoch 4, Loss: 4.1468\n", "Epoch 5, Loss: 3.9345\n", "Epoch 6, Loss: 3.7669\n", "Epoch 7, Loss: 3.5859\n", "Epoch 8, Loss: 3.3568\n", "Epoch 9, Loss: 3.1679\n", "Epoch 10, Loss: 2.9636\n", "\n", "Training PolyGTransformer:\n", "Epoch 1, Loss: 4.7088\n", "Epoch 2, Loss: 4.5384\n", "Epoch 3, Loss: 4.3395\n", "Epoch 4, Loss: 4.1608\n", "Epoch 5, Loss: 3.9750\n", "Epoch 6, Loss: 3.7946\n", "Epoch 7, Loss: 3.6263\n", "Epoch 8, Loss: 3.4579\n", "Epoch 9, Loss: 3.2985\n", "Epoch 10, Loss: 3.1181\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["tensor(32228)\n", "tensor(23300)\n"]}], "source": ["import torch.optim as optim\n", "\n", "def train_for_epochs(model, epochs=5, batch_size=32, seq_len=16, vocab_size=100):\n", "    \"\"\"Train a model for a few epochs on a simple synthetic sequence task.\"\"\"\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "    \n", "    # Simple synthetic task: predict next token in a sequence\n", "    losses = []\n", "    \n", "    for epoch in range(epochs):\n", "        running_loss = 0.0\n", "        \n", "        # Generate synthetic data - sequences where each token is the previous token + 1\n", "        for i in range(5):\n", "            # Generate random starting point for sequences\n", "            starts = torch.randint(0, vocab_size - seq_len, (batch_size, 1))\n", "            # Create sequences with a pattern (each position is previous + 1)\n", "            seqs = torch.cat([starts + j for j in range(seq_len)], dim=1)\n", "            \n", "            # Inputs are all but last token, targets are all but first token\n", "            inputs = seqs[:, :-1]\n", "            targets = seqs[:, 1:].contiguous().view(-1)\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Reshape for loss calculation\n", "            outputs = outputs.view(-1, vocab_size)\n", "            \n", "            # Calculate loss\n", "            loss = criterion(outputs, targets)\n", "            \n", "            # Backward and optimize\n", "            optimizer.zero_grad()\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            running_loss += loss.item()\n", "        \n", "        avg_loss = running_loss / 5\n", "        losses.append(avg_loss)\n", "        print(f'Epoch {epoch+1}, Loss: {avg_loss:.4f}')\n", "    \n", "    return losses\n", "\n", "# Setup simplified models for training test\n", "vocab_size = 100\n", "seq_len = 16\n", "emb = 32\n", "heads = 2\n", "depth = 2\n", "epochs = 10\n", "\n", "print(\"Training GTransformer:\")\n", "g_model = GTransformer(emb, heads, depth, seq_len, vocab_size)\n", "g_losses = train_for_epochs(g_model, epochs=epochs, vocab_size=vocab_size, seq_len=seq_len)\n", "\n", "print(\"\\nTraining PolyGTransformer:\")\n", "poly_g_model = PolyGTransformer(emb, heads, depth, seq_len, vocab_size, degree=2, ff_hidden_mult=4, poly_class=CP_sparse_degree_LU, use_relu=False)\n", "poly_g_losses = train_for_epochs(poly_g_model, epochs=epochs, vocab_size=vocab_size, seq_len=seq_len)\n", "\n", "# Plot loss comparison\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(g_losses, label='GTransformer')\n", "plt.plot(poly_g_losses, label='PolyGTransformer')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.title('Training Loss Comparison')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()\n", "\n", "print(non_zero_count(g_model))\n", "print(non_zero_count(poly_g_model))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Test Inference Speed Comparison"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Measuring inference time for GTransformer...\n", "Measuring inference time for PolyGTransformer (degree=1)...\n", "Measuring inference time for PolyGTransformer (degree=2)...\n", "Measuring inference time for PolyGTransformer (degree=3)...\n", "Measuring inference time for PolyGTransformer (CP_sparse_LU)...\n", "\n", "Inference Speed Comparison:\n", "----------------------------------------------------------------------\n", "Model                          Time (ms)  Relative Speed \n", "----------------------------------------------------------------------\n", "GTransformer                      443.87           1.00x\n", "PolyGTransformer (degree=1)       375.05           1.18x\n", "PolyGTransformer (degree=2)       459.13           0.97x\n", "PolyGTransformer (degree=3)       484.57           0.92x\n", "PolyGTransformer (CP_sparse_LU)    392.93           1.13x\n"]}], "source": ["import time\n", "\n", "def measure_inference_time(model, input_tensor, runs=10):\n", "    \"\"\"Measure average inference time over multiple runs.\"\"\"\n", "    # Warm-up run\n", "    with torch.no_grad():\n", "        _ = model(input_tensor)\n", "    \n", "    # Timed runs\n", "    start_time = time.time()\n", "    \n", "    with torch.no_grad():\n", "        for _ in range(runs):\n", "            _ = model(input_tensor)\n", "            \n", "    end_time = time.time()\n", "    avg_time = (end_time - start_time) / runs\n", "    \n", "    return avg_time\n", "\n", "# Setup models for speed comparison\n", "emb = 256\n", "heads = 8\n", "depth = 4\n", "seq_length = 64\n", "vocab_size = 10000\n", "batch_size = 16\n", "\n", "# Create input tensor for inference\n", "input_tensor = torch.randint(0, vocab_size, (batch_size, seq_length))\n", "\n", "# Create models for comparison\n", "models = {\n", "    \"GTransformer\": GTransformer(emb, heads, depth, seq_length, vocab_size),\n", "    \"PolyGTransformer (degree=1)\": PolyGTransformer(emb, heads, depth, seq_length, vocab_size, degree=1, ff_hidden_mult=2),\n", "    \"PolyGTransformer (degree=2)\": PolyGTransformer(emb, heads, depth, seq_length, vocab_size, degree=2, ff_hidden_mult=2, poly_class=CP_sparse_degree_LU),\n", "    \"PolyGTransformer (degree=3)\": PolyGTransformer(emb, heads, depth, seq_length, vocab_size, degree=3, ff_hidden_mult=2),\n", "    \"PolyGTransformer (CP_sparse_LU)\": PolyGTransformer(emb, heads, depth, seq_length, vocab_size, degree=2, poly_class=CP_sparse_LU, ff_hidden_mult=2)\n", "}\n", "\n", "# Run timing tests\n", "times = {}\n", "for name, model in models.items():\n", "    print(f\"Measuring inference time for {name}...\")\n", "    times[name] = measure_inference_time(model, input_tensor)\n", "\n", "# Print results\n", "print(\"\\nInference Speed Comparison:\")\n", "print(\"-\" * 70)\n", "print(f\"{'Model':<30} {'Time (ms)':<10} {'Relative Speed':<15}\")\n", "print(\"-\" * 70)\n", "\n", "# Use standard transformer as baseline\n", "baseline = times[\"GTransformer\"]\n", "for name, time_taken in times.items():\n", "    rel_speed = baseline / time_taken\n", "    print(f\"{name:<30} {time_taken*1000:>9.2f} {rel_speed:>14.2f}x\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "def visualize_masks(model_class, title, degree=2, dimensions=None):\n", "    \"\"\"Visualize masks from different models with various dimensions\"\"\"\n", "    fig, axes = plt.subplots(len(dimensions), 2, figsize=(12, 4 * len(dimensions)))\n", "    fig.suptitle(f\"{title} Mask Visualization\", fontsize=16)\n", "    \n", "    for i, (d, k) in enumerate(dimensions):\n", "        model = model_class(degree=degree, d=d, k=k, o=10)  # output dimension doesn't matter for masks\n", "        \n", "        # Plot mask1\n", "        ax1 = axes[i, 0]\n", "        im1 = ax1.imshow(model.mask1.cpu().numpy(), cmap='Blues', interpolation='none')\n", "        ax1.set_title(f\"Mask 1 (d={d}, k={k})\")\n", "        ax1.set_xlabel(\"Input dimension\")\n", "        ax1.set_ylabel(\"Rank\")\n", "        fig.colorbar(im1, ax=ax1)\n", "        \n", "        # Plot mask2\n", "        ax2 = axes[i, 1]\n", "        im2 = ax2.imshow(model.mask2.cpu().numpy(), cmap='Reds', interpolation='none')\n", "        ax2.set_title(f\"Mask 2 (d={d}, k={k})\")\n", "        ax2.set_xlabel(\"Input dimension\")\n", "        ax2.set_ylabel(\"Rank\")\n", "        fig.colorbar(im2, ax=ax2)\n", "    \n", "    plt.tight_layout(rect=[0, 0, 1, 0.96])\n", "    plt.show()\n", "\n", "# Define dimensions to test - include square and elongated cases\n", "dimensions = [\n", "    (32, 32),  # Square matrix (should be same as original)\n", "    (64, 16),  # Elongated: d > k\n", "    (16, 64),  # Elongated: k > d\n", "    (100, 20)  # Very elongated: d >> k\n", "]\n", "\n", "# Compare original LU and sawtooth LU\n", "print(\"Original CP_sparse_LU masks:\")\n", "visualize_masks(CP_sparse_LU, \"Original CP_sparse_LU\", dimensions=dimensions)\n", "\n", "print(\"\\nNew CP_sparse_LU_sawtooth masks:\")\n", "visualize_masks(CP_sparse_LU_sawtooth, \"CP_sparse_LU_sawtooth\", dimensions=dimensions)\n", "\n", "# Add numerical comparison\n", "print(\"\\nAnalyzing mask patterns:\")\n", "for d, k in dimensions:\n", "    original = CP_sparse_LU(degree=2, d=d, k=k, o=10)\n", "    sawtooth = CP_sparse_LU_sawtooth(degree=2, d=d, k=k, o=10)\n", "    \n", "    # Count non-zeros in each mask\n", "    orig_nonzeros_1 = original.mask1.sum().item()\n", "    orig_nonzeros_2 = original.mask2.sum().item()\n", "    saw_nonzeros_1 = sawtooth.mask1.sum().item()\n", "    saw_nonzeros_2 = sawtooth.mask2.sum().item()\n", "    \n", "    # Percentage of non-zeros\n", "    orig_perc_1 = 100 * orig_nonzeros_1 / (d * k)\n", "    orig_perc_2 = 100 * orig_nonzeros_2 / (d * k)\n", "    saw_perc_1 = 100 * saw_nonzeros_1 / (d * k)\n", "    saw_perc_2 = 100 * saw_nonzeros_2 / (d * k)\n", "    \n", "    print(f\"\\nDimensions: d={d}, k={k} (total elements = {d*k})\")\n", "    print(f\"Original - Mask1: {orig_nonzeros_1} non-zeros ({orig_perc_1:.1f}%)\")\n", "    print(f\"Original - Mask2: {orig_nonzeros_2} non-zeros ({orig_perc_2:.1f}%)\")\n", "    print(f\"Sawtooth - Mask1: {saw_nonzeros_1} non-zeros ({saw_perc_1:.1f}%)\")\n", "    print(f\"Sawtooth - Mask2: {saw_nonzeros_2} non-zeros ({saw_perc_2:.1f}%)\")\n", "    \n", "    # Check complementarity \n", "    complement_original = (original.mask1 + original.mask2).sum().item() / (d * k)\n", "    complement_sawtooth = (sawtooth.mask1 + sawtooth.mask2).sum().item() / (d * k)\n", "    \n", "    print(f\"Original masks coverage: {complement_original:.3f} of matrix\")\n", "    print(f\"Sawtooth masks coverage: {complement_sawtooth:.3f} of matrix\")\n", "    \n", "    # Check if square matrices produce identical results\n", "    if d == k:\n", "        diff1 = (original.mask1 != sawtooth.mask1).sum().item()\n", "        diff2 = (original.mask2 != sawtooth.mask2).sum().item()\n", "        if diff1 == 0 and diff2 == 0:\n", "            print(\"✅ For square matrices, sawtooth implementation matches original\")\n", "        else:\n", "            print(f\"❌ Masks differ for square matrices: diff1={diff1}, diff2={diff2}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "former", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "undefined.undefined.undefined"}}, "nbformat": 4, "nbformat_minor": 4}