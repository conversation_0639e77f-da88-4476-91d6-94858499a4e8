channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - ca-certificates=2019.5.15=1
  - certifi=2019.6.16=py37_1
  - libedit=3.1.20181209=hc058e9b_0
  - libffi=3.2.1=hd88cf55_4
  - libgcc-ng=9.1.0=hdf63c60_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - ncurses=6.1=he6710b0_1
  - openssl=1.1.1c=h7b6447c_1
  - pip=19.2.2=py37_0
  - python=3.7.4=h265db76_1
  - readline=7.0=h7b6447c_5
  - setuptools=41.0.1=py37_0
  - sqlite=3.29.0=h7b6447c_0
  - tk=8.6.8=hbc83047_0
  - wheel=0.33.4=py37_0
  - xz=5.2.4=h14c3975_4
  - zlib=1.2.11=h7b6447c_3
  - pip:
    - absl-py==0.7.1
    - chardet==3.0.4
    - future==0.17.1
    - grpcio==1.23.0
    - idna==2.8
    - markdown==3.1.1
    - numpy==1.17.0
    - protobuf==3.9.1
    - requests==2.22.0
    - six==1.12.0
    - tb-nightly==1.15.0a20190901
    - torch==1.2.0
    - torchtext==0.4.0
    - tqdm==4.34.0
    - urllib3==1.25.3
    - werkzeug==0.15.5

