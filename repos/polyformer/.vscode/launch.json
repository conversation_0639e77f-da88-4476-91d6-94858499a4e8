{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "showReturnValue": true, "python": "${command:python.interpreterPath}", "debugOptions": ["RedirectOutput"]}, {"name": "Python Debugger: debug_poly_block.py", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/debug_poly_block.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}, "cwd": "${workspaceFolder}", "python": "${command:python.interpreterPath}"}], "compounds": []}