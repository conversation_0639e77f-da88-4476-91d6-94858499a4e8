# Outline of master thesis

## Introduction 
### Quantum Chemistry: the ground state problem

## Quantum Chemical Theories
### Basics: atomic orbitals, molecular orbitals, Slater determinant (anti-symmetry)
### Mean-field solutions: Hartree-Fock
### Going beyond MF: CC, FCI, Ansatz-based approach: NQS
### Kato Cusp conditions and Transcorrelation (BCH expansion, <PERSON><PERSON>'s papers)

## NQS 
### Basics: what is a artificial Neuron, NN? Simple demonstration of NN fitting functions
### Backflow NQS: what is a backflow NQS? Simple ref to other types of NQS, and why backflow NQS?
### How to train it? What are existing methods? 
#### MCMC
#### FSSC
#### Batching algo
#### Gradient descent
#### ITE and its connections to KFAC, Stochastic reconfiguration, natural gradient

## Results and Discussions
### Benchmarks: BF-NQS on normal Hamiltonians
#### Sample size, learning rate, different optimizer (<PERSON>'s paper)
### TC with BF-NQS
#### Benchmarks: Sample size, learning rate, comparing to normal Hamiltonians
#### Basis set convergence

## Conclusion and Outlook
### Reflect on the limitations of our work
### Potential of our work

