## 03.05.2024 Meeting
**TODO** 
- [x] Read about <PERSON><PERSON>chy interlacing theorem and understand why with a larger basis set, the ground state energy will be lower. See: https://en.wikipedia.org/wiki/Min-max_theorem#Cauchy_interlacing_theorem
- [x] Read about Hartree-Fock theory and understand the iterative solution/theory from the book by <PERSON><PERSON><PERSON>.
- [x] Play with PySCF and reproduce HF calculations on Szabo. 
- [x] Calculate HF energies with increasingly large basis sets: STO-3G, STO-6G, cc-pVDZ, cc-pVTZ, cc-pVQZ, cc-pV5Z, etc, and plot the energy versus basis set plot.


Add your plots in the `study.ipynb` file.

## 13.05.2024

- [x] Read about configuration interaction theory
- [x] Learn the basics about JAX: https://jax.readthedocs.io/en/latest/tutorials.html
    and Flax: https://flax.readthedocs.io/en/latest/quick_start.html 
- [x] configure your laptop and upload any notes that have been updated.

## 27.05.2024
- [x] Figure out in PySCF the indexing of slater determinants.
    - add test for this
- [x] Train the simple MLP network with the FCI wavefunction.
    - add test
- [x] Implement the backflow (finish backflow.py)

## 03.06.2024
- [x] Finish debugging backflow
    - produce the same test as in MLP, ensuring the number of parameters are the same in the two networks
    - plot the two trainning process and add them to the Nextcloud latex 

## 10.06.2024
- [x] Start looking at how to implement Hamiltonian.
    - How to find the different indices between two strings of dets. 
    - Ke will add a FCIDUMP reader 
    - Slater-Condon rules: https://en.wikipedia.org/wiki/Slater–Condon_rules 
    - Finish the functions in hamiltonian.py


## 24.06.2024
- [x] **Debug 4 electron system H4**
    - Write simple test inputting two SD directly and compute the known value.
- [x] Fix the warning "assigning array to scalar".

     
## Presentation
- Introduction
  - Backgroud:
      - static (degeneracy) and dynamic correlation (cusp condition, very large basis set, complete basis set limit to compare with experiments),
      - and the exponential size of FCI
  - Motivations: Second quantized NN good at capturing the static correlation (related work), but 2nd formulation plagued by dynamic correlation -> TC 
- Understanding why backflow is good.
  - Introducing BF architechture. 
  - Comparing to other networks, such as MLP, RBM, transformer, in terms of encoding anti-symmetry, and sampling
- Supervised trainning
    - [x] Plot FCI wavefunction, y CI coefficients, x SD index.
- A simple deterministic sampling (all the SD) to train and show the energy reduction
- Conclusion

## 08.07.2024
- [x] Think about how to improve the performance of Hamiltonian part.
    - [x] **jit it, removing the if statements. **
    - [x] *binary rep of SD, see if np array takes more memory?*
      - ~~Dynamically convert binary string to current array format, such that jnp's functions can be use~~
      - Decided to stick to jnp array of uint8 to represent det
      - 
- [x] Clean up existing test files.

## 15.07.2024
- [x] Change current det rep from number array to jnp array of uint8
- [x] Energy expectation value evaluation using batches.
    - [x] For each SD, generate all its connections
    - [x] Change the energy loss function to take advantage of this, instead of using the full H.
    
## 22.07.2024
- [ ] ~~Implement the SCI sampling scheme.~~Moved to next to do.
- [ ] ~~Run for a larger system, where the Hilbert space size is larger than the number of parameters.~~ Remains to be done after FSSC sampler implemented

## 29.07.2024
- [x] Implement the FSSC sampling scheme.
    - Finish the FSSC class and its member function
    - ~~Create a new trainer function for this sampler~~
    - FSSC.sample function should output a tuple of 2 arrays like det|Ci, which are both sorted and then input to the trainer.
     
## 05.08.2024
- [x] Debug FSSC
    - Reduce until working case and add back new features one by one to identify the bug
- [x] Focus on improving the efficiency of FSSC
    - ~~Use lax.scan instead of loops~~ Used jax.unique which scales n*log(n)
    - Use predefined full_space and fill the elements. Testing
- [x] Prepare presentation next week

## 12.08.2024
- [x] Try making some important working arrays static and see if this helps jit compiling and performance
   - Need further check
- [x] Find out why NAN appears in slog and fix this.
      - probably related to number nonconserving samples
- [x] Why at the tail the computing is much faster? Find out the why may help us improve the further the performance
      - Avoid evaluating the network repeatedly

## 19.08.2024
Finish the previous todo's.

## 26.08.2024
- [x] Try to reproduce some results from ref. using the same structures from from PubChem [37].
- [x] Implement multiple SDs to try the orthogonalization among the SDs.
      - Need to change to linear combination of determinants 
- [x] VMAP, Scattering of large determinant not allowed in Jax.
      - Error only happens on CPU, but not on GPU

Ke's todo
- [ ] Implement the stochastic connection generation.

## 04.09.2024
- [x] Refactoring of the trainer function to reduce GPU memory
- [x] Need to change to linear combination of determinants

- Note the idea of using batches.

## 09.09.2024
- [x] Find out which steps are the slowest, and think about how to make them faster.
      - Now uses return inverse jnp.unique to reconstruct original array, such the CI of the #corex#connections dets can be reconstructed without calculating the CI of each det.

## 16.09.2024
- [x] Fix the bug of when #core is large, the simulation gets stuck at HF. Possibly because there is 0 padding in the initial core space.
      **fixed the padding**
    - [x] Rerun N2 and fully converge the calculations. Plot of energy error wrt FCI with x-axis being the bond length.
- [ ] Investigate using abs of energy gradient wrt Ci as the selection criteria for selecting core space. **remained to be explored**

## 25.09.2024
- [x] Try the batching idea, document it in your thesis
- [ ] Look into TC case, understand the differences and what to be changed

## 07.10.2024 
- [ ] Confirm the batching works by trying larger systems from ref Clark etc.
      - Take derivative of the numerator and denominator separately, needing twice the memory the size of the network.

- [x] Try the new nn architecture electron-backflow.
    - Use the index of occupied orbitals as input.
    - Use a product of two Slater determinants for spin-up and spin-down.
     
## 21.10.2024
- [x] Finish the batching idea.
- [x] Investigate and debug the electron-backflow

## 28.10.2024
 - [x] Debugging the remaining memory issue with batching
 - [x] Read and understand the non-hermitian solver from refs. Check TC FCIDUMP from
    - https://github.com/nickirk/tc-qpe/tree/main/werner-hamiltonians/hydrogen-chains/sto-3g/h4/1.8-bohr
    - https://pubs.acs.org/doi/full/10.1021/acs.jctc.4c00070
      
## 04.11.2024
- [x] Rephrase the theory VarQITE in your working document.

## 11.11.2024

    
- [ ] ~~Enabling XLA debugging and backtracing functionalities to find out exactly which functions are causing each error.~~

- [ ] ~~HEAT bath CI, need to restrict the excitation number.~~
- [ ] ~~Study the fat-tail behavior.~~

## 25.11.2024
- [x] Implement the imaginary-time evolution and make sure it converges to the correct number, then plot the energy--step for both the gradient descent and ITE solver.
      - [x] The correct structure of Aij matrix.
      
- [x] Prepare presentation

## 09.12.2024
- [ ] ~~Try to interface KFAC to our code and make solvers more efficient.~~
- [x] Improving your presentation skills:
      - Learning more terminologies in quantum chemistry and neural network and machine learning.
      - Try to explain things slowly and clearly
- [x] Reading more papers on NN and machine learning, e.g. Carleo's papers

## 20.12.2024
- [x]  Do SVD on the A matrix during the beginning, middle and ending phases of the training to see if there are low-rank structures in A at different stages.
- [ ]  Read about QR (with column pivoting) decomposition, and how to use it to solve linear quations
- [x]  Keep on writing thesis.

## 19.02.2024

- [x] Profiling of the code to identify the bottlenecks.
      - Remove memory bottleneck in Hamiltonian evaluation
      
- [x] Finish the batching over the samples in VITE.
      - Broke down into smaller parts which can be batched.
- [ ] ~~Implment the semi-stochastic sampling.~~

## 05.03.2025
- [x] Although memory usage in Hamiltonian is reduced, for larger systems, need further reduction of memory
      - change from vmap to scan to further reduce vram. 
- [x] The solver now takes the most of time.
      - Play maximal iterations, print out the residual to determine the # of iterations needed
- [ ] ~~Try with N2 in ccpvtz, to identify more bottlenecks and optimize them~~
      - Too big for now. Will stick to ccpvdz
- [ ] Keep writing thesis.

## 12.03.2025
- [ ] Since efficiency improvements to Hamiltonian seems to have introduced bugs, fix them and rerun tc on He and Be atom for benchmark.
- [ ] Run n2 in ccpvdz, find the tc-dmrg energies from paper 

## 19.03.2025

- [ ] New one-sided gradient
      - Test on normal Hamiltonian
      - Test on TC Hamiltonian
- [ ] Pretraining: using cost function $\sum_i |C_i-R_i|^2$




