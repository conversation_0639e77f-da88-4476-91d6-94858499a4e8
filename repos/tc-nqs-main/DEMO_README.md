# TC-NQS Demonstration

This directory contains a complete demonstration of the Transcorrelated Second-Quantized Neural Network Quantum State (TC-NQS) package functionality.

## Overview

The demonstration solves a small quantum chemistry problem (H₂ molecule) using both exact Full Configuration Interaction (FCI) and the TC-NQS neural network approach, then compares the results to validate the neural network method.

## Files

- `tc_nqs_demo.py` - Main demonstration script with the `TCNQSDemo` class
- `run_demo.py` - Simple runner script with command-line options
- `DEMO_README.md` - This documentation file

## Quick Start

### Prerequisites

Make sure you have the required dependencies installed:

```bash
pip install jax pyscf numpy matplotlib flax optax
```

### Running the Demo

**Simple run:**
```bash
python run_demo.py
```

**Quick test (fewer epochs):**
```bash
python run_demo.py --quick
```

**Custom parameters:**
```bash
python run_demo.py --bond-length 0.8 --epochs 300 --hidden-layers 16 16
```

**Check dependencies:**
```bash
python run_demo.py --check-deps
```

## What the Demo Does

### 1. Problem Definition
- Defines an H₂ molecule with configurable bond length
- Uses STO-3G basis set (minimal but sufficient for demonstration)
- Performs Hartree-Fock calculation as starting point

### 2. Exact FCI Solution
- Solves the problem exactly using Full Configuration Interaction
- Provides the reference energy and wavefunction coefficients
- Analyzes the CI vector to understand the electronic structure

### 3. Neural Network Setup
- Creates a backflow neural network architecture
- Generates training data from FCI coefficients
- Builds the quantum chemistry Hamiltonian

### 4. Training
- Trains the neural network using supervised learning on FCI coefficients
- Uses logarithmic loss function to optimize wavefunction overlap
- Monitors convergence over training epochs

### 5. Energy Evaluation
- Computes the energy using the trained neural network
- Calculates the expectation value ⟨Ψ|H|Ψ⟩/⟨Ψ|Ψ⟩
- Compares with exact FCI energy

### 6. Results Analysis
- Compares neural network and FCI wavefunction coefficients
- Calculates wavefunction overlap and coefficient correlations
- Reports absolute and relative energy errors

### 7. Visualization
- Generates convergence plots
- Shows energy comparison
- Displays coefficient correlation and error distribution
- Saves results as `tc_nqs_results.png`

## Expected Results

For the default H₂ molecule (0.74 Å, STO-3G basis):

- **FCI Energy**: ~-1.117 Hartree
- **Expected Accuracy**: < 1 mHartree (chemical accuracy)
- **Training Time**: ~30-60 seconds on CPU
- **Wavefunction Overlap**: > 99%

## Command Line Options

```
--bond-length FLOAT    H-H bond length in Angstroms (default: 0.74)
--basis STRING         Basis set: sto-3g, 6-31g, cc-pvdz (default: sto-3g)
--epochs INT           Number of training epochs (default: 200)
--hidden-layers INT+   Hidden layer sizes (default: 8 8)
--quick               Quick demo with fewer epochs
--check-deps          Check dependencies and exit
```

## Understanding the Output

### Training Progress
```
Epoch   20/200: Loss = 0.00123456
```
The loss should decrease steadily. Values < 0.001 indicate good convergence.

### Energy Comparison
```
Neural Network Energy    : -1.11675432 Hartree
FCI Energy (reference)   : -1.11675891 Hartree
Absolute Error          : 4.59e-06 Hartree
Relative Error          : 0.0004 %
```

### Success Criteria
- **Chemical Accuracy**: Error < 1 mHartree (0.001 Hartree)
- **Good Accuracy**: Error < 10 mHartree (0.01 Hartree)
- **Wavefunction Overlap**: > 95%

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
2. **Memory Issues**: The demo uses CPU by default; reduce epochs if needed
3. **Convergence Issues**: Try different hidden layer sizes or more epochs
4. **Numerical Issues**: JAX 64-bit precision is enabled for stability

### Performance Tips

- Use `--quick` for initial testing
- Larger hidden layers may improve accuracy but increase training time
- More epochs generally improve convergence but with diminishing returns

## Extending the Demo

### Different Molecules
Modify the `define_problem()` method to use different molecules:

```python
self.mol.atom = 'Li 0 0 0; H 0 0 1.6'  # LiH molecule
```

### Different Basis Sets
Use larger basis sets for more challenging problems:

```python
python run_demo.py --basis 6-31g
```

### Advanced Training
The demo can be extended to use:
- Variational energy minimization instead of supervised learning
- FSSC sampling for larger systems
- Different neural network architectures

## Technical Details

### Neural Network Architecture
- **Input**: Binary occupation vectors (Slater determinants)
- **Hidden Layers**: Configurable dense layers with activation functions
- **Backflow Transform**: Maps input through neural network
- **Output**: Determinant calculation for wavefunction amplitude

### Mathematical Framework
- **Hamiltonian**: Second-quantized electronic Hamiltonian
- **Wavefunction**: Linear combination of Slater determinants
- **Energy**: Variational expectation value ⟨Ψ|H|Ψ⟩/⟨Ψ|Ψ⟩
- **Training**: Minimize overlap loss or energy functional

### Validation
The demo validates the TC-NQS approach by:
1. Comparing energies with exact FCI results
2. Analyzing wavefunction coefficient correlations
3. Checking convergence behavior
4. Measuring computational efficiency

## References

- TC-NQS methodology and transcorrelation theory
- Neural Network Quantum States (NQS) literature
- Quantum chemistry and electronic structure theory
- JAX/Flax neural network frameworks
