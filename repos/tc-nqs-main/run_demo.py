#!/usr/bin/env python3
"""
Simple runner script for the TC-NQS demonstration.

This script provides an easy way to run the TC-NQS demonstration with different
configurations and handles common setup issues.
"""

import os
import sys
import argparse

def setup_environment():
    """Set up the environment for running TC-NQS."""
    # Add the current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Set JAX configuration for stability
    os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = '0.5'
    os.environ['JAX_PLATFORMS'] = 'cpu'
    
    # Suppress warnings for cleaner output
    import warnings
    warnings.filterwarnings('ignore')

def check_dependencies():
    """Check if required dependencies are available."""
    required_packages = [
        ('jax', 'JAX'),
        ('pyscf', 'PySCF'),
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('flax', 'Flax')
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✓ {name} found")
        except ImportError:
            missing_packages.append(name)
            print(f"✗ {name} not found")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print("pip install jax pyscf numpy matplotlib flax optax")
        return False
    
    return True

def run_demo_with_config(bond_length=0.74, basis='sto-3g', epochs=200, hidden_layers=None):
    """Run the demonstration with specified configuration."""
    if hidden_layers is None:
        hidden_layers = [8, 8]
    
    print(f"Running TC-NQS demo with:")
    print(f"  Bond length: {bond_length} Å")
    print(f"  Basis set: {basis}")
    print(f"  Training epochs: {epochs}")
    print(f"  Hidden layers: {hidden_layers}")
    print()
    
    # Import and run the demo
    from tc_nqs_demo import TCNQSDemo
    
    demo = TCNQSDemo(
        bond_length=bond_length,
        basis=basis,
        random_seed=42
    )
    
    # Run individual steps with custom parameters
    demo.define_problem()
    demo.solve_exact_fci()
    demo.setup_neural_network(hidden_layers=hidden_layers)
    demo.train_supervised(num_epochs=epochs)
    demo.evaluate_energy()
    demo.compare_wavefunctions()
    demo.plot_results()
    
    return demo

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description='Run TC-NQS demonstration with configurable parameters'
    )
    
    parser.add_argument(
        '--bond-length', type=float, default=0.74,
        help='H-H bond length in Angstroms (default: 0.74)'
    )
    
    parser.add_argument(
        '--basis', type=str, default='sto-3g',
        choices=['sto-3g', '6-31g', 'cc-pvdz'],
        help='Basis set for calculation (default: sto-3g)'
    )
    
    parser.add_argument(
        '--epochs', type=int, default=200,
        help='Number of training epochs (default: 200)'
    )
    
    parser.add_argument(
        '--hidden-layers', type=int, nargs='+', default=[8, 8],
        help='Hidden layer sizes (default: 8 8)'
    )
    
    parser.add_argument(
        '--quick', action='store_true',
        help='Run a quick demo with fewer epochs'
    )
    
    parser.add_argument(
        '--check-deps', action='store_true',
        help='Check dependencies and exit'
    )
    
    args = parser.parse_args()
    
    # Set up environment
    setup_environment()
    
    # Check dependencies if requested
    if args.check_deps:
        if check_dependencies():
            print("\nAll dependencies are available!")
            return 0
        else:
            return 1
    
    # Adjust parameters for quick run
    if args.quick:
        args.epochs = 50
        args.hidden_layers = [4, 4]
        print("Quick mode: Using 50 epochs and [4, 4] hidden layers")
    
    try:
        # Check dependencies
        if not check_dependencies():
            return 1
        
        print("\nStarting TC-NQS demonstration...")
        
        # Run the demonstration
        demo = run_demo_with_config(
            bond_length=args.bond_length,
            basis=args.basis,
            epochs=args.epochs,
            hidden_layers=args.hidden_layers
        )
        
        print("\n" + "="*60)
        print(" DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*60)
        
        # Print final results
        abs_error = abs(demo.nn_energy - demo.fci_energy)
        rel_error = abs_error / abs(demo.fci_energy) * 100
        
        print(f"Final Results:")
        print(f"  FCI Energy: {demo.fci_energy:.8f} Hartree")
        print(f"  NN Energy:  {demo.nn_energy:.8f} Hartree")
        print(f"  Error:      {abs_error:.2e} Hartree ({rel_error:.4f}%)")
        
        if abs_error < 1e-3:
            print("  Status: ✓ Chemical accuracy achieved!")
        elif abs_error < 1e-2:
            print("  Status: ✓ Good accuracy achieved!")
        else:
            print("  Status: ⚠ Accuracy could be improved")
        
        return 0
        
    except KeyboardInterrupt:
        print("\nDemonstration interrupted by user.")
        return 1
    except Exception as e:
        print(f"\nError running demonstration: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
