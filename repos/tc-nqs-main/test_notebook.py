#!/usr/bin/env python3
"""
Test script to verify the TC-NQS educational notebook can be executed.
"""

import os
import sys
import subprocess

def test_notebook_execution():
    """Test if the notebook can be executed without errors."""
    
    # Check if jupyter is available
    try:
        result = subprocess.run(['jupyter', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ Jupyter not found. Please install with: pip install jupyter")
            return False
        print("✓ Jupyter found")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Jupyter not available")
        return False
    
    # Check if nbconvert is available
    try:
        result = subprocess.run(['jupyter', 'nbconvert', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ nbconvert not found")
            return False
        print("✓ nbconvert found")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ nbconvert not available")
        return False
    
    # Test notebook syntax
    notebook_path = "TC_NQS_Educational_Walkthrough.ipynb"
    if not os.path.exists(notebook_path):
        print(f"❌ Notebook not found: {notebook_path}")
        return False
    
    print(f"✓ Notebook found: {notebook_path}")
    
    # Try to validate notebook structure
    try:
        import json
        with open(notebook_path, 'r') as f:
            notebook = json.load(f)
        
        print(f"✓ Notebook JSON is valid")
        print(f"  - Cells: {len(notebook.get('cells', []))}")
        print(f"  - Notebook format: {notebook.get('nbformat', 'unknown')}")
        
        # Count cell types
        cell_types = {}
        for cell in notebook.get('cells', []):
            cell_type = cell.get('cell_type', 'unknown')
            cell_types[cell_type] = cell_types.get(cell_type, 0) + 1
        
        for cell_type, count in cell_types.items():
            print(f"  - {cell_type} cells: {count}")
        
    except Exception as e:
        print(f"❌ Error reading notebook: {e}")
        return False
    
    print("\n✓ Notebook appears to be properly formatted!")
    print("\nTo run the notebook:")
    print("1. Activate the quantum conda environment:")
    print("   conda activate quantum")
    print("2. Start Jupyter:")
    print("   jupyter notebook")
    print("3. Open TC_NQS_Educational_Walkthrough.ipynb")
    print("4. Run all cells (Cell -> Run All)")
    
    return True

def main():
    """Main function."""
    print("Testing TC-NQS Educational Notebook...")
    print("=" * 50)
    
    success = test_notebook_execution()
    
    if success:
        print("\n✓ All tests passed! The notebook should work correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the requirements.")
        return 1

if __name__ == "__main__":
    exit(main())
