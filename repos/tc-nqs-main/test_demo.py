#!/usr/bin/env python3
"""
Simple test script to verify TC-NQS demo functionality without full execution.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import jax
        print("✓ JAX imported successfully")
    except ImportError as e:
        print(f"✗ JAX import failed: {e}")
        return False
    
    try:
        import jax.numpy as jnp
        print("✓ JAX NumPy imported successfully")
    except ImportError as e:
        print(f"✗ JAX NumPy import failed: {e}")
        return False
    
    try:
        import pyscf
        print("✓ PySCF imported successfully")
    except ImportError as e:
        print(f"✗ PySCF import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy imported successfully")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✓ Matplotlib imported successfully")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        return False
    
    try:
        import flax
        print("✓ Flax imported successfully")
    except ImportError as e:
        print(f"✗ Flax import failed: {e}")
        return False
    
    return True

def test_tcnqs_modules():
    """Test if TC-NQS modules can be imported."""
    print("\nTesting TC-NQS modules...")
    
    try:
        from tcnqs.utils import generate_ci_data, build_ham_from_pyscf
        print("✓ tcnqs.utils imported successfully")
    except ImportError as e:
        print(f"✗ tcnqs.utils import failed: {e}")
        return False
    
    try:
        import tcnqs.backflow as bf
        print("✓ tcnqs.backflow imported successfully")
    except ImportError as e:
        print(f"✗ tcnqs.backflow import failed: {e}")
        return False
    
    try:
        import tcnqs.trainer as trainer
        print("✓ tcnqs.trainer imported successfully")
    except ImportError as e:
        print(f"✗ tcnqs.trainer import failed: {e}")
        return False
    
    try:
        from tcnqs.hamiltonian import Hamiltonian
        print("✓ tcnqs.hamiltonian imported successfully")
    except ImportError as e:
        print(f"✗ tcnqs.hamiltonian import failed: {e}")
        return False
    
    return True

def test_demo_class():
    """Test if the demo class can be imported and instantiated."""
    print("\nTesting demo class...")
    
    try:
        from tc_nqs_demo import TCNQSDemo
        print("✓ TCNQSDemo class imported successfully")
        
        # Try to instantiate (but don't run)
        demo = TCNQSDemo(bond_length=0.74, basis='sto-3g', random_seed=42)
        print("✓ TCNQSDemo class instantiated successfully")
        
        return True
    except ImportError as e:
        print(f"✗ TCNQSDemo import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ TCNQSDemo instantiation failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without full computation."""
    print("\nTesting basic functionality...")
    
    try:
        import jax
        jax.config.update("jax_enable_x64", True)
        
        import pyscf
        from pyscf import gto
        
        # Create a simple molecule
        mol = gto.Mole()
        mol.atom = 'H 0 0 0; H 0 0 0.74'
        mol.basis = 'sto-3g'
        mol.spin = 0
        mol.charge = 0
        mol.symmetry = False
        mol.build()
        
        print(f"✓ Created H2 molecule with {mol.nelectron} electrons and {mol.nao} orbitals")
        
        # Test HF calculation
        myhf = mol.RHF()
        myhf.verbose = 0
        hf_energy = myhf.kernel()
        
        print(f"✓ HF calculation completed: E = {hf_energy:.6f} Hartree")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("TC-NQS Demo Test Suite")
    print("=" * 40)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test TC-NQS modules
    if not test_tcnqs_modules():
        all_passed = False
    
    # Test demo class
    if not test_demo_class():
        all_passed = False
    
    # Test basic functionality
    if not test_basic_functionality():
        all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✓ ALL TESTS PASSED")
        print("\nThe demo should work correctly. You can run:")
        print("  python3 run_demo.py --quick")
        print("  python3 tc_nqs_demo.py")
        return 0
    else:
        print("✗ SOME TESTS FAILED")
        print("\nPlease install missing dependencies:")
        print("  pip install jax pyscf numpy matplotlib flax optax")
        return 1

if __name__ == "__main__":
    exit(main())
