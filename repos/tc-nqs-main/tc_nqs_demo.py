#!/usr/bin/env python3
"""
TC-NQS Demonstration Script
===========================

This script demonstrates the Transcorrelated Second-Quantized Neural Network Quantum State (TC-NQS)
package functionality by solving a small quantum chemistry problem and comparing the neural network
results with exact Full Configuration Interaction (FCI) calculations.

The demonstration includes:
1. Problem definition (H2 molecule with STO-3G basis)
2. Exact FCI solution using PySCF
3. Neural network training using TC-NQS backflow architecture
4. Results comparison and convergence analysis
"""

import os
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# Set JAX/XLA memory configuration for stability
os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = '0.5'
os.environ['JAX_PLATFORMS'] = 'cpu'  # Use CPU for reproducibility

import jax
import jax.numpy as jnp
from jax import random
import numpy as np
import matplotlib.pyplot as plt
import pyscf
from pyscf import gto, scf, fci

# Enable 64-bit precision for numerical stability
jax.config.update("jax_enable_x64", True)

# Import TC-NQS modules
sys.path.append('tcnqs')
from tcnqs.utils import generate_ci_data, build_ham_from_pyscf
import tcnqs.backflow as bf
import tcnqs.trainer as trainer
from tcnqs.hamiltonian import Hamiltonian

def print_header(title):
    """Print a formatted header for different sections."""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_results(title, value, unit="", precision=8):
    """Print formatted results."""
    if isinstance(value, (int, float, complex)):
        print(f"{title:<30}: {value:.{precision}f} {unit}")
    else:
        print(f"{title:<30}: {value} {unit}")

class TCNQSDemo:
    """Main demonstration class for TC-NQS functionality."""
    
    def __init__(self, bond_length=0.74, basis='sto-3g', random_seed=42):
        """
        Initialize the demonstration with molecular parameters.
        
        Args:
            bond_length (float): H-H bond length in Angstroms
            basis (str): Basis set for quantum chemistry calculation
            random_seed (int): Random seed for reproducibility
        """
        self.bond_length = bond_length
        self.basis = basis
        self.random_seed = random_seed
        self.rng = random.PRNGKey(random_seed)
        
        # Results storage
        self.fci_energy = None
        self.fci_coeffs = None
        self.nn_energy = None
        self.training_losses = []
        self.training_energies = []
        
    def define_problem(self):
        """Define the quantum chemistry problem (H2 molecule)."""
        print_header("1. PROBLEM DEFINITION")
        
        # Define H2 molecule
        self.mol = gto.Mole()
        self.mol.atom = f'H 0 0 0; H 0 0 {self.bond_length}'
        self.mol.basis = self.basis
        self.mol.spin = 0  # Singlet state
        self.mol.charge = 0
        self.mol.symmetry = False
        self.mol.build()
        
        print(f"Molecule: H2")
        print(f"Bond length: {self.bond_length} Å")
        print(f"Basis set: {self.basis}")
        print(f"Number of electrons: {self.mol.nelectron}")
        print(f"Number of orbitals: {self.mol.nao}")
        
        # Perform Hartree-Fock calculation
        print("\nPerforming Hartree-Fock calculation...")
        self.myhf = scf.RHF(self.mol)
        self.myhf.verbose = 0  # Suppress PySCF output
        self.hf_energy = self.myhf.kernel()
        
        print_results("HF Energy", self.hf_energy, "Hartree")
        
        return self.mol, self.myhf
    
    def solve_exact_fci(self):
        """Solve the problem exactly using Full Configuration Interaction."""
        print_header("2. EXACT FCI SOLUTION")
        
        # Perform FCI calculation
        print("Performing Full Configuration Interaction (FCI) calculation...")
        cisolver = fci.FCI(self.myhf)
        cisolver.verbose = 0
        self.fci_energy, self.fci_coeffs = cisolver.kernel()
        
        print_results("FCI Energy", self.fci_energy, "Hartree")
        print_results("Correlation Energy", self.fci_energy - self.hf_energy, "Hartree")
        
        # Analyze CI coefficients
        num_orbitals = self.myhf.mo_coeff.shape[1]
        num_alpha, num_beta = self.mol.nelec
        
        print(f"\nCI vector shape: {self.fci_coeffs.shape}")
        print(f"Number of determinants: {self.fci_coeffs.size}")
        
        # Find dominant configurations
        flat_coeffs = self.fci_coeffs.flatten()
        dominant_indices = np.argsort(np.abs(flat_coeffs))[-5:][::-1]
        
        print("\nTop 5 CI coefficients:")
        for i, idx in enumerate(dominant_indices):
            coeff = flat_coeffs[idx]
            print(f"  {i+1}. Coefficient: {coeff:.6f} (|coeff|²: {abs(coeff)**2:.6f})")
        
        return self.fci_energy, self.fci_coeffs
    
    def setup_neural_network(self, hidden_layers=[8, 8], activation='tanh', n_bf_dets=1):
        """Set up the TC-NQS backflow neural network."""
        print_header("3. NEURAL NETWORK SETUP")
        
        # Generate training data from FCI solution
        num_orbitals = self.myhf.mo_coeff.shape[1]
        num_alpha, num_beta = self.mol.nelec
        
        print("Generating training data from FCI coefficients...")
        self.x_train, self.y_train = generate_ci_data(
            num_orbitals, num_alpha, num_beta, self.fci_coeffs
        )
        
        print(f"Training data shape: {self.x_train.shape}")
        print(f"Target coefficients shape: {self.y_train.shape}")
        print(f"Non-zero coefficients: {np.sum(np.abs(self.y_train) > 1e-8)}")
        
        # Create Hamiltonian
        print("\nBuilding Hamiltonian from PySCF calculation...")
        self.hamiltonian = build_ham_from_pyscf(self.mol, self.myhf)
        
        print(f"Hamiltonian electrons: {self.hamiltonian.n_elec}")
        print(f"Hamiltonian orbitals: {self.hamiltonian.n_orb}")
        print(f"Core energy: {self.hamiltonian.e_core:.8f}")
        
        # Create neural network model
        print(f"\nCreating backflow neural network...")
        print(f"Hidden layers: {hidden_layers}")
        print(f"Activation function: {activation}")
        print(f"Number of backflow determinants: {n_bf_dets}")
        
        input_shape = 2 * num_orbitals  # Spin orbitals
        num_electrons = num_alpha + num_beta
        
        self.model, self.variables = bf.create_model(
            self.rng, 
            input_shape=input_shape,
            num_electrons=num_electrons,
            hidden_layer_sizes=hidden_layers,
            activation=activation,
            n_bf_dets=n_bf_dets
        )
        
        # Create training state
        self.state = trainer.create_train_state(self.rng, self.model, self.variables)
        
        print(f"Neural network parameters: {sum(x.size for x in jax.tree_leaves(self.variables['params']))}")
        
        return self.model, self.hamiltonian
    
    def train_supervised(self, num_epochs=200, batch_size=None):
        """Train the neural network using supervised learning on FCI coefficients."""
        print_header("4. SUPERVISED TRAINING")
        
        if batch_size is None:
            batch_size = len(self.y_train)
        
        print(f"Training epochs: {num_epochs}")
        print(f"Batch size: {batch_size}")
        print(f"Training samples: {len(self.y_train)}")
        
        self.training_losses = []
        num_samples = len(self.y_train)
        
        print("\nStarting supervised training...")
        start_time = time.time()
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            
            # Shuffle training data
            self.rng, subrng = random.split(self.rng)
            perm = random.permutation(self.rng, num_samples)
            x_shuffled = self.x_train[perm]
            y_shuffled = self.y_train[perm]
            
            # Training batches
            num_batches = (num_samples + batch_size - 1) // batch_size
            for i in range(0, num_samples, batch_size):
                batch_x = x_shuffled[i:i+batch_size]
                batch_y = y_shuffled[i:i+batch_size]
                batch = (batch_x, batch_y)
                
                self.state, loss = trainer.train_step_log(self.state, batch)
                epoch_loss += loss
            
            avg_loss = epoch_loss / num_batches
            self.training_losses.append(float(avg_loss))
            
            if epoch % 20 == 0 or epoch == num_epochs - 1:
                print(f"Epoch {epoch+1:4d}/{num_epochs}: Loss = {avg_loss:.8f}")
            
            self.rng = subrng
        
        training_time = time.time() - start_time
        print(f"\nTraining completed in {training_time:.2f} seconds")
        print(f"Final loss: {self.training_losses[-1]:.8f}")
        
        return self.training_losses

    def evaluate_energy(self):
        """Evaluate the energy using the trained neural network."""
        print_header("5. ENERGY EVALUATION")

        # Build full Hamiltonian matrix for energy calculation
        print("Building Hamiltonian matrix...")
        num_dets = len(self.x_train)
        H_matrix = np.zeros((num_dets, num_dets))

        for i in range(num_dets):
            for j in range(num_dets):
                H_matrix[i, j] = self.hamiltonian(self.x_train[i], self.x_train[j])

        # Get neural network predictions
        print("Computing neural network wavefunction coefficients...")
        nn_coeffs = self.model.apply({'params': self.state.params}, self.x_train)

        # Calculate energy: E = <Ψ|H|Ψ> / <Ψ|Ψ>
        numerator = np.dot(nn_coeffs, np.dot(H_matrix, nn_coeffs))
        denominator = np.dot(nn_coeffs, nn_coeffs)
        self.nn_energy = float(numerator / denominator)

        print_results("Neural Network Energy", self.nn_energy, "Hartree")
        print_results("FCI Energy (reference)", self.fci_energy, "Hartree")

        # Calculate errors
        abs_error = abs(self.nn_energy - self.fci_energy)
        rel_error = abs_error / abs(self.fci_energy) * 100

        print_results("Absolute Error", abs_error, "Hartree")
        print_results("Relative Error", rel_error, "%")

        # Energy in other units
        hartree_to_ev = 27.211386245988  # eV per Hartree
        abs_error_ev = abs_error * hartree_to_ev

        print_results("Absolute Error", abs_error_ev, "eV", precision=6)

        return self.nn_energy, abs_error, rel_error

    def compare_wavefunctions(self, top_n=10):
        """Compare neural network and FCI wavefunction coefficients."""
        print_header("6. WAVEFUNCTION COMPARISON")

        # Get neural network coefficients
        nn_coeffs = self.model.apply({'params': self.state.params}, self.x_train)
        fci_coeffs_flat = self.y_train

        # Normalize both wavefunctions
        nn_coeffs_norm = nn_coeffs / np.linalg.norm(nn_coeffs)
        fci_coeffs_norm = fci_coeffs_flat / np.linalg.norm(fci_coeffs_flat)

        # Calculate overlap
        overlap = abs(np.dot(nn_coeffs_norm, fci_coeffs_norm))**2
        print_results("Wavefunction Overlap", overlap, "", precision=6)

        # Find dominant coefficients
        fci_dominant = np.argsort(np.abs(fci_coeffs_flat))[-top_n:][::-1]

        print(f"\nTop {top_n} CI coefficients comparison:")
        print(f"{'Index':<6} {'FCI Coeff':<12} {'NN Coeff':<12} {'Difference':<12}")
        print("-" * 50)

        for idx in fci_dominant:
            fci_val = fci_coeffs_flat[idx]
            nn_val = nn_coeffs[idx]
            diff = abs(fci_val - nn_val)
            print(f"{idx:<6} {fci_val:<12.6f} {nn_val:<12.6f} {diff:<12.6f}")

        return overlap

    def plot_results(self, save_plots=True):
        """Generate convergence plots and save results."""
        print_header("7. RESULTS VISUALIZATION")

        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('TC-NQS Training Results', fontsize=16)

        # Plot 1: Training loss convergence
        ax1.semilogy(self.training_losses)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Training Loss (log scale)')
        ax1.set_title('Training Loss Convergence')
        ax1.grid(True, alpha=0.3)

        # Plot 2: Energy comparison
        energies = [self.fci_energy, self.nn_energy]
        labels = ['FCI (Exact)', 'Neural Network']
        colors = ['blue', 'red']

        bars = ax2.bar(labels, energies, color=colors, alpha=0.7)
        ax2.set_ylabel('Energy (Hartree)')
        ax2.set_title('Energy Comparison')

        # Add energy values on bars
        for bar, energy in zip(bars, energies):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{energy:.6f}', ha='center', va='bottom')

        # Plot 3: Coefficient comparison (scatter plot)
        nn_coeffs = self.model.apply({'params': self.state.params}, self.x_train)

        ax3.scatter(self.y_train, nn_coeffs, alpha=0.6, s=20)
        ax3.plot([self.y_train.min(), self.y_train.max()],
                [self.y_train.min(), self.y_train.max()], 'r--', alpha=0.8)
        ax3.set_xlabel('FCI Coefficients')
        ax3.set_ylabel('Neural Network Coefficients')
        ax3.set_title('Coefficient Correlation')
        ax3.grid(True, alpha=0.3)

        # Plot 4: Error analysis
        abs_errors = np.abs(nn_coeffs - self.y_train)
        ax4.hist(abs_errors, bins=20, alpha=0.7, color='green')
        ax4.set_xlabel('Absolute Error in Coefficients')
        ax4.set_ylabel('Frequency')
        ax4.set_title('Error Distribution')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            plt.savefig('tc_nqs_results.png', dpi=300, bbox_inches='tight')
            print("Results plot saved as 'tc_nqs_results.png'")

        plt.show()

        return fig

    def run_full_demo(self):
        """Run the complete TC-NQS demonstration."""
        print_header("TC-NQS DEMONSTRATION")
        print("Transcorrelated Second-Quantized Neural Network Quantum State")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # Run all demonstration steps
            self.define_problem()
            self.solve_exact_fci()
            self.setup_neural_network()
            self.train_supervised()
            self.evaluate_energy()
            self.compare_wavefunctions()
            self.plot_results()

            # Final summary
            print_header("DEMONSTRATION SUMMARY")
            print_results("Problem", f"H2 molecule, {self.basis} basis")
            print_results("Bond length", self.bond_length, "Å")
            print_results("FCI Energy", self.fci_energy, "Hartree")
            print_results("NN Energy", self.nn_energy, "Hartree")

            abs_error = abs(self.nn_energy - self.fci_energy)
            rel_error = abs_error / abs(self.fci_energy) * 100

            print_results("Absolute Error", abs_error, "Hartree")
            print_results("Relative Error", rel_error, "%")

            # Success criteria
            if abs_error < 1e-3:  # 1 mHartree accuracy
                print("\n✓ SUCCESS: Neural network achieved chemical accuracy!")
            elif abs_error < 1e-2:  # 10 mHartree
                print("\n✓ GOOD: Neural network achieved reasonable accuracy.")
            else:
                print("\n⚠ WARNING: Neural network accuracy could be improved.")

            return True

        except Exception as e:
            print(f"\n❌ ERROR: Demonstration failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main function to run the TC-NQS demonstration."""
    print("Starting TC-NQS Demonstration...")

    # Create and run demonstration
    demo = TCNQSDemo(
        bond_length=0.74,  # H2 equilibrium bond length
        basis='sto-3g',    # Minimal basis set
        random_seed=42     # For reproducibility
    )

    success = demo.run_full_demo()

    if success:
        print("\nDemonstration completed successfully!")
    else:
        print("\nDemonstration encountered errors.")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
