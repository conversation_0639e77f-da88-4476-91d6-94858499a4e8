"""<PERSON><PERSON><PERSON><PERSON> 17-parameter Jastrow factor."""

from .sm7 import SM7


class SM17(SM7):
    """<PERSON><PERSON> 17-parameter Jastrow factor."""
    
    # Class-level coefficient table
    _coeff_table = {
      'He': {
          (0,0,1): 0.50000, (0,0,2): 0.09239, (0,0,3): -0.38664, (0,0,4): 0.95731,
          (2,0,0): 0.23208, (3,0,0): -0.45032, (4,0,0): 0.82777, (2,2,0): -4.15388,
          (2,0,2): 0.80622, (2,2,2): 10.19704, (4,0,2): -4.96259, (2,0,4): -1.35647,
          (4,2,2): -5.90907, (6,0,2): 0.90343, (4,0,4): 5.50739, (2,2,4): -0.03154,
          (2,0,6): -1.05186
      },
      'Li': {
          (0,0,1): 0.50000, (0,0,2): -0.01380, (0,0,3): -0.45313, (0,0,4): 0.71032,
          (2,0,0): 0.30248, (3,0,0): -0.52643, (4,0,0): 1.93859, (2,2,0): -5.14421,
          (2,0,2): 1.38788, (2,2,2): 9.01474, (4,0,2): -9.05453, (2,0,4): 0.03344,
          (4,2,2): 3.11255, (6,0,2): 2.59001, (4,0,4): 4.60054, (2,2,4): -8.58519,
          (2,0,6): -0.92355
      },
      'Be': {
          (0,0,1): 0.50000, (0,0,2): -0.06478, (0,0,3): -0.64088, (0,0,4): 1.37713,
          (2,0,0): 0.38220, (3,0,0): -0.40248, (4,0,0): 1.38175, (2,2,0): -5.23497,
          (2,0,2): 0.65456, (2,2,2): 13.83365, (4,0,2): -5.39941, (2,0,4): -2.66066,
          (4,2,2): -2.13267, (6,0,2): -0.80566, (4,0,4): 8.42055, (2,2,4): -4.73764,
          (2,0,6): -1.49529
      },
      'B': {
          (0,0,1): 0.50000, (0,0,2): -0.18185, (0,0,3): -0.26288, (0,0,4): 0.77686,
          (2,0,0): 0.31118, (3,0,0): -0.14403, (4,0,0): 0.51106, (2,2,0): -4.17149,
          (2,0,2): -0.45334, (2,2,2): 13.71788, (4,0,2): -1.54596, (2,0,4): -1.35381,
          (4,2,2): -3.73788, (6,0,2): -1.54388, (4,0,4): 5.64542, (2,2,4): -4.74009,
          (2,0,6): -1.35761
      },
      'C': {
          (0,0,1): 0.50000, (0,0,2): -0.23934, (0,0,3): -0.51729, (0,0,4): 1.02146,
          (2,0,0): 0.21610, (3,0,0): -0.02774, (4,0,0): 0.28173, (2,2,0): -3.71952,
          (2,0,2): -0.28184, (2,2,2): 13.13613, (4,0,2): -1.02414, (2,0,4): -1.25233,
          (4,2,2): -4.62167, (6,0,2): -1.32220, (4,0,4): 5.22850, (2,2,4): -4.17394,
          (2,0,6): -1.64963
      },
      'N': {
          (0,0,1): 0.50000, (0,0,2): -0.37065, (0,0,3): -0.60669, (0,0,4): 1.17279,
          (2,0,0): 0.07317, (3,0,0): 0.09880, (4,0,0): 0.13151, (2,2,0): -3.20953,
          (2,0,2): 0.40652, (2,2,2): 10.88170, (4,0,2): -1.58579, (2,0,4): -1.62012,
          (4,2,2): -4.92367, (6,0,2): -0.33974, (4,0,4): 4.77867, (2,2,4): -2.10343,
          (2,0,6): -1.61273
      },
      'O': {
          (0,0,1): 0.50000, (0,0,2): -0.49633, (0,0,3): -0.65730, (0,0,4): 1.33494,
          (2,0,0): 0.04858, (3,0,0): 0.09168, (4,0,0): 0.11964, (2,2,0): -3.32856,
          (2,0,2): 1.08465, (2,2,2): 10.00194, (4,0,2): -2.17626, (2,0,4): -2.40739,
          (4,2,2): -5.45914, (6,0,2): -0.08069, (4,0,4): 5.49467, (2,2,4): -0.24671,
          (2,0,6): -1.66943
      },
      'F': {
          (0,0,1): 0.50000, (0,0,2): -0.63266, (0,0,3): -0.51515, (0,0,4): 1.12960,
          (2,0,0): -0.02423, (3,0,0): 0.10417, (4,0,0): 0.10326, (2,2,0): -3.14642,
          (2,0,2): 1.79810, (2,2,2): 7.27033, (4,0,2): -3.43597, (2,0,4): -2.34889,
          (4,2,2): -3.63699, (6,0,2): 1.36655, (4,0,4): 4.48187, (2,2,4): -0.01859,
          (2,0,6): -1.26525
      },
      'Ne': {
          (0,0,1): 0.50000, (0,0,2): -0.80909, (0,0,3): -0.00219, (0,0,4): 0.59188,
          (2,0,0): -0.00567, (3,0,0): 0.14011, (4,0,0): -0.05671, (2,2,0): -3.33767,
          (2,0,2): 1.95097, (2,2,2): 6.83340, (4,0,2): -3.29231, (2,0,4): -2.44998,
          (4,2,2): -2.13029, (6,0,2): 2.25768, (4,0,4): 1.97951, (2,2,4): -2.09241,
          (2,0,6): 0.35493
      }
 }
    