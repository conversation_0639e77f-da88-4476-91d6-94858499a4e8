{"experiment_id": "b58daef9", "experiment_name": "Test_Polynomial_Subclassing", "best_overall": {"model_name": "Standard_GCNN", "energy_error": 1.681326580281599, "n_parameters": 152, "converged": false, "final_energy": -6.318673419718401, "exact_energy": -8.0}, "model_comparison": {"Standard_GCNN": {"energy_error": 1.681326580281599, "n_parameters": 152, "converged": false, "convergence_step": 0, "final_energy": -6.318673419718401, "relative_error": 0.2101658225351999, "quality": "Poor"}, "Standard_RBM": {"energy_error": 2.44632485971586, "n_parameters": 44, "converged": false, "convergence_step": 0, "final_energy": -5.55367514028414, "relative_error": 0.3057906074644825, "quality": "Poor"}}}