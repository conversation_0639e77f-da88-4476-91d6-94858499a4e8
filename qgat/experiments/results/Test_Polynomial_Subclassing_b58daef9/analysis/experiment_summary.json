{"experiment_id": "b58daef9", "experiment_name": "Test_Polynomial_Subclassing", "timestamp": "2025-08-23T13:40:33.280974", "total_experiments": 6, "successful_experiments": 2, "success_rate": 0.3333333333333333, "model_statistics": {"Standard_GCNN": {"total": 1, "successful": 1, "energy_errors": [1.681326580281599], "convergence_rates": [false], "mean_energy_error": 1.681326580281599, "min_energy_error": 1.681326580281599, "max_energy_error": 1.681326580281599, "success_rate": 1.0, "convergence_rate": 0.0}, "Standard_RBM": {"total": 1, "successful": 1, "energy_errors": [2.44632485971586], "convergence_rates": [false], "mean_energy_error": 2.44632485971586, "min_energy_error": 2.44632485971586, "max_energy_error": 2.44632485971586, "success_rate": 1.0, "convergence_rate": 0.0}, "Poly_GCNN_CP": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_GCNN_Sparse": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_RBM_CP": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_RBM_Sparse": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}}, "best_results": {"Standard_GCNN": {"model_name": "Standard_GCNN", "model_type": "gcnn", "problem_name": "heisenberg_1d", "system_size": 4, "success": true, "energy_error": 1.681326580281599, "relative_error": 0.2101658225351999, "converged": false, "convergence_step": 0, "n_parameters": 152, "total_time_s": 4.0017499923706055, "final_energy": -6.318673419718401, "exact_energy": -8.0}, "Standard_RBM": {"model_name": "Standard_RBM", "model_type": "rbm", "problem_name": "heisenberg_1d", "system_size": 4, "success": true, "energy_error": 2.44632485971586, "relative_error": 0.3057906074644825, "converged": false, "convergence_step": 0, "n_parameters": 44, "total_time_s": 1.649515151977539, "final_energy": -5.55367514028414, "exact_energy": -8.0}}}