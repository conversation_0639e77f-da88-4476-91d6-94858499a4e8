{"experiment": {"name": "Test_Polynomial_Subclassing", "description": "Test the new polynomial subclassing approach with poly_gcnn and poly_rbm models"}, "models": [{"name": "Standard_GCNN", "type": "gcnn", "config": {"layers": 2, "features": 4, "param_dtype": "float64"}}, {"name": "Standard_RBM", "type": "rbm", "config": {"n_hidden": 4, "param_dtype": "float64"}}, {"name": "Poly_GCNN_CP", "type": "poly_gcnn", "config": {"layers": 2, "features": 4, "degree": 2, "poly_class": "CP", "poly_layers": 1, "param_dtype": "float64"}}, {"name": "Poly_GCNN_Sparse", "type": "poly_gcnn", "config": {"layers": 2, "features": 4, "degree": 2, "poly_class": "CP_sparse_LU", "poly_layers": 1, "param_dtype": "float64"}}, {"name": "Poly_RBM_CP", "type": "poly_rbm", "config": {"n_hidden": 4, "degree": 2, "poly_class": "CP", "param_dtype": "float64"}}, {"name": "Poly_RBM_Sparse", "type": "poly_rbm", "config": {"n_hidden": 4, "degree": 3, "poly_class": "CP_sparse_degree", "param_dtype": "float64"}}], "optimizers": [{"name": "<PERSON>_<PERSON>", "type": "adam", "config": {"learning_rate": 0.001}}], "samplers": [{"name": "Metropolis_Standard", "type": "metropolis", "config": {"n_chains": 8, "n_samples": 512}}], "physics_systems": [{"name": "Heisenberg_1D_4sites", "type": "spin", "sizes": [4], "config": {"model": "<PERSON><PERSON><PERSON>", "dimension": 1, "J": 1.0, "h": 0.0, "pbc": true}}], "statistical": {"n_runs": 1, "confidence_level": 0.95, "significance_threshold": 0.05}, "execution": {"parallel": false, "max_workers": 1, "n_optimization_steps": 100, "early_stopping": {"patience": 50, "tolerance": "1e-4"}}}