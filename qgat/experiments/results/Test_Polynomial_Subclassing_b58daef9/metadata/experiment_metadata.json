{"experiment_id": "b58daef9", "experiment_name": "Test_Polynomial_Subclassing", "timestamp": "2025-08-23T13:40:27.181448", "system_info": {"platform": "<PERSON>", "platform_version": "macOS-15.6-arm64-arm-64bit-Mach-O", "python_version": "3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 11:23:37) [Clang 14.0.6 ]", "cpu_count": 12, "memory_gb": 24.0, "hostname": "MacBook-Pro-Ashish.local", "username": "ashish"}, "git_info": {"commit_hash": null, "branch": null, "is_dirty": false, "remote_url": null, "commit_message": null, "commit_date": null}, "package_info": {"jax_version": "0.5.3", "netket_version": "3.19.0", "flax_version": "0.10.6", "numpy_version": "2.2.5"}, "working_directory": "/Users/<USER>/Projects/Quantum/qgat"}