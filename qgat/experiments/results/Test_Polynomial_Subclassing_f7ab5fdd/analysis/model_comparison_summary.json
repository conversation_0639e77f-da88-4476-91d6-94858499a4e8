{"experiment_id": "f7ab5fdd", "experiment_name": "Test_Polynomial_Subclassing", "best_overall": {"model_name": "Standard_GCNN", "energy_error": 1.661895440516716, "n_parameters": 152, "converged": false, "final_energy": -6.338104559483284, "exact_energy": -8.0}, "model_comparison": {"Standard_GCNN": {"energy_error": 1.661895440516716, "n_parameters": 152, "converged": false, "convergence_step": 0, "final_energy": -6.338104559483284, "relative_error": 0.2077369300645895, "quality": "Poor"}, "Standard_RBM": {"energy_error": 2.2892280514000074, "n_parameters": 44, "converged": false, "convergence_step": 0, "final_energy": -5.710771948599993, "relative_error": 0.2861535064250009, "quality": "Poor"}}}