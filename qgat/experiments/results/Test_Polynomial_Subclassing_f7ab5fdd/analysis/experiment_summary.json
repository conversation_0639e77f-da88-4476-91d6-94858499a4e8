{"experiment_id": "f7ab5fdd", "experiment_name": "Test_Polynomial_Subclassing", "timestamp": "2025-08-23T13:38:16.396973", "total_experiments": 6, "successful_experiments": 2, "success_rate": 0.3333333333333333, "model_statistics": {"Standard_GCNN": {"total": 1, "successful": 1, "energy_errors": [1.661895440516716], "convergence_rates": [false], "mean_energy_error": 1.661895440516716, "min_energy_error": 1.661895440516716, "max_energy_error": 1.661895440516716, "success_rate": 1.0, "convergence_rate": 0.0}, "Standard_RBM": {"total": 1, "successful": 1, "energy_errors": [2.2892280514000074], "convergence_rates": [false], "mean_energy_error": 2.2892280514000074, "min_energy_error": 2.2892280514000074, "max_energy_error": 2.2892280514000074, "success_rate": 1.0, "convergence_rate": 0.0}, "Poly_GCNN_CP": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_GCNN_Sparse": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_RBM_CP": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}, "Poly_RBM_Sparse": {"total": 1, "successful": 0, "energy_errors": [], "convergence_rates": [], "mean_energy_error": Infinity, "min_energy_error": Infinity, "max_energy_error": Infinity, "success_rate": 0.0, "convergence_rate": 0}}, "best_results": {"Standard_GCNN": {"model_name": "Standard_GCNN", "model_type": "gcnn", "problem_name": "heisenberg_1d", "system_size": 4, "success": true, "energy_error": 1.661895440516716, "relative_error": 0.2077369300645895, "converged": false, "convergence_step": 0, "n_parameters": 152, "total_time_s": 4.041782855987549, "final_energy": -6.338104559483284, "exact_energy": -8.0}, "Standard_RBM": {"model_name": "Standard_RBM", "model_type": "rbm", "problem_name": "heisenberg_1d", "system_size": 4, "success": true, "energy_error": 2.2892280514000074, "relative_error": 0.2861535064250009, "converged": false, "convergence_step": 0, "n_parameters": 44, "total_time_s": 1.653670072555542, "final_energy": -5.710771948599993, "exact_energy": -8.0}}}