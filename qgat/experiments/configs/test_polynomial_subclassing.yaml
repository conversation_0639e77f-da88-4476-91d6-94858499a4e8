# Test Polynomial Subclassing Approach
# Verify that the new polynomial models work correctly

experiment:
  name: "Test_Polynomial_Subclassing"
  description: "Test the new polynomial subclassing approach with poly_gcnn and poly_rbm models"

# Test both new polynomial models
models:
  # Standard models for comparison
  - name: "Standard_GCNN"
    type: "gcnn"
    config:
      layers: 2
      features: 4
      param_dtype: "float64"

  - name: "Standard_RBM"
    type: "rbm"
    config:
      n_hidden: 4
      param_dtype: "float64"

  # New polynomial models (subclassing approach)
  - name: "Poly_GCNN_CP"
    type: "poly_gcnn"
    config:
      layers: 2
      features: 4
      degree: 2
      poly_class: "CP"
      poly_layers: 1
      param_dtype: "float64"

  - name: "Poly_GCNN_Sparse"
    type: "poly_gcnn"
    config:
      layers: 2
      features: 4
      degree: 2
      poly_class: "CP_sparse_LU"
      poly_layers: 1
      param_dtype: "float64"

  - name: "Poly_RBM_CP"
    type: "poly_rbm"
    config:
      n_hidden: 4
      degree: 2
      poly_class: "CP"
      param_dtype: "float64"

  - name: "Poly_RBM_Sparse"
    type: "poly_rbm"
    config:
      n_hidden: 4
      degree: 3
      poly_class: "CP_sparse_degree"
      param_dtype: "float64"

# Standard optimizer
optimizers:
  - name: "Adam_Conservative"
    type: "adam"
    config:
      learning_rate: 0.001

# Standard sampler
samplers:
  - name: "Metropolis_Standard"
    type: "metropolis"
    config:
      n_chains: 8
      n_samples: 512

# Simple test system
physics_systems:
  - name: "Heisenberg_1D_4sites"
    type: "spin"
    sizes: [4]
    config:
      model: "heisenberg"
      dimension: 1
      J: 1.0
      h: 0.0
      pbc: true

# Single run for testing
statistical:
  n_runs: 1
  confidence_level: 0.95

# Conservative execution settings
execution:
  parallel: false
  max_workers: 1
  n_optimization_steps: 100  # Short test
  early_stopping:
    patience: 50
    tolerance: 1e-4

experiment_grid:
  combinations: "all"
