"""
Utility functions for lattice fermion systems.

This module provides utility functions for:
- Creating lattice adjacency matrices for fermion systems
- Converting between different fermion representations
- Visualizing fermion configurations
- Handling fermionic operators and symmetries
"""

import jax.numpy as jnp
import numpy as np
import matplotlib.pyplot as plt
import netket as nk
from typing import Optional, Tuple, Dict, Any, List
from netket.utils.types import Array


def create_lattice_fermion_hilbert(model_name: str, system_size: int, **kwargs) -> nk.hilbert.AbstractHilbert:
    """
    Create Hilbert space for lattice fermion systems.

    Args:
        model_name: Name of the model ("hubbard", "tj", "spinless_fermions")
        system_size: Linear size of the lattice
        **kwargs: Additional parameters (n_fermions, etc.)

    Returns:
        NetKet Hilbert space for the fermionic system
    """
    n_sites = system_size * system_size  # Assume square lattice

    if model_name == "hubbard":
        # Hubbard model with spin-1/2 fermions
        n_fermions = kwargs.get('n_fermions', n_sites)  # Default to half-filling
        return nk.hilbert.SpinOrbitalFermions(n_sites, s=1/2, n_fermions=n_fermions)

    elif model_name == "tj":
        # t-J model with spin-1/2 fermions (no double occupancy)
        n_fermions = kwargs.get('n_fermions', int(0.875 * n_sites))  # Slightly doped
        return nk.hilbert.SpinOrbitalFermions(n_sites, s=1/2, n_fermions=n_fermions)

    elif model_name == "spinless_fermions":
        # Spinless fermions
        n_fermions = kwargs.get('n_fermions', n_sites // 2)  # Half-filling
        return nk.hilbert.Fock(n_max=1, N=n_sites, n_particles=n_fermions)

    else:
        raise ValueError(f"Unknown lattice fermion model: {model_name}")


def create_square_lattice_fermion_adjacency(n_sites: int,
                                          lattice_type: str = "square",
                                          L: Optional[int] = None,
                                          pbc: bool = True) -> Array:
    """
    Create adjacency matrix for fermion systems on various lattices.
    
    Args:
        n_sites: Number of lattice sites
        lattice_type: Type of lattice ("square", "triangular", "honeycomb")
        L: Linear size (if None, inferred from n_sites)
        pbc: Whether to use periodic boundary conditions
        
    Returns:
        Adjacency matrix [n_sites, n_sites]
    """
    if lattice_type == "square":
        if L is None:
            L = int(jnp.sqrt(n_sites))
            if L * L != n_sites:
                raise ValueError(f"For square lattice, n_sites must be a perfect square, got {n_sites}")
        
        return _create_square_lattice_adjacency(L, pbc)
    
    elif lattice_type == "triangular":
        if L is None:
            L = int(jnp.sqrt(n_sites))
            if L * L != n_sites:
                raise ValueError(f"For triangular lattice, n_sites must be a perfect square, got {n_sites}")
        
        return _create_triangular_lattice_adjacency(L, pbc)
    
    elif lattice_type == "honeycomb":
        return _create_honeycomb_lattice_adjacency(n_sites)
    
    else:
        raise ValueError(f"Unknown lattice type: {lattice_type}")


def _create_square_lattice_adjacency(L: int, pbc: bool = True) -> Array:
    """Create adjacency matrix for square lattice."""
    n_sites = L * L

    # Create adjacency matrix using numpy first, then convert to JAX
    adjacency_np = np.zeros((n_sites, n_sites), dtype=np.float64)

    def site_index(i: int, j: int) -> int:
        return i * L + j

    for i in range(L):
        for j in range(L):
            site = site_index(i, j)

            # Right neighbor
            if j < L - 1:
                neighbor = site_index(i, j + 1)
                adjacency_np[site, neighbor] = 1.0
                adjacency_np[neighbor, site] = 1.0
            elif pbc:
                neighbor = site_index(i, 0)
                adjacency_np[site, neighbor] = 1.0
                adjacency_np[neighbor, site] = 1.0

            # Down neighbor
            if i < L - 1:
                neighbor = site_index(i + 1, j)
                adjacency_np[site, neighbor] = 1.0
                adjacency_np[neighbor, site] = 1.0
            elif pbc:
                neighbor = site_index(0, j)
                adjacency_np[site, neighbor] = 1.0
                adjacency_np[neighbor, site] = 1.0

    # Convert to JAX array
    return jnp.array(adjacency_np)


def _create_triangular_lattice_adjacency(L: int, pbc: bool = True) -> Array:
    """Create adjacency matrix for triangular lattice."""
    n_sites = L * L
    adjacency_np = np.zeros((n_sites, n_sites), dtype=np.float64)
    
    def site_index(i: int, j: int) -> int:
        return i * L + j
    
    for i in range(L):
        for j in range(L):
            site = site_index(i, j)
            
            # Three nearest neighbors in triangular lattice
            neighbors = []
            
            # Right neighbor
            if j < L - 1:
                neighbors.append(site_index(i, j + 1))
            elif pbc:
                neighbors.append(site_index(i, 0))
            
            # Down neighbor
            if i < L - 1:
                neighbors.append(site_index(i + 1, j))
            elif pbc:
                neighbors.append(site_index(0, j))
            
            # Diagonal neighbor (down-right)
            if i < L - 1 and j < L - 1:
                neighbors.append(site_index(i + 1, j + 1))
            elif pbc:
                if i < L - 1 and j == L - 1:
                    neighbors.append(site_index(i + 1, 0))
                elif i == L - 1 and j < L - 1:
                    neighbors.append(site_index(0, j + 1))
                elif i == L - 1 and j == L - 1:
                    neighbors.append(site_index(0, 0))
            
            # Set adjacency
            for neighbor in neighbors:
                adjacency_np[site, neighbor] = 1.0
                adjacency_np[neighbor, site] = 1.0

    return jnp.array(adjacency_np)


def _create_honeycomb_lattice_adjacency(n_sites: int) -> Array:
    """Create adjacency matrix for honeycomb lattice."""
    # Simplified honeycomb lattice
    adjacency_np = np.zeros((n_sites, n_sites), dtype=np.float64)
    
    # For honeycomb, we need two sublattices A and B
    # Each A site connects to 3 B sites and vice versa
    n_a = n_sites // 2
    n_b = n_sites - n_a
    
    for i in range(n_a):
        for j in range(n_b):
            a_site = i
            b_site = n_a + j
            
            # Connect A to B sites based on honeycomb geometry
            if abs(i - j) <= 1:  # Simplified connection rule
                adjacency_np[a_site, b_site] = 1.0
                adjacency_np[b_site, a_site] = 1.0

    return jnp.array(adjacency_np)


def create_triangular_lattice_fermion_adjacency(L: int, pbc: bool = True) -> Array:
    """Create adjacency matrix for triangular lattice fermion system."""
    return _create_triangular_lattice_adjacency(L, pbc)


def create_honeycomb_lattice_fermion_adjacency(n_sites: int) -> Array:
    """Create adjacency matrix for honeycomb lattice fermion system."""
    return _create_honeycomb_lattice_adjacency(n_sites)


def fermion_occupation_to_features(occupation: Array, 
                                 has_spin: bool = True) -> Dict[str, Array]:
    """
    Convert fermion occupation to various feature representations.
    
    Args:
        occupation: Fermion occupation vector [n_orbitals]
        has_spin: Whether the system has spin
        
    Returns:
        Dictionary with different feature representations
    """
    if has_spin:
        n_sites = len(occupation) // 2
        n_up = occupation[:n_sites]
        n_down = occupation[n_sites:]
        
        features = {
            'site_occupations': jnp.stack([n_up, n_down], axis=1),  # [n_sites, 2]
            'charge_density': n_up + n_down,  # [n_sites]
            'spin_density': n_up - n_down,    # [n_sites]
            'double_occupancy': n_up * n_down,  # [n_sites]
            'total_charge': jnp.sum(n_up + n_down),
            'total_spin': jnp.sum(n_up - n_down),
            'n_doubly_occupied': jnp.sum(n_up * n_down)
        }
    else:
        # Spinless fermions
        features = {
            'site_occupations': occupation.reshape(-1, 1),  # [n_sites, 1]
            'charge_density': occupation,  # [n_sites]
            'total_charge': jnp.sum(occupation),
            'n_occupied_sites': jnp.sum(occupation)
        }
    
    return features


def visualize_fermion_configuration(occupation: Array,
                                  lattice_type: str = "square",
                                  L: Optional[int] = None,
                                  has_spin: bool = True,
                                  save_path: Optional[str] = None) -> plt.Figure:
    """
    Visualize fermion configuration on a lattice.
    
    Args:
        occupation: Fermion occupation vector
        lattice_type: Type of lattice
        L: Linear size of lattice
        has_spin: Whether system has spin
        save_path: Optional path to save figure
        
    Returns:
        Matplotlib figure
    """
    if has_spin:
        n_sites = len(occupation) // 2
        n_up = occupation[:n_sites]
        n_down = occupation[n_sites:]
    else:
        n_sites = len(occupation)
        n_up = occupation
        n_down = jnp.zeros_like(occupation)
    
    if L is None:
        L = int(jnp.sqrt(n_sites))
    
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
    
    # Reshape for 2D visualization
    if lattice_type == "square":
        n_up_2d = n_up.reshape(L, L)
        n_down_2d = n_down.reshape(L, L)
        charge_density_2d = (n_up + n_down).reshape(L, L)
        
        # Spin-up configuration
        im1 = ax1.imshow(n_up_2d, cmap='Reds', vmin=0, vmax=1)
        ax1.set_title('Spin-Up Density')
        ax1.set_xlabel('x')
        ax1.set_ylabel('y')
        plt.colorbar(im1, ax=ax1)
        
        # Spin-down configuration
        im2 = ax2.imshow(n_down_2d, cmap='Blues', vmin=0, vmax=1)
        ax2.set_title('Spin-Down Density')
        ax2.set_xlabel('x')
        ax2.set_ylabel('y')
        plt.colorbar(im2, ax=ax2)
        
        # Total charge density
        im3 = ax3.imshow(charge_density_2d, cmap='viridis', vmin=0, vmax=2)
        ax3.set_title('Total Charge Density')
        ax3.set_xlabel('x')
        ax3.set_ylabel('y')
        plt.colorbar(im3, ax=ax3)
    
    else:
        # For non-square lattices, use scatter plot
        positions = _get_lattice_positions(n_sites, lattice_type, L)
        
        ax1.scatter(positions[:, 0], positions[:, 1], c=n_up, 
                   cmap='Reds', s=100, vmin=0, vmax=1)
        ax1.set_title('Spin-Up Density')
        
        ax2.scatter(positions[:, 0], positions[:, 1], c=n_down,
                   cmap='Blues', s=100, vmin=0, vmax=1)
        ax2.set_title('Spin-Down Density')
        
        ax3.scatter(positions[:, 0], positions[:, 1], c=n_up + n_down,
                   cmap='viridis', s=100, vmin=0, vmax=2)
        ax3.set_title('Total Charge Density')
    
    plt.tight_layout()
    
    if save_path:
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Fermion configuration plot saved to {save_path}")
    
    return fig


def _get_lattice_positions(n_sites: int, lattice_type: str, L: int) -> Array:
    """Get 2D positions for lattice sites."""
    if lattice_type == "square":
        positions = []
        for i in range(L):
            for j in range(L):
                positions.append([j, L - 1 - i])  # Standard x-y coordinates
        return jnp.array(positions)
    
    elif lattice_type == "triangular":
        positions = []
        for i in range(L):
            for j in range(L):
                x = j + 0.5 * i
                y = i * jnp.sqrt(3) / 2
                positions.append([x, y])
        return jnp.array(positions)
    
    elif lattice_type == "honeycomb":
        # Simplified honeycomb positions
        positions = []
        n_a = n_sites // 2
        
        # A sublattice
        for i in range(n_a):
            x = i % int(jnp.sqrt(n_a))
            y = i // int(jnp.sqrt(n_a))
            positions.append([x, y])
        
        # B sublattice (offset)
        for i in range(n_sites - n_a):
            x = i % int(jnp.sqrt(n_sites - n_a)) + 0.5
            y = i // int(jnp.sqrt(n_sites - n_a)) + 0.5
            positions.append([x, y])
        
        return jnp.array(positions)

    else:
        # Default to square positions
        return _get_lattice_positions(n_sites, "square", L)


class FermionSystemFactory:
    """
    Factory for creating fermionic lattice systems.

    This is a convenience wrapper around the PhysicsFactory for backward compatibility
    with notebooks that expect a FermionSystemFactory.
    """

    @staticmethod
    def create(model: str, lattice: str, L: int, t: float = 1.0, U: float = 2.0,
               n_particles: int = None, pbc: bool = True, **kwargs):
        """
        Create a fermionic lattice system.

        Args:
            model: Model type ('hubbard', 't_j', 'extended_hubbard')
            lattice: Lattice type ('1d_chain', '2d_square', 'triangular')
            L: Lattice size
            t: Hopping parameter
            U: On-site interaction
            n_particles: Number of particles
            pbc: Periodic boundary conditions

        Returns:
            Dictionary with system information
        """
        # Import here to avoid circular imports
        try:
            from models.registry import PhysicsFactory
        except ImportError:
            from ...models.registry import PhysicsFactory

        # Map model names
        model_map = {
            'hubbard': 'hubbard',
            't_j': 'tj',
            'extended_hubbard': 'hubbard'  # Use hubbard as fallback
        }

        # Map lattice names to system size
        if lattice == '1d_chain':
            system_size = L
        elif lattice in ['2d_square', 'triangular']:
            system_size = L  # L is the linear size
        else:
            system_size = L

        # Create config for PhysicsFactory
        config = {
            'model': model_map.get(model, 'hubbard'),
            't': t,
            'U': U,
            'n_fermions': n_particles,
            'pbc': pbc,
            **kwargs
        }

        try:
            # Use PhysicsFactory to create the system
            system = PhysicsFactory.create('lattice_fermion', config, system_size=system_size)

            # Add additional fields expected by the notebook
            system.update({
                'n_sites': system_size * system_size if lattice != '1d_chain' else system_size,
                'n_particles': n_particles or system_size,
                'problem_name': f"{model}_{lattice}"
            })

            return system

        except Exception as e:
            # Fallback to simple system creation
            print(f"Warning: PhysicsFactory failed ({e}), creating simple fallback system")

            # Create simple fallback system
            if lattice == '1d_chain':
                n_sites = L
                graph = nk.graph.Chain(L, pbc=pbc)
            else:
                n_sites = L * L
                graph = nk.graph.Square(L, pbc=pbc)

            if n_particles is None:
                n_particles = n_sites // 2

            # Create simple Hilbert space
            hilbert = nk.hilbert.Fock(n_max=1, N=n_sites, n_particles=n_particles)

            # Create simple Hamiltonian (Bose-Hubbard as approximation)
            hamiltonian = nk.operator.BoseHubbard(
                hilbert=hilbert,
                graph=graph,
                U=U,
                t=t
            )

            return {
                'hilbert': hilbert,
                'hamiltonian': hamiltonian,
                'problem_name': f"{model}_{lattice}",
                'n_sites': n_sites,
                'n_particles': n_particles
            }


def compute_fermion_correlations(occupation: Array,
                                adjacency: Array,
                                has_spin: bool = True) -> Dict[str, float]:
    """
    Compute various correlation functions for fermion systems.
    
    Args:
        occupation: Fermion occupation vector
        adjacency: Lattice adjacency matrix
        has_spin: Whether system has spin
        
    Returns:
        Dictionary of correlation functions
    """
    if has_spin:
        n_sites = len(occupation) // 2
        n_up = occupation[:n_sites]
        n_down = occupation[n_sites:]
        
        # Nearest neighbor correlations
        nn_charge_corr = 0.0
        nn_spin_corr = 0.0
        nn_pairs = 0
        
        for i in range(n_sites):
            for j in range(n_sites):
                if adjacency[i, j] > 0:
                    # Charge correlation
                    charge_i = n_up[i] + n_down[i]
                    charge_j = n_up[j] + n_down[j]
                    nn_charge_corr += charge_i * charge_j
                    
                    # Spin correlation
                    spin_i = n_up[i] - n_down[i]
                    spin_j = n_up[j] - n_down[j]
                    nn_spin_corr += spin_i * spin_j
                    
                    nn_pairs += 1
        
        if nn_pairs > 0:
            nn_charge_corr /= nn_pairs
            nn_spin_corr /= nn_pairs
        
        correlations = {
            'nn_charge_correlation': float(nn_charge_corr),
            'nn_spin_correlation': float(nn_spin_corr),
            'total_charge': float(jnp.sum(n_up + n_down)),
            'total_spin': float(jnp.sum(n_up - n_down)),
            'double_occupancy': float(jnp.sum(n_up * n_down)),
            'kinetic_energy_scale': float(nn_charge_corr)  # Approximate
        }
    
    else:
        # Spinless fermions
        nn_corr = 0.0
        nn_pairs = 0
        
        for i in range(len(occupation)):
            for j in range(len(occupation)):
                if adjacency[i, j] > 0:
                    nn_corr += occupation[i] * occupation[j]
                    nn_pairs += 1
        
        if nn_pairs > 0:
            nn_corr /= nn_pairs
        
        correlations = {
            'nn_correlation': float(nn_corr),
            'total_charge': float(jnp.sum(occupation)),
            'kinetic_energy_scale': float(nn_corr)
        }
    
    return correlations
