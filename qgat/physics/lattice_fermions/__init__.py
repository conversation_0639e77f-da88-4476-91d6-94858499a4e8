"""
Lattice Fermion GAT Implementation

This module provides Graph Attention Networks for lattice fermion systems
such as the Hubbard model, t-J model, and other strongly correlated
electron systems.

Components:
- LatticeFermionGAT: Main GAT implementation for lattice fermions
- Utility functions for lattice construction and fermion operators
- Integration with NetKet's fermionic operators and Hilbert spaces
- Hamiltonian definitions for common lattice fermion models
"""

from .lattice_fermion_gat import LatticeFermionGAT, NetKetLatticeFermionGAT
from .utils import (
    create_lattice_fermion_hilbert,
    create_square_lattice_fermion_adjacency,
    create_triangular_lattice_fermion_adjacency,
    create_honeycomb_lattice_fermion_adjacency,
    fermion_occupation_to_features,
    visualize_fermion_configuration,
    FermionSystemFactory
)
from .hamiltonians import (
    create_hubbard_hamiltonian,
    create_tj_hamiltonian,
    create_spinless_fermion_hamiltonian,
    get_exact_lattice_fermion_energies
)

__all__ = [
    "LatticeFermionGAT",
    "NetKetLatticeFermionGAT",
    "create_lattice_fermion_hilbert",
    "create_square_lattice_fermion_adjacency",
    "create_triangular_lattice_fermion_adjacency",
    "create_honeycomb_lattice_fermion_adjacency",
    "fermion_occupation_to_features",
    "visualize_fermion_configuration",
    "FermionSystemFactory",
    "create_hubbard_hamiltonian",
    "create_tj_hamiltonian",
    "create_spinless_fermion_hamiltonian",
    "get_exact_lattice_fermion_energies"
]
