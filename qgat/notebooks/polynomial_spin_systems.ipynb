{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial Neural Networks for Spin Systems\n", "\n", "This notebook demonstrates the new polynomial subclassing approach for quantum spin systems. You can easily compare standard and polynomial variants of neural networks.\n", "\n", "## Features:\n", "- **Easy Model Selection**: Choose from standard and polynomial variants\n", "- **Interactive Configuration**: Modify parameters directly in the notebook\n", "- **Real-time Visualization**: See training progress and results\n", "- **Automatic Comparison**: Compare multiple models side-by-side"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 'physics.spin_systems'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 15\u001b[39m\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Import QGAT components\u001b[39;00m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmodels\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ModelFactory\n\u001b[32m---> \u001b[39m\u001b[32m15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mphysics\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mspin_systems\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m SpinSystemFactory\n\u001b[32m     16\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mground_state\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mexact_solver\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ExactGroundStateSolver\n\u001b[32m     18\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ Imports successful!\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'physics.spin_systems'"]}], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from typing import Dict, List, Any\n", "\n", "# Import QGAT components\n", "from models import ModelFactory\n", "from physics.spin_systems import SpinSystemFactory\n", "from ground_state.exact_solver import ExactGroundStateSolver\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Configure Your Experiment\n", "\n", "Modify the parameters below to customize your experiment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# =============================================================================\n", "# EXPERIMENT CONFIGURATION - Modify these parameters\n", "# =============================================================================\n", "\n", "# Physics system configuration\n", "SYSTEM_CONFIG = {\n", "    'model': 'heisenberg',     # 'heisenberg', 'ising', 'xy'\n", "    'dimension': 1,            # 1D or 2D\n", "    'system_size': 6,          # Number of spins\n", "    'J': 1.0,                  # Coupling strength\n", "    'h': 0.0,                  # Magnetic field\n", "    'pbc': True                # Periodic boundary conditions\n", "}\n", "\n", "# Models to compare (you can add/remove models)\n", "MODELS_TO_TEST = [\n", "    {\n", "        'name': 'Standard_RBM',\n", "        'type': 'rbm',\n", "        'config': {\n", "            'n_hidden': 8,\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_CP',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 8,\n", "            'degree': 2,\n", "            'poly_class': 'CP',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_Sparse',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 8,\n", "            'degree': 3,\n", "            'poly_class': 'CP_sparse_LU',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Standard_GCNN',\n", "        'type': 'gcnn',\n", "        'config': {\n", "            'layers': 2,\n", "            'features': 8,\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_GCNN',\n", "        'type': 'poly_gcnn',\n", "        'config': {\n", "            'layers': 2,\n", "            'features': 8,\n", "            'degree': 2,\n", "            'poly_class': 'CP',\n", "            'param_dtype': 'float64'\n", "        }\n", "    }\n", "]\n", "\n", "# Training configuration\n", "TRAINING_CONFIG = {\n", "    'n_optimization_steps': 500,\n", "    'learning_rate': 0.001,\n", "    'n_chains': 16,\n", "    'n_samples': 1024\n", "}\n", "\n", "print(f\"🎯 Configured experiment for {SYSTEM_CONFIG['model']} model\")\n", "print(f\"   System size: {SYSTEM_CONFIG['system_size']} spins\")\n", "print(f\"   Models to test: {len(MODELS_TO_TEST)}\")\n", "print(f\"   Training steps: {TRAINING_CONFIG['n_optimization_steps']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Setup Physics System and Exact Solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the physics system\n", "physics_system = SpinSystemFactory.create(\n", "    model=SYSTEM_CONFIG['model'],\n", "    dimension=SYSTEM_CONFIG['dimension'],\n", "    system_size=SYSTEM_CONFIG['system_size'],\n", "    J=SYSTEM_CONFIG['J'],\n", "    h=SYSTEM_CONFIG['h'],\n", "    pbc=SYSTEM_CONFIG['pbc']\n", ")\n", "\n", "print(f\"✅ Created {SYSTEM_CONFIG['model']} system:\")\n", "print(f\"   Hilbert space: {physics_system['hilbert']}\")\n", "print(f\"   Hamiltonian: {physics_system['hamiltonian']}\")\n", "\n", "# Compute exact ground state\n", "print(\"\\n🔍 Computing exact ground state...\")\n", "exact_solver = ExactGroundStateSolver()\n", "exact_result = exact_solver.solve(physics_system['hamiltonian'])\n", "\n", "print(f\"✅ Exact ground state energy: {exact_result['energy']:.8f}\")\n", "print(f\"   Hilbert space size: {exact_result['hilbert_size']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Create and Compare Models\n", "\n", "This section creates all the models and compares their architectures:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create models and store information\n", "models_info = []\n", "\n", "print(\"🧠 Creating Models:\")\n", "print(\"=\" * 50)\n", "\n", "for model_config in MODELS_TO_TEST:\n", "    try:\n", "        # Create model\n", "        model, param_count = ModelFactory.create(\n", "            type_name=model_config['type'],\n", "            config=model_config['config'],\n", "            physics_type='spin',\n", "            system_size=SYSTEM_CONFIG['system_size'],\n", "            problem_name=physics_system['problem_name'],\n", "            hilbert=physics_system['hilbert'],\n", "            random_seed=42\n", "        )\n", "        \n", "        models_info.append({\n", "            'name': model_config['name'],\n", "            'type': model_config['type'],\n", "            'model': model,\n", "            'param_count': param_count,\n", "            'config': model_config['config']\n", "        })\n", "        \n", "        print(f\"✅ {model_config['name']}: {param_count:,} parameters\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Failed to create {model_config['name']}: {e}\")\n", "\n", "print(f\"\\n📊 Successfully created {len(models_info)} models\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Architecture Comparison\n", "\n", "Let's visualize the parameter counts and architectures:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create parameter comparison plot\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Parameter count comparison\n", "names = [info['name'] for info in models_info]\n", "param_counts = [info['param_count'] for info in models_info]\n", "colors = ['blue' if 'Standard' in name else 'red' for name in names]\n", "\n", "bars = ax1.bar(range(len(names)), param_counts, color=colors, alpha=0.7)\n", "ax1.set_xlabel('Model')\n", "ax1.set_ylabel('Parameter Count')\n", "ax1.set_title('Model Parameter Comparison')\n", "ax1.set_xticks(range(len(names)))\n", "ax1.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "\n", "# Add value labels on bars\n", "for bar, count in zip(bars, param_counts):\n", "    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(param_counts)*0.01,\n", "             f'{count}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Model type distribution\n", "model_types = {}\n", "for info in models_info:\n", "    model_type = info['type']\n", "    if model_type not in model_types:\n", "        model_types[model_type] = 0\n", "    model_types[model_type] += 1\n", "\n", "ax2.pie(model_types.values(), labels=model_types.keys(), autopct='%1.0f%%', startangle=90)\n", "ax2.set_title('Model Type Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print detailed comparison\n", "print(\"\\n📋 Detailed Model Comparison:\")\n", "print(\"-\" * 80)\n", "print(f\"{'Model':<25} {'Type':<15} {'Parameters':<12} {'Key Features':<25}\")\n", "print(\"-\" * 80)\n", "\n", "for info in models_info:\n", "    config = info['config']\n", "    if 'degree' in config:\n", "        features = f\"degree={config['degree']}, {config.get('poly_class', 'N/A')}\"\n", "    elif 'n_hidden' in config:\n", "        features = f\"hidden={config['n_hidden']}\"\n", "    elif 'layers' in config:\n", "        features = f\"layers={config['layers']}, features={config['features']}\"\n", "    else:\n", "        features = \"N/A\"\n", "    \n", "    print(f\"{info['name']:<25} {info['type']:<15} {info['param_count']:<12,} {features:<25}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Training Function\n", "\n", "Define a function to train models and track progress:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_model(model_info, physics_system, exact_energy, training_config):\n", "    \"\"\"Train a single model and return training history.\"\"\"\n", "    \n", "    print(f\"\\n🚀 Training {model_info['name']}...\")\n", "    \n", "    # Create sampler\n", "    sampler = nk.sampler.MetropolisLocal(\n", "        hilbert=physics_system['hilbert'],\n", "        n_chains=training_config['n_chains']\n", "    )\n", "    \n", "    # Create variational state\n", "    vs = nk.vqs.MCState(\n", "        sampler=sampler,\n", "        model=model_info['model'],\n", "        n_samples=training_config['n_samples']\n", "    )\n", "    \n", "    # Create optimizer\n", "    optimizer = nk.optimizer.<PERSON>(learning_rate=training_config['learning_rate'])\n", "    \n", "    # Create ground state solver\n", "    gs = nk.VMC(\n", "        hamiltonian=physics_system['hamiltonian'],\n", "        optimizer=optimizer,\n", "        variational_state=vs\n", "    )\n", "    \n", "    # Training history\n", "    history = {\n", "        'steps': [],\n", "        'energies': [],\n", "        'errors': [],\n", "        'variances': []\n", "    }\n", "    \n", "    # Training loop with progress tracking\n", "    try:\n", "        for step in range(training_config['n_optimization_steps']):\n", "            # Run optimization step\n", "            gs.run(n_iter=1)\n", "            \n", "            # Get current energy\n", "            energy = gs.energy.mean.real\n", "            variance = gs.energy.variance.real\n", "            error = abs(energy - exact_energy)\n", "            \n", "            # Store history\n", "            history['steps'].append(step)\n", "            history['energies'].append(energy)\n", "            history['errors'].append(error)\n", "            history['variances'].append(variance)\n", "            \n", "            # Print progress every 50 steps\n", "            if step % 50 == 0 or step == training_config['n_optimization_steps'] - 1:\n", "                print(f\"   Step {step:4d}: Energy = {energy:8.6f}, Error = {error:8.6f}\")\n", "    \n", "    except Exception as e:\n", "        print(f\"   ❌ Training failed: {e}\")\n", "        return None\n", "    \n", "    final_error = history['errors'][-1]\n", "    print(f\"   ✅ Final error: {final_error:.6f}\")\n", "    \n", "    return history\n", "\n", "print(\"✅ Training function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Run Training for All Models\n", "\n", "Train all models and collect results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train all models\n", "training_results = {}\n", "exact_energy = exact_result['energy']\n", "\n", "print(f\"🎯 Training {len(models_info)} models on {SYSTEM_CONFIG['model']} system\")\n", "print(f\"   Target energy: {exact_energy:.8f}\")\n", "print(\"=\" * 60)\n", "\n", "for model_info in models_info:\n", "    history = train_model(model_info, physics_system, exact_energy, TRAINING_CONFIG)\n", "    if history is not None:\n", "        training_results[model_info['name']] = {\n", "            'history': history,\n", "            'model_info': model_info\n", "        }\n", "\n", "print(f\"\\n✅ Training complete! {len(training_results)} models trained successfully.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Results Visualization\n", "\n", "Create comprehensive plots showing training progress and final results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive results visualization\n", "if training_results:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Energy convergence\n", "    for name, result in training_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax1.plot(history['steps'], history['energies'], \n", "                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax1.axhline(y=exact_energy, color='black', linestyle=':', alpha=0.7, label='Exact')\n", "    ax1.set_xlabel('Training Step')\n", "    ax1.set_ylabel('Energy')\n", "    ax1.set_title('Energy Convergence')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 2. Error convergence (log scale)\n", "    for name, result in training_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax2.semilogy(history['steps'], history['errors'], \n", "                    label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax2.axhline(y=0.001, color='red', linestyle=':', alpha=0.7, label='Chemical Accuracy')\n", "    ax2.set_xlabel('Training Step')\n", "    ax2.set_ylabel('Energy Error (log scale)')\n", "    ax2.set_title('Error Convergence')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 3. Final error comparison\n", "    names = list(training_results.keys())\n", "    final_errors = [training_results[name]['history']['errors'][-1] for name in names]\n", "    colors = ['blue' if 'Standard' in name else 'red' for name in names]\n", "    \n", "    bars = ax3.bar(range(len(names)), final_errors, color=colors, alpha=0.7)\n", "    ax3.set_xlabel('Model')\n", "    ax3.set_ylabel('Final Energy Error')\n", "    ax3.set_title('Final Error Comparison')\n", "    ax3.set_xticks(range(len(names)))\n", "    ax3.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "    ax3.set_yscale('log')\n", "    \n", "    # Add value labels\n", "    for bar, error in zip(bars, final_errors):\n", "        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1,\n", "                f'{error:.3e}', ha='center', va='bottom', fontsize=8, rotation=45)\n", "    \n", "    # 4. Parameter efficiency (error vs parameters)\n", "    param_counts = [training_results[name]['model_info']['param_count'] for name in names]\n", "    \n", "    for i, name in enumerate(names):\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        marker = 'o' if 'RBM' in name else 's'\n", "        ax4.loglog(param_counts[i], final_errors[i], \n", "                  marker=marker, color=color, markersize=10, alpha=0.8, label=name.replace('_', ' '))\n", "    \n", "    ax4.set_xlabel('Parameter Count')\n", "    ax4.set_ylabel('Final Energy Error')\n", "    ax4.set_title('Parameter Efficiency')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary table\n", "    print(\"\\n📊 FINAL RESULTS SUMMARY\")\n", "    print(\"=\" * 80)\n", "    print(f\"{'Model':<25} {'Parameters':<12} {'Final Error':<15} {'Best Error':<15} {'Quality':<10}\")\n", "    print(\"-\" * 80)\n", "    \n", "    for name, result in training_results.items():\n", "        history = result['history']\n", "        param_count = result['model_info']['param_count']\n", "        final_error = history['errors'][-1]\n", "        best_error = min(history['errors'])\n", "        \n", "        if best_error < 0.001:\n", "            quality = \"Excellent\"\n", "        elif best_error < 0.01:\n", "            quality = \"Good\"\n", "        elif best_error < 0.1:\n", "            quality = \"Fair\"\n", "        else:\n", "            quality = \"Poor\"\n", "        \n", "        print(f\"{name:<25} {param_count:<12,} {final_error:<15.6f} {best_error:<15.6f} {quality:<10}\")\n", "    \n", "    # Find best models\n", "    best_overall = min(training_results.items(), key=lambda x: min(x[1]['history']['errors']))\n", "    print(f\"\\n🏆 Best Overall: {best_overall[0]} (Error: {min(best_overall[1]['history']['errors']):.6f})\")\n", "    \n", "else:\n", "    print(\"❌ No successful training results to display\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Polynomial vs Standard Comparison\n", "\n", "Direct comparison between polynomial and standard variants:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare polynomial vs standard models\n", "if training_results:\n", "    standard_models = {name: result for name, result in training_results.items() if 'Standard' in name}\n", "    polynomial_models = {name: result for name, result in training_results.items() if 'Polynomial' in name}\n", "    \n", "    print(\"\\n🔬 POLYNOMIAL vs STANDARD COMPARISON\")\n", "    print(\"=\" * 60)\n", "    \n", "    if standard_models and polynomial_models:\n", "        print(f\"Standard models: {len(standard_models)}\")\n", "        print(f\"Polynomial models: {len(polynomial_models)}\")\n", "        \n", "        # Calculate average improvements\n", "        standard_errors = [min(result['history']['errors']) for result in standard_models.values()]\n", "        polynomial_errors = [min(result['history']['errors']) for result in polynomial_models.values()]\n", "        \n", "        avg_standard_error = np.mean(standard_errors)\n", "        avg_polynomial_error = np.mean(polynomial_errors)\n", "        \n", "        improvement = (avg_standard_error - avg_polynomial_error) / avg_standard_error * 100\n", "        \n", "        print(f\"\\nAverage best error:\")\n", "        print(f\"  Standard models: {avg_standard_error:.6f}\")\n", "        print(f\"  Polynomial models: {avg_polynomial_error:.6f}\")\n", "        print(f\"  Improvement: {improvement:+.1f}%\")\n", "        \n", "        if improvement > 0:\n", "            print(\"✅ Polynomial models show improvement!\")\n", "        else:\n", "            print(\"⚠️ Standard models performed better in this case\")\n", "    \n", "    else:\n", "        print(\"⚠️ Need both standard and polynomial models for comparison\")\n", "\n", "print(\"\\n🎯 Experiment Complete!\")\n", "print(\"\\nTo run a new experiment:\")\n", "print(\"1. Modify the EXPERIMENT CONFIGURATION section\")\n", "print(\"2. Re-run the cells from section 2 onwards\")\n", "print(\"3. Try different polynomial classes: 'CP', 'CP_sparse_LU', 'CP_sparse_degree'\")\n", "print(\"4. Experiment with different degrees: 2, 3, 4\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}