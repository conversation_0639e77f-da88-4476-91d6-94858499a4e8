# Import required libraries
import sys
import os
sys.path.append('..')

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
import time
from typing import Dict, List, Any, Tuple
import json

# Import polynomial classes from core module
from core.polynomial_layers import (
    CP, CP_sparse_LU, CP_sparse_degree, 
    QuantumPolynomialLayer, get_polynomial_class
)

# Set up plotting
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

print("✅ Imports successful!")
print(f"JAX devices: {jax.devices()}")
print(f"NetKet version: {nk.__version__}")
print(f"JAX backend: {jax.default_backend()}")
print(f"🎯 Loaded polynomial classes: {list(['CP', 'CP_sparse_LU', 'CP_sparse_degree', 'QuantumPolynomial'])}")

import jax
import jax.numpy as jnp
from flax import nnx
from typing import Optional, Callable, Any
import math


class Model2(nnx.Module):

    def __init__(self, N: int, *, rngs: nnx.Rngs):
        self.linear1 = nnx.Linear(
            in_features=N, out_features=2 * N, dtype=jnp.complex128, rngs=rngs
        )
        self.linear2 = nnx.Linear(
            in_features=2 * N, out_features=N, dtype=jnp.complex128, rngs=rngs
        )

    def __call__(self, x: jax.Array):
        x = self.linear1(x)
        x = nk.nn.activation.log_cosh(x)
        x = self.linear2(x)
        x = nk.nn.activation.log_cosh(x)
        return jnp.sum(x, axis=-1)
    
class CP(nnx.Module):
    """
    Canonical Polyadic (CP) decomposition polynomial network.
    
    Implements polynomial transformations using CP decomposition for efficient
    computation of high-degree polynomial features.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int, 
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        """
        Initialize CP polynomial network.
        
        Args:
            degree: Polynomial degree
            input_dim: Input feature dimension
            rank: CP decomposition rank (hidden dimension)
            output_dim: Output feature dimension
            param_dtype: Parameter data type
            rngs: Random number generators
        """
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        
        # Create U matrices for each degree
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(
                input_dim, rank, use_bias=False, 
                param_dtype=param_dtype, rngs=rngs
            ))
        
        # Final combination layer
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)

    def __call__(self, x):
        """Forward pass through CP polynomial network."""
        # Store original shape for batch processing
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        
        # Start with first degree
        out = getattr(self, 'U1')(x_flat)
        
        # Apply polynomial combinations
        for i in range(2, self.degree + 1):
            ui_out = getattr(self, f'U{i}')(x_flat)
            out = ui_out * out + out
        
        # Final linear combination
        result = self.layer_C(out).squeeze()
        
        # Restore original shape
        return result

import jax
import jax.numpy as jnp
from flax import nnx
from typing import Optional, Callable, Any
import math


class Model2(nnx.Module):

    def __init__(self, N: int, *, rngs: nnx.Rngs):
        self.linear1 = nnx.Linear(
            in_features=N, out_features=2 * N, dtype=jnp.complex128, rngs=rngs
        )
        self.linear2 = nnx.Linear(
            in_features=2 * N, out_features=N, dtype=jnp.complex128, rngs=rngs
        )

    def __call__(self, x: jax.Array):
        x = self.linear1(x)
        x = nk.nn.activation.log_cosh(x)
        x = self.linear2(x)
        x = nk.nn.activation.log_cosh(x)
        return jnp.sum(x, axis=-1)
    
class CP(nnx.Module):
    """
    Canonical Polyadic (CP) decomposition polynomial network.
    
    Implements polynomial transformations using CP decomposition for efficient
    computation of high-degree polynomial features.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int, 
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        """
        Initialize CP polynomial network.
        
        Args:
            degree: Polynomial degree
            input_dim: Input feature dimension
            rank: CP decomposition rank (hidden dimension)
            output_dim: Output feature dimension
            param_dtype: Parameter data type
            rngs: Random number generators
        """
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        
        # Create U matrices for each degree
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(
                input_dim, rank, use_bias=False, 
                param_dtype=param_dtype, rngs=rngs
            ))
        
        # Final combination layer
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)

    def __call__(self, x):
        """Forward pass through CP polynomial network."""
        # Store original shape for batch processing
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        
        # Start with first degree
        out = getattr(self, 'U1')(x_flat)
        
        # Apply polynomial combinations
        for i in range(2, self.degree + 1):
            ui_out = getattr(self, f'U{i}')(x_flat)
            out = ui_out * out + out
        
        # Final linear combination
        result = self.layer_C(out).squeeze()
        
        # Restore original shape
        return result

def compare_models(models, exact_energy=None, smooth_window=11, title="Model comparison"):
    # helpers
    def moving_avg(y, w):
        if w is None or w <= 1 or len(y) < w:
            return None, None
        y = np.asarray(y, dtype=float)
        # Filter out NaN/Inf values
        y = y[np.isfinite(y)]
        if len(y) < w:
            return None, None
        y_s = np.convolve(y, np.ones(w)/w, mode="valid")
        return y_s, np.arange(w-1, w-1+len(y_s))

    def cummin_abs_delta(y, e0):
        y = np.asarray(y, dtype=float)
        # Filter out NaN/Inf values
        y = y[np.isfinite(y)]
        if len(y) == 0:
            return np.array([])
        d = np.abs(y - float(e0))
        return np.minimum.accumulate(d)

    # figure
    fig, (ax_top, ax_bot) = plt.subplots(2, 1, figsize=(12, 9), sharex=True, gridspec_kw={"height_ratios": [2, 1]})
    colors = plt.rcParams['axes.prop_cycle'].by_key()['color']

    # plot energies
    for i, (name, (iters, energy)) in enumerate(models.items()):
        c = colors[i % len(colors)]
        # Filter out NaN/Inf values
        energy = np.asarray(energy, dtype=float)
        iters = np.asarray(iters)
        finite_mask = np.isfinite(energy)
        energy = energy[finite_mask]
        iters = iters[finite_mask]
        
        if len(energy) == 0:
            print(f"Warning: All values are NaN/Inf for model {name}")
            continue
            
        # raw (light)
        ax_top.plot(iters, energy, color=c, alpha=0.25, linewidth=1)
        # smoothed (emphasize)
        y_s, x_s = moving_avg(energy, smooth_window)
        if y_s is not None:
            ax_top.plot(iters[x_s], y_s, color=c, label=name, linewidth=2)
        else:
            ax_top.plot(iters, energy, color=c, label=name, linewidth=2)

    if exact_energy is not None:
        ax_top.axhline(float(exact_energy), color="k", linestyle="--", linewidth=1.5, label="Exact")

    ax_top.set_ylabel("Energy")
    ax_top.set_title(title)
    ax_top.grid(True, alpha=0.3)
    ax_top.legend(ncol=3, bbox_to_anchor=(1.0, 1.02), loc="lower right")

    # y-lims with small margin near exact if available - with NaN/Inf filtering
    all_finite_vals = []
    for name, (iters, energy) in models.items():
        energy = np.asarray(energy, dtype=float)
        finite_energy = energy[np.isfinite(energy)]
        if len(finite_energy) > 0:
            all_finite_vals.extend(finite_energy)
    
    if len(all_finite_vals) > 0:
        all_finite_vals = np.array(all_finite_vals)
        y_min = all_finite_vals.min()
        y_max = all_finite_vals.max()
        if exact_energy is not None:
            y_min = min(y_min, float(exact_energy))
            y_max = max(y_max, float(exact_energy))
        pad = 0.03 * max(1.0, abs(y_max - y_min))
        ax_top.set_ylim(y_min - pad, y_max + pad)

    # plot |E - E0| (running best) on log scale for visibility
    if exact_energy is not None:
        for i, (name, (iters, energy)) in enumerate(models.items()):
            c = colors[i % len(colors)]
            best_delta = cummin_abs_delta(energy, exact_energy)
            if len(best_delta) > 0:
                energy = np.asarray(energy, dtype=float)
                iters = np.asarray(iters)
                finite_mask = np.isfinite(energy)
                ax_bot.semilogy(iters[finite_mask][:len(best_delta)], best_delta, color=c, linewidth=2, label=name)
        ax_bot.set_ylabel("best |E - E0|")
        ax_bot.set_xlabel("Iteration")
        ax_bot.grid(True, which="both", alpha=0.3)
    else:
        ax_bot.axis("off")

    plt.tight_layout()
    plt.show()

# Define different system sizes to test
system_sizes = [14]
results_by_size = {}

for L in system_sizes:
    print(f"\n=== Running simulations for L={L} ===")
    
    # Create graph and Hilbert space
    g_temp = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)
    hi_temp = nk.hilbert.Spin(s=0.5, total_sz=0, N=g_temp.n_nodes)
    ha_temp = nk.operator.Heisenberg(hilbert=hi_temp, graph=g_temp)
    
    # Get exact ground state energy
    evals_temp = nk.exact.lanczos_ed(ha_temp, compute_eigenvectors=False)
    exact_energy = evals_temp[0]
    
    # Create models
    models = {
        'RBM': nk.models.RBM(alpha=1),
        'RBM_Symm': nk.models.RBMSymm(symmetries=g_temp.translation_group(), alpha=1),
        #'Jastrow': nk.models.Jastrow(),
        #'FF': nk.models.FastARNNConv1D(layers=4, features=16, kernel_size=3, hilbert=hi_temp),
        #'FF (2 layers)': nk.models.FastARNNConv1D(layers=2),
        'FF': Model2(N=hi_temp.size, rngs=nnx.Rngs(1)),
        'GCNN':nk.models.GCNN(  # Add this
        symmetries=g_temp.translation_group(),
        layers=2,
        features=16,
        mode="auto",
        complex_output=False,
        param_dtype=float
    ),
        'CP': CP(degree=2, input_dim=hi_temp.size, rank=hi_temp.size//2, 
                 output_dim=1, param_dtype=jnp.complex128, rngs=nnx.Rngs(1))
    }

    
    # Run simulations for each model
    size_results = {}
    for model_name, model in models.items():
        print(f"Running {model_name} for L={L}")
        
        try:
            # Create sampler, optimizer, and preconditioner
            sa_temp = nk.sampler.MetropolisExchange(hilbert=hi_temp, graph=g_temp)
            op_temp = nk.optimizer.Sgd(learning_rate=0.01)
            
            # Configure SR properly for each model type
            if model_name == 'CP':
                # CP has complex parameters, so it can be holomorphic
                sr_temp = nk.optimizer.SR(diag_shift=0.1, holomorphic=True)
            else:
                # RBM, RBM_Symm, FF, GCNN have real parameters - let NetKet auto-detect
                sr_temp = nk.optimizer.SR(diag_shift=0.1)
           
            
            # Create and run VMC
            vs_temp = nk.vqs.MCState(sa_temp, model, n_samples=1008)
            gs_temp = nk.VMC(hamiltonian=ha_temp, optimizer=op_temp, 
                            preconditioner=sr_temp, variational_state=vs_temp)
            
            start_time = time.time()
            gs_temp.run(out=f"{model_name}_L{L}", n_iter=200)
            end_time = time.time()
            
            # Load results
            data_temp = json.load(open(f"{model_name}_L{L}.log"))
            iters = data_temp["Energy"]["iters"]
            if "real" in data_temp["Energy"]["Mean"]:
                energies = data_temp["Energy"]["Mean"]["real"]
            else:
                energies = data_temp["Energy"]["Mean"]
            
            size_results[model_name] = {
                'iters': iters,
                'energies': energies,
                'n_params': vs_temp.n_parameters,
                'time': end_time - start_time
            }
            
            print(f"  {model_name}: {vs_temp.n_parameters} params, {end_time-start_time:.1f}s")
            
        except Exception as e:
            print(f"  {model_name}: Failed with error - {str(e)}")
            continue
    
    results_by_size[L] = {
        'exact_energy': exact_energy,
        'models': size_results
    }

# Create comparison plots for different system sizes with log scale
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.flatten()

for i, L in enumerate(system_sizes):
    ax = axes[i]
    exact_energy = results_by_size[L]['exact_energy']
    
    models_for_plot = {}
    for model_name, results in results_by_size[L]['models'].items():
        models_for_plot[f"{model_name} ({results['n_params']} params)"] = (
            results['iters'], results['energies']
        )
    
    # Use logarithmic plotting for better visibility
    colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
    
    # Collect all finite energy values for plotting
    all_finite_energies = []
    
    for j, (name, (iters, energy)) in enumerate(models_for_plot.items()):
        c = colors[j % len(colors)]
        energy = np.asarray(energy, dtype=float)
        iters = np.asarray(iters)
        finite_mask = np.isfinite(energy)
        energy_filtered = energy[finite_mask]
        iters_filtered = iters[finite_mask]
        
        if len(energy_filtered) > 0:
            # Plot |E - E_exact| on log scale
            energy_diff = np.abs(energy_filtered - exact_energy)
            energy_diff = np.maximum(energy_diff, 1e-12)  # Avoid log(0)
            ax.semilogy(iters_filtered, energy_diff, color=c, alpha=0.7, linewidth=1.5, label=name)
    
    ax.set_title(f"L={L} (N={L} sites)")
    ax.set_ylabel("|E - E_exact|")
    ax.set_xlabel("Iteration")
    ax.grid(True, which="both", alpha=0.3)
    ax.legend(fontsize=8)

plt.tight_layout()
plt.suptitle("Heisenberg 1D Chain: Energy Error (Log Scale)", y=1.02, fontsize=16)
plt.show()







