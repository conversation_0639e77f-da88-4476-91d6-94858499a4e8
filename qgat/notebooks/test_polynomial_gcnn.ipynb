{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial GCNN Testing and Demonstration\n", "\n", "This notebook demonstrates the polynomial GCNN implementation and compares it with baseline NetKet GCNN on simple quantum systems.\n", "\n", "## 🎯 **Objectives:**\n", "- Test basic polynomial GCNN functionality\n", "- Compare with baseline NetKet GCNN\n", "- Demonstrate different polynomial configurations\n", "- Analyze performance and parameter counts\n", "- Visualize results on simple spin systems"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Imports successful!\n", "JAX devices: [CpuDevice(id=0)]\n", "NetKet version: 3.19.0\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import time\n", "from typing import Dict, List, Any\n", "\n", "# Import polynomial GCNN\n", "from core.polygcnn import PolyGCNN, PolyGCNN_FFT, PolyDenseEquivariantFFT\n", "from core.polygcnn.factory import create_poly_gcnn_for_spin_system\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Basic Polynomial GCNN Usage\n", "\n", "Let's start by creating and testing basic polynomial GCNN models."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["System: 4 spins, 4 symmetries\n", "Test configurations shape: (4, 4)\n", "Test configurations:\n", "[[ 1. -1.  1. -1.]\n", " [ 1.  1. -1. -1.]\n", " [ 1.  1.  1.  1.]\n", " [-1. -1. -1. -1.]]\n"]}], "source": ["# =============================================================================\n", "# BASIC POLYNOMIAL GCNN USAGE\n", "# =============================================================================\n", "\n", "def create_test_system(system_size=4):\n", "    \"\"\"Create a simple test system.\"\"\"\n", "    # 1D Heisenberg chain\n", "    g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=True)\n", "    hilbert = nk.hilbert.Spin(s=1/2, N=system_size)\n", "    ha = nk.operator.Heisenberg(hilbert=hilbert, graph=g)\n", "    \n", "    return g, hilbert, ha\n", "\n", "# Create test system\n", "g, hilbert, ha = create_test_system(system_size=4)\n", "print(f\"System: {hilbert.size} spins, {len(g.translation_group())} symmetries\")\n", "\n", "# Create test spin configurations\n", "test_configs = jnp.array([\n", "    [1, -1, 1, -1],   # Alternating\n", "    [1, 1, -1, -1],   # Domain wall\n", "    [1, 1, 1, 1],     # All up\n", "    [-1, -1, -1, -1]  # All down\n", "], dtype=jnp.float64)\n", "\n", "print(f\"Test configurations shape: {test_configs.shape}\")\n", "print(f\"Test configurations:\\n{test_configs}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Creating Polynomial GCNN Models\n", "==================================================\n", "Created 6 models:\n", "  poly_degree2   : PolyGCNN_FFT\n", "  poly_degree3   : PolyGCNN_FFT\n", "  pure_poly      : PolyGCNN_FFT\n", "  poly_output    : PolyGCNN_FFT\n", "  multi_degree   : PolyGCNN_FFT\n", "  baseline       : GCNN_FFT\n"]}], "source": ["# Create different polynomial GCNN configurations\n", "print(\"🧠 Creating Polynomial GCNN Models\")\n", "print(\"=\" * 50)\n", "\n", "models = {}\n", "\n", "# 1. Basic polynomial GCNN (degree 2)\n", "models['poly_degree2'] = create_poly_gcnn_for_spin_system(\n", "    system_size=4,\n", "    layers=2,\n", "    features=[8, 16],\n", "    degree=2\n", ")\n", "\n", "# 2. Higher degree polynomial\n", "models['poly_degree3'] = create_poly_gcnn_for_spin_system(\n", "    system_size=4,\n", "    layers=2,\n", "    features=[8, 16],\n", "    degree=3\n", ")\n", "\n", "# 3. Pure polynomial (no activations)\n", "models['pure_poly'] = create_poly_gcnn_for_spin_system(\n", "    system_size=4,\n", "    layers=3,\n", "    features=[8, 16, 32],\n", "    degree=2,\n", "    no_activation=True\n", ")\n", "\n", "# 4. Polynomial output mode\n", "models['poly_output'] = create_poly_gcnn_for_spin_system(\n", "    system_size=4,\n", "    layers=2,\n", "    features=[8, 16],\n", "    degree=2,\n", "    poly_output=True\n", ")\n", "\n", "# 5. Per-layer degrees\n", "models['multi_degree'] = create_poly_gcnn_for_spin_system(\n", "    system_size=4,\n", "    layers=3,\n", "    features=[8, 16, 32],\n", "    poly_degrees=(2, 3)\n", ")\n", "\n", "# 6. Baseline NetKet GCNN for comparison\n", "models['baseline'] = nk.models.GCNN(\n", "    symmetries=g,  # Pass the graph directly\n", "    layers=2,\n", "    features=[8, 16],\n", "    mode=\"fft\"\n", ")\n", "\n", "print(f\"Created {len(models)} models:\")\n", "for name, model in models.items():\n", "    model_type = type(model).__name__\n", "    print(f\"  {name:15}: {model_type}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Model Initialization and Parameter Analysis\n", "\n", "Let's initialize all models and analyze their parameter counts."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Initializing Models and Counting Parameters\n", "============================================================\n", "poly_degree2   : 1,096 parameters\n", "poly_degree3   : 1,624 parameters\n", "pure_poly      : 5,256 parameters\n", "poly_output    : 1,096 parameters\n", "multi_degree   : 7,336 parameters\n", "baseline       : 1,080 parameters\n", "\n", "📊 Parameter Count Summary:\n", "  poly_degree2   : 1.01x baseline\n", "  poly_degree3   : 1.50x baseline\n", "  pure_poly      : 4.87x baseline\n", "  poly_output    : 1.01x baseline\n", "  multi_degree   : 6.79x baseline\n", "  baseline       : baseline\n"]}], "source": ["# =============================================================================\n", "# MODEL INITIALIZATION AND PARAMETER ANALYSIS\n", "# =============================================================================\n", "\n", "# Initialize all models\n", "key = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "model_params = {}\n", "param_counts = {}\n", "\n", "print(\"🔧 Initializing Models and Counting Parameters\")\n", "print(\"=\" * 60)\n", "\n", "for name, model in models.items():\n", "    try:\n", "        params = model.init(key, test_configs)\n", "        model_params[name] = params\n", "        \n", "        # Count parameters\n", "        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params))\n", "        param_counts[name] = param_count\n", "        \n", "        print(f\"{name:15}: {param_count:,} parameters\")\n", "        \n", "    except Exception as e:\n", "        print(f\"{name:15}: ❌ Failed to initialize - {e}\")\n", "\n", "print(f\"\\n📊 Parameter Count Summary:\")\n", "baseline_count = param_counts.get('baseline', 1)\n", "for name, count in param_counts.items():\n", "    if name != 'baseline':\n", "        ratio = count / baseline_count\n", "        print(f\"  {name:15}: {ratio:.2f}x baseline\")\n", "    else:\n", "        print(f\"  {name:15}: baseline\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📈 Polynomial models generally have more parameters due to degree-many sub-layers\n"]}], "source": ["# Visualize parameter counts\n", "fig, ax = plt.subplots(1, 1, figsize=(10, 6))\n", "\n", "names = list(param_counts.keys())\n", "counts = list(param_counts.values())\n", "colors = ['red' if name == 'baseline' else 'blue' for name in names]\n", "\n", "bars = ax.bar(names, counts, color=colors, alpha=0.7)\n", "ax.set_ylabel('Parameter Count')\n", "ax.set_title('Parameter Count Comparison: Polynomial vs Baseline GCNN')\n", "ax.tick_params(axis='x', rotation=45)\n", "\n", "# Add value labels on bars\n", "for bar, count in zip(bars, counts):\n", "    height = bar.get_height()\n", "    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "            f'{count:,}', ha='center', va='bottom', fontsize=9)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📈 Polynomial models generally have more parameters due to degree-many sub-layers\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Forward Pass Testing and Output Analysis\n", "\n", "Test forward passes through all models and analyze outputs."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Testing Forward Passes\n", "==================================================\n", "poly_degree2   : (4,) - [5.2916806 +0.j 4.90314106+0.j 5.32439306+0.j 8.15056632+0.j]\n", "poly_degree3   : (4,) - [ 9.44666353+0.j  5.75937777+0.j  6.94753877+0.j 12.13320036+0.j]\n", "pure_poly      : (4,) - [ 6.97118523+0.j 10.29916517+0.j  5.78928034+0.j 15.67863055+0.j]\n", "poly_output    : (4,) - [ 5.39715881 -0.42639257 10.94651199 40.619889  ]\n", "multi_degree   : (4,) - [ 9.20592692+0.j  8.104294  +0.j  7.72966257+0.j 44.20544783+0.j]\n", "baseline       : (4,) - [5.5175039 +0.j 5.23616022+0.j 5.67606592+0.j 5.81191536+0.j]\n", "\n", "✅ All models produced finite outputs!\n"]}], "source": ["# =============================================================================\n", "# FORWARD PASS TESTING\n", "# =============================================================================\n", "\n", "print(\"🚀 Testing Forward Passes\")\n", "print(\"=\" * 50)\n", "\n", "model_outputs = {}\n", "\n", "for name, model in models.items():\n", "    if name in model_params:\n", "        try:\n", "            params = model_params[name]\n", "            output = model.apply(params, test_configs)\n", "            model_outputs[name] = output\n", "            \n", "            print(f\"{name:15}: {output.shape} - {output}\")\n", "            \n", "            # Check for finite outputs\n", "            if not jnp.all(jnp.isfinite(output)):\n", "                print(f\"  ⚠️ Warning: Non-finite outputs detected!\")\n", "                \n", "        except Exception as e:\n", "            print(f\"{name:15}: ❌ Forward pass failed - {e}\")\n", "\n", "print(\"\\n✅ All models produced finite outputs!\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************************/R/mQn/Mzdf7Y7OZ/7UypURf7REAAMhZGKQDAIAMK1So0A23JyQkKCgoKNW2wMBAXbp0KVPrh4UFW2I45G0hIUHp3wmGoT/m5qv9SfR3GF1ChvlqjwAAQM7CIB0AANyyoKAgXbx4MdW2K1euKDg4OFPrREcn+NwR6SEhQYqLuyyn02V0OfgX+mNuvt6fgCSn0SWky2aTAiSf61FoaOayDwAAWAODdAAAcMvCw8P1zTffpNp2+PBhlS5dOlPruFxuuVxub5ZmCU6nS8nJvjNkshr6Y26+2h9/txVeK6+9MeqrPQIAADkLJ6sDAAC3rFGjRjp37pwWL16spKQkffvtt/roo4/UsmVLo0sDAAAAAOCWMUgHAAC3LDQ0VG+//bY2b96s6tWr6+WXX9bLL7+sGjVqGF0aAAAAAAC3jFO7AACAm3LgwIFUX1eoUEErV640qBoAAAAAALIOR6QDAAAAAAAAAJAGBukAAAAAAAAAAKSBQToAAAAAAAAAAGlgkA4AAAAAAAAAQBoYpAMAAAAAkMNFR0erUaNG2r17t2fbli1b1KxZMz344IOqX7++Zs+eLZfLZWCVAACYF4N0AAAAAABysL1796pNmzY6duyYZ9tvv/2mwYMHq1+/ftqzZ48WLlyodevWafHixcYVCgCAifkZXQAAAEBWCVqzyegS0mSz2aRerY0uAwCQg61fv16zZs3SoEGD1L9/f8/2v//+W23bttUjjzwiSSpZsqQaNWqk77//Xs8995xR5QIAYFockQ4AAAAAQA5Vu3Ztbdu2TU2aNEm1vXHjxho2bJjn6ytXrmjHjh0qV65cdpcIAIAlcEQ6AAAAAAA5VKFChdK9T3x8vF588UUFBgYqIiIiU+vb7TbZ7babrM56HA57qv/DXOiPufl6f2w2879WppToqz1KD4N0AAAAAAB81J9//qm+ffuqQIECWrp0qfLkyZOpx4eFBVtiOORtISFBRpeANNAfc/PV/iT6O4wuIcN8tUfpYZAOAAAAAIAP+uKLLzRgwAC1bt1aL730kvz8Mj8iiI5O8Lkj0kNCghQXd1lOp8vocvAv9MfcfL0/AUlOo0tIl80mBUg+16PQ0OAM3Y9BOgAAAAAAPuann35SZGSkRo8erVatWt30Oi6XWy6X24uVWYPT6VJysu8MmayG/pibr/bH322F18prb4z6ao/SY+oT3sTExGjw4MGqXr26qlatqt69e+vs2bNGlwUAAAAAgKXNnz9fycnJGj9+vCpVquT57/nnnze6NAAATMnUR6S/8MILypcvn7Zt2ya73a5hw4Zp5MiRWrBggdGlAQAAAABgKQcOHPD8ef78+QZWAgCA9Zh2kP7bb7/p559/1s6dOz0XOxk3bpyioqIMrgwAAAAAAAAA4EtMO0j/5ZdfVKpUKa1evVrvvfeeLl++rDp16mjIkCEZXsNut/ncRU/++X+YC/0xN1/vj81m/tfKlBJ9tUcAAAAAAMA4ph2kx8bG6sCBAypfvrzWr1+vK1euaPDgwRoyZEiGT+0SFhZsieGQt4WEBBldAtJAf8zNV/uT6O8wuoQM89UeAQAAAAAA45h2kB4QECBJGjFihHLlyqU8efKoX79+at26tRISEhQcHJzuGtHRCT53RHpISJDi4i7L6eTKumZDf8zN1/sTkOQ0uoR02WxSgORzPQoNTT/vAAAAAABA1jLtIL1UqVJyuVxKSkpSrly5JEku17XBidvtztAaLpdbLlfG7puTOJ0uJSf7zpDJauiPuflqf/wz+LpqrGtvjPpqjwAAAAAAgHFMe6LZmjVrqnjx4ho+fLgSEhIUHR2t6dOnq2HDhp6LjwIAAAAAAAAAkNVMO0j39/fXsmXL5HA41LhxYzVu3FhFihTRhAkTjC4NAAAAAAAAAOBDTHtqF0kqXLiwpk+fbnQZAAAAAAAAAAAfZtoj0gEAAAAAAAAAMAMG6QAAAAAAAAAApIFBOgAAAAAAAAAAaWCQDgAAAAAAAABAGrJkkH727FnNnj1bAwYM0Pnz5/XJJ5/oyJEjWbErAACQAWQzAADWQnYDAGAuXh+kHz16VE2bNtX69eu1detWXbp0SZ988olatWqlH374wdu7AwAA6SCbAQCwFrIbAADz8fogfdKkSWrYsKG2b98uf39/SdL06dPVsGFDTZs2zdu7AwAA6SCbAQCwFrIbAADz8fog/ccff1SXLl1ks9k82xwOh3r27Kk//vjD27sDAADpIJsBALAWshsAAPPx+iDd6XTK5XJdtz0+Pl4Oh8PbuwMAAOkgmwEAsBayGwAA8/H6IL127dqaN2+enE6nZ9uFCxc0ZcoU1ahRw9u7AwAA6cjObN63b586dOigKlWqqHbt2nr11VeVmJjo1X0AAJDT8Xs1AADm4/VB+tChQ/X777+rZs2aunr1qnr16qX69evr77//1pAhQ7y9OwAAkI7symaXy6UePXqocePG+u6777R27Vp9/fXXWrhwodf2AQCAL+D3agAAzMfP2wsWLlxYGzZs0Mcff6w//vhDLpdL7dq1U7NmzZQnTx5v7w4AAKQju7I5NjZWUVFRcrlccrvdkiS73a6goCCv7QMAAF/A79UAAJiP1wfpkhQUFKRnnnkmK5YGAAA3ITuyOTQ0VBEREZo8ebJee+01OZ1ONWjQQBEREVm6XwAAciJ+rwYAwFy8Pkjv1KlTmrcvXbrU27sEAABpyK5sdrlcCgwM1MiRI9WqVSsdPXpUffr00axZs9SvX78MrWG322S327xSjyTZbN5bKyuklOdweP1se/CClL7QH3Py9f6Y/fVN4jXuVvB7NQAA5uP1QXqxYsVSfZ2UlKRjx47p4MGDHJEGAIABsiubt23bpi1btmjz5s2SpNKlSysyMlLjx4/P8CA9LCzYq8OhRH+H19bKSiEhnP7GzOiPuflqf6zy+ib5bo9uRVZkd3R0tNq0aaNXX31V1atXlyT9/PPPevXVV3X48GGFhoaqV69eHAWPNAWt2WR0CWmy2WxSr9ZGlwEgh/L6IH3ixIk33D5r1iydP3/e27sDYBH8wAUYJ7uy+dSpU0pMTEy1zc/PT/7+/hleIzo6watHpAckOb22Vlaw2aQASXFxl+V0uowuB//icNgVEhJEf0zK1/tj9tc3yXdf40JDg295DW9n9969ezV06FAdO3bMsy02Nlbdu3dX37591aZNG33//feKjIxUmTJldP/999907QAA5FRZco70G2nRooVatmypMWPGZNcuvY5BIAAgJ/F2NteuXVtTp07V/Pnz1a1bN508eVLz5s1T06ZNM7yGy+WWy+X2Sj2S5O/23lpZ49qbBk6nS8nJvjNkshr6Y26+2h/zv75JvMZ5381k9/r16zVr1iwNGjRI/fv392zfunWr8ufPrw4dOkiSHnroITVt2lQrVqxgkA4AwA1k28nqDh8+LLclftgDAMA3eDubS5UqpQULFuizzz5T9erV1alTJ9WvXz/VL+0AAODm3Ux2165dW9u2bVOTJk1SbT906JDCw8NTbStVqpT2799/y3UCAJATef2I9GHDhl237eLFi/rmm2/02GOPeXt3AAAgHdmZzTVr1lTNmjW9uiYAAL7Gm9ldqFChG25PSEhQUFDq89cHBgbq0qVLmVrf2xcKNzsudGzuXnORY3Pj+WPu54/Ecyg9Xh+knzhx4rptAQEB6tq1q7p06eLt3QEAgHSQzQAAWEt2ZHdQUJAuXryYatuVK1cUHJy5c7x7+0LhVuGrF9G1yoWOfbU/VuGr/bHK80fy3R6lx+uD9GXLlnl7SQAAcAvIZgAArCU7sjs8PFzffPNNqm2HDx9W6dKlM7WOty8UbnZc6NjcFzr21YscWwXPH3M/fyTffQ5l9ELhXhmknzx5MsP3vf322zN8302bNmngwIHKlSuXZ1vDhg01ZcqUTNUHAICvyapsBgAAWSO7s7tRo0aaMmWKFi9erA4dOmjv3r366KOPNHfu3Eyt4+0LhVuFr15E1/wXOuYix1bgq/0x//NH4jmUNq8M0uvXr5/uR7ncbrdsNpv++OOPDK/766+/qlmzZpo4ceKtlggAgE/JqmwGAABZI7uzOzQ0VG+//bbGjx+vWbNmKSwsTC+//LJq1Khxy2sDAJATeWWQvnTpUm8sc51ff/1Vjz/+eJasDQBATpZV2QwAALJGdmT3gQMHUn1doUIFrVy5Msv3CwBATuCVQXq1atW8sUwqLpdL+/btU1BQkBYtWiSn06l69epp4MCBypcvX4bW8PbVw81+ARWurGtuXJ2a54+Zmb0/Ej3KrKzIZgAAkHXIbgAAzM3rFxtNTEzUqlWrdODAATmdzlTbf/31V23dujVD60RHR+u+++5T48aNNWvWLF24cEFDhgzRoEGD9Oabb2ZoDW9fPdwqV9flyrrm5qv94fljblbpj+S7PboV3spmAACQPchuAADMx+uD9AkTJmjdunUqV66cfv75Z1WqVElHjx7V+fPnFRERkeF1ChYsqBUrVni+DgoK0qBBg9S6dWvFx8crT5486a7h7auHm/3qur56ZV2r4OrUPH/MzOz9kXy3Rxm9enhavJXNAAAge5DdAACYj9cH6du3b9ekSZPUpEkTPfrooxo3bpyKFy+u/v37KykpKcPr7N+/Xx9//LFeeuklz1HliYmJstvtCggIyNAa3r56uPmvrsuVda3AV/vD88fczN8fydd7dCu8lc0AACB7kN0AAJiP1wfpMTExeuCBByRJ4eHh+v3333XPPfeoR48e6tevn15++eUMrZM/f36tWLFC+fLlU5cuXXT27FlNmTJFLVq0yPAgHb4laM0mo0tIk81mk3q1NroMAD7IW9kMAACyB9kNAID5eP2KbQULFtT58+clSXfeeacOHjwoSQoNDdW5c+cyvE6RIkW0YMECffrpp6pWrZpatmypChUqaNSoUd4uGQCAHM1b2QwAALIH2Q0AgPl4fZBer149vfLKKzpw4IAefPBBffTRR/r111+1YsUKFSlSJFNrVatWTStXrtQPP/ygXbt26eWXX1auXLm8XTIAADmaN7MZAABkPbIbAADz8cog/dKlS54/Dxw4UEWKFNGePXvUoEEDlS5dWs8884yWLVumvn37emN3AAAgHWQzAADWQnYDAGBuXjlHeq1atfTEE0/omWeeUcWKFTV37lzPbW+++aZ+//13FSxYULfddps3dgcAANJBNgMAYC1kNwAA5uaVI9J79eqln376SW3atNGTTz6pxYsX68KFC57b77vvPsIeAIBsRDYDAGAtZDcAAObmlUF69+7d9fHHH2v16tWqXr26FixYoLp16+rFF1/UN998441dAACATCCbAQCwFrIbAABz88qpXVLcf//9uv/++zVs2DDt2LFDGzZsUM+ePVWoUCE9/fTTatmypYoWLerNXQIAgDSQzQBuVtCaTUaXkCabzSb1am10GYDXkd0AAJiTVwfpnkX9/NSwYUM1bNhQsbGx2rx5s1atWqV58+Zp3759WbFLAACQBrIZAABrIbsBADCXLBmkp4iOjtamTZv0ySef6MCBA3rwwQezcncAACAdZDMAANZCdgMAYA5eH6RfvnxZ27dv10cffaSdO3cqNDRULVq00IQJE1SiRAlv7w4AAKSDbAYAwFrIbgAAzMcrg3Sn06mvv/5aH330kT799FMlJSWpXr16euONN1SvXj3Z7V65pikAAMggshkAAGshuwEAMDevDNJr166tmJgY3XXXXYqMjFSLFi1UoEABbywNAABuAtkMAIC1kN0AAJibVwbpDz/8sFq1aqXKlSt7YzkAAHCLyGYAAKyF7AYAwNy8MkifOHGiN5YBAABeQjYDAGAtZDcAAObGSdYAAAAAAAAAAEgDg3QAAAAAAAAAANLAIB0AAAAAAAAAgDR4fZA+bNgwxcfHX7c9JiZGkZGR3t4dAABIB9kMAIC1ZGd279u3Tx06dFCVKlVUu3Ztvfrqq0pMTPTqPgAAyAm8crHRvXv36vjx45KkDRs2qFy5csqTJ0+q+xw5ckQ7d+70xu4AAEA6yGYAAKzFiOx2uVzq0aOHunfvrmXLluns2bOKiIhQaGgob7YDAPAvXhmk22w2DR061PPnV1999br75M6dW127dvXG7gAAQDrIZgAArMWI7I6NjVVUVJRcLpfcbrckyW63KygoyGv7AAAgp/DKIP3BBx/U/v37JUlly5bV119/rYIFC3pjaQAAcBPIZgAArMWI7A4NDVVERIQmT56s1157TU6nUw0aNFBERESW7hcAACvyyiD9n1KCHwAAmEN2ZnNMTIwmTJigL774Qi6XS1WrVtXo0aN12223ZVsNAABYXXZlt8vlUmBgoEaOHKlWrVrp6NGj6tOnj2bNmqV+/fplaA273Sa73Za1hZqIw2FP9X9fY7OZu9cp5flqf8yO54+5nz8Sz6H0eH2QPmzYsDRvnzhxYqbWczqdioiIULFixTRp0qRbKQ0AAJ/k7WxOywsvvKB8+fJp27ZtstvtGjZsmEaOHKkFCxZ4bR8AAOR02ZXd27Zt05YtW7R582ZJUunSpRUZGanx48dneJAeFhZsieGQt4WE+ObpbxL9HUaXkCG+2h+r8NX+WOX5I/luj9Lj9UH6iRMnUn2dnJys48ePKyEhQU2aNMn0erNnz9aePXtUrFgxb5UIAIBP8XY2/5fffvtNP//8s3bu3Om5ONq4ceMUFRXltX0AAOALsiu7T506pcTExFTb/Pz85O/vn+E1oqMTfO6I9JCQIMXFXZbT6TK6nGwXkOQ0uoQ02WxSgOSz/TE7nj/mfv5IvvscCg0NztD9vD5IX7Zs2XXb3G63XnnlFYWGhmZqrV27dmnr1q169NFHvVUeAAA+x5vZnJZffvlFpUqV0urVq/Xee+/p8uXLqlOnjoYMGeK1fQAA4AuyK7tr166tqVOnav78+erWrZtOnjypefPmqWnTphlew+Vyy+Vye60mq3A6XUpO9p0hUwp/t9l7fe1NHV/tj1X4an/M//yReA6lzeuD9Bux2Wx67rnn1KFDB/Xv3z9Djzl//rxGjBihuXPnavHixVlbIAAAPuZmsjk9sbGxOnDggMqXL6/169frypUrGjx4sIYMGZLhU7t4+zyrZv+oOecgNDfO48nzx8zM3h+JHnlbVmR3qVKltGDBAs2YMUOLFi1S3rx59dRTTykyMtIr6wMAkJNkyyBdks6dO6dLly5l6L4ul0uDBg1Sly5dVLZs2ZveJ7+M+xb6Y270x9zM3h+JHmWFzGRzRgQEBEiSRowYoVy5cilPnjzq16+fWrdurYSEBAUHp/9xOW+fZ9Uq5yHkHITm5qv94fljblbpj+S7PcoK3s5uSapZs6Zq1qzp1TUBAMiJvD5Inz179nXbLl68qI0bN6pWrVoZWmPBggUKCAhQx44db6kWfhn3LfTH3OiPuVmlP5Lv9uhWeCObM6JUqVJyuVxKSkpSrly5JF17c1y69nH0jPD2eVbNfh5CXz0HoVVwHk+eP2Zm9v5IvtujjJ5nNS3Zld0AACDjvD5IX7du3XXb/P39VadOHQ0YMCBDa3zwwQc6e/asqlSpIkm6cuWKJGn79u3as2dPhmvhl3HfQn/Mjf6Ym9n7I/luj7zxy7g3sjkjatasqeLFi2v48OGaOHGirl69qunTp6thw4aei4+mx9vnWTX/eQg5B6EV+Gp/eP6Ym/n7I/l6j25FdmU3AADIOK8P0j/77LNbXmPz5s2pvh46dKgkadKkSZlah1/GfQv9MTf6Y27m74/k6z26Fd7I5ozw9/fXsmXLNGnSJDVu3FhXr15V/fr1NWLEiGzZPwAAOUV2ZTcAAMi4LDlHusvl0q5du3Tw4EHZ7XaVK1fOc3Q5AADIftmVzYULF9b06dO9vi4AAL6G36sBADAXrw/Sz549q65du+rQoUMKCQmR2+3WxYsX9eCDD2rBggXKmzdvptfM7JHoAADgf7IimwFvCFqzyegS0mSz2aRerY0uA4APIrsBADAfu7cXHDt2rAICArRp0yZ99913+v777/XRRx/pypUrmjBhgrd3BwAA0kE2AwBgLWQ3AADm4/VB+s6dOzVmzBjdc889nm2lS5fWK6+8ok8//dTbuwMAAOkgmwEAsBayGwAA8/H6ID0kJERJSUnXbXc4HPLzy5JTsgMAgDSQzQAAWAvZDQCA+Xh9kN63b1+NGjVK+/fv92w7fvy4xo8fr8jISG/vDgAApINsBgDAWshuAADMx+tvZc+cOVPnz59XixYtFBwcLD8/P8XGxsrtduunn37Sq6++6rnvH3/84e3dAwCAfyGbAQCwFrIbAADz8fogvV+/ft5eEgAA3AKyGQAAayG7AQAwH68P0m02m5o0aaKAgIBU2y9duqTVq1crIiLC27sEAABpIJsBALAWX87uoDWbjC4hTTabTerV2ugyAAAG8MogPTo6WleuXJEkDRs2TKVLl1ZoaGiq+/zxxx+aNm1ajg58AADMgmwGAMBayG4AAMzNK4P0L7/8UkOHDpXNZpPb7VarVq2uu4/b7Va9evW8sTsAAJAOshkAAGshuwEAMDevDNKbN2+uYsWKyeVyqXPnzpo1a5by5cvnud1msyl37twKDw/3xu4AAEA6yGYAAKyF7AYAwNy8do70qlWrSpKWLl2qBx98UH5+Xj/9OgAAyASyGQAAayG7AQAwL6+n8smTJ3Xy5Mn/vL158+be3iUAAEgD2QwAgLWQ3QAAmI/XB+lDhw694fZcuXKpSJEiBD4AANmMbAYAwFrIbgAAzMfrg/T9+/en+jo5OVlHjx7VqFGj1KFDB2/vDgAApINsBgDAWshuAADMx57VO/Dz81PJkiU1dOhQzZw5M6t3BwAA0kE2AwBgLWQ3AADGy/JBeorcuXPr1KlT2bU7AACQDrIZAABrIbsBADCO10/t8v3336f62u126+LFi1q8eLHKli3r7d0BAIB0kM0AAFgL2Q0AgPl4fZDesWNH2Ww2ud3uVNuLFy+u119/3du7AwAA6SCbAQCwFrIbAADz8fog/dNPP71um7+/v2677TZv7woAAGQA2QwAgLVkZ3bHxMRowoQJ+uKLL+RyuVS1alWNHj2anxMAAPgXr58jvVixYnK5XDp//rxcLpduv/12AhgAAAORzQAAWEt2ZvcLL7ygS5cuadu2bfr888/lcDg0cuTILNkXAABW5rUj0t1ut9566y0tW7ZMZ8+e9WwvWLCgnn32WXXr1k12e7Zd2xQAAJ9HNgMAYC3Znd2//fabfv75Z+3cuVN58uSRJI0bN05RUVFe2wcAADmF1wbpffv21Y4dO9SsWTM99NBDCg0NVWxsrL799lvNmzdPP/74o+bPn5+pNXft2qVp06bpyJEjCgoK0mOPPaZBgwYpMDDQW2UDAJBjZUU2AwCArJPd2f3LL7+oVKlSWr16td577z1dvnxZderU0ZAhQzK8ht1uk91u81pNNpv31soKKeU5HL55MAL9wa1I6Yuv9sfszx+J51B6vDJI37Bhg3bv3q01a9ZcdwXxxx9/XO3atVPnzp31/vvvq2XLlhlaMzo6Wj169NDo0aPVvHlznTt3Tl27dtWbb76pvn37eqNsAAByrKzIZgAAkHWMyO7Y2FgdOHBA5cuX1/r163XlyhUNHjxYQ4YM0YIFCzK0RlhYsFeHQ4n+Dq+tlZVCQoKMLsEQ9Afe4Kv9scrzR/LdHqXHK4P0VatWqW/fvteFfYqyZcuqb9++mQr8sLAwz8fL3G63YmJidPXqVYWFhXmjZAAAcrSsyGYAAJB1jMjugIAASdKIESOUK1cu5cmTR/369VPr1q2VkJCg4ODgdNeIjk7w6hHpAUlOr62VFWw2KUBSXNxlOZ0uo8vJdvQHt8LhsCskJMhn+2P254/ku8+h0ND0807y0iD98OHDqlWrVpr3qVOnjmbMmJGpdVPO0VavXj2dOXNGVapU0dNPP32zZQIA4DOyKpsBAEDWMCK7S5UqJZfLpaSkJOXKlUuS5HJdG5y43e4MreFyueVyZey+GeGfwf0a59qbBk6nS8nJvjNkSkF/4A2+2h/zP38knkNp88ogPTk5WQ5H+h9PuNmPe23dulWxsbEaOHCg+vbtq0WLFmXocZyrzbfQH3OjP+Zm9v5I9CizsjqbAQCAdxmR3TVr1lTx4sU1fPhwTZw4UVevXtX06dPVsGFDz4FtAKwjaM0mo0tIk81mk3q1NroM4KZ5ZZBeqlQp7dy5U3feeed/3uerr75SyZIlb2r9wMBABQYGatCgQXrmmWcUGxurfPnypfs4ztXmW+iPudEfc7NKfyTf7VFmZXU2AwAA7zIiu/39/bVs2TJNmjRJjRs31tWrV1W/fn2NGDHCa/sAACCn8MogvUWLFnrjjTdUq1YtFS9e/LrbDx8+rNmzZ2vw4MEZXvOHH37Q8OHD9eGHH3rO25aYmCh/f38FBWVsiMK52nwL/TE3+mNuZu+P5Ls9yui52v4tK7IZAABkHaOyu3Dhwpo+fbpX1wQAICfyyiC9bdu22rFjh55++mk9/fTTqlSpkvLnz6/4+Hjt3r1ba9euVZ06ddSiRYsMr1mmTBlduXJFU6dO1UsvvaSoqChNnjxZrVq18gzW08O52nwL/TE3+mNu5u+P5Os9yqysyGYAAJB1yG4AAMzNK4N0u92uefPmad68eVqxYoWWLFniua1gwYLq3bu3unbtmqk1g4ODtWjRIk2YMEG1atVS3rx51bRpU0VGRnqjZAAAcrSsyGYAAJB1yG4AAMzNK4N0SXI4HOrTp4/69Omjv/76SzExMcqfP79KlCghu/3mLgxXqlQpvf32294qEQAAn5IV2QwAALIO2Q0AgHl5bZD+T3fffXdWLAsAAG5Sdmaz0+lURESEihUrpkmTJmXbfgEAyEn4vRoAAHPhLW0AAOBVs2fP1p49e4wuAwAAAAAAr2GQDgAAvGbXrl3aunWrHn30UaNLAQAAAADAa7Lk1C4AAMD3nD9/XiNGjNDcuXO1ePHim1rDbrfJbrd5rSabzXtrZYWU8hwO3zy2gf6YG/0xN7P3R6JHAAAgZ2GQDgAAbpnL5dKgQYPUpUsXlS1b9qbXCQsL9upwKNHf4bW1slJISJDRJRiC/pgb/TE3q/RH8t0eAQCAnIVBOgAAuGULFixQQECAOnbseEvrREcnePWI9IAkp9fWygo2mxQgKS7uspxOl9HlZDv6Y270x9zM3h/Jd3sUGhpsdAkAACALMEgHAAC37IMPPtDZs2dVpUoVSdKVK1ckSdu3b8/UhUddLrdcLrfX6vJ3e2+trHHtTQOn06XkZN8ZMqWgP+ZGf8zN/P2RfL1HAAAgZ2GQDgAAbtnmzZtTfT106FBJ0qRJk4woBwAAAAAAr+KqLwAAAAAAAAAApIEj0gEAgNdxJDoAAAAAICfhiHQAAAAAAAAAANLAIB0AAAAAAAAAgDQwSAcAAAAAAAAAIA0M0gEAAAAAAAAASAODdAAAAAAAAAAA0sAgHQAAAAAAAACANDBIBwAAAAAAAAAgDQzSAQAAAAAAAABIA4N0AAAAAAAAAADSwCAdAAAAAAAf5nQ61bFjRw0dOtToUgAAMC0G6QAAAAAA+LDZs2drz549RpcBAICpmXqQvn//fnXp0kXVqlVTrVq1NHjwYEVHRxtdFgAAAAAAOcKuXbu0detWPfroo0aXAgCAqfkZXcB/uXLlip5//nm1bt1aCxYsUEJCgoYMGaLhw4dr/vz5RpcHAAAAAIClnT9/XiNGjNDcuXO1ePHim1rDbrfJbrd5rSabzXtrZYWU8hwOUx+XmGXoj7nRH3Mze38kepQe0w7ST548qbJlyyoyMlIOh0MBAQFq06aNBg8ebHRpAAAAAABYmsvl0qBBg9SlSxeVLVv2ptcJCwv26nAo0d/htbWyUkhIkNElGIL+mBv9MTer9Efy3R6lx7SD9HvuuUeLFi1KtW3Lli0qV66cQRUBAAAAAJAzLFiwQAEBAerYseMtrRMdneDVI9IDkpxeWysr2GxSgKS4uMtyOl1Gl5Pt6I+50R9zM3t/JN/tUWhocIbuZ9pB+j+53W7NmDFDn3/+uZYvX57hx/ERM99Cf8yN/pib2fsj0SMAAABv+uCDD3T27FlVqVJF0rXTq0rS9u3bM3XhUZfLLZfL7bW6/N3eWytrXPuh1Ol0KTnZd4ZMKeiPudEfczN/fyRf71F6TD9Ij4+P17Bhw7Rv3z4tX75cZcqUyfBj+YiZb6E/5kZ/zM0q/ZF8t0cAAADetHnz5lRfDx06VJI0adIkI8oBAMD0TD1IP3bsmLp166bbb79da9euVVhYWKYez0fMfAv9MTf6Y25m74/kuz3K6EfMAAAAAABA1jHtID02NladO3dWjRo1NH78eNntmf8oPx8x8y30x9zoj7mZvz+Sr/cIAAAgK3EkOgAAaTPtIH3dunU6efKkPvnkk+s+cvbjjz8aVBUAAAAAAAAAwNeYdpDepUsXdenSxegyAAAAAAAAAAA+LvPnSwEAAAAAAAAAwIcwSAcAAAAAAAAAIA0M0gEAAAAAAAAASAODdAAAAAAAAAAA0sAgHQAAAAAAAACANDBIBwAAAAAAAAAgDQzSAQAAAAAAAABIA4N0AAAAAAAAAADSwCAdAAAAAAAAAIA0MEgHAAAAAAAAACANDNIBAIDX7N+/X126dFG1atVUq1YtDR48WNHR0UaXBQAAAADALWGQDgAAvOLKlSt6/vnnValSJX399df6+OOPFRMTo+HDhxtdGgAAAAAAt4RBOgAA8IqTJ0+qbNmyioyMVEBAgEJDQ9WmTRt9//33RpcGAAAAAMAt8TO6AAAAkDPcc889WrRoUaptW7ZsUbly5QyqCAAAAAAA72CQDgAAvM7tdmvGjBn6/PPPtXz58gw/zm63yW63ea0Om817a2WFlPIcDt/8kCD9MTf6Y25m749EjwAAQM7CIB0AAHhVfHy8hg0bpn379mn58uUqU6ZMhh8bFhbs1eFQor/Da2tlpZCQIKNLMAT9MTf6Y25W6Y/kuz0CAAA5C4N0AADgNceOHVO3bt10++23a+3atQoLC8vU46OjE7x6RHpAktNra2UFm00KkBQXd1lOp8vocrId/TE3+mNuZu+P5Ls9Cg0NNroEAACQBRikAwAAr4iNjVXnzp1Vo0YNjR8/XnZ75j/K73K55XK5vVaTv9t7a2WNa28aOJ0uJSf7zpApBf0xN/pjbubvj+TrPQIAADkLg3QAAOAV69at08mTJ/XJJ59o8+bNqW778ccfDaoKAAAAAIBbxyAdAAB4RZcuXdSlSxejywAAAAAAwOu4fDoAAAAAAD5q//796tKli6pVq6ZatWpp8ODBio6ONrosAABMxxKD9OjoaDVq1Ei7d+82uhQAAAAAAHKEK1eu6Pnnn1elSpX09ddf6+OPP1ZMTIyGDx9udGkAAJiO6Qfpe/fuVZs2bXTs2DGjSwEAAAAAIMc4efKkypYtq8jISAUEBCg0NFRt2rTR999/b3RpAACYjqnPkb5+/XrNmjVLgwYNUv/+/Y0uBwAAAACAHOOee+7RokWLUm3bsmWLypUrl+E17Hab7Hab12qy2by3VlZIKc/hMP1xiVmC/pgb/TE3s/dHokfpMfUgvXbt2mratKn8/PxuapBOoPsW+mNu9MfczN4fiR4BAABkJbfbrRkzZujzzz/X8uXLM/y4sLBgr/4smejv8NpaWSkkJMjoEgxBf8yN/pibVfoj+W6P0mPqQXqhQoVu6fEEum+hP+ZGf8zNKv2RfLdHAAAAWSU+Pl7Dhg3Tvn37tHz5cpUpUybDj42OTvDqAWwBSU6vrZUVbDYpQFJc3GU5nS6jy8l29Mfc6I+5mb0/ku/2KDQ0OEP3M/Ug/VYR6L6F/pgb/TE3s/dH8t0eZTTQAQAAbsaxY8fUrVs33X777Vq7dq3CwsIy9XiXyy2Xy+21evzd3lsra1ybMTidLiUn+87PpCnoj7nRH3Mzf38kX+9RenL0IJ1A9y30x9zoj7mZvz+Sr/cIAADA22JjY9W5c2fVqFFD48ePl93OKfQAAPgvOXqQDgAAAAAAbmzdunU6efKkPvnkE23evDnVbT/++KNBVQEAYE4M0gEAAAAA8EFdunRRly5djC4DAABLsMwg/cCBA0aXAAAAAAAAAADwQZwADQAAAAAAAACANDBIBwAAAAAAAAAgDQzSAQAAAAAAAABIA4N0AAAAAAAAAADSwCAdAAAAAAAAAIA0MEgHAAAAAAAAACANDNIBAAAAAAAAAEgDg3QAAAAAAAAAANLAIB0AAAAAAAAAgDQwSAcAAAAAAAAAIA0M0gEAAAAAAAAASAODdAAAAAAAAAAA0sAgHQAAAAAAAACANDBIBwAAAAAAAAAgDQzSAQAAAAAAAABIA4N0AAAAAAAAAADSwCAdAAAAAAAAAIA0MEgHAAAAAAAAACANDNIBAAAAAAAAAEgDg3QAAAAAAAAAANLAIB0AAAAAAAAAgDSYepB+/vx59e7dW1WqVFH16tU1fvx4JScnG10WAAD4D2Q3AADWQnYDAJAxph6k9+vXT7lz59ZXX32ltWvXateuXVq8eLHRZQEAgP9AdgMAYC1kNwAAGWPaQfrRo0f13XffadCgQQoKClLx4sXVu3dvrVixwujSAADADZDdAABYC9kNAEDG+RldwH85dOiQ8ufPr8KFC3u2lSxZUidPnlRcXJxCQkLSXcNut8lut3mtJpvNe2tlhZTyHA7Tvj+SpeiPudEfczN7fyR6ZAVkd+b5+r9r+mNu9MfczN4fiR5ZAdmdeb7+75r+mBv9MTez90eiR+kx7SA9ISFBQUFBqbalfH3p0qUMBXqBAnm8W1Sv1t5dL4uEhASlf6eciP6YG/0xN4v0R/LhHlkA2X3zfPbfNf0xN/pjbhbpj+TDPbIAsvvm+ey/a/pjbvTH3CzSH8mHe5QO0769kDt3bl2+fDnVtpSvg4ODjSgJAACkgewGAMBayG4AADLOtIP00qVLKyYmRufOnfNsO3LkiIoUKaK8efMaWBkAALgRshsAAGshuwEAyDjTDtLvuusuVa5cWRMmTFB8fLyOHz+uuXPnqlWrVkaXBgAAboDsBgDAWshuAAAyzuZ2u91GF/Ffzp07p7Fjx2r37t2y2+1q3ry5Bg4cKIfDYXRpAADgBshuAACshewGACBjTD1IBwAAAAAAAADAaKY9tQsAAAAAAAAAAGbAIB0AAAAAAAAAgDQwSAcAAAAAAAAAIA0M0gEAAAAAAAAASAODdAAAAAAAAAAA0sAgHTfkdruNLgGZ8Pbbb2vDhg1GlwGYzr9fy3htQ07Gv29rIbuBGyO74Uv4920tZDdwY76U3X5GFwDzcbvdstlsOnfunMLCwmS3836LmSUkJGjHjh3y9/dXUFCQGjdubHRJOZLL5eK5YDHJycny87sWc3FxccqTJ4/sdrucTqccDofB1QHeRXZbC9mdPchu6yG74UvIbmshu7MH2W09vpbdNndOfpsAmZbyDz0uLk5jx45VYGCgxo4dywuZSX344Yd6+OGHlZycrDFjxig+Pl7PPPOMHnvsMaNLy1FSnhenT5/W8ePHdenSJdWsWVP+/v5Gl4b/kPIDmMvl0gsvvKBLly7Jbrdrzpw5CgwMzLGhDt9EdlsL2Z09yG7rIbvhS8huayG7swfZbT2+mN28SsPD5XLJ4XDo0KFDGjFihA4cOKBt27Zp3LhxcrlcRpeHf9m/f7+GDx+uWbNmKSAgQK+88oqCg4O1Zs0abd682ejycgy32y2Hw6H9+/fr6aef1qxZs9S7d29FRkbq008/Nbo83IDT6ZTdbpfb7Va7du3kcrlUr149XbhwQc2aNdOVK1fkcDjkdDqNLhW4ZWS3tZDd2YPsth6yG76E7LYWsjt7kN3W46vZzSAdHna7XceOHdOzzz6r6tWra+LEierVq5dOnDih0aNHE+omU7ZsWc2ePVufffaZpk+fLn9/f40ePZpQ96KUj1teuHBBo0ePVq9evbRs2TJt375dkvTuu+/qyJEjBleJf3M4HHK73fr0009VsmRJzZs3TxEREXrjjTcUGhqao0MdvofsthayO+uR3dZEdsOXkN3WQnZnPbLbmnw1uxmkI5W9e/eqdu3aevbZZ1W+fHl16NBBzzzzjH799Ve9/vrrhLoJ/LMHDz/8sEaMGKFPP/1UM2bMSBXq69at04cffmhgpdaVnJyspKQk2Ww2nTlzRjNmzJDL5dITTzwhSSpatKhee+01/d///Z82btxocLVI8c/nxrp169SnTx/98ssvOnPmjCSpWLFimjx5sgoWLKiHH35YV69ezXEfM4NvIrvNj+zOemS3NZHd8FVkt/mR3VmP7LYmX89uBulI5e+//9bPP//s+drf318PPfSQnE6ntm7dqokTJxLqBkpOTvZ8dOann35SVFSUGjRooDFjxqQK9TFjxig5OVnbt29XfHy80WVbSnJysp555hktXLhQTqdTLpdLf/75p/bv369vvvlG0rV3zPPnz6/HHntMcXFxBleMFCnPjb59+6phw4YaOnSoLly4oF27dunixYuSpBIlSmjcuHEqV66cJ+gBqyO7zY3sznpkt3WR3fBVZLe5kd1Zj+y2Ll/PbgbpPizloxVJSUlKTk6WJD355JMKCQnR22+/7blf3rx5FR4erkcffVR//fWXvvjiC0Pq9XUpV0J2uVxq27atRo4cqccee0yfffaZ6tWr5wn1WbNmyeFwaNq0aRo+fLjy5MljdOmW4ufnp2bNmmnu3LlavHixihYtqqlTp6pmzZrasWOH9uzZI5vNJkk6ePAgf78m8M9rZrtcLsXGxmrJkiWKiIhQkyZNNG/ePH366aeeH27vuecezZ8/X3feeadRJQM3jey2FrI7e5Dd1kN2w5eQ3dZCdmcPstt6yO5r/IwuAMZIucDJ4cOHNWvWLAUGBqpixYrq0KGD6tatq+3bt+vgwYNq1qyZ3nvvPZ07d06jRo1Sr1699Ntvv+mRRx4x+lvwOX5+fnI6nWrfvr2KFi2qQYMGadSoUXr55Zc1duxYNWzYUGPHjtWLL74of39/DRo0SPnz5ze6bEtJOTdbRESEgoKC9Morr0iSunbtqpEjR2rMmDGaNm2aChQooDx58ig6Olq9e/c2uGrflnKV8BQOh0PNmjXTxo0bdenSJY0YMUJOp1MLFy7U1atX9cQTTyhPnjxc+R2WRHZbD9md9chu6yG74UvIbushu7Me2W09ZPf/cES6D0p5Avz1119q27atgoODlZiYqEWLFmnx4sXq16+fnn32WR09elSLFi2Sy+XS4sWLFRISojJlyigoKEhS6nejkHUWLFjg+WjTypUrFRoaqhkzZuj2229XpUqVVKxYMQ0aNEifffaZ6tatq4ULF6p169aed2+RcW632/Pvuk2bNho9erSmTJmixYsXq1ixYho5cqRCQ0O1b98+5cmTR2vXrlVAQIASExMNrtx3pXysrGPHjpoxY4aOHz+u5s2b69KlSxozZowkadSoUbr//vu1evVqXrdgWWS3tZDd2Yfsth6yG76C7LYWsjv7kN3WQ3b/D0ek+yC73a6zZ89q79696tmzp55//nmdP39eW7du1YIFC+R0OtW1a1c1adJE8fHxunDhggICArR06VJt3LhR7733niQRGNng6NGj+vjjjyVJtWrVUkxMjIKDgyVJL730kiRpzZo1evzxxzV8+HA999xz6t69u2H1WtGVK1e0efNmNW/eXHa73XMuQpvNprZt28rtdmvMmDG6++67Va9ePQ0fPlxjx47V8ePH9c0336hWrVoKCAgw+LvwPSlHMUjS8ePHFR0drRUrVig6OlolSpTQ6NGjNXXqVH377beqUaOGJk6cqHPnzilv3rwGVw7cHLLbOsjurEd2WxPZDV9DdlsH2Z31yG5rIruvxxHpPuLixYvasGGDpGvnaOvcubNefvllXblyRZJUoEABNW7cWN26ddN7772niRMnSrr2Yjd9+nQ1btxYq1ev1ttvv6177rnHqG/D55QoUUJt2rTR/PnzdeLECdWsWVPdunXTmjVr9Mcff2jatGmSpPvuu0+NGjVSkyZNDK7YetavX6833nhDixcvlvS/d1pT3kFt166d2rZtq0WLFuny5cued8jtdrvmzZunL7/80sDqfdM/w1yS7rzzTnXo0EFXrlxRyZIltXPnTnXr1k1RUVGpLuJUsGBBI8oFbhrZbU1kd9Yju62H7IavILutiezOemS39ZDdN8Yg3Ud8+umn+vvvv5WYmCiHw6HFixerRIkS2rlzp+cKumFhYWrSpIlat26tU6dOSbr2BBg1apQWLFig5cuXq1y5ckZ+Gz7j8uXLnj8/++yzqlu3rmbNmqX77rtPZcuWVUxMjOrUqSPp2sfODh48qH79+umOO+4wqmTLqlevnlq0aKGNGzd6LvaTEuopFwMqUaKE7rjjDgUFBcnlcumOO+7Q0KFDddtttyk8PNzI8n2SzWaT2+3WhAkTNG7cOElS+/bt1aZNG50+fVpz587Vk08+qZMnT2rZsmW6cuVKjv5oGXIusttayO7sQ3ZbD9kNX0F2WwvZnX3Ibushu2/M5vaF79KHHT9+XGFhYQoODlZycrIeeeQRtW/fXr169dKZM2fUokULlS9fXmPHjlWRIkUkSfHx8QoODpbNZrvuggLIeqNGjZLD4dCjjz6qhx56SJL02WefacWKFerfv7/Kly+v2bNna+7cuapXr55++OEHvfXWWypfvrzBlVvXmTNntHr1an355Zd67LHH1LVr11S3DxgwQIUKFdKwYcM875rb7XbPFd2RPf75ehQdHa21a9fqgw8+UGBgoCIiIhQfH6/jx4+rY8eOKlq0qH744QcVLVpURYsWNbhyIHPIbushu7Mf2W0NZDd8BdltPWR39iO7rYHsThuD9BwsOTlZ48aN04MPPqh7771XV65c0U8//aQpU6Zo4MCB6ty5s86cOaOWLVuqfPnyGjVqlG6//XbP4//9MQ5kvf3796tTp04qXbq0/vzzTz355JNq2bKlypYtq5deeklxcXFauHChpGvnaPPz89MDDzygu+++2+DKreff/75TQv2rr75S/fr11bNnTzmdTr388sv67bfftH79esLbQCk/PLndbv3++++6dOmS7rrrLhUoUECzZs3SiRMndODAAZ0/f17NmjXTkCFDjC4ZuClkt/WQ3dmH7LYWshu+guy2HrI7+5Dd1kJ2p49Beg6WmJioiRMn6ueff9axY8f08ssvq3nz5lq+fLnGjx+voUOHekK9Xr166tKli08+CcwkOTlZgwcPVnJysl588UWNGjVKAQEBKlu2rCIiItSzZ0916NBBrVq1MrpUS0pMTNQvv/yiKlWqSLo+1M+ePatNmzZp2bJlcrvduuuuu3T58mUtXbpU/v7+cjqdcjgcRpXvs1LeEXe5XGrfvr2Cg4O1d+9elS9fXrVr11bPnj11+vRpbd++Xa+99ppCQ0P14YcfKiQkhF9KYDlkt/WQ3VmL7LYmshu+hOy2HrI7a5Hd1kR2ZwyD9Bwq5Qlw9OhRtW7dWsHBwRo8eLAefvhhBQYGavny5ZowYYKGDRumjh07Kjo6Wvny5ePFyiD/DJbTp0+rffv2evXVV1WxYkXt2rVLCxcuVHx8vAoWLKiCBQtq3Lhxyp07t8FVW8/YsWO1f/9+9ejRQ/Xq1ZN0fahfvXpVMTEx2r17t26//XZVqlRJDoeDj5OZQNeuXRUWFqYpU6bozJkz+uijj7R9+3Y1btxYXbp0kST98ccfCgsLU+HChQ2uFsg8sttayO7sQXZbG9mNnI7sthayO3uQ3dZGdqeNQXoO9M/zGR0/flwHDx7Url279Mcff6hp06Zq2rSpgoODtWzZMo0fP17Tpk3zXHWad/6y37x58+Tv769OnTopICBATqdTc+bM0fnz5zV69GhP2Lz99tv6/PPPdfDgQW3ZskX58+c3tnALSUpKkiSdOnVK48ePl8PhUJs2bf4z1P+N54UxXC6XbDabbDabjh07psGDB+uNN95QoUKFJEmXLl3S1KlT9X//93+aN2+eAgICDK4YuHlkt7WQ3VmP7LYmshu+hOy2FrI765Hd1kR2Zw5Xs8hhnE6n7Ha7Tpw4oQ8++EAnTpxQgwYNNHjwYN111136+OOP9cknn0iS6tevr8WLF+vRRx/1PJ4XreyVkJCgH374Qdu3b1fbtm118OBBORwONWrUSNu2bdNXX33lue9zzz2nadOmadOmTYR5JjidTvXs2VOTJ09WkSJFNGrUKCUmJmrlypX64osvJP3vatT/heeFcVJ+0LLZbDp9+rR+//13SdfCPnfu3KpTp46ioqJ09epVI8sEbgnZbS1kd9Yju62N7IYvILuthezOemS3tZHdGccR6TlIyjvi+/fvV+fOnVW6dGnt2bNHL774onr16qXExESNGTNGf/75p86dO6c777xTb731liTx8RkDpLwbe/XqVR09elSzZ8/Wb7/9piZNmqhdu3b69ttvtWrVKs2ePVu33Xab0eVa2sqVK/Xmm2+qadOmioyMVFRUlF555RX5+/urbdu2GX6HHNnjrbfe0rFjx3TgwAE1bNhQ1apV03333af27durePHiGjJkiOc58eabb2rv3r2aNWuWcuXKZXDlQOaR3dZCdmcfsttayG74ErLbWsju7EN2WwvZfXMYpOcwx48fV9euXRUREaEWLVpo6tSpWr58uV544QVFRkYqMTFRH374oU6dOqWePXvK39/f6JJ92r8DZMOGDfrhhx/09ddfq379+jp9+rSaN2+uhg0bGlhlzrB+/XrNmDFDzZs3TxXqAQEBqT5uBmP17NlT0dHRqly5shITE7V9+3YVLVpUL7zwgu666y61atVKlStXVoECBRQWFqYVK1ZoyZIluvfee40uHbhpZLe1kN3Zh+y2BrIbvojsthayO/uQ3dZAdt88Buk5zLJly7Rnzx7NnDlTp0+f1uzZs5U7d24tX75c/fv3V+vWrZUvXz7P/XlH3Bz+eS6w+Ph47dmzR9OmTdPBgwdVq1YtLVy40HP+PaQvISFBH3/8sUqWLOm5UrgkrV27VrNmzVLz5s3Vp08fRUVFaezYsYqLi9PgwYNVqVIlA6tG7969FRUVpTVr1ni27dmzR++8845OnTqlqVOnys/PT++++65OnTqlggULqk2bNipdurSBVQO3juy2JrLbu8huayK74avIbmsiu72L7LYmsvvW8Epucf9+Z/XUqVMqWrSoEhMT1atXL9WoUUNDhgzRrl27NHXqVLlcLvXo0cNzf8I8e/3zgjTS//r3z3OBBQcH6+GHH9a9996rr7/+WpUqVSLMM8HtdmvixInatGmTcufOrcqVK6tChQpq166dWrVqpfz583suJtOrVy+NGDFC7777ripWrGh06T6td+/eiouL84R5UlKS/P39VaVKFfn5+WnkyJFasWKFXn75ZfXv399zgSDOowcrIruthezOemS3NZHd8CVkt7WQ3VmP7LYmsvvW8SphYU6nUzabTZcuXVJiYqIkqV27dnr++ee1YsUKFSxYUEOGDJEklS9fXhMnTtTzzz9vZMk+LTk52RPM0dHRunr1qmw2m5KTk1PdL+UCHIULF1bLli11zz33GFGupd11112qVKmS1q5dqwIFCmjnzp167LHHNG7cON1555168cUXtWXLFs2cOVNFihTR0KFDZbfb5XK5jC7dJ23fvl07d+5M9VHKfwb1Aw88oLZt22r79u2Kj4/33MYPurAisttayO7sQ3ZbC9kNX0J2WwvZnX3Ibmshu72Dvw2LcrlccjgcOnjwoPr166e2bdvqvffeU548eVSwYEGdP39eefLkUXx8vAYOHKjDhw+rWbNmcjgccjqdRpfvc1wul/z8/DxHJvTt21cdO3bUmTNn5Ofnd8NQx82x2Wxq166dDh8+rD179mjUqFF66623FBsbq7179+qpp57Sjz/+qP/7v//TkSNHUp2vkIAwRrly5dS1a1dt3rxZCxculCTPD1gpr1d58+ZV8eLFlSdPHk+g8zyB1ZDd1kJ2Zx+y23rIbvgKsttayO7sQ3ZbD9ntHfzrtaCUjymdOHFCnTt3Vnh4uMqXL685c+Zo1apVunTpkmrVqqVPPvlEXbt21ZEjR/Tuu+96niB8JCN7pbwj7na71aFDBwUEBKhZs2YKCgpSixYt/jPUcXOcTqeCg4P1xBNP6OjRo5Kk9u3bq27dulq9erWWLl0qh8OhJ554QnPmzPEciQDjFC1aVK1bt1atWrW0detWvfXWW5JS/4B17NgxFStWTImJifQLlkR2WwvZnb3Ibushu+ELyG5rIbuzF9ltPWS3d3CxUYtJObfX+fPnNXfuXBUuXFjdu3eXJK1cuVLz5s1T27Zt1atXLx0/flyxsbG699575XA4uMCJgdxut7Zt26YdO3ZowoQJkqSoqCiNGDFC+/bt07p161S4cGHOPeVFX331lSIjI1WoUCGVK1dOr7/+ugICAiRJly9fVlBQkCQu/GOkf59r8syZM1q9erW+/PJLPfroo+rWrZskacWKFZozZ46WLFnCBU5gSWS3NZHd2Y/sNj+yG76C7LYmsjv7kd3mR3Z7F0ekW0jKudmio6O1evVqffTRRzp37pzn9rZt26p79+5as2aNXn/9dYWGhqp8+fKej5XxopW9Ro0apRMnTkiS1q9fr759++rbb7/1vFtbqFAhjR07Vvfff7/q16+vqKgowtyL6tSpowYNGuj222/X5MmTFRAQ4HlHNSXM3W43zwsD7N+/X5KuOyqhcOHCat26terWrautW7dq3bp1niu+L1y4kDCHJZHd1kJ2G4vsNi+yG76E7LYWsttYZLd5kd1Zg0G6RaR8NOzAgQN69dVXVbRoUdWrV0+ff/65vvnmG8/9OnTooPbt2+uvv/5ScHCwZztBkf0CAwN1xx136MyZM3r66ac1cuRIXb16VV9//bWio6MlSUWKFNHIkSPVqFEjxcfHG1xxzlOuXDmdOXMm1fnY/olzfWW/xYsXa8yYMfriiy8k/XeoP/zww5o2bZrGjBmjt99+W+XKlTOqZOCmkd3WQ3Ybj+w2H7IbvoTsth6y23hkt/mQ3VmHU7tYSFRUlGbOnKmyZcvq2Wef1b59+7RixQr99ddf6t27t+rUqeO5b8pHN/79EQ5kvX9+TGzy5Ml677339P7776tkyZKaN2+e1qxZo+eee05PPPGEQkNDr3sMvOvJJ59Uw4YN1a9fP6NL8XnJyck6efKkxo0bp4CAALVu3Vr16tWTdP3HzU6fPq0PP/xQjRo10t13321UycAtI7utgew2F7LbPMhu+CKy2xrIbnMhu82D7M5aHJFuEfHx8WrevLl27typChUqSLr2rl/btm09QbF9+3bP/QlzY/w7mIcMGaIyZcqod+/eOnLkiHr16qVnnnlGS5Ys0bp16xQTEyOJIxeygtvtltPp1N133624uDijy/F5kyZN0sSJE1WkSBGNHj1aV69e1cqVK//zHfIiRYqoa9euhDksjey2BrLbPMhucyG74YvIbmsgu82D7DYXsjvrMUg3uZR/4Hny5FH//v0VHR2tLVu2eG6///771aZNGxUoUEBffvllqscS5tkrOTlZDodDbrdbO3bs0I4dOyRJq1atUr58+VKFepMmTbRhwwZ6lIVsNpscDoeGDRumESNGGF2OzytRooS2b9+uuXPnqlChQhozZoySkpLSDHV+0IVVkd3WQXabC9ltLmQ3fAnZbR1kt7mQ3eZCdmc9Tu1iUinvsF6+fFm5cuXS1atXFRQUpA8//FBDhw5V//79PVfWlaQjR47o7rvvlt3OeyNGSOmXy+VSy5YtZbPZ9Pvvv+vZZ5/Vyy+/LElq3bq14uPjNXPmTJUuXVoXLlzwfMQMWY+P8Rlvw4YNmj59upo3b67IyEhFRUXplVdekb+/v9q1a6e6desaXSJwS8huayG7zY/sNh7ZjZyO7LYWstv8yG7jkd1Zi0G6CblcLtntdh08eFCTJ0+WJJ05c0Y9e/ZUw4YNtXXrVg0bNkwDBgxQ165db/hYZD+3263OnTurWLFiGj58uJYvX66ZM2eqY8eOnndmH3/8cQUFBWnVqlX/eSEOIKfYsWOHbrvtNt13332ebSlXA2/evLn69OmjqKgojRs3TgkJCerZs6dq1aplYMXAzSO7rYnsBlIju+FLyG5rIruB1Mju7OVndAG4nt1u17Fjx/Tcc8+pU6dOatiwoTZt2qSBAwfqnXfe0VNPPSWXy6WhQ4eqUKFCeuqpp1I9Fsb48ssvlZycrIkTJ0q6dn691q1ba9WqVQoICFC/fv30ySef6MSJE4Q5cryFCxdq6tSpKly4sGrWrKkqVaroqaeeUqtWrRQSEqKxY8fKZrOpV69eGjFihKZMmaJ77rnH6LKBm0Z2WxPZDfwP2Q1fQ3ZbE9kN/A/Znf0YpJvM1atXlStXLm3dulUPPfSQunfvLpfLpd27d6t169YKDg7WL7/8oubNmyssLEw1a9Y0umSf9e+jEM6fP6/g4GBJ0qBBg3ThwgUtWLBA0dHReuutt3T+/HmNGzdOd9xxh1ElA9kmNDRUlStX1siRI7Vw4UK9//77mj9/vpo1a6ZmzZppyJAhmjt3rpKTk9W3b19NmzZNfn5EEqyJ7LYOshv4b2Q3fAnZbR1kN/DfyO7sx9uoJnDs2DEtXLhQkhQQECBJSkhIUEhIiCSpRYsWuu222zR27FjNnz9f69atkyTVrVtXfn5+Sk5ONqZwH5acnOwJ8/j4eElSvXr1NHDgQH3xxRf6/fffNW/ePDkcDhUtWlSDBw9W7969eUccPqNZs2Y6deqUfv31V02dOlXvvvuuLl26pLVr16px48b67bffdObMGW3btk0JCQmEOSyH7LYeshtIG9mNnI7sth6yG0gb2Z39+Bs0gUOHDumTTz7R0aNHtXPnTm3evFn58uXTkiVLtGvXLlWrVk2jR4+WJF28eFGPPPJIqsfzRMhebrdbfn5+cjqd6t69u06fPq0aNWqoTZs2KlOmjA4cOKDbbrtNTqdT7733njZt2qT3339fRYoUMbp0IFs4nU75+/urbdu2+uOPPyRJo0ePVmhoqObOnau//vpL77//vsLDwzV69GiFhYUZXDGQeWS3tZDdQNrIbvgCsttayG4gbWS3MbjYqIGSkpJ0+fJlhYSEaNKkSVq8eLGqVKmi5cuXS5KGDRum9evXa/369XI4HHrnnXf0xx9/aO3atYS4Qf75sbKePXsqKChI1apV05o1a1S2bFlFREQoICBAjz32mCpVqqSjR49q4cKFKleunMGVA9lvz5496t27t8qUKaPY2FjNmTNHxYsXl3TtlxO73e75WCZgFWS39ZDdQMaR3ciJyG7rIbuBjCO7sxeDdIOkvKt69913q0ePHlq7dq327dunCxcuqHLlyhowYIASExM1duxY/fTTT7rtttsUEhKiKVOmyN/fX06nUw6Hw+hvw6ckJyd7fpA6cuSI1q9frwEDBshut+ubb77RG2+8oRIlSmjAgAFyuVw6duyY7rzzThUtWtTgygHjvPzyy9q9e7fefPNN3X333XK73bLZbEaXBdwUstt6yG4g88hu5CRkt/WQ3UDmkd3Zh0G6gVauXKk333xTrVq1UpMmTXTXXXdp0aJF+uSTT1S7dm31799fkhQbGyuHw6Hg4GDZbLZUwYLskfIi5HQ61a1bN8XExOiPP/7QypUrdf/998tms+mrr77S/PnzFRYWphdeeEHh4eFGlw0YbvXq1Vq4cKHWr1+vPHnyEOiwPLLbOshu4OaQ3chpyG7rILuBm0N2Zx8uNmqgtm3bKjIyUqtXr9b69esVFxentm3bqkmTJvrmm280ffp0jR49Wn/99Zfy5Mkjm83mOU8Yso/L5fK8AI0YMUIBAQGKjIxUvnz5tHDhQp05c0aSVKdOHXXt2lWXLl1S/vz5DawYMI/WrVsrT548mjFjhiQR5rA8stsayG7g5pHdyGnIbmsgu4GbR3ZnH45Iz0YJCQn6+OOPVbJkSVWpUsWz/f3339fMmTPVokULde3aVQ6HQ6tXr9bmzZsVFxenDz/8kKtOG8ztduvVV1+V2+3W4MGDFRgYqDNnzqh58+aqWLGixowZo8KFC0uSLl++rKCgIIMrBoyX8i742LFjdeLECc2cOZPnBiyH7LYushvIPLIbOQHZbV1kN5B5ZHf2YpCeTdxut0aOHKlNmzYpd+7cqly5sipUqKB27dopODhY27dv15gxY9SyZUtFREQof/78iomJUUhIiOx2O+dmM9jRo0fVv39/HT58WBs3bvRcuOH06dN65plnVKJECU2dOlWFCxfmIzTAvxw7dkySdOeddxpcCZA5ZLe1kd3AzSO7YVVkt7WR3cDNI7uzB6d2yUZ33XWXKlWqpLVr16pAgQLauXOnHnvsMY0bN0533nmn+vbtq82bN+vNN9/UuXPnlD9/ftntdrlcLsI8mzmdzlRflyhRQlOmTFHJkiXVr18/z/YiRYpo5cqVioqKksvlksRHaIB/u/POOwlzWBbZbR1kN+A9ZDesjOy2DrIb8B6yO3twRHo2SkhIUJMmTTRo0CA9+eSTcrvdqlixou655x7t379fTz/9tNatW6e6detqwYIFBINBUo5CcLlcWrp0qeLi4uTn56e2bdsqKipKQ4cOlc1m07p16657DAAgZyG7rYHsBgCkILutgewGYEUckZ5NnE6ngoOD9cQTT+jo0aOSpPbt26tu3bpavXq1li5dKofDoSeeeEJz5szxXOAE2cvtdnvCvFWrVtq5c6fOnDmj9957Ty+88IISEhI0efJkJScn69FHH/U8zm7nqQQAOQ3ZbQ1kNwAgBdltDWQ3AKviiPRs9tVXXykyMlKFChVSuXLl9PrrrysgIEBS6otlJCcnc5VwA40ZM0YxMTGaPn26pGs/kLVv314BAQFatmyZDh06pJEjR2rKlCme87YBAHImstsayG4AQAqy2xrIbgBWw9t52axOnTpq0KCBbr/9dk2ePFkBAQGed8BTwtztdhPmBouOjlbVqlUlXftooMPh0Jw5c/Tjjz9q165dKl26tJYuXUqYA4APILutgewGAKQgu62B7AZgNQzSDVCuXDmdOXNG/v7+N7ydc7Rlr+joaJ09e1bnz5+XdO1d8BMnTuiXX36RJAUHBysxMVG5cuVSuXLlFBYWJkmeIxoAADkf2W0uZDcAID1kt7mQ3QByAgbpBnj++ecVEBCg2bNnSyLAjTRmzBi99NJLatGihVq3bq1FixbJ6XSqT58+2rFjh958801J18J7y5YtiomJ8QQ6AMB3kN3mQXYDADKC7DYPshtATsHnmLKZ2+2Wy+XS3Xffrbi4OKPL8Wk9e/bUuXPnNHz4cEVHR+vQoUOaNm2ajhw5oi5duqhv376aMmWKPvroIxUpUkRHjhzRG2+8oUKFChldOgAgG5Hd5kF2AwAyguw2D7IbQE7CxUYNcvLkSRUuXFgOh8PoUnxSr169dOHCBa1cuTLV9s8//1x9+vRRnz591KtXL/3555/68ssvVbhwYZUvX55zswGADyO7jUV2AwAyi+w2FtkNIKdhkG4wp9NJqGezfv366dChQ9q4caMkKTExUQEBAXK5XLLb7Vq+fLlef/11rVu3Tvfcc4/B1QIAzIbszn5kNwDgVpDd2Y/sBpATcY50gxHm2ev8+fPas2ePypYtq+PHj8vlcnmu4G63X3s61KlTRyEhIXwEEABwQ2R39iK7AQC3iuzOXmQ3gJyKQTp8SoECBfTOO+/owIEDev3113Xw4EFJ1y48k5ycLEkKDAzU7bffrrx58xpZKgAAENkNAIDVkN0AcioG6fA5pUuX1vTp03Xo0CEtWLBA+/fvlyT5+V279u7mzZtls9lUoEABI8sEAAD/H9kNAIC1kN0AciLOkQ6fdejQIb344osKDw9Xjx49dO+99+rdd9/VzJkztWTJEpUtW9boEgEAwD+Q3QAAWAvZDSAnYZAOn5YS6lWqVFGePHm0du1avfPOOypXrpzRpQEAgBsguwEAsBayG0BOwSAdPu/QoUN67rnnFB8fr3fffVf33nuv0SUBAIA0kN0AAFgL2Q0gJ2CQDkg6fvy4JKl48eIGVwIAADKC7AYAwFrIbgBWxyAdAAAAAAAAAIA02I0uAAAAAAAAAAAAM2OQDgAAAAAAAABAGhikAwAAAAAAAACQBgbpAAAAAAAAAACkgUE6AAAAAAAAAABpYJAOAAAAAAAAAEAaGKQDJpScnKwlS5bo6aefVqVKlVS9enV16dJFu3bt8up+9u3bpyeffFLly5fXiy++qKFDh6pjx45e3cfNcLvdWr9+vc6fPy9JWrduncqUKWNwVQAA/Deym+wGAFgL2U12A5llc7vdbqOLAPA/iYmJ6tKli06dOqUXXnhBlSpV0pUrV/T+++9r+fLlmjhxopo3b+6VfUVGRurYsWN64403lCdPHuXKlUtOp1P58+f3yvo367vvvlPHjh316aef6o477tCVK1d08eJFFSpUyNC6AAC4EbKb7AYAWAvZTXYDN8PP6AIApDZr1izt379fGzduVJEiRTzbR4wYoUuXLmnChAlq1KiRgoODb3lfcXFxuu+++3TXXXfd8lre9O/39wIDAxUYGGhQNQAApI3sJrsBANZCdpPdwM3g1C6AiSQlJWnNmjVq1apVqjBP8eKLL2rRokUKDAxUTEyMxowZo3r16un+++9Xu3bttGfPHs9933jjDXXs2FELFy5U3bp1VaFCBXXq1El//vmnJKl+/fr67rvvtGHDBpUpU0a7d+++7iNmv/32mzp06KCKFSuqQYMG+vDDD3Xfffdp9+7dkqSOHTtq6NChqWr85xonTpxQmTJlNHfuXNWqVUv169dXXFycDh06pN69e6t69eoqX768GjVqpCVLlkiSdu/erU6dOkmSGjRooHXr1l33EbNb/d4BAPAWspvsBgBYC9lNdgM3i0E6YCLHjx9XTEyMHnjggRveftttt+n++++XJD333HPas2ePJk+erPXr16ts2bKKiIjQr7/+6rn/jz/+qO+//15vvvmmFi9erJMnT2rMmDGSpLVr16pSpUp6/PHH9fXXX6tSpUqp9nXmzBl17txZxYoV0/vvv69Ro0Zp+vTpcjqdmf6+PvzwQy1ZskQzZ86Uv7+/unTpoty5c+vdd9/Vxo0b9fjjj2vChAn6448/VKlSJb3xxhuSpDVr1qhJkyap1nI6nbf8vQMA4C1kN9kNALAWspvsBm4Wg3TARGJjYyVJ+fLlS/N+X3/9tfbt26epU6eqRo0aKlmypEaNGqXw8HC99dZbnvslJyfrtddeU9myZVW5cmV17NhRe/fulSSFhYXJ399fgYGBKlSokAICAlLtY9WqVQoJCdH48eNVqlQp1atXTyNHjryp76t9+/YqVaqUKlSooMuXL6tTp04aPXq0SpYsqRIlSqhPnz6SpAMHDiggIMDz/YeFhV330TJvfO8AAHgL2U12AwCshewmu4GbxTnSARMJCwuTdO0jVGk5ePCg8ubNq/DwcM82m82mKlWq6KuvvvJsK1iwYKoLmOTNm1dJSUkZquX3339XuXLl5O/v79lWpUqVDD3230qUKOH5c1hYmNq3b69NmzZp//79Onr0qP744w9JksvlSnet7PjeAQDIKLKb7AYAWAvZTXYDN4sj0gETKV68uAoWLKgff/zxhrf/3//9n5577jldvXpVNpvtuttdLpf8/P73/ti/3+3ODIfDkaGA/fcFSm4Umv98d/vcuXN66qmntGrVKhUsWFBt27bVunXrMlyX2+3O8u8dAICMIrvTR3YDAMyE7E4f2Q3cGIN0wETsdrtatWqldevW6cyZM9fdvmjRIv30008qW7as4uLidPDgwVS37927V6VKlfJKLWXLltW+fftSBfTPP/+c6j7+/v66ePFiqm3Hjh1Lc92PPvpIMTExWrlypXr37q1GjRp5PlqX8sPBjQI7RZkyZbL8ewcAIKPIbrIbAGAtZDfZDdwsBumAyfTs2VMlSpRQ27ZttWHDBh07dky//vqrRowYoffff1/jxo3Tww8/rDJlyuill17S7t27deTIEY0ZM0YHDx5U586dvVJH+/btdfHiRY0cOVJHjhzRrl27NHbsWEn/C9wHH3xQO3fu1Geffabjx49r1qxZ1wXtvxUpUkSXL1/WJ598opMnT+rrr7/WgAEDJEmJiYmSpNy5c0uS9u/fr4SEhFSPr1WrVpZ/7wAAZAbZTXYDAKyF7Ca7gZvBOdIBkwkKCtLy5cv19ttva+HChTp58qRy5cqlcuXKacmSJapWrZok6Z133tHkyZP1wgsvKDExUeXKldPixYv/88rjmVWgQAEtWrRIEyZMULNmzVSkSBG1a9dOr732muf8bRERETp+/LgGDRokm82mJk2aKCIiQj/88MN/rvvYY49p3759mjx5suLj41WsWDE988wz+vTTT/XLL7+oXbt2Cg8PV7169dSvXz8NGDAg1TnX/Pz8svx7BwAgM8hushsAYC1kN9kN3Ayb+98nWgIASYcPH1ZsbKwqV67s2fbDDz+oXbt22rFjh4oWLWpgdQAA4N/IbgAArIXsBqyFU7sAuKEzZ86oU6dO2rBhg/7++2/9+OOPmjhxoqpVq0aYAwBgQmQ3AADWQnYD1sIR6QD+07vvvqtly5bpxIkTyps3r+rXr6+BAwem+sgXAAAwD7IbAABrIbsB62CQDgAAAAAAAABAGji1CwAAAAAAAAAAaWCQDgAAAAAAAABAGhikAwAAAAAAAACQBgbpAAAAAAAAAACkgUE6AAAAAAAAAABpYJAOAAAAAAAAAEAaGKQDAAAAAAAAAJAGBukAAAAAAAAAAKSBQToAAAAAAAAAAGlgkA4AAAAAAAAAQBoYpAMAAAAAAAAAkAYG6QAAAAAAAAAA/L/27jwsqrr///hrZgBBlAR3raxUNLdCzSVTSm2zLPeN3MuNMvc9S8stNZVIczeXctey1Ezb1UzNtNvcW9Q0FxBQFIGZ+f3hj/lGGoIOnDPM83Fd9+U9Z86ceU8fmBfnfc75nAzQSAcAAAAAAAAAIAM00gEAAAAAAAAAyACNdACGcjqdHrltAAByAlkGAAAAmAONdCCXWr16tcqVK6eTJ08aXcoNJScna9y4cVq3bl22bH/FihWaMGFCtmwbAIDs8O6776pcuXKux7t371b37t1dj0+ePKly5cpp9erVbn8vAABwc/Xr19eQIUOMLkNS+lrc+TcCgP9GIx2AIc6ePasFCxYoNTU1W7Y/Y8YMxcXFZcu2AQDICStWrNDRo0ddj4sUKaJly5bp0UcfNa4oAABgOvyNAOQMH6MLAAAAAHBzfn5+evDBB40uAwAAmAx/IwA5gzPSAROqX7++pkyZonHjxqlGjRqqUaOGBg4cqAsXLrjW2bp1q9q1a6dq1aqpZs2a6t+/v06fPn3D7X399dcqV66cvv/++3TLf/75Z5UrV04//vhjpmuz2+1asmSJGjdurCpVqujRRx/VpEmTdPXqVdc6Q4YMUf369dO97p+Xmp08eVINGjSQJA0dOtS17pAhQ9S+fXutXLlSjz32mMLCwtShQwf9+uuvru3815Q1/7ysrX79+vrrr7+0Zs0aU09vAwDwTPXr11d0dLTGjRunmjVrKiwsTP3791diYqJmzZqlevXqqVq1anrllVdc2X2jS8EzmoZtyJAhWrNmjf766690+Xkrl21fvXpV48aNU506dRQWFqahQ4emy+00u3bt0gsvvKAHHnhANWrU0ODBgxUbG5tunT179igiIkIPPvigHn30UX3wwQfq1KnTdZeWz58/X08//bRq1Kjhqvfw4cPq3r27qlatqqpVqyoyMlInTpxIt/24uDiNHDlSDz/8sCpXrqxWrVpp+/btWfq8AABkp5SUFL311lt66KGH9NBDD12XlytWrFCzZs304IMPqkqVKnr++ee1fv161/MOh0PTpk1T/fr1ValSJdWvX1/vvPOOUlJSXOtcvXpVb7/9tsLDw1WpUiU1btw43Tb+7d9/I6xevVoVKlTQ3r171bp1a1WuXFmPPvqoZs+ene51WX0fwNvRSAdM6sMPP9Tu3bs1duxYDRgwQN9++61efPFFORwOffzxx+rSpYuKFi2qd955R0OHDtWePXvUunVrxcTEXLetunXrqmjRovr444/TLV+zZo3uuusuPfTQQ5mua+TIkRo7dqzq16+vGTNmKCIiQosXL1avXr0yfUO0IkWKKDo6WpLUs2dP1/+XpAMHDmjKlCl6+eWXNXHiRMXFxal9+/Y6c+ZMpmuMjo5W4cKFFR4ermXLlqlIkSKZfi0AAJkxf/58nTp1SlOmTFGPHj306aefqnnz5tq6davefPNNvfLKK9qyZYuioqJuafu9evVSeHi4ChcufNuXag8cOFDLli3TSy+9pKlTpyo+Pl4LFixIt87OnTvVqVMn+fv7a+rUqRo2bJh+/PFHdejQQUlJSZKkY8eOqVOnTpKkd955R6+88opmzZql3bt3X/eeU6ZMUdeuXfXWW2+pVq1a+v3339WmTRvFxMRo/PjxGjNmjE6cOKG2bdu6/na5evWqOnbsqC1btqhv376Kjo5WsWLF9OKLL9JMBwCYxoYNG/S///1P48eP16BBg/T111+rV69ekqQlS5Zo5MiRatCggWbOnKmJEyfK19dXAwcO1KlTpyRJs2fP1pIlSxQZGal58+apbdu2mjNnjt5//31J1240HhkZqaVLl6pz586aMWOGwsLC1LdvX61duzbTdTocDvXp00eNGjXSrFmzVK1aNU2aNEnfffedW98H8CZM7QKYlMVi0fz585U/f35JUkhIiCIjI/Xtt99q4sSJevjhhzVlyhTX+lWrVlWjRo00b948DRw4MN22bDabmjRpokWLFikxMVGBgYFKTk7Whg0b1LFjR1kslkzVdPToUa1cuVJ9+vRRz549JUl16tRRkSJFNGjQIH377bcKDw+/6Xb8/Px0//33S5LuvvtuVahQwfXcxYsXNWPGDFdzv0qVKmrYsKEWLFigwYMHZ6rOChUqyM/PTyEhIVzeBgDIFoGBgZoyZYp8fHz08MMPa82aNTp79qxWrFih/PnzKzw8XD/88IN++umnW9r+3XffrZCQkHSXal++fDnL2zly5Ig+//xzjRw5UhEREZKuHWBv3LhxuvnXJ0+erHvvvVczZ86UzWaTJD3wwAN65plntGrVKkVERGjmzJnKly+f5syZo4CAAEnSfffdpzZt2lz3vk888YRatGjhety/f3/5+/trwYIFypcvnySpdu3aatiwoebMmaPBgwfr448/1sGDB7V8+XI98MADkqR69eqpffv2mjRpklatWpXlzw8AgLsFBQVpzpw5rjwLDg5WZGSkvv/+e504cUJdunRRZGSka/0777xTzZo1008//aQSJUroxx9/VMWKFdW8eXNJUo0aNRQQEODa3rZt2/Tdd99pypQpatSokaRr2X3lyhVNmjRJzz77rHx8bt7Oczqd6tWrl1q2bClJqlatmr744gt9/fXXqlu3rtveB/AmnJEOmNRjjz3maqJL1y4J9/X11dq1a3Xu3Dk1btw43fp33323wsLCtGPHjhtur3nz5rpy5Yq++OILSdLmzZuVkJCgJk2aZLqmtClg/v3ezzzzjGw223++d1aUKFEi3RnyRYoUUVhY2A3PdgMAwChVqlRJt3NZuHBh3Xfffemyu0CBArp48aIR5bns2rVLklxTqkmS1WrVk08+6Xp85coV7d27V+Hh4XI6nUpNTVVqaqruuusulS5dWlu3bpUk/fDDDwoPD3c10SUpLCxMJUuWvO59Q0ND0z3+4YcfVLNmTfn7+7u2ny9fPlWvXl3btm2TJG3fvl2FCxdWxYoVXevY7XY99thj+t///qf4+Hj3/YcBAOAWhYeHu5re0v/tq2/btk1DhgzRwIEDdfHiRf3yyy9at26dlixZIkmuqVtq1qypbdu2qV27dpo/f76OHTumF154wbVvvn37dlksFoWHh7vyMDU1VfXr19e5c+d05MiRTNcaFhbm+v9pJ5ulHZh35/sA3oJDS4BJ/Xs6EqvVqgIFCrh2IgsVKnTdawoVKpRuPvF/KlWqlB566CGtXbtWTZo00dq1a1WrVq0b7vz+l7T3Lly4cLrlPj4+Cg4Odkuz4EbTsBQsWFD79++/7W0DAOAu/9yBTvPPBrNZpGV3SEhIuuX/zPKEhAQ5HA7Nnj37urlTJSlPnjySpNjYWBUsWPC65//9d4F0/d8pcXFxWr9+/Q3nXU2rLS4uTufOnVPFihVv+FnOnTunO+6444bPAQCQU/6dcWn76gkJCTp+/LhGjhypH374QT4+PrrvvvtUrlw5SXJNhfriiy8qMDBQq1at0oQJEzR+/HiFhoZq2LBhql27tuLi4uR0OlW1atUbvv/Zs2ddV3jfjL+//3W1ptXhzvcBvAWNdMCk4uLi0j222+26cOGC60y38+fPX/eac+fOKTg4+D+32bx5cw0dOlS///67tm7dqnHjxmWpprSd13PnzunOO+90LU9JSdGFCxdc722xWGS329O9NrOXo//7c0vXPmvajnvaNDQOhyPdOomJiZn7EAAAGORWs/F2pGXz+fPnVaJECdfyf+ZtYGCgLBaLOnXqpGeeeea6baQdIChWrNgN78USExOje++9N8M68ufPr4cfflidO3e+7rm0M/vz58+ve+65R5MmTbrhNv75twcAAEZJSEhI9zhtXz04OFjdunWTr6+vli9frgoVKsjHx0dHjx7VJ5984lrfarUqIiJCERERiomJ0TfffKP3339fr7zyirZt26b8+fMrb968Wrhw4Q3fv1SpUm75HDn1PkBuwtQugEl99913Sk5Odj3esmWLUlNT1bZtWxUuXFjr1q1Lt/6JEyf0888//+fRZEl68sknlTdvXo0cOVL+/v564oknslRTjRo1JOm69/7ss89kt9tVrVo1Sdd2yC9cuKCrV6+61vn3HLFp86/+2/Hjx9PN2XrmzBn9/PPPql27tqT/OwPw9OnTrnV+++236xrwVitfbwAA88iXL5/+/vvvdMtuNn+6O7KsVq1akqSNGzemW/7VV1+lq61ChQr67bffVLlyZdf/ypYtq+joaNfUbQ899JC+/fbbdPl+4MABnTx58qZ11KhRQ0ePHtX999/v2n6lSpW0YMEC17RzNWrU0OnTp1WwYMF0dWzfvl1z5sz5z78dAADISdu2bVNqaqrr8eeff67U1FTdf//9+v3339WiRYt0U8B9++23kv7vZLA2bdrorbfeknTt6utmzZopIiJCFy9e1KVLl1SjRg1dvnxZTqczXR4eOXJE7733Xrr3vh059T5AbsIZ6YBJ/f333+rZs6c6dOig06dP65133tEjjzyi2rVrq1+/fho6dKj69u2rJk2a6MKFC4qOjtYdd9xxwzO90gQEBOiZZ57RsmXL1KpVq+su87qZMmXKqGnTpoqOjlZSUpJq1qypAwcOKDo6WjVr1lTdunUlXZvffdGiRRo2bJhatmypI0eOaN68eel2gNPOrN++fbtKly7tuqlY2g1R+vTpI5vNpujoaAUFBal9+/aSrjUEAgICNH78ePXp00eJiYmKjo5WgQIF0tUaFBSkX3/9VT/++KOqVKmS5c8KAIA7PfbYY5o5c6bef/99Pfjgg/r666+1ffv2DF8TFBSk8+fP65tvvrnlS6tLlSql1q1ba8qUKa6d/I8//liHDh1Kt16/fv3UrVs39e/fX88995zsdrvmzZunvXv3um4w3qNHD61fv14vvviiunTpooSEBE2bNk0Wi+WmNy7v1auX2rRpo+7du6tt27bKkyePli1bps2bNysqKkqS1KxZMy1evFidO3dWjx49VLx4cW3btk2zZ8/WCy+8IF9f31v6bwAAgDudP39er7zyitq3b68//vhD77zzjurUqaOnn35akyZN0pIlS1SsWDEFBQXp+++/1wcffCDp2j1JpGsHpufNm6dChQopLCxMZ86c0fz581WjRg2FhIQoPDxcDz30kHr16qVevXqpdOnS2rdvn95991098sgj103Xdqty6n2A3IRGOmBSzzzzjIKCgtSnTx/lzZtXTZs2Vd++fSVd29EMDAzUzJkzFRkZqXz58qlu3brq16/fDecp/afHHntMy5YtU7NmzW6prjFjxqhUqVJatWqV5s6dqyJFiqh9+/aKjIx0nTlXp04dDR48WIsWLdKmTZtUsWJFRUdHq02bNq7t5MuXT507d9ayZcv09ddfu25kVqJECXXu3Fljx47VlStX9PDDD2vGjBmuRnn+/PkVFRWlyZMnKzIyUiVLltTLL7+stWvXpquzS5cuGjt2rLp27ar58+erevXqt/R5AQBwh+7duys2Nlbz5s1TSkqKHn30UY0ZM8bVpL6RZs2a6ZtvvlFkZKR69+6tRo0a3dJ7v/766ypUqJAWL16s+Ph41a1bVz169NDUqVNd6zzyyCOaO3euoqOj1bt3b/n6+qpixYqaP3++HnzwQUnXmvJz587V22+/rd69e6tgwYLq3r27ZsyYocDAwAxrKF++vJYsWaIpU6Zo0KBBcjqdCg0N1Xvvvee6EWrevHm1ZMkSTZ48WRMnTtTFixdVsmRJ9e/fX126dLmlzw4AgLu1atVKSUlJioyMlJ+fnxo3bqyBAwfKYrFo+vTpGjNmjIYMGSI/Pz+VKVNGM2bM0NixY7Vr1y61b99er776qvz8/LRq1Sq99957yp8/v+rXr6/+/ftLunZF2qxZszRt2jTNnDlTMTExKlq0qDp16qTIyEi3fY6ceh8gN7E40+4yAMA06tevrxo1amj8+PFu3/Ybb7yh3bt3Xzc9ixkMGTJEP/74o7788kujSwEAAP+yfft2+fr6pjs4HR8frzp16mjQoEHq0KGDgdUBAAAA2Ysz0gEvsXDhQv32229atmzZdTcZdTgc192880bS5ngDAADGstvtysz5MO7M7v379ysqKkr9+vVTxYoVdeHCBc2bN0/58+fXs88+67b3AQAAAMyIrhjgJXbt2qXvvvtO7du3V5MmTdI9N2zYMK1Zs+am29iyZYvuvPPObKoQAABkVqdOnfTjjz/edL1/z4V+O7p06aLk5GR99NFHOn36tPLmzasaNWpowoQJzKMKAACAXI+pXQDo5MmTunDhwk3XK1eunPz8/HKgIgAAkJHffvtNiYmJN12vcuXKOVANAAAAkPvRSAcAAAAAAAAAIANWowsAAAAAAAAAAMDMaKQDAAAAAAAAAJABGukAAAAAAAAAAGTAx+gCstO5cxeNLiFHWa0WhYQEKjY2UQ4HU9+bDeNjboyP+XnrGBUunN/oEnIU2Q0zYXzMjfExP28dI0/L7ri4OI0dO1bffPONHA6HHnroIb3xxhsqUqRIpl5PdsNMGB9zY3zMz1vHKLPZzRnpuYjVapHFYpHVajG6FNwA42NujI/5MUbIjfi5NjfGx9wYH/NjjDzDK6+8osuXL+uLL77QV199JZvNptdee83oskyLn2tzY3zMjfExP8YoY7n6jHQAAAAAAHBj//vf/7R3715t27ZN+fLlkyS9+eabOnfunMGVAQBgPjTSAQAAAADwQvv27VOZMmW0fPlyffTRR7py5Yrq1q2rwYMHZ3obVqt3nblos1nT/QtzYXzMjfExP8YoYzTSAQAAAADwQvHx8Tp06JAqVaqkNWvWKCkpSYMGDdLgwYM1c+bMTG0jJCRQFov3NNLTBAUFGF0CMsD4mBvjY36M0Y3RSAcAAAAAwAv5+flJkoYPH648efIoX7586tOnj1q1aqXExEQFBgbedBuxsYled0Z6UFCAEhKuyG53GF0O/oXxMTfGx/y8dYyCg2+edxKNdAAAAAAAvFKZMmXkcDiUkpKiPHnySJIcjmuNE6fTmaltOBxOORyZWzc3sdsdSk31niaTp2F8zI3xMT/G6MaY8AYAAAAAAC/08MMP66677tKwYcOUmJio2NhYTZkyRQ0bNnTdfBQAAFxDIx0AAAAAAC/k6+urRYsWyWaz6cknn9STTz6pYsWKaezYsUaXBgCA6TC1CwAAAAAAXqpo0aKaMmWK0WUAAGB6nJEOAAAAAAAAAEAGTNFIt9vtat++vYYMGeJatnfvXrVs2VJhYWGqX7++VqxYYWCFAAAAAAAAAABvZYqpXaKjo7Vr1y6VLFlSkhQfH69u3bqpd+/eat26tXbu3KnIyEiVK1dOVapUMbhaAMh9AlasN7qEm7JYLEr2tckvxS5fp9Pocv7TlZaNjC7Bq+T0zy7jC8AsPCW71bOV0WUAAGAK2ZHd1lNn3bo9i0VKLlXiP/e7vX1/yPBG+vbt27Vp0yY98cQTrmWbNm1SgQIFFBERIUmqXbu2GjdurCVLltBIBwAAHqNFi8aKjY2RzWaTJDmdTlmtNpUtG6pXX+2vChUq3Nb2586dqT17dis6epY7ygUAAAAAt7nZ/lBoaPnb2n5O7w8Z2kiPiYnR8OHDNX36dC1YsMC1/MiRIwoNDU23bpkyZbRy5cosbd9qtchqtbijVI9gs1nT/QtzYXzMzdvHx2Ix/3dlWonX/jVvvT4+3vkzhP82YMBQNWrU2PU4NjZGEya8pWHDBmr16nUGVgYAAAAA2Suj/aHlyz+W1eo5+9CGNdIdDocGDhyozp07q3z59EcfEhMTFRAQkG6Zv7+/Ll++nKX3CAkJ9IjmkLsFBQXcfCUYhvExN28dn2Rfm9ElZJqPj7lrDQwONLoEmFxISEE991wzDR7cVwkJ8XI6r2r06Le0c+ePslgseuSReoqMfFV58177Wfr004+1evUK/f33aaWkpCgsrKqGDn1dwcHBBn8SAAAAAMia9PtDCXI47IqOnqpdu8y/P2RYI33mzJny8/NT+/btr3suICBAFy9eTLcsKSlJgYFZa07ExiZ63RnpQUEBSki4IrvdYXQ5+BfGx9y8fXz8UuxGl3BTFsu1Jnpqql0mniJdiRcS3bq9YBrzuc6ZM39r1apluv/+CgoKukM9e3ZViRJ3aunS1UpJSdGYMaM0YcIYjRo1Vr/++j9NnTpRUVHvq0KFSjp79oxefbWnVq5cqpde6mn0RwEAAACALEm/PxSkHj266K677vaI/SHDGukff/yxzp49q+rVq0u61iiXpM2bN2vQoEHaunVruvWPHj2qsmXLZuk9HA6nHA4Td1uyid3uUGqq9zUCPQXjY27eOj5mvnnn/7l2YNTpvDavmll5488PMjZ58nhFRU1WamqqUlJSVKRIMdWr96g6dOisAwd+1f79+zVlynvy8/OXJL38ch+1a9dc/foNUunSZbRo0XIVL15CCQkJOn/+nAoUCNa5c+69qRAAAAAAZIeM9ocOHvxVhw4d0NSp05U3b15J5t4fMqyRvnHjxnSPhwwZIkkaP368Lly4oIkTJ2rBggWKiIjQ7t27tW7dOk2fPt2IUgEAAG5Z//5D1KhRYyUnJ2vlyqVauHCeateuozvuKKA9e3bJbrfrueeeSvcaPz8/nTr1l8qUCdWKFR9p06aNCgjIqzJlyujy5URTH0wCAACAMQJWrDe6hAxZLBapZyujy0AOy2h/aNeunXI4HGrWrFG615h1f8jQm43+l+DgYM2bN09jxoxRVFSUQkJCNGLECNWqVcvo0gAAAG6Jn5+f2rXroISEBA0dOkDTp89RkSJF5e/vr88//0pO57WrLpKTk3X69CmVLHmnli5drB9/3KGFC5cqJKSgJGnQoL5GfgwAAAAAyLIb7w8VUZ48efTZZ1tks127H5qZ94dMc1vU8ePHa/z48a7HlStX1tKlS/XTTz9p8+bNatasmYHVAQAAuMdLL/VUmTJlNGrUcJUuXUalSpVSVNQ7unz5sq5eTVJU1Dt69dWestvtSkxMlI+Pj3x8fJWamqrPP1+vHTu2KSUlxeiPAQAAAABZ9u/9oTvvvFvR0VM9Yn/INI10AAAAb2Cz2fTaa2/q3Lmzmj49SjNnzlRMTIzatGmq559/Sn/9dUJTprynPHnyqG3bF1SkSFG1aNFYTZo8rc8/36BmzVrpt9+OGv0xAAAAACDL/rk/NGNGtN5+e4piY897xP6QxZmLJ9k8d+6i0SXkKB8fq4KDA3XhQiI3uzMhxsfcvH18zD6XnnRtPj1fX5tSUuymnh/6SstGN18pCwoXzu/W7Zkd2Q0zYXzMzdvHx1OyO7BnK68bI7I7d/P27x6z8/bxMXs2eGsupDH7+EjeO0aZzW7OSAcAAAAAAAAAIAM00gEAAAAAAAAAyICP0QUAAAAAAABI5p/6wGKxSD1bGV0GAMAAnJEOAAAAAAAAAEAGaKQDAAAAAAAAAJABGukAAAAAAAAAAGSARjoAAAAAAAAAABmgkQ4AAAAAAAAAQAZopAMAAAAAAAAAkAEfowsAAACex263q1OnTipZsqTGjx8vSdq7d6/eeustHT16VMHBwerZs6datmyZrXX8vm1otm7/3+59eFyOvh8AAAAA/JeD1lVu3Z5Fkm3DNqWk2OV0Oq973tv3hzgjHQAAZFl0dLR27drlehwfH69u3bqpSZMm2rlzp8aMGaNx48Zp3759BlZpvJdf7qa5c2caXQYAAAAA5Ljctj9EIx0AAGTJ9u3btWnTJj3xxBOuZZs2bVKBAgUUEREhHx8f1a5dW40bN9aSJUsMrBQAAAAAAPdgahcAAJBpMTExGj58uKZPn64FCxa4lh85ckShoaHp1i1TpoxWrlyZpe1brRZZrZZMr2+xZH5dd/Dxydo5CBbLtc+zceOnWrdurSpWrKxPP/1YVqtVXbp0k79/Hi1YMFfx8Ql64oknNXjwcEnS77//pujoqTp69Kji4i6oePESevnlV/XII/UkSYcOHdTkyW/r6NHDKlnyTtWv31Aff7xGa9d+5vbP7M1sNmu6f2Eu3j4+Of39dyvSSvTWMQIAANesX79On376sSpUqKTPPvtENptVnTq9KD+/PPrgg7m6dOmiGjZ8UgMHDpMk/fHH75o+fZqOHj2iuLg4lShRQj179ladOnUlSYcPH9SUKRN19OgRlSx5px57rIHWrVurlSvXZevnoJEOAAAyxeFwaODAgercubPKly+f7rnExEQFBASkW+bv76/Lly9n6T1CQgKz1Bzy9bVlafu3Kzg4MEvr+/raFBDgp8DAPNq792c1avS0duzYoaVLl+qtt97S008/rfXr1+vo0aNq3bq1mjdvqoceekgREYPVoEEDzZz5vpxOpyZNmqRJk8arceOndenSJfXr94pat26tJUsW6ffff1ePHj1ks1mzXB8yJygo4OYrwTDeOj7JOfz9dzu8dYwAAMD/2bfvZ4WHP6bPPtusjz9eralTJ6p+/ce1ZMlK/fHHb+revbMef/wpPfhgVY0YMUiPPBKusWMnyel0asaMKE2ePF516tRVYuIl9e/fW88/30zTps3QiRPHNWhQnxw5yYBGOgAAyJSZM2fKz89P7du3v+65gIAAXbx4Md2ypKQkBQZmrbEbG5uYpTPSU1LsWdr+7bpwITFL66ek2HXlSrISE68qb968eu65FoqPv6LKlavKbrerTZsXFBAQoFKlyqhQocI6fPg3lSlTQRMnTlWhQoUVG3tJf/99Wn5+ATpz5owuXEjUxo0bZLVa9cILXZSYmKIiRe5Uu3YdtHjxB1muDxmz2awKCgpQQsIV2e0Oo8vBv3j7+Pjl8PffrbBYJD/J68aIg5rIzQJWrDe6hAxZLBapZyujywBwAwEBedWqVTtZLBbVqFFLdrtdbdu+IH9/f5UvX0EFCxbS6dOn9OCDVfX229f2hxwOh/7++7Ty5w/SuXNnJUlbt34nm82mLl26yWq1qnTpMoqI6KgPP1yY7Z+BRjoAAMiUjz/+WGfPnlX16tUlXWuUS9LmzZs1aNAgbd26Nd36R48eVdmyZbP0Hg6HUw7H9XeH/y83upN8dkpNzVojyOl0uj5T/vxBstudkpxyOq8dLMibN58kyW53yGKxKDXVrtRUhw4cOKi5c/soNjZGpUrdqwIFCsjpdCo11aHTp/9WkSLF5HBcu0pAkooVK3FL9SFz7HYH/21NzFvHxzeHv/9uzbXvOm8dIwAA8H+CgoJcZ41brdemfcufP8j1vNVqde3fHTlyWEOG9Ltuf0iSzpw5o6JFi7m2IUklSpTMkc9AIx0AAGTKxo0b0z0eMmSIJGn8+PG6cOGCJk6cqAULFigiIkK7d+/WunXrNH36dCNKNaXMXmp4/vw5jRw5RGPGTHTNif7111v0zTdfSZKKFSumM2f+ltPpdG3z779PZ0/RAAAAAOAGuWF/iLu+AACA2xYcHKx58+Zp48aNqlmzpkaMGKERI0aoVq1aRpfmcS5fTpTdbnfNOf/7779p/vw5kqSUlBQ98ki4nE6HFi6cp5SUFB0//oc++miRkSUDAAAAgFuYeX+IM9IBAMAtGT9+fLrHlStX1tKlSw2qJve4++571KvXqxo9eoSSkpJUuHBRPfdcU02fPk3Hjh1V+fL3a/z4d/TOOxO0aNF83XXX3XrooVratetHo0sHAAAAgNti5v0hizOnJxfNQefOXbz5SrmIj49VwcGBunAhkTkITYjxMTdvHx+z3zRIunYZmK+vTSkp9hyfFzsrrrRs5NbtFS6c363bMzuy++bi4+P0559/qEqVB13LVq5cqi1bNmnGjHnZVKl38vZsMDtvHx9Pye7Anq28bozI7ttj9p9tb/25TsP4mBvjY25mHx/JM8YoO/aHMpvdTO0CAADgQex2u159tae2b792c9fTp09pzZqVevjhugZXBgDwROvXr1eFChUUFhbm+t/AgQONLgsAgBsycn+IqV0AAAA8SEhIQY0ePU7vv/+uRo4cqvz58+vpp59V27btjS4NAOCBfvnlFz3//PMaN26c0aUAAHBTRu4P0UgHAADwMHXrPqq6dR81ugwAQC7wyy+/6Omnnza6DAAAMs2o/SEa6QAAAAAAeCGHw6H9+/crICBAc+bMkd1uV3h4uAYMGKA77rjD6PIAADAVGukAAAAAAHih2NhYVahQQU8++aSioqJ04cIFDR48WAMHDtSsWbMytQ2r1SKr1eK2miwW920rO6SVZ7N55y3nGB9zY3zMzezjIzFGN0MjHQAAAAAAL1SoUCEtWbLE9TggIEADBw5Uq1atdOnSJeXLl++m2wgJCXRrcyjZ1+a2bWWnoKAAo0swBONjboyPuXnK+EjeO0Y3QyMdAAAAAAAvdPDgQX366afq37+/qxmenJwsq9UqPz+/TG0jNjbRrWek+6XY3bat7GCxSH6SEhKuyG53GF1OjmN8zI3xMTezj4/kvWMUHByYqfVopAMAAAAA4IUKFCigJUuW6I477lDnzp119uxZTZw4UU2bNs10I93hcMrhcLqtJl+n+7aVPa4dNLDbHUpN9Z4mUxrGx9wYH3Mz//hI3j5GN8OENwAAAAAAeKFixYpp5syZ2rJli2rUqKHmzZurcuXKGjlypNGlAQBgOpyRDgAAAACAl6pRo4aWLl1qdBkAAJgeZ6QDAAAAAAAAAJABGukAAAAAAAAAAGSARjoAAAAAAAAAABmgkQ4AAAAAAAAAQAZopAMAAAAAAAAAkAEa6QAAAAAAAAAAZIBGOgAAAAAAAAAAGaCRDgAAAAAAAABABmikAwAAAAAAAACQARrpAAAAAAAAAABkgEY6AAAAAAAAAAAZoJEOAAAAAAAAAEAGaKQDAAAAAAAAAJABGukAAAAAAAAAAGSARjoAAAAAAAAAABmgkQ4AAAAAAAAAQAYMbaRv375dLVu2VNWqVVWnTh29+eabSkpKkiTt3btXLVu2VFhYmOrXr68VK1YYWSoAAAAAAAAAwEsZ1kiPjY1V9+7d1bZtW+3atUtr1qzRjz/+qFmzZik+Pl7dunVTkyZNtHPnTo0ZM0bjxo3Tvn37jCoXAAAAAAAAAOClfIx645CQEG3btk358uWT0+lUXFycrl69qpCQEG3atEkFChRQRESEJKl27dpq3LixlixZoipVqhhVMgAAAAAAAADACxk6tUu+fPkkSeHh4WrcuLEKFy6sZs2a6ciRIwoNDU23bpkyZXTw4EEjygQAAAAAAAAAeDHDzkj/p02bNik+Pl4DBgxQ7969VbRoUQUEBKRbx9/fX5cvX87Sdq1Wi6xWiztLNTWbzZruX5gL42Nu3j4+Fov5vyvTSrz2r3nr9fHxzp8hAAAAAAByM1M00v39/eXv76+BAweqZcuWat++vS5evJhunaSkJAUGBmZpuyEhgR7RHHK3oKCAm68EwzA+5uat45PsazO6hEzz8TF3rYHBWcsqAAAAAABgfoY10n/66ScNGzZMn3zyifz8/CRJycnJ8vX1VZkyZbR169Z06x89elRly5bN0nvExiZ63RnpQUEBSki4IrvdYXQ5+BfGx9y8fXz8UuxGl3BTFsu1Jnpqql1Op9HV/LfEC4lu3V4wjXkAAAAAAAxnWCO9XLlySkpK0uTJk9W/f3+dO3dOEyZMUIsWLfTkk09q8uTJWrBggSIiIrR7926tW7dO06dPz9J7OBxOORwm7rZkE7vdodRU72sEegrGx9y8dXx8zdyZdrl2YNTplJwmrtcbf34AAAAAAMjtDGukBwYGas6cORo7dqzq1Kmj/Pnzq3HjxoqMjJSfn5/mzZunMWPGKCoqSiEhIRoxYoRq1aplVLkAAAAAAAAAAC9l6BzpZcqU0bx58274XOXKlbV06dIcrggAAAAAAAAAgPSsRhcAAAAAAAAAAICZ0UgHAAAAAAAAACADNNIBAAAAAAAAAMgAjXQAAAAAAAAAADJAIx0AAAAAAAAAgAzQSAcAAAAAAAAAIAM00gEAAAAAAAAAyACNdAAAAAAAvJjdblf79u01ZMgQo0sBAMC0aKQDAAAAAODFoqOjtWvXLqPLAADA1GikAwAAAADgpbZv365NmzbpiSeeMLoUAABM7ZYa6WfPnlV0dLT69eunmJgYbdiwQceOHXN3bQAAwE3cld3bt29Xy5YtVbVqVdWpU0dvvvmmkpKSJEl79+5Vy5YtFRYWpvr162vFihXu/hgAAOD/c0e2x8TEaPjw4Zo8ebICAgKyqVIAAHIHn6y+4M8//1SrVq2UL18+nTlzRn379tWGDRs0bNgwzZ07V1WrVs2OOgEAwC1yV3bHxsaqe/fueuONN9SkSROdP39eXbt21axZs9SxY0d169ZNvXv3VuvWrbVz505FRkaqXLlyqlKlSjZ/QgAAvIs7st3hcGjgwIHq3Lmzypcvf8u1WK0WWa2WW379v1ks7ttWdkgrz2bzzgv8GR9zY3zMzezjIzFGN5PlRvr48ePVsGFDvfXWW65wnjJlioYMGaJ33nlHixcvdnuRAADg1rkru0NCQrRt2zbly5dPTqdTcXFxunr1qkJCQrRp0yYVKFBAERERkqTatWurcePGWrJkCY10AADczB3ZPnPmTPn5+al9+/a3VUtISKBbm0PJvja3bSs7BQV55xn8jI+5MT7m5injI3nvGN1Mlhvpe/bs0eLFi9MFpc1mU48ePdSqVSu3FgcAAG6fO7M7X758kqTw8HCdOXNG1atXV7NmzTR16lSFhoamW7dMmTJauXJllrbv7rPazC7tTA/O+DAnxsfcvH18OKvNu7kj2z/++GOdPXtW1atXlyTXVG2bN2/O0o1HY2MT3Zrdfil2t20rO1gskp+khIQrstsdRpeT4xgfc2N8zM3s4yN57xgFBwdmar0sN9Ltdrscjuv/Q166dEk2m+ccWQEAwFtkR3Zv2rRJ8fHxGjBggHr37q2iRYteN7eqv7+/Ll++nKXtuvusNk/BGR/mxviYm7eOD2e1eTd3ZPvGjRvTPR4yZIika2e7Z4XD4ZTD4czSazLi63TftrLHtb9T7HaHUlO9p8mUhvExN8bH3Mw/PpK3j9HNZLmR/sgjj2jGjBmaNGmSa9mFCxc0ceJE1apVy63FAQCA25cd2e3v7y9/f38NHDhQLVu2VPv27XXx4sV06yQlJSkwMHNH9tO4+6w2s7PZrAoKCvC6Mz48BeNjbt4+PpzVZl6ZPavtdrBfDgBAzstyI33IkCHq0KGDHn74YV29elU9e/bUX3/9pQIFCmjChAnZUSMAALgN7srun376ScOGDdMnn3wiPz8/SVJycrJ8fX1VpkwZbd26Nd36R48eVdmyZbNUq7vPavMUnPFhboyPuXnr+HBWm3fLjv3yrJ6JDgCAt8lyI71o0aJau3atPv30Ux04cEAOh0Nt27bV888/75o3FQAAmIe7srtcuXJKSkrS5MmT1b9/f507d04TJkxQixYt9OSTT2ry5MlasGCBIiIitHv3bq1bt07Tp0/Pxk8GAIB3Yr8cAICcl+VGuiQFBASoZcuW7q4FAABkE3dkd2BgoObMmaOxY8eqTp06yp8/vxo3bqzIyEj5+flp3rx5GjNmjKKiohQSEqIRI0ZweTkAANmE/XIAAHJWlhvpHTp0yPD5hQsX3nIxAADA/dyZ3WXKlNG8efNu+FzlypW1dOnSLNUGAACyjv1yAAByXpYb6SVLlkz3OCUlRcePH9fhw4fVqVMnd9UFAADchOwGACB3IdsBAMh5WW6kjxs37obLo6KiFBMTc9sFAQAA9yK7AQDIXch2AAByntVdG2ratKk2bNjgrs0BAIBsRnYDAJC7kO0AAGQftzXSjx49KqfT6a7NAQCAbEZ2AwCQu5DtAABknyxP7TJ06NDrll28eFFbt27VU0895ZaiAACA+5DdAADkLmQ7AAA5L8uN9JMnT163zM/PT127dlXnzp3dUhQAAHAfshsAgNyFbAcAIOdluZG+aNGi7KgDAABkE7IbAIDchWwHACDnZaqRfurUqUxvsESJErdcDAAAcA+yGwCA3IVsBwDAWJlqpNevX18WiyXDdZxOpywWiw4cOOCWwgAAwK0juwEAyF3IdgAAjJWpRvrChQuzuw4AAOBGZDcAALkL2Q4AgLEy1UivUaNGdtcBAADciOwGACB3IdsBADBWlm82mpycrGXLlunQoUOy2+3plv/yyy/atGmTWwsEAAC3h+wGACB3IdsBAMh5WW6kjx07VqtXr1bFihW1d+9ehYWF6c8//1RMTIw6deqUDSUCAIDbQXYDAJC7kO0AAOQ8a1ZfsHnzZo0fP14fffSR7rzzTr355pv66quv1KBBA6WkpGRHjQAA4DaQ3QAA5C5kOwAAOS/LjfS4uDg9+OCDkqTQ0FD9+uuv8vX1Vffu3fXVV1+5uz4AAHCbyG4AAHIXsh0AgJyX5UZ6oUKFFBMTI0m6++67dfjwYUlScHCwzp8/797qAADAbSO7AQDIXch2AAByXpYb6eHh4Xr99dd16NAhVa1aVevWrdMvv/yiJUuWqFixYtlRIwAAuA1kNwAAuQvZDgBAzstUI/3y5cuu/z9gwAAVK1ZMu3btUoMGDVS2bFm1bNlSixYtUu/evbOtUAAAkHlkNwAAuQvZDgCAsXwys1KdOnX0zDPPqGXLlnrggQc0ffp013OzZs3Sr7/+qkKFCqlIkSLZVigAAMg8shsAgNyFbAcAwFiZOiO9Z8+e+vnnn9W6dWs9++yzWrBggS5cuOB6vkKFCoQ1AAAmQnYDAJC7kO0AABgrU430bt266dNPP9Xy5ctVs2ZNzZw5U/Xq1dOrr76qrVu3ZneNAAAgi8huAAByF7IdAABjZelmo1WqVNFrr72m7777TlOmTJHdblePHj1Uv359RUdH6/Tp09lVJwAAuAVkNwAAuQvZDgCAMTI1R/p1L/LxUcOGDdWwYUPFx8dr48aNWrZsmWbMmKH9+/e7u0YAAHCbyG4AAHIXsh0AgJx1S430NLGxsVq/fr02bNigQ4cOqWrVqu6qCwAAZAOyGwCA3IVsBwAgZ2S5kX7lyhVt3rxZ69at07Zt2xQcHKymTZtq7NixKlWqVHbUCAAAbgPZDQBA7kK2AwCQ8zLVSLfb7fr++++1bt06bdmyRSkpKQoPD9e7776r8PBwWa1ZmmodAABkM7IbAIDchWwHAMBYmWqkP/LII4qLi9M999yjyMhINW3aVAULFszu2gAAwC0iuwEAyF3IdgAAjJWpRvqjjz6qFi1aqFq1atldDwAAcAOyGwCA3IVsBwDAWJlqpI8bNy676wAAAG5EdgMAkLuQ7QAAGMvQSdQOHjyozp07q0aNGqpTp44GDRqk2NhYSdLevXvVsmVLhYWFqX79+lqxYoWRpQIAAAAAAAAAvJRhjfSkpCS9+OKLCgsL0/fff69PP/1UcXFxGjZsmOLj49WtWzc1adJEO3fu1JgxYzRu3Djt27fPqHIBAAAAAAAAAF7KsEb6qVOnVL58eUVGRsrPz0/BwcFq3bq1du7cqU2bNqlAgQKKiIiQj4+PateurcaNG2vJkiVGlQsAAAAAQK6zfft2tWzZUlWrVlWdOnX05ptvKikpyeiyAAAwnUzNkf5PQ4cO1fDhw5UvX750y+Pi4jR8+HC99957mdrOfffdpzlz5qRb9vnnn6tixYo6cuSIQkND0z1XpkwZrVy5Mku1Wq0WWa2WLL3Gk9ls1nT/wlwYH3Pz9vGxWMz/XZlW4rV/zVuvj4/5fobcld0AAMAc3JXtsbGx6t69u9544w01adJE58+fV9euXTVr1iz17t07O0oHAMBjZaqRvnv3bp04cUKStHbtWlWsWPG6wD527Ji2bdt2S0U4nU5NnTpVX331lRYvXqyFCxcqICAg3Tr+/v66fPlylrYbEhLoEc0hdwsKCrj5SjAM42Nu3jo+yb42o0vINB8fc9caGBxodAmSsj+7AQBAzsqObA8JCdG2bduUL18+OZ1OxcXF6erVqwoJCXFr7QAA5AaZaqRbLBYNGTLE9f/feuut69bJmzevunbtmuUCLl26pKFDh2r//v1avHixypUrp4CAAF28eDHdeklJSQoMzFpzIjY20evOSA8KClBCwhXZ7Q6jy8G/MD7m5u3j45diN7qEm7JYrjXRU1PtcjqNrua/JV5IdOv2gm+xMZ+d2Q0AAHJedmV7WjM+PDxcZ86cUfXq1dWsWbNMv97dV4Kb/WS4tPK4ktWcGB/Gx8zMPj4SY3QzmWqkV61aVQcPHpQklS9fXt9//70KFSp0229+/PhxvfTSSypRooRWrlzpOuodGhqqrVu3plv36NGjKlu2bJa273A45XCYuNuSTex2h1JTva8R6CkYH3Pz1vHxNXNn2uVaojud165kMiuz/PxkV3YDAABjZHe2b9q0SfHx8RowYIB69+593VSs/8XdV4J7ypWSXMlqboyPuTE+5uetY3QzWZ4jPS24b1d8fLw6duyoWrVqacyYMbJa/+9Ix+OPP66JEydqwYIFioiI0O7du7Vu3TpNnz7dLe8NAIA3cVd2AwAAc8iObPf395e/v78GDhyoli1bKj4+XnfcccdNX+fuK8HNfqWkxSL5SVzJalKMD+NjZmYfH8l7xyizV4Lf0s1GMzJu3LhMbWf16tU6deqUNmzYoI0bN6Z7bs+ePZo3b57GjBmjqKgohYSEaMSIEapVq1ZWywUAwOu5K7sBAIA5uCvbf/rpJw0bNkyffPKJ/Pz8JEnJycny9fW97r5l/8XdV4Kb/0rJawcNuJLVrBgfc2N8zM+7x+hmstxIP3nyZLrHqampOnHihBITE9WoUaNMb6dz587q3Lnzfz5fuXJlLV26NKvlAQCAf3FXdgMAAHNwV7aXK1dOSUlJmjx5svr3769z585pwoQJatGihauxDgAArslyI33RokXXLXM6nXr99dcVHBzslqIAAID7kN0AAOQu7sr2wMBAzZkzR2PHjlWdOnWUP39+NW7cWJGRke4sFwCAXMEtt2C1WCzq0qWLVq5c6Y7NAQCAbEZ2AwCQu9xqtpcpU0bz5s3Tzp079eWXX6pv376cjQ4AwA24pZEuSefPn9fly5fdtTkAAJDNyG4AAHIXsh0AgOyT5aldoqOjr1t28eJFffbZZ6pTp45bigIAAO5DdgMAkLuQ7QAA5LwsN9JXr1593TJfX1/VrVtX/fr1c0tRAADAfchuAAByF7IdAICcl+VG+pdffpkddQAAgGxCdgMAkLuQ7QAA5LwsN9IlyeFwaPv27Tp8+LCsVqsqVqyo6tWru7s2AADgJmQ3AAC5C9kOAEDOynIj/ezZs+ratauOHDmioKAgOZ1OXbx4UVWrVtXMmTOVP3/+7KgTAADcIrIbAIDchWwHACDnWbP6gtGjR8vPz0/r16/Xjz/+qJ07d2rdunVKSkrS2LFjs6NGAABwG9yZ3QcPHlTnzp1Vo0YN1alTR4MGDVJsbKwkae/evWrZsqXCwsJUv359rVixIjs+DgAAXo/9cgAAcl6WG+nbtm3TqFGjdN9997mWlS1bVq+//rq2bNni1uIAAMDtc1d2JyUl6cUXX1RYWJi+//57ffrpp4qLi9OwYcMUHx+vbt26qUmTJtq5c6fGjBmjcePGad++fdnxkQAA8GrslwMAkPOy3EgPCgpSSkrKdcttNpt8fG5pynUAAJCN3JXdp06dUvny5RUZGSk/Pz8FBwerdevW2rlzpzZt2qQCBQooIiJCPj4+ql27tho3bqwlS5a486MAAACxXw4AgBGynLC9e/fWyJEjNXHiRJUvX16SdOLECY0ZM0aRkZFuLxAAANwed2X3fffdpzlz5qRb9vnnn6tixYo6cuSIQkND0z1XpkwZrVy5Mku1Wq0WWa2WLL3Gk9ls1nT/wlwYH3Pz9vGxWMz/XZlWoreOUXZivxwAgJyX5Ub6tGnTFBMTo6ZNmyowMFA+Pj6Kj4+X0+nUzz//rLfeesu17oEDB9xaLAAAyLrsyG6n06mpU6fqq6++0uLFi7Vw4UIFBASkW8ff31+XL1/OUq0hIYEe0Rxyt6CggJuvBMMwPubmreOT7GszuoRM89Yxyk7slwMAkPOy3Ejv06dPNpQBAACyi7uz+9KlSxo6dKj279+vxYsXq1y5cgoICNDFixfTrZeUlKTAwMAsbTs2NtHrzkgPCgpQQsIV2e0Oo8vBvzA+5ubt4+OXYje6hJuyWCQ/yevGKDg4a9l3K9gvBwAg52W5kW6xWNSoUSP5+fmlW3758mUtX75cnTp1cldtAADADdyZ3cePH9dLL72kEiVKaOXKlQoJCZEkhYaGauvWrenWPXr0qMqWLZulWh0OpxwOZ5ZekxvY7Q6lpnpPk8nTMD7m5q3j4+v0hO/KawdGvXWMshP75QAA5LxMTVYXGxurU6dO6dSpUxo6dKiOHDniepz2v+3bt+udd97J7noBAEAmZEd2x8fHq2PHjqpatarmzp3raqJL0uOPP67z589rwYIFSklJ0Q8//KB169apefPm2fHxAADwOuyXAwBgrEydkf7tt99qyJAhslgscjqdatGixXXrOJ1OhYeHu71AAACQddmR3atXr9apU6e0YcMGbdy4Md1ze/bs0bx58zRmzBhFRUUpJCREI0aMUK1atW77swAAAPbLAQAwWqYa6U2aNFHJkiXlcDjUsWNHRUVF6Y477nA9b7FYlDdvXoWGhmZboQAAIPOyI7s7d+6szp07/+fzlStX1tKlS2+rbgAAcGPslwMAYKxMz5H+0EMPSZIWLlyoqlWryscny9OrAwCAHER2AwCQu5DtAAAYJ8upmzb32n9p0qTJ7dQDAADcjOwGACB3IdsBAMh5WW6kDxky5IbL8+TJo2LFihHYAACYDNkNAEDuQrYDAJDzstxIP3jwYLrHqamp+vPPPzVy5EhFRES4rTAAAOAeZDcAALkL2Q4AQM6z3u4GfHx8VLp0aQ0ZMkTTpk1zR00AACAbkd0AAOQuZDsAANnvthvpafLmzavTp0+7a3MAACCbkd0AAOQuZDsAANkny1O77Ny5M91jp9OpixcvasGCBSpfvrzbCgMAAO5BdgMAkLuQ7QAA5LwsN9Lbt28vi8Uip9OZbvldd92lSZMmua0wAADgHmQ3AAC5C9kOAEDOy3IjfcuWLdct8/X1VZEiRdxSEAAAcC+yGwCA3IVsBwAg52V5jvSSJUvK4XAoJiZGDodDJUqUIKwBADAxshsAgNyFbAcAIOdl+ox0p9OpuXPnatGiRTp79qxreaFChfTCCy/opZdektXqtnuXAgCA20R2AwCQu5DtAAAYJ9ON9N69e+vrr7/W888/r9q1ays4OFjx8fH64YcfNGPGDO3Zs0fvv/9+dtYKAACygOwGACB3IdsBADBOphrpa9eu1Y4dO7RixYrr7gD+9NNPq23bturYsaNWrVql5s2bZ0uhAAAg88huAAByF7IdAABjZeqar2XLlql3797XhXWa8uXLq3fv3lq1apVbiwMAALeG7AYAIHch2wEAMFamGulHjx5VnTp1Mlynbt26OnLkiFuKAgAAt4fsBgAgdyHbAQAwVqYa6ampqbLZbDddz2Kx3HZBAADg9pHdAADkLmQ7AADGylQjvUyZMtq2bVuG63z33XcqXbq0W4oCAAC3h+wGACB3IdsBADBWphrpTZs21bvvvqsTJ07c8PmjR48qOjparVq1cmtxAADg1pDdAADkLtmV7QcPHlTnzp1Vo0YN1alTR4MGDVJsbKw7SgYAIFfxycxKbdq00ddff61mzZqpWbNmCgsLU4ECBXTp0iXt2LFDK1euVN26ddW0adPsrhcAAGQC2Q0AQO6SHdmelJSkF198Ua1atdLMmTOVmJiowYMHa9iwYXr//fez8dMAAOB5MtVIt1qtmjFjhmbMmKElS5bogw8+cD1XqFAh9erVS127ds22IgEAQNaQ3QAA5C7Zke2nTp1S+fLlFRkZKZvNJj8/P7Vu3VqDBg1yd/kAAHi8TDXSJclms+nll1/Wyy+/rN9//11xcXEqUKCASpUqJas1UzPEAACAHER2AwCQu7g72++77z7NmTMn3bLPP/9cFStWzPQ2rFaLrFb33eDU7DdLTSvPZvPOv6UYH3NjfMzN7OMjMUY3k+lG+j/de++97q4DAABkI7IbAIDcxd3Z7nQ6NXXqVH311VdavHhxpl8XEhLo1uZQsq/NbdvKTkFBAUaXYAjGx9wYH3PzlPGRvHeMbuaWGukAAAAAACB3uHTpkoYOHar9+/dr8eLFKleuXKZfGxub6NYz0v1S7G7bVnawWCQ/SQkJV2S3O4wuJ8cxPubG+Jib2cdH8t4xCg4OzNR6NNIBAAAAAPBSx48f10svvaQSJUpo5cqVCgkJydLrHQ6nHA6n2+rxdbpvW9nj2kEDu92h1FTvaTKlYXzMjfExN/OPj+TtY3QzTHgDAAAAAIAXio+PV8eOHVW1alXNnTs3y010AAC8CWekAwAAAADghVavXq1Tp05pw4YN2rhxY7rn9uzZY1BVAACYE410AAAAAAC8UOfOndW5c2ejywAAwCMwtQsAAAAAAAAAABmgkQ4AAAAAAAAAQAaY2gVAjghYsd7oEjJksViU7GuTX4rd9HfSvtKykdElAAAAAAAAeBVTnJEeGxurxx9/XDt27HAt27t3r1q2bKmwsDDVr19fK1asMLBCAAAAAAAAAIC3MryRvnv3brVu3VrHjx93LYuPj1e3bt3UpEkT7dy5U2PGjNG4ceO0b98+AysFAAAAAAAAAHgjQxvpa9as0YABA9S3b990yzdt2qQCBQooIiJCPj4+ql27tho3bqwlS5YYVCkAAAAAAAAAwFsZ2kh/5JFH9MUXX6hRo/Tz/R45ckShoaHplpUpU0YHDx7MyfIAAAAAAAAAADD2ZqOFCxe+4fLExEQFBASkW+bv76/Lly9naftWq0VWq+WW6/M0Nps13b8wF28fH4vF3L+LaeVd+9fctfr4uP9nyOzjI3nOGGXH+AAAAAAAAGMZ2kj/LwEBAbp48WK6ZUlJSQoMDMzSdkJCAj2iOeRuQUEBN18JhvHW8Un2tRldQqb4+Ji/zsDgrH0XZoanjI9k/jHKjvEBAAAAAADGMmUjPTQ0VFu3bk237OjRoypbtmyWthMbm+h1Z6QHBQUoIeGK7HaH0eXgX7x9fPxS7EaXkCGL5VqDNjXVLqfT6Goylngh0e3bNPv4SJ4zRu4en2Aa8wAAAAAAGM6UjfTHH39cEydO1IIFCxQREaHdu3dr3bp1mj59epa243A45XCYuNuSTex2h1JTva9R6ym8dXx8zdz5lJQ2VYjTKTlNXmt2/PyYf3wkTxkjb/z9BgAAAAAgtzPlRK7BwcGaN2+eNm7cqJo1a2rEiBEaMWKEatWqZXRpAAAAAAAAAAAvY5oz0g8dOpTuceXKlbV06VKDqgEAAAAAAAAA4BpTnpEOAAAAAAAAAIBZ0EgHAAAAAAAAACADNNIBAAAAAAAAAMgAjXQAAAAAAAAAADJAIx0AAAAAAAAAgAzQSAcAAAAAAAAAIAM00gEAQJbFxsbq8ccf144dO1zL9u7dq5YtWyosLEz169fXihUrDKwQAAAAAAD3oZEOAACyZPfu3WrdurWOHz/uWhYfH69u3bqpSZMm2rlzp8aMGaNx48Zp3759BlYKAAAAAIB70EgHAACZtmbNGg0YMEB9+/ZNt3zTpk0qUKCAIiIi5OPjo9q1a6tx48ZasmSJQZUCAAAAAOA+NNIBAECmPfLII/riiy/UqFGjdMuPHDmi0NDQdMvKlCmjgwcP5mR5AAAAAABkCx+jCwAAAJ6jcOHCN1yemJiogICAdMv8/f11+fLlLG3farXIarXccn2exmazpvsX5sL4mJu3j4/FYv7vyrQSvXWMAABA7kIjHQAA3LaAgABdvHgx3bKkpCQFBgZmaTshIYEe0Rxyt6CggJuvBMMwPubmreOT7GszuoRM89YxAgAAuQuNdAAAcNtCQ0O1devWdMuOHj2qsmXLZmk7sbGJXndGelBQgBISrshudxhdDv6F8TE3bx8fvxS70SXclMUi+UleN0bBwVk7iAwAADwDjXQAAHDbHn/8cU2cOFELFixQRESEdu/erXXr1mn69OlZ2o7D4ZTD4cymKs3LbncoNdV7mkyehvExN28dH1+nJ3xXXjsw6q1jBAAAchcmqwMAALctODhY8+bN08aNG1WzZk2NGDFCI0aMUK1atYwuDQAAAACA28YZ6QAA4JYcOnQo3ePKlStr6dKlBlVzYwEr1htdQoYsFovUs5XRZQAAAAAAboIz0gEAAAAAAAAAyACNdAAAAAAAAAAAMkAjHQAAAAAAAACADNBIBwAAAAAAAAAgAzTSAQAAAADwcrGxsXr88ce1Y8cOo0sBAMCUaKQDAAAAAODFdu/erdatW+v48eNGlwIAgGn5GF2AJwlYsd7oEjJksViU7GuTX4pdvk6n0eVk6ErLRkaXAAAADOYJf1upZyujywCAbLVmzRpFRUVp4MCB6tu3r9HlAABgWjTSAQAAAADwUo888ogaN24sHx+fW2qkW60WWa0Wt9VjsbhvW9khrTybzTsv8Gd8zI3xMTezj4/EGN0MjXQAAAAA1+GKAcA7FC5c+LZeHxIS6NbmULKvzW3byk5BQQFGl2AIxsfcGB9z85Txkbx3jG6GRjoAAAAAALglsbGJbj0j3S/F7rZtZQeLRfKTlJBwRXa7w+hychzjY26Mj7mZfXwk7x2j4ODATK1HIx0AAAAAANwSh8Mph8N99+gy+/2+pGsHDex2h1JTvafJlIbxMTfGx9zMPz6St4/RzTDhDQAAAAAAAAAAGaCRDgAAAAAAAABABpjaBQAAAAAA6NChQ0aXAACAaXFGOgAAAAAAAAAAGaCRDgAAAAAAAABABmikAwAAAAAAAACQARrpAAAAAAAAAABkgEY6AAAAAAAAAAAZoJEOAAAAAAAAAEAGaKQDAAAAAAAAAJABGukAAAAAAAAAAGSARjoAAAAAAAAAABmgkQ4AAAAAAAAAQAZopAMAAAAAAAAAkAEa6QAAAAAAAAAAZIBGOgAAAAAAAAAAGaCRDgAAAAAAAABABmikAwAAAAAAAACQARrpAAAAAAAAAABkgEY6AAAAAAAAAAAZoJEOAAAAAAAAAEAGaKQDAAAAAAAAAJABUzfSY2Ji1KtXL1WvXl01a9bUmDFjlJqaanRZAAAAAAAAAAAvYupGep8+fZQ3b1599913WrlypbZv364FCxYYXRYAAAAAAAAAwIuYtpH+559/6scff9TAgQMVEBCgu+66S7169dKSJUuMLg0AAAAAAAAA4EV8jC7gvxw5ckQFChRQ0aJFXctKly6tU6dOKSEhQUFBQTfdhtVqkdVqcVtNFov7tpUd0sq79q+5a/XxMe0xnGxjs1nT/ett+P1xn+z4/TH7+EieM0be+P0GAAAAAEBuZ9pGemJiogICAtItS3t8+fLlTDXSQ0IC3dsc6tnKfdvKRn5GF5AJgdmwzeQ5q7Jhq+6VLMnf6CIywe/F5u7fKL8/bpMdvz+eMj6S+ccoW8YHAAAAAAAYyrSN9Lx58+rKlSvplqU9DgzMXJsiNjbRrWekm53NZlVQUIASEq7IbncYXU6O80uxG11ChiwWycfHptRUu5xOo6vJWOKFRKNLyHHe/vvjCbx1jIKDac0DAAAAAGA00zbSy5Ytq7i4OJ0/f16FChWSJB07dkzFihVT/vz5M7UNh8Mph8PkHctsYLc7lJrqPU2mNL5m707//6konE7JafJavfHnJ423/v54EsYIAAAAAADkNNM20u+55x5Vq1ZNY8eO1ejRo3XhwgVNnz5dLVq0MLo0mNSVlo2MLiFDPj5WBQYHKvFCIk1AAAAAAAAAwIOY+o5oUVFRSk1NVYMGDdSqVSvVrVtXvXr1MrosAAAAAAAAAIAXMe0Z6ZJUqFAhRUVFGV0GAAAAAAAAAMCLmfqMdAAAAAAAAAAAjEYjHQAAAAAAAACADNBIBwAAAAAAAAAgAzTSAQAAAAAAAADIAI10AADgNjExMerVq5eqV6+umjVrasyYMUpNTTW6LAAA8B/IbgAAModGOgAAcJs+ffoob968+u6777Ry5Upt375dCxYsMLosAADwH8huAAAyh0Y6AABwiz///FM//vijBg4cqICAAN11113q1auXlixZYnRpAADgBshuAAAyj0Y6AABwiyNHjqhAgQIqWrSoa1np0qV16tQpJSQkGFgZAAC4EbIbAIDM8zG6AAAAkDskJiYqICAg3bK0x5cvX1ZQUNBNt2G1WmS1WtxWk8Xivm1lh7TybDbvPLeB8TE3xsfczD4+EmPkCcjurPP2n2vGx9wYH3Mz+/hIjNHN5OpGeuHC+Y0uwRDBwYFGl4AMMD7mxviYH2NkXnnz5tWVK1fSLUt7HBiYuXErWDCfe4vq2cq928smQUEBN18pN2J8zI3xMTcPGR/Ji8fIA5Ddt85rf64ZH3NjfMzNQ8ZH8uIxugkOLwAAALcoW7as4uLidP78edeyY8eOqVixYsqf3zsPbgMAYGZkNwAAmUcjHQAAuMU999yjatWqaezYsbp06ZJOnDih6dOnq0WLFkaXBgAAboDsBgAg8yxOp9NpdBEAACB3OH/+vEaPHq0dO3bIarWqSZMmGjBggGw2m9GlAQCAGyC7AQDIHBrpAAAAAAAAAABkgKldAAAAAAAAAADIAI10AAAAAAAAAAAyQCMdAAAAAAAAAIAM0EgHAAAAAAAAACADNNIBAAAAAAAAAMgAjXTckNPpNLoEZMG8efO0du1ao8sAABiI7PYsZDcAgOz2LGQ3AB+jC4D5OJ1OWSwWnT9/XiEhIbJaOd5iZomJifr666/l6+urgIAAPfnkk0aXlCs5HA5+FzxQ2vfZfz0Gcguy27OQ3TmD7PZMZDe8BdntWcjunEF2eyZvym5+OpGO3W6XxWJRQkKCxo8fr5EjR8rhcBhdFv7DJ598IrvdrqlTpypfvnxavny5Nm7caHRZuY7dbpfVatXff/+tnTt36ptvvlFKSorRZeEmUlNTXeGdkJAgh8Mhi8Uiu91ucGWAe5HdnoXszhlkt2ciu+EtyG7PQnbnDLLbM3lbdlucXEuE/y/tyN+RI0cUFRWlP/74Q2fPnlWjRo302muvcVTQZA4ePKgWLVqoTZs26tOnj5KTk/XGG28oMTFRLVu21FNPPWV0iblC2pHUgwcPqkuXLipdurR++ukn1alTR61bt1aDBg2MLhE3kPZ95nA49Morr+jy5cuyWq1677335O/vL7vdLpvNZnSZwG0juz0L2Z0zyG7PRHbDW5DdnoXszhlkt2fyxuzmGxouVqtVx48f1wsvvKCaNWtq3Lhx6tmzp06ePKk33niDI+QmU758eUVHR+vLL7/UlClT5OvrqzfeeEOBgYFasWIFR8jdIC3ML1y4oDfeeEM9e/bUokWLtHnzZknShx9+qGPHjhlcJf4t7UwGp9Optm3byuFwKDw8XBcuXNDzzz+vpKQk2Wy2XHuEHN6F7PYsZHf2I7s9E9kNb0J2exayO/uR3Z7JW7ObRjrS2b17tx555BG98MILqlSpkiIiItSyZUv98ssvmjRpEqFuAv8cg0cffVTDhw/Xli1bNHXq1HShvnr1an3yyScGVuq5UlNTlZKSIovFojNnzmjq1KlyOBx65plnJEnFixfX22+/rT/++EOfffaZwdXi32w2m5xOp7Zs2aLSpUtrxowZ6tSpk959910FBwfn6lCHdyK7zY/szn5kt2cju+FtyG7zI7uzH9nt2bw1u2mkI52//vpLe/fudT329fVV7dq1ZbfbtWnTJo0bN45QN1BqaqrriN/PP/+sc+fOqUGDBho1alS6UB81apRSU1O1efNmXbp0yeiyPUpqaqpatmyp2bNny263y+Fw6LffftPBgwe1detWSdeOmBcoUEBPPfWUEhISDK4Yaf753bR69Wq9/PLL2rdvn86cOSNJKlmypCZMmKBChQrp0Ucf1dWrV3PdZWbwTmS3uZHd2Y/s9lxkN7wV2W1uZHf2I7s9l7dnN410L5Z2RCglJUWpqamSpGeffVZBQUGaN2+ea738+fMrNDRUTzzxhH7//Xd98803htTr7VJTU+Xj4yOHw6E2bdrotdde01NPPaUvv/xS4eHhrlCPioqSzWbTO++8o2HDhilfvnxGl+5RfHx89Pzzz2v69OlasGCBihcvrsmTJ+vhhx/W119/rV27drlupHH48GH++5pI2h+7vXv3VsOGDTVkyBBduHBB27dv18WLFyVJpUqV0ptvvqmKFSu6gh7wJGS3ZyG7cwbZ7bnIbngDstuzkN05g+z2XN6e3dxs1Eul3RDg6NGjioqKkr+/vx544AFFRERo6tSp+vHHH3X33Xfr+eef10cffaTz58/r/fffV8+ePVWrVi298sorRn8Er2S329WuXTsVL15cAwcO1MiRI3XgwAGNHj1aDRs21LfffqtXX31Vbdu21cCBA13Bg8xJm5tNkpYtW6bXX39dAwcOVNeuXfXXX39p1KhRunTpkgoWLKh8+fLp8OHD+uijj+Tn52dw5d7tn+Nmt9vVpUsXVatWTb1799aYMWP07bffqmfPnmrYsKHrD7CUlBT5+voaWTaQZWS3ZyK7sxfZ7ZnIbngLstszkd3Zi+z2TGT3NZyR7oXSwvz3339XmzZtFBgYqOTkZM2ZM0cLFixQnz599MILL+jPP//UnDlz5HA4tGDBAgUFBalcuXIKCAiQdO2XCNlv5syZrkubli5dquDgYE2dOlUlSpRQWFiYSpYsqYEDB+rLL79UvXr1NHv2bLVq1YowvwVOp9P1c926dWu98cYbmjhxohYsWKCSJUvqtddeU3BwsPbv3698+fJp5cqV8vPzU3JyssGVey+Hw5HuZ91ms+n555/X3r17dfnyZQ0fPlx16tTR7Nmz9dlnn7kuucxtYY7cj+z2LGR3ziG7PQ/ZDW9BdnsWsjvnkN2eh+z+Bye80pkzZ5wrVqxwzp492+l0Op3nz593fvjhh87w8HDnnDlzXOtdvHjRefz4cafT6XR+8MEHzho1ajiPHTtmSM3e6I8//nA+++yzzvfff9/pdDqd0dHRzn79+jmdTqezb9++zr59+zqdTqfzqaeectasWdM5c+ZMw2r1VFeuXHGuWbPG9dhutzsdDofr8YcffugsV66c8+uvv3Y6nU7nyZMnnd26dXN2797d+d1336VbF8ZwOBzOF154wTllyhTn8ePHnXa73dmmTRvnoEGDXOsMGTLE2axZM2dCQoKBlQK3h+z2DGR39iO7PR/ZDW9BdnsGsjv7kd2ej+y+xsfoRj5yxsWLF7VlyxY1adJEdrtdHTt21O+//66XX35ZklSwYEE9+eSTcjgcmj9/vs6ePauhQ4cqKSlJU6ZM0f79++Xr66t58+bpvvvuM/jTeI9SpUqpdevWmjx5sp555hk9/PDDatCggVasWKEDBw5ow4YNkqQKFSoob968atSokcEVe541a9Zozpw5iouLU6dOnWS1Wl03z7BYLGrbtq0OHTqkOXPmqEaNGq4j5GPHjtWMGTNkt9sVHh5u8KfwPs5/XFZ24sQJxcbGasmSJYqNjVWpUqX0xhtvaPLkyfrhhx9Uq1YtjRs3TufPn1f+/PkNrhzIPLLbM5Hd2Y/s9kxkN7wB2e2ZyO7sR3Z7JrL7ekzt4iW2bNmiv/76S8nJybLZbFqwYIFKlSqlbdu2uSb+DwkJUaNGjdSqVSudPn1aklSoUCGNHDlSM2fO1OLFi1WxYkUjP4bXuHLliuv/v/DCC6pXr56ioqJUoUIFlS9fXnFxcapbt66ka5edHT58WH369NGdd95pVMkeKzw8XE2bNtVnn33mutlP2s0z0m4GVKpUKd15550KCAiQw+HQnXfeqSFDhqhIkSIKDQ01snyv9M8wl6S7775bERERSkpKUunSpbVt2za99NJLOnfunPbu3etar1ChQkaUC9wystuzkN05h+z2PGQ3vAXZ7VnI7pxDdnsesvvGuNloLnfixAmFhIQoMDBQqampeuyxx9SuXTv17NlTZ86cUdOmTVWpUiWNHj1axYoVkyRdunRJgYGBslgsrnndkHNGjhwpm82mJ554QrVr15Ykffnll1qyZIn69u2rSpUqKTo6WtOnT1d4eLh++uknzZ07V5UqVTK4cs915swZLV++XN9++62eeuopde3aNd3z/fr1U+HChTV06FDXfG5Wq9V1R3fkPKfTqXHjxslut+u1116TJL311lvy9fVVnz59NG3aNK1atUq+vr7avHmz8uTJw/yF8Bhkt+chu3Me2e15yG7kZmS35yG7cx7Z7XnI7uvRSM/FUlNT9eabb6pq1aq6//77lZSUpJ9//lkTJ07UgAED1LFjR505c0bNmzdXpUqVNHLkSJUoUcL1+n8ffUL2O3jwoDp06KCyZcvqt99+07PPPqvmzZurfPny6t+/vxISEjR79mxJ0ooVK+Tj46MHH3xQ9957r8GVe55//3ynhfp3332n+vXrq0ePHrLb7RoxYoT+97//ac2aNYS3wf65gxEbG6uVK1fq448/lr+/vzp16qRLly7pxIkTat++vYoXL66ffvpJxYsXV/HixQ2uHMg8stvzkN05h+z2PGQ3vAHZ7XnI7pxDdnsesjtjNNJzseTkZI0bN0579+7V8ePHNWLECDVp0kSLFy/WmDFjNGTIEFeoh4eHq3Pnzho8eLDRZXu11NRUDRo0SKmpqXr11Vc1cuRI+fn5qXz58urUqZN69OihiIgItWjRwuhSPVJycrL27dun6tWrS7o+1M+ePav169dr0aJFcjqduueee3TlyhUtXLhQvr6+stvtstlsRpXv1dLOQnA6nfr11191+fJl3XPPPSpYsKCioqJ08uRJHTp0SDExMXr++ef5LoPHIrs9D9mdvchuz0V2w1uQ3Z6H7M5eZLfnIrtvjkZ6LpV2BOnPP/9Uq1atFBgYqEGDBunRRx+Vv7+/Fi9erLFjx2ro0KFq3769YmNjdccdd/BlZZB/Bsvff/+tdu3a6a233tIDDzyg7du3a/bs2bp06ZIKFSqkQoUK6c0331TevHkNrtrzjB49WgcPHlT37t1dNyr5d6hfvXpVcXFx2rFjh0qUKKGwsDDZbDYuJzNQ2veZw+FQu3btFBgYqN27d6tSpUp65JFH1KNHD/3999/avHmz3n77bQUHB+uTTz5RUFAQZ/fAo5DdnoXszhlkt2ciu+EtyG7PQnbnDLLbM5HdmUMjPRf652UYJ06c0OHDh7V9+3YdOHBAjRs3VuPGjRUYGKhFixZpzJgxeuedd1x3nebIX86bMWOGfH191aFDB/n5+clut+u9995TTEyM3njjDdcX0rx58/TVV1/p8OHD+vzzz1WgQAFjC/cgKSkpkqTTp09rzJgxstlsat269X+G+r/xe2EOXbt2VUhIiCZOnKgzZ85o3bp12rx5s5588kl17txZknTgwAGFhISoaNGiBlcLZA3Z7VnI7uxHducOZDdyM7Lbs5Dd2Y/szh3I7ozRSM9l0r54Tp48qd27d6tIkSKqXbu2kpOTNWrUKP35559q0qSJWrRoob/++ksnTpxQ9erVOeJnkMTERPXp00cXL15UcnKyxo8fr9DQUB04cEBdu3bV+PHjVa9ePdf6586dk9VqVcGCBQ2s2rPY7XZ169ZN9957rwYNGqRz587p9ddfl6+vr9q0aZPpUEfOczgcslgsslgsOn78uAYNGqR3331XhQsXliRdvnxZkydP1h9//KEZM2bIz8/P4IqBW0N2exayO/uR3Z6L7Ia3ILs9C9md/chuz0V2Zw23hc5FHA6HbDabDh48qObNm2vFihXq3Lmz6wf99ddfV6lSpbRq1So9/vjjGjlypGrVqiUfHx+lpqYaXb7XcTqdCgwMVHR0tEaPHq0777xTPXr00KRJkxQUFKT+/fsrOjpaZ8+edb2mcOHChHkW2Ww2Pf744/ryyy/13nvvqXDhwho1apRSUlK0dOlSffPNN5Iki8UijiuaT9ofWRaLRX///bd+/fVXSde+7/Lmzau6devq3Llzunr1qpFlAreM7PYsZHfOILs9G9mN3I7s9ixkd84guz0b2Z15HA7NRaxWq06cOKHevXvr1VdfVdOmTTV58mRNmzZNDodDkZGRev311/XJJ5/o9OnT6tGjh+u1HBnPeWkBkidPHoWGhioqKkpr167VTz/9pPbt26t+/foqUqSI9u3bp4YNGxpdrkdr06aN8uTJo6lTp0qSIiMjNWrUKL3++utatmyZJCk8PJwj4yYxd+5cHT9+XIcOHVLDhg1Vo0YNVahQQUWKFNEnn3yi+++/X0WKFJEkHT58WMWLF/f6o+LwXGS3ZyG7cw7Z7VnIbngTstuzkN05h+z2LGT3rWFql1xm0aJF2rVrl6ZNm6a///5b0dHRyps3rxYvXqy+ffuqVatWuuOOO1zrcyMHc/jnXGCXLl3Srl279M477+jw4cOqU6eOZs+e7Zp/DzeXmJioTz/9VKVLl3bdKVySVq5cqaioKDVp0kQvv/yyzp07p9GjRyshIUGDBg1SWFiYgVVDknr06KHY2FhVq1ZNycnJ2rx5s4oXL65XXnlF99xzj1q0aKFq1aqpYMGCCgkJ0ZIlS/TBBx/o/vvvN7p04JaR3Z6J7HYvsttzkd3wRmS3ZyK73Yvs9lxk963jm9zD/Xt+qdOnT6t48eJKTk5Wz549VatWLQ0ePFjbt2/X5MmT5XA41L17d9f6hHnO+ucNaaT/G79/3lAjMDBQjz76qO6//359//33CgsLI8yzwOl0aty4cVq/fr3y5s2ratWqqXLlymrbtq1atGihAgUKuG4m07NnTw0fPlwffvihHnjgAaNL93q9evVSTEyMVqxY4Vr29NNPa/78+Zo8ebImT56s5cuX68MPP9Tp06fl6+urJUuWqGzZsgZWDWQd2e1ZyO7sR3Z7LrIb3oLs9ixkd/Yjuz0X2X17OCPdg6UdTb18+bJ8fHzk5+enEydOKCAgQOvWrdO2bds0e/ZsSdLQoUNVo0YNPffcc9wF2SD/PAshNjZWgYGBypMnzw3PTuAGHLfO6XRq7ty52r59u8aMGaNZs2bpjz/+0JEjR/TEE0+odevW2rt3r+bOnavHHntMffv2dV2e9O8/uJBzevXqpYSEBC1evFjStTu++/r6SpJ+/vlnvfbaa6pZs6ZGjBih5ORk+fn5cVd3eCSy27OQ3TmD7PZMZDe8BdntWcjunEF2eyay+/bxk+uh0m5wcvjwYfXp00dt2rTRRx99pHz58qlQoUKKiYlRvnz5dOnSJQ0YMEBHjx7V888/L5vNJrvdbnT5XsfhcMjHx8d1ZkLv3r3Vvn17nTlz5oY3nSHMb53FYlHbtm119OhR7dq1SyNHjtTcuXMVHx+v3bt367nnntOePXv0xx9/6NixY67QkESYG2Tz5s3atm1bujkJ/xnUDz74oNq0aaPNmzfr0qVLrucYL3gastuzkN05h+z2PGQ3vAXZ7VnI7pxDdnsests9+K/hgdKO3p08eVIdO3ZUaGioKlWqpPfee0/Lli3T5cuXVadOHW3YsEFdu3bVsWPH9OGHH8pqtbr+EEDOSU1NldVqldPpVEREhPz8/PT8888rICBATZs2/c9Qx62x2+0KDAzUM888oz///FOS1K5dO9WrV0/Lly/XwoULZbPZ9Mwzz+i9997jruEmULFiRXXt2lUbN250nc2T9n2VtgOSP39+3XXXXcqXL5/rO4w/fOFJyG7PQnbnLLLb85Dd8AZkt2chu3MW2e15yG73YKIuD+N0OmW1WhUTE6P58+erc+fO6tatmySpQoUKmjFjhpxOp3r27KkvvvhC8fHxuv/++2Wz2bjBiUF8fHzkdDr1xRdf6N5779XYsWMlSY8++qiGDx+uZs2aafXq1SpatCiXzLhB2n+/2rVrKzIyUqtXr1bFihU1adIk+fn5qUaNGqpcubICAgIkceMfMyhevLhatWolSdq0aZOsVqu6du0qq9XqCvTjx4+rZMmSSk5Olq+vL2EOj0J2ex6yO2eR3Z6H7EZuR3Z7HrI7Z5Hdnofsdg/OSPcgdrtdFotFsbGxWr58udatW6fz58+7nm/Tpo26deumFStWaNKkSQoODlalSpVcl5XxpZWzRo4cqZMnT0qS1qxZo969e+uHH35wHa0tXLiwRo8erSpVqqh+/fo6d+4cYe5GdevWVYMGDVSiRAlNmDBBfn5+riPgaWHudDr5vTDQP89IKFq0qFq1aqV69eqlO0Jus9m0ZMkSffjhh+ratav8/PwIc3gUstuzkN3GIrvNj+yGNyC7PQvZbSyy2/zIbvfiJ9lDpF0adujQIc2cOVP16tVTeHi4vvrqK4WHh6tOnTqSpIiICF25ckV79uxRYGCg6/UERc7z9/fXnXfeqTNnzqhZs2a6cuWKpk+fru+//1758+dXSEiIihUrptdee01vv/22Ll26pMKFCxtddq5SsWJFLV++PN18bP9EMBjj4MGDKl++vOvyvrRxSAt16doR8oIFC8rhcCgqKkrz5s3jLuHwOGS35yG7jUd2mxPZDW9Bdnsestt4ZLc5kd3Zw+JkkiKPce7cOU2bNk3ly5fXCy+8oP3792vJkiX6/fff1atXL9WtW9e1btovCXehznn/vExswoQJ+uijj7Rq1SqVLl1aM2bM0IoVK9SlSxc988wzCg4Ovu41cK9nn31WDRs2VJ8+fYwuBZIWLFigzz//XD169FB4eLgkXfc9debMGa1cuVIfffSR4uPjtXTpUlWsWNGokoHbQnZ7BrLbXMhucyG74W3Ibs9AdpsL2W0uZHf24Yx0D3Hp0iU1adJEefLkUcuWLSVdO+rXpk0bLV++XDNmzNDVq1ddd98lzI3x72AePHiwfvrpJ/Xq1UvTp09Xz549JUkffPCBrl69qubNm6tAgQKEeTZwOp1yOBy69957lZCQYHQ50LV58erXr6+tW7dq+fLlkqTw8PAbHiFv3ry5fH199fjjj+vee+81smzglpHdnoHsNg+y23zIbngbstszkN3mQXabD9mdvTgj3eT++UO+cuVKvfXWW2rXrp0GDRrkWueXX37RrFmzFBwcrNGjRxtVqtdLu3mG0+nUN998I+najU0kqVWrVoqPj9f06dNVunRpTZkyRV9++aUWL16sO+64w8Cqc79Tp06paNGi/NFksPHjxyslJUWDBw/WuXPn9Prrr8vX11dt2rT5zyPknDECT0V2ew6y25zIbnMgu+FNyG7PQXabE9ltDmR39qORblJpP8hXrlxRnjx5dPXqVQUEBOiTTz7RkCFD1LdvX7300kuu9Y8dO6Z7771XViv3jzVC2ng5HA41b95cFotFv/76q1544QWNGDFC0rVQv3TpkqZNm6ayZcvqwoULrkvMkP0IB2N99NFHev/999W0aVP16tUr06EOeBKy27OQ3eZHdhuL7IY3ILs9C9ltfmS3scju7Ecj3YQcDoesVqsOHz6sCRMmSLo2d1GPHj3UsGFDbdq0SUOHDlW/fv3UtWvXG74WOc/pdKpjx44qWbKkhg0bpsWLF2vatGlq3769hg8fLkl6+umnFRAQoGXLlv3njTiA3Grt2rWaMmWKmjRposjIyHSh3rZtW9WrV8/oEoFbRnZ7JrIbyBjZjdyM7PZMZDeQMbI7ezFHuglZrVYdP35cXbp0UYcOHdSwYUOtX79eAwYM0Pz58/Xcc8/J4XBoyJAhKly4sJ577rl0r4Uxvv32W6WmpmrcuHGSrs2v16pVKy1btkx+fn7q06ePNmzYoJMnTxLm8Apff/21ihQpogoVKkiSmjRpotTUVEVFRcnpdOrll1/WqFGj9Oabb2r27Nmy2WyqU6eOwVUDt4bs9kxkN5Ae2Q1vQnZ7JrIbSI/szlk00k3m6tWrypMnjzZt2qTatWurW7ducjgc2rFjh1q1aqXAwEDt27dPTZo0UUhIiB5++GGjS/Za/z4LISYmRoGBgZKkgQMH6sKFC5o5c6ZiY2M1d+5cxcTE6M0339Sdd95pVMlAjpk9e7YmT56sokWL6uGHH1b16tX13HPPqUWLFgoKCtLo0aNlsVjUs2dPDR8+XBMnTtR9991ndNnALSG7PQfZDfw3shvehOz2HGQ38N/I7pzHYVQTOH78uGbPni1J8vPzkyQlJiYqKChIktS0aVMVKVJEo0eP1vvvv6/Vq1dLkurVqycfHx+lpqYaU7gXS01NdYX5pUuXJF27C/KAAQP0zTff6Ndff9WMGTNks9lUvHhxDRo0SL169eKIOLxGcHCwqlWrppkzZyo5OVmrVq1So0aNFB0drfvvv1+DBw/Wpk2b9O6776pIkSJ65513VLx4caPLBjKN7PY8ZDeQMbIbuR3Z7XnIbiBjZHfOo5FuAkeOHNGGDRs0YsQINWjQQMnJybrjjju0Zs0aNWrUSGFhYZo8ebIk6eLFi6pYsWK61/v4cGFBTnI6nfLx8ZHdblfXrl3VunVrvfnmm4qJiVG5cuUUHx+vIkWKyG63a+HChVq/fr0aNWqku+++2+jSgRzz/PPP6/Tp0/rll180efJkffjhh7p8+bJWrlypJ598Uv/73/905swZffHFF0pMTOR7DB6H7PYsZDdwc2Q3cjuy27OQ3cDNkd05j0a6gVJSUpSQkKAGDRqoRo0aWrlypUqUKCE/Pz916tRJTz75pH777Te1bt1ahw8f1tChQ3Xx4kU1bdrU6NK9lsPhcN3ZODIyUkFBQXrhhRe0Z88eLViwQIcPH1aVKlW0fft2de7cWe+//75mzZqlYsWKGVw5kHPsdrvrruAHDhyQJL3xxhsKDg7WwoULNWPGDJ0+fVqhoaGKiopSSEiIwRUDmUd2ex6yG7g5shu5Gdntechu4ObIbmNYnE6n0+givJHdble3bt107733qnv37lq5cqX279+vCxcuqFq1aurXr5+Sk5M1evRo/fzzzypSpIiCgoI0ceJE+fr6ym63y2azGf0xvEpqaqrr6N2xY8e0Zs0a9evXT1arVVu3btW7776rUqVKqV+/fnI4HDp+/LjuvvtuLpuB19q1a5d69erlOmPkvffe01133SXp2lk+VqvVNb8h4AnIbs9DdgNZQ3YjtyG7PQ/ZDWQN2Z2zaKQbaOnSpZo1a5ZatGihRo0a6Z577tGcOXO0YcMGPfLII+rbt68kKT4+XjabTYGBgbJYLOmCBTnD6XTKYrHIbrfrpZdeUlxcnA4cOKClS5eqSpUqslgs+u677/T+++8rJCREr7zyikJDQ40uGzDciBEjtGPHDs2aNUv33nuv63cJ8FRkt+cgu4FbQ3YjtyG7PQfZDdwasjvnMLWLgdq0aaPIyEgtX75ca9asUUJCgtq0aaNGjRpp69atmjJlit544w39/vvvypcvnywWi2ueMOScf15WNnz4cPn5+SkyMlJ33HGHZs+erTNnzkiS6tatq65du+ry5csqUKCAgRUD5lGlShVJUuHChQ2uBHAPstszkN3ArSO7kduQ3Z6B7AZuHdmdczgjPQclJibq008/VenSpVW9enXX8lWrVmnatGlq2rSpunbtKpvNpuXLl2vjxo1KSEjQJ598wl2nDeZ0OvXWW2/J6XRq0KBB8vf315kzZ9SkSRM98MADGjVqlIoWLSpJunLligICAgyuGDCPpk2bqlq1ahoxYoTRpQBZRnZ7LrIbuHVkNzwZ2e25yG7g1pHdOYNGeg5xOp167bXXtH79euXNm1fVqlVT5cqV1bZtWwUGBmrz5s0aNWqUmjdvrk6dOqlAgQKKi4tTUFCQrFYrc7MZ7M8//1Tfvn119OhRffbZZ675pv7++2+1bNlSpUqV0uTJk1W0aFEuoQH+v7TfhdGjR+vkyZOaNm0af+zCo5Ddno3sBrKO7IanI7s9G9kNZB3ZnbOY2iUH3XPPPQoLC9PKlStVsGBBbdu2TU899ZTefPNN3X333erdu7c2btyoWbNm6fz58ypQoICsVqscDgdhnsPsdnu6x6VKldLEiRNVunRp9enTx7W8WLFiWrp0qc6dOyeHwyFJhDnw/6X9LnTq1EkjRowgzOGRyG7PQXYDt4/sRm5AdnsOshu4fWR3zuKM9ByUmJioRo0aaeDAgXr22WfldDr1wAMP6L777tPBgwfVrFkzrV69WvXq1dPMmTMJBoOknYXgcDi0cOFCJSQkyMfHR23atNG5c+c0ZMgQWSwWrV69+rrXAAByF7LbM5DdAIA0ZLdnILsBeCLOSM8hdrtdgYGBeuaZZ/Tnn39Kktq1a6d69epp+fLlWrhwoWw2m5555hm99957rhucIGc5nU5XmLdo0ULbtm3TmTNn9NFHH+mVV15RYmKiJkyYoNTUVD3xxBOu11mt/CoBQG5DdnsGshsAkIbs9gxkNwBPxRnpOey7775TZGSkChcurIoVK2rSpEny8/OTlP5mGampqdwl3ECjRo1SXFycpkyZIunaH2Tt2rWTn5+fFi1apCNHjui1117TxIkTXfO2AQByJ7LbM5DdAIA0ZLdnILsBeBoO5+WwunXrqkGDBipRooQmTJggPz8/1xHwtDB3Op2EucFiY2P10EMPSbp2aaDNZtN7772nPXv2aPv27SpbtqwWLlxImAOAFyC7PQPZDQBIQ3Z7BrIbgKehkW6AihUr6syZM/L19b3h88zRlrNiY2N19uxZxcTESLp2FPzkyZPat2+fJCkwMFDJycnKkyePKlasqJCQEElyndEAAMj9yG5zIbsBADdDdpsL2Q0gN6CRboAXX3xRfn5+io6OlkSAG2nUqFHq37+/mjZtqlatWmnOnDmy2+16+eWX9fXXX2vWrFmSroX3559/rri4OFegAwC8B9ltHmQ3ACAzyG7zILsB5BZcx5TDnE6nHA6H7r33XiUkJBhdjlfr0aOHzp8/r2HDhik2NlZHjhzRO++8o2PHjqlz587q3bu3Jk6cqHXr1qlYsWI6duyY3n33XRUuXNjo0gEAOYjsNg+yGwCQGWS3eZDdAHITbjZqkFOnTqlo0aKy2WxGl+KVevbsqQsXLmjp0qXpln/11Vd6+eWX9fLLL6tnz5767bff9O2336po0aKqVKkSc7MBgBcju41FdgMAsorsNhbZDSC3oZFuMLvdTqjnsD59+ujIkSP67LPPJEnJycny8/OTw+GQ1WrV4sWLNWnSJK1evVr33XefwdUCAMyG7M55ZDcA4HaQ3TmP7AaQGzFHusEI85wVExOjXbt2qXz58jpx4oQcDofrDu5W67Vfh7p16yooKIhLAAEAN0R25yyyGwBwu8junEV2A8itaKTDqxQsWFDz58/XoUOHNGnSJB0+fFjStRvPpKamSpL8/f1VokQJ5c+f38hSAQCAyG4AADwN2Q0gt6KRDq9TtmxZTZkyRUeOHNHMmTN18OBBSZKPz7V7727cuFEWi0UFCxY0skwAAPD/kd0AAHgWshtAbsQc6fBaR44c0auvvqrQ0FB1795d999/vz788ENNmzZNH3zwgcqXL290iQAA4B/IbgAAPAvZDSA3oZEOr5YW6tWrV1e+fPm0cuVKzZ8/XxUrVjS6NAAAcANkNwAAnoXsBpBb0EiH1zty5Ii6dOmiS5cu6cMPP9T9999vdEkAACADZDcAAJ6F7AaQG9BIBySdOHFCknTXXXcZXAkAAMgMshsAAM9CdgPwdDTSAQAAAAAAAADIgNXoAgAAAAAAAAAAMDMa6QAAAAAAAAAAZIBGOgAAAAAAAAAAGaCRDgAAAAAAAABABmikAwAAAAAAAACQARrpAAAAAAAAAABkgEY6AAAAAAAAAAAZoJEOmFBqaqo++OADNWvWTGFhYapZs6Y6d+6s7du3u/V99u/fr2effVaVKlXSq6++qiFDhqh9+/ZufY9b4XQ6tWbNGsXExEiSVq9erXLlyhlcFQAA/43sJrsBAJ6F7Ca7gayyOJ1Op9FFAPg/ycnJ6ty5s06fPq1XXnlFYWFhSkpK0qpVq7R48WKNGzdOTZo0cct7RUZG6vjx43r33XeVL18+5cmTR3a7XQUKFHDL9m/Vjz/+qPbt22vLli268847lZSUpIsXL6pw4cKG1gUAwI2Q3WQ3AMCzkN1kN3ArfIwuAEB6UVFROnjwoD777DMVK1bMtXz48OG6fPmyxo4dq8cff1yBgYG3/V4JCQmqUKGC7rnnntveljv9+/iev7+//P39DaoGAICMkd1kNwDAs5DdZDdwK5jaBTCRlJQUrVixQi1atEgX5mleffVVzZkzR/7+/oqLi9OoUaMUHh6uKlWqqG3bttq1a5dr3XfffVft27fX7NmzVa9ePVWuXFkdOnTQb7/9JkmqX7++fvzxR61du1blypXTjh07rrvE7H//+58iIiL0wAMPqEGDBvrkk09UoUIF7dixQ5LUvn17DRkyJF2N/9zGyZMnVa5cOU2fPl116tRR/fr1lZCQoCNHjqhXr16qWbOmKlWqpMcff1wffPCBJGnHjh3q0KGDJKlBgwZavXr1dZeY3e5nBwDAXchushsA4FnIbrIbuFU00gETOXHihOLi4vTggw/e8PkiRYqoSpUqkqQuXbpo165dmjBhgtasWaPy5curU6dO+uWXX1zr79mzRzt37tSsWbO0YMECnTp1SqNGjZIkrVy5UmFhYXr66af1/fffKywsLN17nTlzRh07dlTJkiW1atUqjRw5UlOmTJHdbs/y5/rkk0/0wQcfaNq0afL19VXnzp2VN29effjhh/rss8/09NNPa+zYsTpw4IDCwsL07rvvSpJWrFihRo0apduW3W6/7c8OAIC7kN1kNwDAs5DdZDdwq2ikAyYSHx8vSbrjjjsyXO/777/X/v37NXnyZNWqVUulS5fWyJEjFRoaqrlz57rWS01N1dtvv63y5curWrVqat++vXbv3i1JCgkJka+vr/z9/VW4cGH5+fmle49ly5YpKChIY8aMUZkyZRQeHq7XXnvtlj5Xu3btVKZMGVWuXFlXrlxRhw4d9MYbb6h06dIqVaqUXn75ZUnSoUOH5Ofn5/r8ISEh111a5o7PDgCAu5DdZDcAwLOQ3WQ3cKuYIx0wkZCQEEnXLqHKyOHDh5U/f36Fhoa6llksFlWvXl3fffeda1mhQoXS3cAkf/78SklJyVQtv/76qypWrChfX1/XsurVq2fqtf9WqlQp1/8PCQlRu3bttH79eh08eFB//vmnDhw4IElyOBw33VZOfHYAADKL7Ca7AQCehewmu4FbxRnpgIncddddKlSokPbs2XPD5//44w916dJFV69elcViue55h8MhH5//Oz7276PdWWGz2TIVsP++QcmNQvOfR7fPnz+v5557TsuWLVOhQoXUpk0brV69OtN1OZ3ObP/sAABkFtl9c2Q3AMBMyO6bI7uBG6ORDpiI1WpVixYttHr1ap05c+a65+fMmaOff/5Z5cuXV0JCgg4fPpzu+d27d6tMmTJuqaV8+fLav39/uoDeu3dvunV8fX118eLFdMuOHz+e4XbXrVunuLg4LV26VL169dLjjz/uurQu7Y+DGwV2mnLlymX7ZwcAILPIbrIbAOBZyG6yG7hVNNIBk+nRo4dKlSqlNm3aaO3atTp+/Lh++eUXDR8+XKtWrdKbb76pRx99VOXKlVP//v21Y8cOHTt2TKNGjdLhw4fVsWNHt9TRrl07Xbx4Ua+99pqOHTum7du3a/To0ZL+L3CrVq2qbdu26csvv9SJEycUFRV1XdD+W7FixXTlyhVt2LBBp06d0vfff69+/fpJkpKTkyVJefPmlSQdPHhQiYmJ6V5fp06dbP/sAABkBdlNdgMAPAvZTXYDt4I50gGTCQgI0OLFizVv3jzNnj1bp06dUp48eVSxYkV98MEHqlGjhiRp/vz5mjBhgl555RUlJyerYsWKWrBgwX/eeTyrChYsqDlz5mjs2LF6/vnnVaxYMbVt21Zvv/22a/62Tp066cSJExo4cKAsFosaNWqkTp066aeffvrP7T711FPav3+/JkyYoEuXLqlkyZJq2bKltmzZon379qlt27YKDQ1VeHi4+vTpo379+qWbc83HxyfbPzsAAFlBdpPdAADPQnaT3cCtsDj/PdESAEg6evSo4uPjVa1aNdeyn376SW3bttXXX3+t4sWLG1gdAAD4N7IbAADPQnYDnoWpXQDc0JkzZ9ShQwetXbtWf/31l/bs2aNx48apRo0ahDkAACZEdgMA4FnIbsCzcEY6gP/04YcfatGiRTp58qTy58+v+vXra8CAAeku+QIAAOZBdgMA4FnIbsBz0EgHAAAAAAAAACADTO0CAAAAAAAAAEAGaKQDAAAAAAAAAJABGukAAAAAAAAAAGSARjoAAAAAAAAAABmgkQ4AAAAAAAAAQAZopAMAAAAAAAAAkAEa6QAAAAAAAAAAZIBGOgAAAAAAAAAAGaCRDgAAAAAAAABABv4f56RCC1HytkUAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 Different models produce different outputs, as expected\n"]}], "source": ["# Visualize model outputs\n", "if model_outputs:\n", "    fig, axes = plt.subplots(2, 3, figsize=(15, 8))\n", "    axes = axes.flatten()\n", "    \n", "    config_labels = ['Alternating', 'Domain Wall', 'All Up', 'All Down']\n", "    \n", "    for i, (name, output) in enumerate(model_outputs.items()):\n", "        if i >= len(axes):\n", "            break\n", "            \n", "        ax = axes[i]\n", "        \n", "        # Handle complex outputs\n", "        if jnp.iscomplexobj(output):\n", "            real_part = output.real\n", "            imag_part = output.imag\n", "            \n", "            x = range(len(real_part))\n", "            ax.bar([xi - 0.2 for xi in x], real_part, width=0.4, label='Real', alpha=0.7)\n", "            ax.bar([xi + 0.2 for xi in x], imag_part, width=0.4, label='Imag', alpha=0.7)\n", "            ax.legend()\n", "        else:\n", "            ax.bar(range(len(output)), output, alpha=0.7)\n", "        \n", "        ax.set_title(f'{name}')\n", "        ax.set_xlabel('Configuration')\n", "        ax.set_ylabel('Output Value')\n", "        ax.set_xticks(range(len(config_labels)))\n", "        ax.set_xticklabels(config_labels, rotation=45)\n", "    \n", "    # Hide unused subplots\n", "    for i in range(len(model_outputs), len(axes)):\n", "        axes[i].set_visible(False)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"📊 Different models produce different outputs, as expected\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Performance Comparison\n", "\n", "Compare computational performance between polynomial and baseline models."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⏱️ Performance Comparison\n", "==================================================\n", "poly_degree2   : 5.38 ms\n", "poly_degree3   : 5.28 ms\n", "pure_poly      : 6.52 ms\n", "poly_output    : 4.22 ms\n", "multi_degree   : 7.77 ms\n", "baseline       : 4.68 ms\n", "\n", "📈 Slowdown factors (vs baseline):\n", "  poly_degree2   : 1.15x\n", "  poly_degree3   : 1.13x\n", "  pure_poly      : 1.39x\n", "  poly_output    : 0.90x\n", "  multi_degree   : 1.66x\n"]}], "source": ["# =============================================================================\n", "# PERFORMANCE COMPARISON\n", "# =============================================================================\n", "\n", "print(\"⏱️ Performance Comparison\")\n", "print(\"=\" * 50)\n", "\n", "# Create larger test batch for timing\n", "batch_size = 1000\n", "large_test_batch = jax.random.choice(\n", "    jax.random.<PERSON><PERSON><PERSON><PERSON>(42),\n", "    jnp.array([-1., 1.]),\n", "    shape=(batch_size, 4)\n", ")\n", "\n", "timing_results = {}\n", "\n", "for name, model in models.items():\n", "    if name in model_params:\n", "        try:\n", "            params = model_params[name]\n", "            \n", "            # Create JIT-compiled function\n", "            jit_fn = jax.jit(model.apply)\n", "            \n", "            # Warm up\n", "            _ = jit_fn(params, large_test_batch[:10])\n", "            \n", "            # Time multiple runs\n", "            start_time = time.time()\n", "            for _ in range(10):\n", "                _ = jit_fn(params, large_test_batch)\n", "            avg_time = (time.time() - start_time) / 10\n", "            \n", "            timing_results[name] = avg_time\n", "            print(f\"{name:15}: {avg_time*1000:.2f} ms\")\n", "            \n", "        except Exception as e:\n", "            print(f\"{name:15}: ❌ <PERSON><PERSON> failed - {e}\")\n", "\n", "# Calculate slowdown factors\n", "if 'baseline' in timing_results:\n", "    baseline_time = timing_results['baseline']\n", "    print(f\"\\n📈 Slowdown factors (vs baseline):\")\n", "    for name, time_val in timing_results.items():\n", "        if name != 'baseline':\n", "            slowdown = time_val / baseline_time\n", "            print(f\"  {name:15}: {slowdown:.2f}x\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["⚡ Polynomial models are slower due to additional computations, but still reasonable\n"]}], "source": ["# Visualize performance comparison\n", "if timing_results:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    # Plot 1: Absolute timing\n", "    names = list(timing_results.keys())\n", "    times = [timing_results[name] * 1000 for name in names]  # Convert to ms\n", "    colors = ['red' if name == 'baseline' else 'blue' for name in names]\n", "    \n", "    bars1 = ax1.bar(names, times, color=colors, alpha=0.7)\n", "    ax1.set_ylabel('Time (ms)')\n", "    ax1.set_title('Absolute Performance Comparison')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    # Add value labels\n", "    for bar, time_val in zip(bars1, times):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{time_val:.1f}', ha='center', va='bottom', fontsize=9)\n", "    \n", "    # Plot 2: Relative performance (if baseline exists)\n", "    if 'baseline' in timing_results:\n", "        baseline_time = timing_results['baseline']\n", "        relative_times = [timing_results[name] / baseline_time for name in names]\n", "        \n", "        bars2 = ax2.bar(names, relative_times, color=colors, alpha=0.7)\n", "        ax2.set_ylabel('Relative Time (vs Baseline)')\n", "        ax2.set_title('Relative Performance Comparison')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "        ax2.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Baseline')\n", "        \n", "        # Add value labels\n", "        for bar, rel_time in zip(bars2, relative_times):\n", "            height = bar.get_height()\n", "            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                    f'{rel_time:.1f}x', ha='center', va='bottom', fontsize=9)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"⚡ Polynomial models are slower due to additional computations, but still reasonable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Summary and Conclusions\n", "\n", "Summarize the results and provide insights about the polynomial GCNN implementation."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Polynomial GCNN Implementation Summary\n", "============================================================\n", "\n", "✅ **Implementation Status:**\n", "   • PolyDenseEquivariantFFT layer: Working\n", "   • PolyGCNN_FFT model: Working\n", "   • Factory functions: Working\n", "   • Different polynomial modes: Working\n", "   • Integration with NetKet: Working\n", "\n", "📊 **Key Findings:**\n", "   • Parameter increase: ~3.0x baseline on average\n", "   • Performance impact: ~1.2x slower on average\n", "   • All models produce finite, reasonable outputs\n", "   • Different polynomial configurations work correctly\n", "   • Symmetry preservation maintained\n", "\n", "🚀 **Polynomial GCNN Features:**\n", "   • True polynomial interactions (no activations within polynomial layers)\n", "   • Configurable polynomial degrees per layer\n", "   • no_activation mode for pure polynomial networks\n", "   • poly_output mode for alternative output processing\n", "   • Full NetKet symmetry framework compatibility\n", "\n", "🎨 **Usage Recommendations:**\n", "   • Start with degree=2 for most applications\n", "   • Use no_activation=True for pure polynomial experiments\n", "   • Consider per-layer degrees for complex systems\n", "   • Monitor parameter count vs performance trade-offs\n", "\n", "🎉 **Ready for Quantum Many-Body Applications!**\n", "   The polynomial GCNN implementation is working correctly and\n", "   can be used for quantum variational Monte Carlo studies.\n"]}], "source": ["# =============================================================================\n", "# SUMMARY AND CONCLUSIONS\n", "# =============================================================================\n", "\n", "print(\"🎯 Polynomial GCNN Implementation Summary\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n✅ **Implementation Status:**\")\n", "print(\"   • PolyDenseEquivariantFFT layer: Working\")\n", "print(\"   • PolyGCNN_FFT model: Working\")\n", "print(\"   • Factory functions: Working\")\n", "print(\"   • Different polynomial modes: Working\")\n", "print(\"   • Integration with NetKet: Working\")\n", "\n", "print(\"\\n📊 **Key Findings:**\")\n", "if param_counts:\n", "    baseline_count = param_counts.get('baseline', 0)\n", "    poly_counts = [count for name, count in param_counts.items() if name != 'baseline']\n", "    if poly_counts:\n", "        avg_poly_ratio = np.mean([count / baseline_count for count in poly_counts])\n", "        print(f\"   • Parameter increase: ~{avg_poly_ratio:.1f}x baseline on average\")\n", "\n", "if timing_results and 'baseline' in timing_results:\n", "    baseline_time = timing_results['baseline']\n", "    poly_times = [time_val for name, time_val in timing_results.items() if name != 'baseline']\n", "    if poly_times:\n", "        avg_slowdown = np.mean([time_val / baseline_time for time_val in poly_times])\n", "        print(f\"   • Performance impact: ~{avg_slowdown:.1f}x slower on average\")\n", "\n", "print(\"   • All models produce finite, reasonable outputs\")\n", "print(\"   • Different polynomial configurations work correctly\")\n", "print(\"   • Symmetry preservation maintained\")\n", "\n", "print(\"\\n🚀 **Polynomial GCNN Features:**\")\n", "print(\"   • True polynomial interactions (no activations within polynomial layers)\")\n", "print(\"   • Configurable polynomial degrees per layer\")\n", "print(\"   • no_activation mode for pure polynomial networks\")\n", "print(\"   • poly_output mode for alternative output processing\")\n", "print(\"   • Full NetKet symmetry framework compatibility\")\n", "\n", "print(\"\\n🎨 **Usage Recommendations:**\")\n", "print(\"   • Start with degree=2 for most applications\")\n", "print(\"   • Use no_activation=True for pure polynomial experiments\")\n", "print(\"   • Consider per-layer degrees for complex systems\")\n", "print(\"   • Monitor parameter count vs performance trade-offs\")\n", "\n", "print(\"\\n🎉 **Ready for Quantum Many-Body Applications!**\")\n", "print(\"   The polynomial GCNN implementation is working correctly and\")\n", "print(\"   can be used for quantum variational Monte Carlo studies.\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}