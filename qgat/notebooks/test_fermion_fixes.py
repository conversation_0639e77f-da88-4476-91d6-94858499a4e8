#!/usr/bin/env python3
"""
Test script to verify that the fermion notebook fixes work correctly.

This script tests the key components that were fixed:
1. FermionSystemFactory import and creation
2. ModelFactory with lattice_fermion physics type
3. Basic functionality of the polynomial models
"""

import sys
import os
sys.path.append('..')

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

def test_fermion_system_factory():
    """Test the FermionSystemFactory."""
    print("🧪 Testing FermionSystemFactory...")
    
    try:
        from physics.lattice_fermions import FermionSystemFactory
        
        # Test system creation
        fermion_system = FermionSystemFactory.create(
            model='hubbard',
            lattice='1d_chain',
            L=4,
            t=1.0,
            U=2.0,
            n_particles=2,
            pbc=True
        )
        
        print(f"✅ FermionSystemFactory works!")
        print(f"   Hilbert space: {fermion_system['hilbert']}")
        print(f"   Problem name: {fermion_system['problem_name']}")
        print(f"   Sites: {fermion_system['n_sites']}")
        print(f"   Particles: {fermion_system['n_particles']}")
        
        return fermion_system
        
    except Exception as e:
        print(f"❌ FermionSystemFactory failed: {e}")
        return None

def test_model_factory():
    """Test ModelFactory with lattice_fermion physics type."""
    print("\n🧪 Testing ModelFactory with lattice_fermion...")
    
    try:
        from models import ModelFactory
        
        # Create a simple fermion system for testing
        fermion_system = test_fermion_system_factory()
        if fermion_system is None:
            print("❌ Cannot test ModelFactory without working FermionSystemFactory")
            return
        
        # Test RBM model
        print("\n   Testing RBM model...")
        try:
            model, param_count = ModelFactory.create(
                type_name='rbm',
                config={'n_hidden': 8, 'param_dtype': 'float64'},
                physics_type='lattice_fermion',
                system_size=4,
                problem_name=fermion_system['problem_name'],
                hilbert=fermion_system['hilbert'],
                random_seed=42
            )
            print(f"   ✅ RBM model created: {param_count:,} parameters")
        except Exception as e:
            print(f"   ❌ RBM model failed: {e}")
        
        # Test GCNN model
        print("\n   Testing GCNN model...")
        try:
            model, param_count = ModelFactory.create(
                type_name='gcnn',
                config={'layers': 2, 'features': 8, 'param_dtype': 'float64'},
                physics_type='lattice_fermion',
                system_size=4,
                problem_name=fermion_system['problem_name'],
                hilbert=fermion_system['hilbert'],
                random_seed=42
            )
            print(f"   ✅ GCNN model created: {param_count:,} parameters")
        except Exception as e:
            print(f"   ❌ GCNN model failed: {e}")
        
        # Test Polynomial RBM model
        print("\n   Testing Polynomial RBM model...")
        try:
            model, param_count = ModelFactory.create(
                type_name='poly_rbm',
                config={
                    'n_hidden': 8,
                    'degree': 3,
                    'poly_class': 'CP',
                    'param_dtype': 'float64'
                },
                physics_type='lattice_fermion',
                system_size=4,
                problem_name=fermion_system['problem_name'],
                hilbert=fermion_system['hilbert'],
                random_seed=42
            )
            print(f"   ✅ Polynomial RBM model created: {param_count:,} parameters")
        except Exception as e:
            print(f"   ❌ Polynomial RBM model failed: {e}")
        
        # Test Polynomial GCNN model
        print("\n   Testing Polynomial GCNN model...")
        try:
            model, param_count = ModelFactory.create(
                type_name='poly_gcnn',
                config={
                    'layers': 2,
                    'features': 8,
                    'degree': 3,
                    'poly_class': 'CP',
                    'param_dtype': 'float64'
                },
                physics_type='lattice_fermion',
                system_size=4,
                problem_name=fermion_system['problem_name'],
                hilbert=fermion_system['hilbert'],
                random_seed=42
            )
            print(f"   ✅ Polynomial GCNN model created: {param_count:,} parameters")
        except Exception as e:
            print(f"   ❌ Polynomial GCNN model failed: {e}")
            
    except Exception as e:
        print(f"❌ ModelFactory test failed: {e}")

def test_exact_solver():
    """Test the ExactGroundStateSolver."""
    print("\n🧪 Testing ExactGroundStateSolver...")
    
    try:
        from ground_state.exact_solver import ExactGroundStateSolver
        
        # Create a simple system
        fermion_system = test_fermion_system_factory()
        if fermion_system is None:
            print("❌ Cannot test ExactGroundStateSolver without working FermionSystemFactory")
            return
        
        # Test exact solver
        exact_solver = ExactGroundStateSolver(
            hamiltonian=fermion_system['hamiltonian'],
            hilbert=fermion_system['hilbert']
        )
        ground_energy, ground_state = exact_solver.solve()
        
        print(f"✅ ExactGroundStateSolver works!")
        print(f"   Ground state energy: {ground_energy:.8f}")
        print(f"   Hilbert space size: {fermion_system['hilbert'].n_states}")
        
    except Exception as e:
        print(f"❌ ExactGroundStateSolver failed: {e}")

def main():
    """Run all tests."""
    print("🔬 Testing Fermion Notebook Fixes")
    print("=" * 50)
    
    print(f"JAX devices: {jax.devices()}")
    print(f"NetKet version: {nk.__version__}")
    print()
    
    # Run tests
    test_fermion_system_factory()
    test_model_factory()
    test_exact_solver()
    
    print("\n" + "=" * 50)
    print("🎉 Test complete! Check results above.")

if __name__ == "__main__":
    main()
