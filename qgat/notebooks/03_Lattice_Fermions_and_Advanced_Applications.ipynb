{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lattice Fermions and Advanced Applications\n", "\n", "**A comprehensive tutorial for condensed matter theorists working with fermionic systems**\n", "\n", "This notebook demonstrates how to use Quantum Graph Attention Networks (QGAT) for lattice fermion systems such as the Hubbard model, t-J model, and other strongly correlated electron systems. We'll integrate with the lattice-fermions.ipynb methodology and provide advanced benchmarking capabilities.\n", "\n", "## Learning Objectives\n", "\n", "By the end of this tutorial, you will:\n", "- Understand the mathematical treatment of fermionic lattice models\n", "- Implement GAT architectures for strongly correlated electron systems\n", "- Apply QGAT to Hubbard models, t-J models, and spinless fermion systems\n", "- Compare with exact diagonalization results for small systems\n", "- Perform advanced benchmarking and scaling analysis\n", "- Visualize fermion configurations and correlation functions\n", "\n", "## Table of Contents\n", "\n", "1. [Mathematical Background](#1-mathematical-background)\n", "2. [Environment Setup](#2-environment-setup)\n", "3. [Hubbard Model with QGAT](#3-hubbard-model-with-qgat)\n", "4. [t-J Model Implementation](#4-tj-model-implementation)\n", "5. [Spinless Fermion Systems](#5-spinless-fermion-systems)\n", "6. [Exact Diagonalization Comparison](#6-exact-diagonalization-comparison)\n", "7. [Advanced Benchmarking](#7-advanced-benchmarking)\n", "8. [Scaling Analysis](#8-scaling-analysis)\n", "9. [Exercises and Extensions](#9-exercises-and-extensions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Mathematical Background\n", "\n", "### 1.1 Hubbard <PERSON>\n", "\n", "The Hubbard model describes electrons on a lattice with on-site interactions:\n", "\n", "$$H = -t \\sum_{\\langle i,j \\rangle, \\sigma} \\left( c_{i,\\sigma}^\\dagger c_{j,\\sigma} + \\text{h.c.} \\right) + U \\sum_i n_{i,\\uparrow} n_{i,\\downarrow}$$\n", "\n", "Where:\n", "- $t$: hopping parameter\n", "- $U$: on-site Coulomb repulsion\n", "- $c_{i,\\sigma}^\\dagger$: creation operator for electron with spin $\\sigma$ at site $i$\n", "- $n_{i,\\sigma} = c_{i,\\sigma}^\\dagger c_{i,\\sigma}$: number operator\n", "\n", "### 1.2 t-J Model\n", "\n", "The t-J model is derived from the Hubbard model in the strong coupling limit ($U \\gg t$):\n", "\n", "$$H = -t \\sum_{\\langle i,j \\rangle, \\sigma} P \\left( c_{i,\\sigma}^\\dagger c_{j,\\sigma} + \\text{h.c.} \\right) P + J \\sum_{\\langle i,j \\rangle} \\left( \\vec{S}_i \\cdot \\vec{S}_j - \\frac{n_i n_j}{4} \\right)$$\n", "\n", "Where $P$ projects out doubly occupied sites and $J = 4t^2/U$.\n", "\n", "### 1.3 Spinless Fermions\n", "\n", "For spinless fermions with nearest-neighbor interactions:\n", "\n", "$$H = -t \\sum_{\\langle i,j \\rangle} \\left( c_i^\\dagger c_j + \\text{h.c.} \\right) + V \\sum_{\\langle i,j \\rangle} n_i n_j$$\n", "\n", "### 1.4 Graph Representation for <PERSON><PERSON><PERSON> Fermions\n", "\n", "For lattice fermion systems:\n", "- **Nodes**: Lattice sites\n", "- **Edges**: Hopping terms and interactions\n", "- **Node Features**: Fermion occupations (spin-up, spin-down)\n", "- **Attention**: Learns correlation patterns and many-body effects"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Environment Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Lattice Fermion QGAT environment setup complete!\n", "JAX version: 0.5.3\n", "NetKet version: 3.19.0\n", "🔬 Ready for strongly correlated electron calculations!\n"]}], "source": ["# Import necessary libraries\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from flax import nnx\n", "import seaborn as sns\n", "\n", "# Enable 64-bit precision for numerical accuracy\n", "jax.config.update(\"jax_enable_x64\", True)\n", "\n", "# Import QGAT components\n", "import sys\n", "import os\n", "sys.path.append(os.path.join(os.getcwd(), '..'))  # Add parent directory to path\n", "\n", "try:\n", "    from physics.lattice_fermions.lattice_fermion_gat import (\n", "        NetKetLatticeFermionGAT, HubbardGAT, TJModelGAT\n", "    )\n", "    from physics.lattice_fermions.hamiltonians import (\n", "        create_hubbard_hamiltonian, create_tj_hamiltonian, \n", "        create_spinless_fermion_hamiltonian, get_exact_lattice_fermion_energies\n", "    )\n", "    from physics.lattice_fermions.utils import (\n", "        create_square_lattice_fermion_adjacency, fermion_occupation_to_features,\n", "        visualize_fermion_configuration, compute_fermion_correlations\n", "    )\n", "    from visualization.plotting import (\n", "        plot_energy_convergence, plot_model_comparison, plot_scaling_analysis\n", "    )\n", "    from visualization.analysis import BenchmarkAnalyzer, ConvergenceAnalyzer\n", "    from visualization.utils import setup_publication_style\n", "    \n", "    # Set up plotting style\n", "    setup_publication_style()\n", "    \n", "    print(\"✅ Lattice Fermion QGAT environment setup complete!\")\n", "    print(f\"JAX version: {jax.__version__}\")\n", "    print(f\"NetKet version: {nk.__version__}\")\n", "    print(\"🔬 Ready for strongly correlated electron calculations!\")\n", "    \n", "except ImportError as e:\n", "    print(f\"⚠️ Import error: {e}\")\n", "    print(\"Please ensure you're running this notebook from the qgat/notebooks/ directory\")\n", "    print(\"and that the QGAT package is properly installed.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON> with QGAT\n", "\n", "Let's start with the paradigmatic Hubbard model on a small square lattice."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Problem Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔲 Setting up Hubbard model\n", "Lattice: 2×2 = 4 sites\n", "Interaction strength: U/t = 4.0\n", "Number of fermions: 4 (half-filling)\n", "Number of orbitals: 8 (spin up + spin down)\n", "\n", "<PERSON>:\n", "  Hilbert space size: 8\n", "  Fermion constraint: 4 fermions\n", "  Approximate ground state energy: -2.500000\n", "\n", "🔬 Energy scales:\n", "  Kinetic energy scale: -8.000\n", "  Interaction energy scale: 4.000\n", "  Regime: Intermediate coupling\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/netket/graph/common_lattices.py:126: InitializePeriodicLatticeOnSmallLatticeWarning: \n", "You are attempting to define a lattice with length 2 in dimension 0 using periodic boundary condition.\n", "\n", "Lattice with less than two sites in one direction does not support periodic boundary condition.\n", "The behavior of the lattice is equivalent to an open boundary condition in this direction.\n", "\n", "To avoid this warning, consider either using a lattice with more than two sites in the direction you want to be periodic,\n", "or define the graph using :class:`~netket.graph.Graph` by adding the edges manually.\n", "\n", "\n", "-------------------------------------------------------\n", "For more detailed informations, visit the following link:\n", "\t https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.InitializePeriodicLatticeOnSmallLatticeWarning.html\n", "or the list of all common errors and warnings at\n", "\t https://netket.readthedocs.io/en/latest/api/errors.html\n", "-------------------------------------------------------\n", "\n", "  warnings.warn(\n"]}], "source": ["# Hubbard model parameters\n", "L = 2  # 2x2 lattice\n", "n_sites = L * L\n", "U = 4.0  # On-site Coulomb repulsion\n", "t = 1.0  # Hopping parameter\n", "n_fermions = n_sites  # Half-filling (one electron per site on average)\n", "\n", "print(\"🔲 Setting up Hubbard model\")\n", "print(f\"Lattice: {L}×{L} = {n_sites} sites\")\n", "print(f\"Interaction strength: U/t = {U/t}\")\n", "print(f\"Number of fermions: {n_fermions} (half-filling)\")\n", "print(f\"Number of orbitals: {n_sites * 2} (spin up + spin down)\")\n", "\n", "# C<PERSON> <PERSON>\n", "hamiltonian, exact_energy = create_hubbard_hamiltonian(\n", "    L=L, U=U, t=t, n_fermions=n_fermions, pbc=True\n", ")\n", "hilbert = hamiltonian.hilbert\n", "\n", "print(f\"\\nHubbard Hamiltonian:\")\n", "print(f\"  Hilbert space size: {hilbert.size}\")\n", "print(f\"  Fermion constraint: {n_fermions} fermions\")\n", "print(f\"  Approximate ground state energy: {exact_energy:.6f}\")\n", "\n", "# Physical interpretation\n", "kinetic_scale = -2 * t * n_fermions\n", "interaction_scale = U * n_fermions / 4\n", "print(f\"\\n🔬 Energy scales:\")\n", "print(f\"  Kinetic energy scale: {kinetic_scale:.3f}\")\n", "print(f\"  Interaction energy scale: {interaction_scale:.3f}\")\n", "print(f\"  Regime: {'Strong coupling' if U > 4*t else 'Weak coupling' if U < t else 'Intermediate coupling'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Lattice Structure and Adjacency"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔗 Lattice structure:\n", "Adjacency matrix shape: (4, 4)\n", "Number of nearest-neighbor pairs: 4.0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Lattice properties:\n", "  Coordination number: 2.0\n", "  Lattice dimension: 2D\n", "  Boundary conditions: Periodic\n"]}], "source": ["# Create lattice adjacency matrix\n", "lattice_adjacency = create_square_lattice_fermion_adjacency(\n", "    n_sites=n_sites, lattice_type=\"square\", L=L, pbc=True\n", ")\n", "\n", "print(\"🔗 Lattice structure:\")\n", "print(f\"Adjacency matrix shape: {lattice_adjacency.shape}\")\n", "print(f\"Number of nearest-neighbor pairs: {jnp.sum(lattice_adjacency) // 2}\")\n", "\n", "# Visualize lattice structure\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Plot adjacency matrix\n", "plt.subplot(1, 2, 1)\n", "plt.imshow(lattice_adjacency, cmap='Blues', aspect='auto')\n", "plt.colorbar(label='Connection')\n", "plt.title(f'{L}×{L} Square Lattice Adjacency')\n", "plt.xlabel('Site Index')\n", "plt.ylabel('Site Index')\n", "\n", "# Add site labels\n", "for i in range(n_sites):\n", "    for j in range(n_sites):\n", "        if lattice_adjacency[i, j] > 0:\n", "            plt.text(j, i, '1', ha=\"center\", va=\"center\", color=\"white\", fontweight='bold')\n", "\n", "# Plot lattice geometry\n", "plt.subplot(1, 2, 2)\n", "# Site positions for 2x2 lattice\n", "positions = jnp.array([[0, 0], [1, 0], [0, 1], [1, 1]])\n", "plt.scatter(positions[:, 0], positions[:, 1], s=300, c='lightblue', \n", "           edgecolors='black', linewidth=2, zorder=3)\n", "\n", "# Draw bonds\n", "for i in range(n_sites):\n", "    for j in range(i+1, n_sites):\n", "        if lattice_adjacency[i, j] > 0:\n", "            plt.plot([positions[i, 0], positions[j, 0]], \n", "                    [positions[i, 1], positions[j, 1]], \n", "                    'k-', linewidth=2, alpha=0.7, zorder=1)\n", "\n", "# Add site labels\n", "for i, pos in enumerate(positions):\n", "    plt.text(pos[0], pos[1], str(i), ha='center', va='center', \n", "            fontsize=12, fontweight='bold', color='darkblue')\n", "\n", "plt.xlim(-0.3, 1.3)\n", "plt.ylim(-0.3, 1.3)\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.title(f'{L}×{L} Square Lattice Geometry')\n", "plt.grid(True, alpha=0.3)\n", "plt.axis('equal')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n📊 Lattice properties:\")\n", "print(f\"  Coordination number: {jnp.sum(lattice_adjacency[0])}\")\n", "print(f\"  Lattice dimension: 2D\")\n", "print(f\"  Boundary conditions: Periodic\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Creating the Hubbard GAT Model"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'get'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Create Hubbard GAT model\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m model = \u001b[43mNetKetLatticeFermionGAT\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m      3\u001b[39m \u001b[43m    \u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[43m    \u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlattice_type\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43msquare\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m      6\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhas_spin\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m      7\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel_type\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mhubbard\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Use Hubbard-specific features\u001b[39;49;00m\n\u001b[32m      8\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhidden_features\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m24\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m12\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      9\u001b[39m \u001b[43m    \u001b[49m\u001b[43mn_heads\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m6\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[43m    \u001b[49m\u001b[43mU\u001b[49m\u001b[43m=\u001b[49m\u001b[43mU\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Pass Hubbard parameters\u001b[39;49;00m\n\u001b[32m     11\u001b[39m \u001b[43m    \u001b[49m\u001b[43mt\u001b[49m\u001b[43m=\u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrngs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnnx\u001b[49m\u001b[43m.\u001b[49m\u001b[43mRngs\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m42\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m🧠 Hubbard GAT model created!\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     16\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mModel type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(model.gat_model).\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:141\u001b[39m, in \u001b[36mObjectMeta.__call__\u001b[39m\u001b[34m(cls, *args, **kwargs)\u001b[39m\n\u001b[32m    140\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, *args: Any, **kwargs: Any) -> Any:\n\u001b[32m--> \u001b[39m\u001b[32m141\u001b[39m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_graph_node_meta_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:150\u001b[39m, in \u001b[36m_graph_node_meta_call\u001b[39m\u001b[34m(cls, *args, **kwargs)\u001b[39m\n\u001b[32m    148\u001b[39m node = \u001b[38;5;28mcls\u001b[39m.\u001b[34m__new__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, *args, **kwargs)\n\u001b[32m    149\u001b[39m \u001b[38;5;28mvars\u001b[39m(node)[\u001b[33m'\u001b[39m\u001b[33m_object__state\u001b[39m\u001b[33m'\u001b[39m] = ObjectState()\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_object_meta_construct\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m node\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:144\u001b[39m, in \u001b[36mObjectMeta._object_meta_construct\u001b[39m\u001b[34m(cls, self, *args, **kwargs)\u001b[39m\n\u001b[32m    143\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_object_meta_construct\u001b[39m(\u001b[38;5;28mcls\u001b[39m, \u001b[38;5;28mself\u001b[39m, *args, **kwargs):\n\u001b[32m--> \u001b[39m\u001b[32m144\u001b[39m   \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/Quantum/qgat/notebooks/../physics/lattice_fermions/lattice_fermion_gat.py:330\u001b[39m, in \u001b[36mNetKetLatticeFermionGAT.__init__\u001b[39m\u001b[34m(self, n_sites, n_fermions, lattice_type, has_spin, model_type, hidden_features, n_heads, **kwargs)\u001b[39m\n\u001b[32m    316\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    317\u001b[39m \u001b[33;03mInitialize NetKet-compatible lattice fermion GAT.\u001b[39;00m\n\u001b[32m    318\u001b[39m \u001b[33;03m\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    327\u001b[39m \u001b[33;03m    **kwargs: Additional arguments\u001b[39;00m\n\u001b[32m    328\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    329\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m model_type == \u001b[33m\"\u001b[39m\u001b[33mhubbard\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m330\u001b[39m     \u001b[38;5;28mself\u001b[39m.gat_model = \u001b[43mHubbardGAT\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    331\u001b[39m \u001b[43m        \u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    332\u001b[39m \u001b[43m        \u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    333\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlattice_type\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlattice_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    334\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhidden_features\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhidden_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    335\u001b[39m \u001b[43m        \u001b[49m\u001b[43mn_heads\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_heads\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    336\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m    337\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    338\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m model_type == \u001b[33m\"\u001b[39m\u001b[33mtj\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    339\u001b[39m     \u001b[38;5;28mself\u001b[39m.gat_model = TJModelGAT(\n\u001b[32m    340\u001b[39m         n_sites=n_sites,\n\u001b[32m    341\u001b[39m         n_fermions=n_fermions,\n\u001b[32m   (...)\u001b[39m\u001b[32m    345\u001b[39m         **kwargs\n\u001b[32m    346\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:141\u001b[39m, in \u001b[36mObjectMeta.__call__\u001b[39m\u001b[34m(cls, *args, **kwargs)\u001b[39m\n\u001b[32m    140\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, *args: Any, **kwargs: Any) -> Any:\n\u001b[32m--> \u001b[39m\u001b[32m141\u001b[39m   \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_graph_node_meta_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:150\u001b[39m, in \u001b[36m_graph_node_meta_call\u001b[39m\u001b[34m(cls, *args, **kwargs)\u001b[39m\n\u001b[32m    148\u001b[39m node = \u001b[38;5;28mcls\u001b[39m.\u001b[34m__new__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, *args, **kwargs)\n\u001b[32m    149\u001b[39m \u001b[38;5;28mvars\u001b[39m(node)[\u001b[33m'\u001b[39m\u001b[33m_object__state\u001b[39m\u001b[33m'\u001b[39m] = ObjectState()\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_object_meta_construct\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m node\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/quantum/lib/python3.13/site-packages/flax/nnx/object.py:144\u001b[39m, in \u001b[36mObjectMeta._object_meta_construct\u001b[39m\u001b[34m(cls, self, *args, **kwargs)\u001b[39m\n\u001b[32m    143\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_object_meta_construct\u001b[39m(\u001b[38;5;28mcls\u001b[39m, \u001b[38;5;28mself\u001b[39m, *args, **kwargs):\n\u001b[32m--> \u001b[39m\u001b[32m144\u001b[39m   \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/Quantum/qgat/notebooks/../physics/lattice_fermions/lattice_fermion_gat.py:151\u001b[39m, in \u001b[36mHubbardGAT.__init__\u001b[39m\u001b[34m(self, n_sites, n_fermions, U, t, **kwargs)\u001b[39m\n\u001b[32m    148\u001b[39m \u001b[38;5;28mself\u001b[39m.U = U\n\u001b[32m    149\u001b[39m \u001b[38;5;28mself\u001b[39m.t = t\n\u001b[32m--> \u001b[39m\u001b[32m151\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    152\u001b[39m \u001b[43m    \u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_sites\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    153\u001b[39m \u001b[43m    \u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mn_fermions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhas_spin\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Hubbard model always has spin\u001b[39;49;00m\n\u001b[32m    155\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    158\u001b[39m \u001b[38;5;66;03m# Add Hubbard-specific layers\u001b[39;00m\n\u001b[32m    159\u001b[39m rngs = kwargs.get(\u001b[33m'\u001b[39m\u001b[33mrngs\u001b[39m\u001b[33m'\u001b[39m, nnx.Rngs(\u001b[32m42\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/Quantum/qgat/notebooks/../physics/lattice_fermions/lattice_fermion_gat.py:77\u001b[39m, in \u001b[36mLatticeFermionGAT.__init__\u001b[39m\u001b[34m(self, n_sites, n_fermions, lattice_type, has_spin, lattice_adjacency, lattice_params, hidden_features, n_heads, **kwargs)\u001b[39m\n\u001b[32m     73\u001b[39m L = \u001b[38;5;28mint\u001b[39m(np.sqrt(n_sites))\n\u001b[32m     74\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m lattice_type == \u001b[33m\"\u001b[39m\u001b[33msquare\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m     75\u001b[39m     \u001b[38;5;28mself\u001b[39m.lattice_adjacency = create_square_lattice_fermion_adjacency(\n\u001b[32m     76\u001b[39m         n_sites=n_sites, lattice_type=lattice_type, L=L,\n\u001b[32m---> \u001b[39m\u001b[32m77\u001b[39m         pbc=\u001b[43mlattice_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m(\u001b[33m'\u001b[39m\u001b[33mpbc\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m     78\u001b[39m     )\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     80\u001b[39m     \u001b[38;5;28mself\u001b[39m.lattice_adjacency = create_square_lattice_fermion_adjacency(\n\u001b[32m     81\u001b[39m         n_sites=n_sites, lattice_type=lattice_type, L=L\n\u001b[32m     82\u001b[39m     )\n", "\u001b[31mAttributeError\u001b[39m: 'NoneType' object has no attribute 'get'"]}], "source": ["# Create Hubbard GAT model\n", "model = NetKetLatticeFermionGAT(\n", "    n_sites=n_sites,\n", "    n_fermions=n_fermions,\n", "    lattice_type=\"square\",\n", "    has_spin=True,\n", "    model_type=\"hubbard\",  # Use Hubbard-specific features\n", "    hidden_features=[24, 12],\n", "    n_heads=[6, 2],\n", "    U=U,  # Pass Hubbard parameters\n", "    t=t,\n", "    rngs=nnx.Rngs(42)\n", ")\n", "\n", "print(\"🧠 Hubbard GAT model created!\")\n", "print(f\"Model type: {type(model.gat_model).__name__}\")\n", "print(f\"Architecture:\")\n", "print(f\"  Input: {n_sites * 2} orbitals (spin up + spin down)\")\n", "print(f\"  Hidden layers: {model.gat_model.hidden_features}\")\n", "print(f\"  Attention heads: {model.gat_model.n_heads}\")\n", "print(f\"  Hubbard parameters: U={model.gat_model.U}, t={model.gat_model.t}\")\n", "\n", "# Test with sample Hubbard configurations\n", "test_configs = [\n", "    jnp.array([1, 0, 0, 1, 1, 0, 0, 1]),  # Antiferromagnetic order\n", "    jnp.array([1, 1, 0, 0, 0, 0, 1, 1]),  # Charge density wave\n", "    jnp.array([1, 0, 1, 0, 0, 1, 0, 1]),  # Checkerboard pattern\n", "]\n", "\n", "config_names = [\"Antiferromagnetic\", \"Charge density wave\", \"Checkerboard\"]\n", "\n", "print(f\"\\n🧪 Testing Hubbard GAT with characteristic configurations:\")\n", "for name, config in zip(config_names, test_configs):\n", "    output = model(config)\n", "    # Analyze configuration\n", "    features = fermion_occupation_to_features(config, has_spin=True)\n", "    double_occ = jnp.sum(features['double_occupancy'])\n", "    total_spin = features['total_spin']\n", "    \n", "    print(f\"  {name:18s}: output={output:8.4f}, double_occ={double_occ:.0f}, total_spin={total_spin:+.0f}\")\n", "\n", "print(f\"\\n✅ Hubbard GAT successfully processes fermionic configurations!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 VMC Optimization for Hubbard Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up VMC for Hubbard model\n", "sampler = nk.sampler.MetropolisLocal(hilbert, n_chains=8)\n", "vs = nk.vqs.MCState(sampler, model, n_samples=800)\n", "\n", "# Use smaller learning rate for strongly correlated systems\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.003)\n", "vmc = nk.VMC(hamiltonian, optimizer, variational_state=vs)\n", "\n", "print(\"⚙️ VMC setup for Hubbard model:\")\n", "print(f\"Sampler: {type(sampler).__name__}\")\n", "print(f\"Chains: {sampler.n_chains}, Samples: {vs.n_samples}\")\n", "print(f\"Optimizer: {type(optimizer).__name__} (lr={optimizer.lr})\")\n", "print(f\"Strong correlation regime: U/t = {U/t}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run VMC optimization\n", "n_steps = 50\n", "energies = []\n", "variances = []\n", "double_occupancies = []\n", "\n", "print(f\"🚀 Optimizing Hubbard model ({n_steps} steps)\")\n", "print(\"Step | Energy      | Variance    | Error       | Double Occ  | Progress\")\n", "print(\"-\" * 70)\n", "\n", "for step in range(n_steps):\n", "    vmc.advance()\n", "    energy = vs.expect(hamiltonian)\n", "    \n", "    energies.append(energy.mean)\n", "    variances.append(energy.variance)\n", "    \n", "    # Estimate double occupancy from current samples\n", "    samples = vs.samples\n", "    if len(samples) > 0:\n", "        sample_features = fermion_occupation_to_features(samples[0], has_spin=True)\n", "        double_occ = jnp.sum(sample_features['double_occupancy'])\n", "        double_occupancies.append(float(double_occ))\n", "    else:\n", "        double_occupancies.append(0.0)\n", "    \n", "    if step % 10 == 0 or step == n_steps - 1:\n", "        error = abs(energy.mean - exact_energy)\n", "        progress = \"█\" * (step * 20 // n_steps) + \"░\" * (20 - step * 20 // n_steps)\n", "        print(f\"{step:4d} | {energy.mean:10.5f} | {energy.variance:10.6f} | {error:10.6f} | {double_occupancies[-1]:10.2f} | {progress}\")\n", "\n", "final_energy = energies[-1]\n", "final_error = abs(final_energy - exact_energy)\n", "final_double_occ = double_occupancies[-1]\n", "\n", "print(f\"\\n✅ Hubbard optimization complete!\")\n", "print(f\"Final energy: {final_energy:.6f}\")\n", "print(f\"Approximate exact: {exact_energy:.6f}\")\n", "print(f\"Energy error: {final_error:.6f}\")\n", "print(f\"Final double occupancy: {final_double_occ:.2f}\")\n", "print(f\"Expected double occ (U→∞): 0.0, (U=0): {n_sites/2:.1f}\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}