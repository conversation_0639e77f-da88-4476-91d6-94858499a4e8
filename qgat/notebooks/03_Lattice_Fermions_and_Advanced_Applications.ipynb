# Import necessary libraries
import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
from flax import nnx
import seaborn as sns

# Enable 64-bit precision for numerical accuracy
jax.config.update("jax_enable_x64", True)

# Import QGAT components
import sys
import os
sys.path.append(os.path.join(os.getcwd(), '..'))  # Add parent directory to path

try:
    from physics.lattice_fermions.lattice_fermion_gat import (
        NetKetLatticeFermionGAT, HubbardGAT, TJModelGAT
    )
    from physics.lattice_fermions.hamiltonians import (
        create_hubbard_hamiltonian, create_tj_hamiltonian, 
        create_spinless_fermion_hamiltonian, get_exact_lattice_fermion_energies
    )
    from physics.lattice_fermions.utils import (
        create_square_lattice_fermion_adjacency, fermion_occupation_to_features,
        visualize_fermion_configuration, compute_fermion_correlations
    )
    from visualization.plotting import (
        plot_energy_convergence, plot_model_comparison, plot_scaling_analysis
    )
    from visualization.analysis import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ConvergenceAnalyzer
    from visualization.utils import setup_publication_style
    
    # Set up plotting style
    setup_publication_style()
    
    print("✅ Lattice Fermion QGAT environment setup complete!")
    print(f"JAX version: {jax.__version__}")
    print(f"NetKet version: {nk.__version__}")
    print("🔬 Ready for strongly correlated electron calculations!")
    
except ImportError as e:
    print(f"⚠️ Import error: {e}")
    print("Please ensure you're running this notebook from the qgat/notebooks/ directory")
    print("and that the QGAT package is properly installed.")

# Hubbard model parameters
L = 2  # 2x2 lattice
n_sites = L * L
U = 4.0  # On-site Coulomb repulsion
t = 1.0  # Hopping parameter
n_fermions = n_sites  # Half-filling (one electron per site on average)

print("🔲 Setting up Hubbard model")
print(f"Lattice: {L}×{L} = {n_sites} sites")
print(f"Interaction strength: U/t = {U/t}")
print(f"Number of fermions: {n_fermions} (half-filling)")
print(f"Number of orbitals: {n_sites * 2} (spin up + spin down)")

# Create Hubbard Hamiltonian
hamiltonian, exact_energy = create_hubbard_hamiltonian(
    L=L, U=U, t=t, n_fermions=n_fermions, pbc=True
)
hilbert = hamiltonian.hilbert

print(f"\nHubbard Hamiltonian:")
print(f"  Hilbert space size: {hilbert.size}")
print(f"  Fermion constraint: {n_fermions} fermions")
print(f"  Approximate ground state energy: {exact_energy:.6f}")

# Physical interpretation
kinetic_scale = -2 * t * n_fermions
interaction_scale = U * n_fermions / 4
print(f"\n🔬 Energy scales:")
print(f"  Kinetic energy scale: {kinetic_scale:.3f}")
print(f"  Interaction energy scale: {interaction_scale:.3f}")
print(f"  Regime: {'Strong coupling' if U > 4*t else 'Weak coupling' if U < t else 'Intermediate coupling'}")

# Create lattice adjacency matrix
lattice_adjacency = create_square_lattice_fermion_adjacency(
    n_sites=n_sites, lattice_type="square", L=L, pbc=True
)

print("🔗 Lattice structure:")
print(f"Adjacency matrix shape: {lattice_adjacency.shape}")
print(f"Number of nearest-neighbor pairs: {jnp.sum(lattice_adjacency) // 2}")

# Visualize lattice structure
plt.figure(figsize=(12, 5))

# Plot adjacency matrix
plt.subplot(1, 2, 1)
plt.imshow(lattice_adjacency, cmap='Blues', aspect='auto')
plt.colorbar(label='Connection')
plt.title(f'{L}×{L} Square Lattice Adjacency')
plt.xlabel('Site Index')
plt.ylabel('Site Index')

# Add site labels
for i in range(n_sites):
    for j in range(n_sites):
        if lattice_adjacency[i, j] > 0:
            plt.text(j, i, '1', ha="center", va="center", color="white", fontweight='bold')

# Plot lattice geometry
plt.subplot(1, 2, 2)
# Site positions for 2x2 lattice
positions = jnp.array([[0, 0], [1, 0], [0, 1], [1, 1]])
plt.scatter(positions[:, 0], positions[:, 1], s=300, c='lightblue', 
           edgecolors='black', linewidth=2, zorder=3)

# Draw bonds
for i in range(n_sites):
    for j in range(i+1, n_sites):
        if lattice_adjacency[i, j] > 0:
            plt.plot([positions[i, 0], positions[j, 0]], 
                    [positions[i, 1], positions[j, 1]], 
                    'k-', linewidth=2, alpha=0.7, zorder=1)

# Add site labels
for i, pos in enumerate(positions):
    plt.text(pos[0], pos[1], str(i), ha='center', va='center', 
            fontsize=12, fontweight='bold', color='darkblue')

plt.xlim(-0.3, 1.3)
plt.ylim(-0.3, 1.3)
plt.xlabel('x')
plt.ylabel('y')
plt.title(f'{L}×{L} Square Lattice Geometry')
plt.grid(True, alpha=0.3)
plt.axis('equal')

plt.tight_layout()
plt.show()

print(f"\n📊 Lattice properties:")
print(f"  Coordination number: {jnp.sum(lattice_adjacency[0])}")
print(f"  Lattice dimension: 2D")
print(f"  Boundary conditions: Periodic")

# Create Hubbard GAT model
model = NetKetLatticeFermionGAT(
    n_sites=n_sites,
    n_fermions=n_fermions,
    lattice_type="square",
    has_spin=True,
    model_type="hubbard",  # Use Hubbard-specific features
    hidden_features=[24, 12],
    n_heads=[6, 2],
    U=U,  # Pass Hubbard parameters
    t=t,
    rngs=nnx.Rngs(42)
)

print("🧠 Hubbard GAT model created!")
print(f"Model type: {type(model.gat_model).__name__}")
print(f"Architecture:")
print(f"  Input: {n_sites * 2} orbitals (spin up + spin down)")
print(f"  Hidden layers: {model.gat_model.hidden_features}")
print(f"  Attention heads: {model.gat_model.n_heads}")
print(f"  Hubbard parameters: U={model.gat_model.U}, t={model.gat_model.t}")

# Test with sample Hubbard configurations
test_configs = [
    jnp.array([1, 0, 0, 1, 1, 0, 0, 1]),  # Antiferromagnetic order
    jnp.array([1, 1, 0, 0, 0, 0, 1, 1]),  # Charge density wave
    jnp.array([1, 0, 1, 0, 0, 1, 0, 1]),  # Checkerboard pattern
]

config_names = ["Antiferromagnetic", "Charge density wave", "Checkerboard"]

print(f"\n🧪 Testing Hubbard GAT with characteristic configurations:")
for name, config in zip(config_names, test_configs):
    output = model(config)
    # Analyze configuration
    features = fermion_occupation_to_features(config, has_spin=True)
    double_occ = jnp.sum(features['double_occupancy'])
    total_spin = features['total_spin']
    
    print(f"  {name:18s}: output={output:8.4f}, double_occ={double_occ:.0f}, total_spin={total_spin:+.0f}")

print(f"\n✅ Hubbard GAT successfully processes fermionic configurations!")

# Set up VMC for Hubbard model
sampler = nk.sampler.MetropolisLocal(hilbert, n_chains=8)
vs = nk.vqs.MCState(sampler, model, n_samples=800)

# Use smaller learning rate for strongly correlated systems
optimizer = nk.optimizer.Sgd(learning_rate=0.003)
vmc = nk.VMC(hamiltonian, optimizer, variational_state=vs)

print("⚙️ VMC setup for Hubbard model:")
print(f"Sampler: {type(sampler).__name__}")
print(f"Chains: {sampler.n_chains}, Samples: {vs.n_samples}")
print(f"Optimizer: {type(optimizer).__name__} (lr={optimizer.lr})")
print(f"Strong correlation regime: U/t = {U/t}")

# Run VMC optimization
n_steps = 50
energies = []
variances = []
double_occupancies = []

print(f"🚀 Optimizing Hubbard model ({n_steps} steps)")
print("Step | Energy      | Variance    | Error       | Double Occ  | Progress")
print("-" * 70)

for step in range(n_steps):
    vmc.advance()
    energy = vs.expect(hamiltonian)
    
    energies.append(energy.mean)
    variances.append(energy.variance)
    
    # Estimate double occupancy from current samples
    samples = vs.samples
    if len(samples) > 0:
        sample_features = fermion_occupation_to_features(samples[0], has_spin=True)
        double_occ = jnp.sum(sample_features['double_occupancy'])
        double_occupancies.append(float(double_occ))
    else:
        double_occupancies.append(0.0)
    
    if step % 10 == 0 or step == n_steps - 1:
        error = abs(energy.mean - exact_energy)
        progress = "█" * (step * 20 // n_steps) + "░" * (20 - step * 20 // n_steps)
        print(f"{step:4d} | {energy.mean:10.5f} | {energy.variance:10.6f} | {error:10.6f} | {double_occupancies[-1]:10.2f} | {progress}")

final_energy = energies[-1]
final_error = abs(final_energy - exact_energy)
final_double_occ = double_occupancies[-1]

print(f"\n✅ Hubbard optimization complete!")
print(f"Final energy: {final_energy:.6f}")
print(f"Approximate exact: {exact_energy:.6f}")
print(f"Energy error: {final_error:.6f}")
print(f"Final double occupancy: {final_double_occ:.2f}")
print(f"Expected double occ (U→∞): 0.0, (U=0): {n_sites/2:.1f}")