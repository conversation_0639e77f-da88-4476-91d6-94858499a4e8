{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# NetKet GCNN Architecture Deep Dive\n", "\n", "This notebook provides an interactive exploration of NetKet's Group Convolutional Neural Network (GCNN) architecture. We'll examine how symmetry-preserving operations transform input data through the network layers.\n", "\n", "## 🎯 **Learning Objectives:**\n", "- Understand the internal structure of NetKet's GCNN classes\n", "- Visualize data flow through symmetry-preserving layers\n", "- Compare different GCNN variants (FFT vs Irrep, with/without parity)\n", "- Explore how symmetry operations affect quantum state representations\n", "- Prepare for implementing polynomial enhancements"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Imports successful!\n", "JAX devices: [CpuDevice(id=0)]\n", "NetKet version: 3.19.0\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "sys.path.append('../repos/netket')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Any\n", "\n", "# Import GCNN classes directly\n", "from netket.models.equivariant import (\n", "    GCNN_FFT, GCNN_Irrep, GCNN_Parity_FFT, GCNN_Parity_Irrep, GCNN\n", ")\n", "from netket.jax import logsumexp_cplx\n", "from netket.nn.activation import reim_selu\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup: Create Quantum Systems and Symmetries\n", "\n", "First, let's create different quantum systems to explore how GCNN handles various symmetry groups."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Quantum Systems Created:\n", "==================================================\n", "1d_chain    : 1D Chain (L=6) with translation symmetry\n", "              Sites: 6, Symmetries: 6\n", "\n", "2d_square   : 2D Square (3x3) with translation symmetry\n", "              Sites: 9, Symmetries: 9\n", "\n", "small_chain : Small Chain (L=4) for detailed analysis\n", "              Sites: 4, Symmetries: 4\n", "\n"]}], "source": ["# =============================================================================\n", "# QUANTUM SYSTEM SETUP\n", "# =============================================================================\n", "\n", "def create_quantum_systems():\n", "    \"\"\"Create various quantum systems with different symmetries.\"\"\"\n", "    \n", "    systems = {}\n", "    \n", "    # 1D Chain with translation symmetry\n", "    L1D = 6\n", "    graph_1d = nk.graph.Chain(L1D, pbc=True)\n", "    hilbert_1d = nk.hilbert.Spin(s=1/2, N=L1D)\n", "    symmetries_1d = graph_1d.translation_group()\n", "    \n", "    systems['1d_chain'] = {\n", "        'graph': graph_1d,\n", "        'hilbert': hilbert_1d,\n", "        'symmetries': symmetries_1d,\n", "        'shape': (L1D,),\n", "        'description': f'1D Chain (L={L1D}) with translation symmetry'\n", "    }\n", "    \n", "    # 2D Square lattice\n", "    L2D = 3  # 3x3 = 9 sites\n", "    graph_2d = nk.graph.Square(L2D, pbc=True)\n", "    hilbert_2d = nk.hilbert.Spin(s=1/2, N=graph_2d.n_nodes)\n", "    symmetries_2d = graph_2d.translation_group()\n", "    \n", "    systems['2d_square'] = {\n", "        'graph': graph_2d,\n", "        'hilbert': hilbert_2d,\n", "        'symmetries': symmetries_2d,\n", "        'shape': (L2D, L2D),\n", "        'description': f'2D Square ({L2D}x{L2D}) with translation symmetry'\n", "    }\n", "    \n", "    # Small system for detailed analysis\n", "    L_small = 4\n", "    graph_small = nk.graph.Chain(L_small, pbc=True)\n", "    hilbert_small = nk.hilbert.Spin(s=1/2, N=L_small)\n", "    symmetries_small = graph_small.translation_group()\n", "    \n", "    systems['small_chain'] = {\n", "        'graph': graph_small,\n", "        'hilbert': hilbert_small,\n", "        'symmetries': symmetries_small,\n", "        'shape': (L_small,),\n", "        'description': f'Small Chain (L={L_small}) for detailed analysis'\n", "    }\n", "    \n", "    return systems\n", "\n", "# Create systems\n", "quantum_systems = create_quantum_systems()\n", "\n", "# Display system information\n", "print(\"🔬 Quantum Systems Created:\")\n", "print(\"=\" * 50)\n", "for name, system in quantum_systems.items():\n", "    n_sites = system['graph'].n_nodes\n", "    n_symm = len(system['symmetries'])\n", "    print(f\"{name:12}: {system['description']}\")\n", "    print(f\"{'':12}  Sites: {n_sites}, Symmetries: {n_symm}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. GCNN Class Instantiation and Comparison\n", "\n", "Let's create different GCNN variants and examine their structure."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 GCNN Models Created:\n", "============================================================\n", "FFT            : GCNN_FFT\n", "                 Layers: 2, Features: [4, 8]\n", "\n", "Irrep          : GCNN_Irrep\n", "                 Layers: 2, Features: [4, 8]\n", "\n", "Parity_FFT     : GCNN_Parity_FFT\n", "                 Layers: 2, Features: [4, 8]\n", "                 Parity: Even (1)\n", "\n", "Parity_Irrep   : GCNN_Parity_Irrep\n", "                 Layers: 2, Features: [4, 8]\n", "                 Parity: <PERSON> (-1)\n", "\n", "System: Small Chain (L=4) for detailed analysis\n", "Symmetries: 4 elements\n"]}], "source": ["# =============================================================================\n", "# GCNN MODEL CREATION\n", "# =============================================================================\n", "\n", "def create_gcnn_variants(system_name='small_chain'):\n", "    \"\"\"Create different GCNN variants for comparison.\"\"\"\n", "    \n", "    system = quantum_systems[system_name]\n", "    symmetries = system['symmetries']\n", "    shape = system['shape']\n", "    \n", "    # Common parameters\n", "    layers = 2\n", "    features = [4, 8]  # [first_layer_features, second_layer_features]\n", "    \n", "    models = {}\n", "    \n", "    # 1. GCNN_FFT\n", "    models['FFT'] = GCNN(\n", "        symmetries=symmetries,\n", "        mode=\"fft\",\n", "        shape=shape,\n", "        layers=layers,\n", "        features=features,\n", "        complex_output=True,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # 2. GCNN_Irrep\n", "    models['Irrep'] = GCNN(\n", "        symmetries=symmetries,\n", "        mode=\"irreps\",\n", "        layers=layers,\n", "        features=features,\n", "        complex_output=True,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # 3. GCNN_Parity_FFT\n", "    models['Parity_FFT'] = GCNN(\n", "        symmetries=symmetries,\n", "        mode=\"fft\",\n", "        shape=shape,\n", "        layers=layers,\n", "        features=features,\n", "        parity=1,  # Even parity\n", "        complex_output=True,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    # 4. GCNN_Parity_Irrep\n", "    models['Parity_Irrep'] = GCNN(\n", "        symmetries=symmetries,\n", "        mode=\"irreps\",\n", "        layers=layers,\n", "        features=features,\n", "        parity=-1,  # Odd parity\n", "        complex_output=True,\n", "        param_dtype=jnp.float64\n", "    )\n", "    \n", "    return models, system\n", "\n", "# Create models\n", "gcnn_models, current_system = create_gcnn_variants('small_chain')\n", "\n", "# Display model information\n", "print(\"🧠 GCNN Models Created:\")\n", "print(\"=\" * 60)\n", "for name, model in gcnn_models.items():\n", "    model_type = type(model).__name__\n", "    print(f\"{name:15}: {model_type}\")\n", "    print(f\"{'':15}  Layers: {model.layers}, Features: {model.features}\")\n", "    if hasattr(model, 'parity') and model.parity is not None:\n", "        parity_str = \"Even\" if model.parity == 1 else \"Odd\"\n", "        print(f\"{'':15}  Parity: {parity_str} ({model.parity})\")\n", "    print()\n", "\n", "print(f\"System: {current_system['description']}\")\n", "print(f\"Symmetries: {len(current_system['symmetries'])} elements\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Poly_GCNN_FFT(GCNN_FFT):\n", "    r\"\"\"Implements a GCNN using a fast fourier transform over the translation group.\n", "\n", "    The group convolution can be written in terms of translational convolutions with\n", "    symmetry transformed filters as described in ` <PERSON> et. *al* <http://proceedings.mlr.press/v48/cohenc16.pdf>`_\n", "    The translational convolutions are then implemented with Fast Fourier Transforms.\n", "    \"\"\"\n", "\n", "    symmetries: Has<PERSON>leArray\n", "    \"\"\"A group of symmetry operations (or array of permutation indices) over which the network should be equivariant.\n", "    Numpy/Jax arrays must be wrapped into an :class:`netket.utils.HashableArray`.\n", "    \"\"\"\n", "    product_table: HashableArray\n", "    \"\"\"Product table describing the algebra of the symmetry group\n", "    Numpy/Jax arrays must be wrapped into an :class:`netket.utils.HashableArray`.\n", "    \"\"\"\n", "    shape: tuple\n", "    \"\"\"Shape of the translation group\"\"\"\n", "    #layers: int\n", "    \"\"\"Number of layers (not including sum layer over output).\"\"\"\n", "    degree: int\n", "    \"\"\"Degree of the polynomial\"\"\"\n", "    features: tuple\n", "    \"\"\"Number of features in each layer starting from the input. If a single number is given,\n", "    all layers will have the same number of features.\"\"\"\n", "    characters: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    \"\"\"Array specifying the characters of the desired symmetry representation\"\"\"\n", "    param_dtype: Any = float\n", "    \"\"\"The dtype of the weights.\"\"\"\n", "    activation: Any = reim_selu\n", "    \"\"\"The nonlinear activation function between hidden layers.\"\"\"\n", "    output_activation: Any = identity\n", "    \"\"\"The nonlinear activation before the output. Defaults to the identity.\"\"\"\n", "    input_mask: Array = None\n", "    \"\"\"Optional array of shape `(n_sites,)` used to restrict the convolutional\n", "        kernel. Only parameters with mask :math:'\\ne 0' are used. For best performance a\n", "        boolean mask should be used.\"\"\"\n", "    hidden_mask: Array = None\n", "    \"\"\"Optional array of shape `(n_symm,)` where `(n_symm,)` = `len(graph.automorphisms())`\n", "        used to restrict the convolutional kernel. Only parameters with mask :math:'\\ne 0' are used.\n", "        For best performance a boolean mask should be used\"\"\"\n", "    equal_amplitudes: bool = False\n", "    \"\"\"If true forces all basis states to have the same amplitude by setting `Re[logψ] = 0`\"\"\"\n", "    use_bias: bool = True\n", "    \"\"\"if True uses a bias in all layers.\"\"\"\n", "    precision: Any = None\n", "    \"\"\"numerical precision of the computation see :class:`jax.lax.Precision` for details.\"\"\"\n", "    kernel_init: NNInitFunc = default_gcnn_initializer\n", "    \"\"\"Initializer for the kernels of all layers.\"\"\"\n", "    bias_init: NNInitFunc = zeros\n", "    \"\"\"Initializer for the biases of all layers.\"\"\"\n", "    complex_output: bool = True\n", "    \"\"\"Use complex-valued `logsumexp`. Necessary when parameters are real but some\n", "    `characters` are negative.\"\"\"\n", "\n", "    def setup(self):\n", "        self.degree = degree\n", "        self.n_symm = np.asarray(self.symmetries).shape[0]\n", "\n", "        self.dense_symm = DenseSymmFFT(\n", "            space_group=self.symmetries,\n", "            shape=self.shape,\n", "            features=self.features[0],\n", "            param_dtype=self.param_dtype,\n", "            use_bias=self.use_bias,\n", "            kernel_init=self.kernel_init,\n", "            bias_init=self.bias_init,\n", "            precision=self.precision,\n", "            mask=self.input_mask,\n", "        )\n", "\n", "        self.equivariant_layers = [\n", "            DenseEquivariantFFT(\n", "                product_table=self.product_table,\n", "                shape=self.shape,\n", "                features=self.features[layer + 1],\n", "                use_bias=self.use_bias,\n", "                param_dtype=self.param_dtype,\n", "                precision=self.precision,\n", "                kernel_init=self.kernel_init,\n", "                bias_init=self.bias_init,\n", "                mask=self.hidden_mask,\n", "            )\n", "            for layer in range(self.degree)\n", "        ]\n", "\n", "    @nn.compact\n", "    def __call__(self, x):\n", "        if x.ndim < 3:\n", "            x = jnp.expand_dims(x, -2)  # add a feature dimension\n", "        x = self.dense_symm(x)\n", "\n", "        for degree in range(2, self.degree + 1):\n", "            x = self.equivariant_layers[degree](x) * x + x\n", "    \n", "\n", "        x = self.output_activation(x)\n", "\n", "        if self.polynomial_output:\n", "            return jnp.sum(x, axis=-(-2, -1))  # Sum over spatial dimension, keep batch\n", "\n", "        if self.complex_output:\n", "            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(self.characters))\n", "        else:\n", "            x = logsumexp(x, axis=(-2, -1), b=jnp.asarray(self.characters))\n", "\n", "        if self.equal_amplitudes:\n", "            return 1j * jnp.imag(x)\n", "        else:\n", "            return x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Flow Visualization: Step-by-Step Forward Pass\n", "\n", "Now let's trace how data flows through a GCNN, examining shapes and transformations at each step."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Test Input Data:\n", "   Shape: (2, 4)\n", "   Values:\n", "[[ 1. -1.  1. -1.]\n", " [ 1.  1. -1. -1.]]\n", "\n", "🔍 Tracing Forward Pass: FFT\n", "============================================================\n", "1. Input shape: (2, 4)\n", "   After expand_dims: (2, 1, 4)\n", "3.1. After activation 0: (2, 1, 4)\n", "❌ Error in forward pass: Initializer expected to generate shape (8, 4, 4) but got shape (8, 1, 4) instead for parameter \"kernel\" in \"/equivariant_layers_0\". (https://flax.readthedocs.io/en/latest/api_reference/flax.errors.html#flax.errors.ScopeParamShapeError)\n", "✅ Standard forward pass successful: [3.75912577+0.j 4.12414087+0.j]\n", "\n", "🔍 Tracing Forward Pass: Irrep\n", "============================================================\n", "1. Input shape: (2, 4)\n", "   After expand_dims: (2, 1, 4)\n", "3.1. After activation 0: (2, 1, 4)\n", "❌ Error in forward pass: Initializer expected to generate shape (8, 4, 4) but got shape (8, 1, 4) instead for parameter \"kernel\" in \"/equivariant_layers_0\". (https://flax.readthedocs.io/en/latest/api_reference/flax.errors.html#flax.errors.ScopeParamShapeError)\n", "✅ Standard forward pass successful: [3.75912577+0.j 4.12414087+0.j]\n", "\n", "🔍 Tracing Forward Pass: Parity_FFT\n", "============================================================\n", "1. Input shape: (2, 4)\n", "   After expand_dims: (2, 1, 4)\n", "❌ Error in forward pass: cannot access local variable 'x_flip' where it is not associated with a value\n", "✅ Standard forward pass successful: [4.43396766+0.j 4.98901095+0.j]\n", "\n", "🔍 Tracing Forward Pass: Parity_Irrep\n", "============================================================\n", "1. Input shape: (2, 4)\n", "   After expand_dims: (2, 1, 4)\n", "❌ Error in forward pass: cannot access local variable 'x_flip' where it is not associated with a value\n", "✅ Standard forward pass successful: [-34.94424564+3.14159265j -35.52353771+3.14159265j]\n"]}], "source": ["# =============================================================================\n", "# DATA FLOW ANALYSIS\n", "# =============================================================================\n", "\n", "def trace_gcnn_forward_pass(model, input_data, model_name=\"GCNN\"):\n", "    \"\"\"Trace the forward pass through a GCNN model step by step.\"\"\"\n", "    \n", "    print(f\"\\n🔍 Tracing Forward Pass: {model_name}\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Initialize model parameters\n", "    key = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "    params = model.init(key, input_data)\n", "    \n", "    # Create a modified forward pass that captures intermediates\n", "    def forward_with_intermediates(params, x):\n", "        intermediates = {}\n", "        \n", "        # Step 1: Input preprocessing\n", "        print(f\"1. Input shape: {x.shape}\")\n", "        if x.ndim < 3:\n", "            x = jnp.expand_dims(x, -2)\n", "        print(f\"   After expand_dims: {x.shape}\")\n", "        intermediates['input_expanded'] = x\n", "        \n", "        # Step 2: Dense symmetry layer\n", "        if hasattr(model, 'dense_symm'):\n", "            # For parity models, we need to handle both x and -x\n", "            if 'Parity' in model_name:\n", "                x_flip = model.apply(params, -1 * x, method=lambda m, x: m.dense_symm(x))\n", "                x = model.apply(params, x, method=lambda m, x: m.dense_symm(x))\n", "                print(f\"2. After dense_symm (parity): x={x.shape}, x_flip={x_flip.shape}\")\n", "                intermediates['dense_symm'] = x\n", "                intermediates['dense_symm_flip'] = x_flip\n", "            else:\n", "                x = model.apply(params, x, method=lambda m, x: m.dense_symm(x))\n", "                print(f\"2. After dense_symm: {x.shape}\")\n", "                intermediates['dense_symm'] = x\n", "        \n", "        # Step 3: Equivariant layers\n", "        for layer_idx in range(model.layers - 1):\n", "            # Activation\n", "            x = model.activation(x)\n", "            if 'Parity' in model_name:\n", "                x_flip = model.activation(x_flip)\n", "            \n", "            print(f\"3.{layer_idx+1}. After activation {layer_idx}: {x.shape}\")\n", "            \n", "            # Equivariant layer\n", "            if 'Parity' in model_name:\n", "                # Simplified parity handling for demonstration\n", "                x_new = x  # Placeholder - actual parity logic is more complex\n", "                x_flip = x_flip\n", "                x = x_new\n", "            else:\n", "                # Apply equivariant layer\n", "                layer_fn = lambda m, x: m.equivariant_layers[layer_idx](x)\n", "                x = model.apply(params, x, method=layer_fn)\n", "            \n", "            print(f\"     After equivariant layer {layer_idx}: {x.shape}\")\n", "            intermediates[f'equivariant_{layer_idx}'] = x\n", "        \n", "        # Step 4: Output activation\n", "        x = model.output_activation(x)\n", "        print(f\"4. After output_activation: {x.shape}\")\n", "        intermediates['output_activation'] = x\n", "        \n", "        # Step 5: Final reduction (logsumexp)\n", "        if model.complex_output:\n", "            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(model.characters))\n", "        else:\n", "            x = jax.scipy.special.logsumexp(x, axis=(-2, -1), b=jnp.asarray(model.characters))\n", "        \n", "        print(f\"5. After logsumexp reduction: {x.shape}\")\n", "        intermediates['final_output'] = x\n", "        \n", "        return x, intermediates\n", "    \n", "    # Run the traced forward pass\n", "    try:\n", "        output, intermediates = forward_with_intermediates(params, input_data)\n", "        print(f\"\\n✅ Final output: {output}\")\n", "        print(f\"   Output shape: {output.shape}\")\n", "        print(f\"   Output dtype: {output.dtype}\")\n", "        return output, intermediates, params\n", "    except Exception as e:\n", "        print(f\"❌ Error in forward pass: {e}\")\n", "        # Fallback: use standard model.apply\n", "        output = model.apply(params, input_data)\n", "        print(f\"✅ Standard forward pass successful: {output}\")\n", "        return output, {}, params\n", "\n", "# Create test input data\n", "n_sites = current_system['graph'].n_nodes\n", "batch_size = 2\n", "\n", "# Create sample quantum states (spin configurations)\n", "test_input = jnp.array([\n", "    [1, -1, 1, -1],  # Alternating spins\n", "    [1, 1, -1, -1]   # Domain wall\n", "], dtype=jnp.float64)\n", "\n", "print(f\"📊 Test Input Data:\")\n", "print(f\"   Shape: {test_input.shape}\")\n", "print(f\"   Values:\\n{test_input}\")\n", "\n", "# Trace forward pass for each model\n", "results = {}\n", "for name, model in gcnn_models.items():\n", "    try:\n", "        output, intermediates, params = trace_gcnn_forward_pass(model, test_input, name)\n", "        results[name] = {\n", "            'output': output,\n", "            'intermediates': intermediates,\n", "            'params': params\n", "        }\n", "    except Exception as e:\n", "        print(f\"❌ Failed to trace {name}: {e}\")\n", "        continue"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Interactive Experimentation\n", "\n", "Let's create interactive widgets to experiment with different GCNN configurations."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Testing Different GCNN Configurations:\n", "\n", "\n", "Experiment 1:\n", "🧪 GCNN Configuration Experiment\n", "System: small_chain\n", "Mode: auto, Layers: 2, Features: 4\n", "Parity: None, Complex Output: True\n", "==================================================\n", "✅ Created: GCNN_Irrep\n", "✅ Forward pass successful!\n", "   Input shape: (1, 4)\n", "   Output: [2.91591305+0.j]\n", "   Output shape: (1,)\n", "   Parameters: 88\n", "\n", "\n", "Experiment 2:\n", "🧪 GCNN Configuration Experiment\n", "System: small_chain\n", "Mode: fft, Layers: 3, Features: 8\n", "Parity: None, Complex Output: True\n", "==================================================\n", "✅ Created: GCNN_FFT\n", "✅ Forward pass successful!\n", "   Input shape: (1, 4)\n", "   Output: [4.04592361+0.j]\n", "   Output shape: (1,)\n", "   Parameters: 568\n", "\n", "\n", "Experiment 3:\n", "🧪 GCNN Configuration Experiment\n", "System: small_chain\n", "Mode: irreps, Layers: 2, Features: 6\n", "Parity: 1, Complex Output: True\n", "==================================================\n", "✅ Created: GCNN_Parity_Irrep\n", "✅ Forward pass successful!\n", "   Input shape: (1, 4)\n", "   Output: [4.35469548+0.j]\n", "   Output shape: (1,)\n", "   Parameters: 330\n", "\n", "\n", "Experiment 4:\n", "🧪 GCNN Configuration Experiment\n", "System: small_chain\n", "Mode: auto, Layers: 1, Features: 16\n", "Parity: -1, Complex Output: True\n", "==================================================\n", "✅ Created: GCNN_Parity_Irrep\n", "✅ Forward pass successful!\n", "   Input shape: (1, 4)\n", "   Output: [4.26206605+3.14159265j]\n", "   Output shape: (1,)\n", "   Parameters: 80\n", "\n"]}], "source": ["# =============================================================================\n", "# INTERACTIVE EXPERIMENTATION\n", "# =============================================================================\n", "\n", "def experiment_with_gcnn_config(system_name='small_chain', \n", "                               mode='auto', \n", "                               layers=2, \n", "                               features_per_layer=8,\n", "                               parity=None,\n", "                               complex_output=True):\n", "    \"\"\"Interactive function to experiment with GCNN configurations.\"\"\"\n", "    \n", "    print(f\"🧪 GCNN Configuration Experiment\")\n", "    print(f\"System: {system_name}\")\n", "    print(f\"Mode: {mode}, Layers: {layers}, Features: {features_per_layer}\")\n", "    print(f\"Parity: {parity}, Complex Output: {complex_output}\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Get system\n", "    system = quantum_systems[system_name]\n", "    \n", "    # Create features list\n", "    features = [features_per_layer] * layers\n", "    \n", "    # Create GCNN\n", "    try:\n", "        if mode == 'fft':\n", "            gcnn = GCNN(\n", "                symmetries=system['symmetries'],\n", "                mode=mode,\n", "                shape=system['shape'],\n", "                layers=layers,\n", "                features=features,\n", "                parity=parity,\n", "                complex_output=complex_output,\n", "                param_dtype=jnp.float64\n", "            )\n", "        else:\n", "            gcnn = GCNN(\n", "                symmetries=system['symmetries'],\n", "                mode=mode,\n", "                layers=layers,\n", "                features=features,\n", "                parity=parity,\n", "                complex_output=complex_output,\n", "                param_dtype=jnp.float64\n", "            )\n", "        \n", "        print(f\"✅ Created: {type(gcnn).__name__}\")\n", "        \n", "        # Test with sample input\n", "        n_sites = system['graph'].n_nodes\n", "        test_input = jnp.ones((1, n_sites))  # All spins up\n", "        \n", "        # Initialize and run\n", "        key = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "        params = gcnn.init(key, test_input)\n", "        output = gcnn.apply(params, test_input)\n", "        \n", "        print(f\"✅ Forward pass successful!\")\n", "        print(f\"   Input shape: {test_input.shape}\")\n", "        print(f\"   Output: {output}\")\n", "        print(f\"   Output shape: {output.shape}\")\n", "        \n", "        # Count parameters\n", "        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params))\n", "        print(f\"   Parameters: {param_count:,}\")\n", "        \n", "        return gcnn, params, output\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\n", "        return None, None, None\n", "\n", "# Test different configurations\n", "print(\"🔬 Testing Different GCNN Configurations:\")\n", "print()\n", "\n", "configs = [\n", "    {'mode': 'auto', 'layers': 2, 'features_per_layer': 4, 'parity': None},\n", "    {'mode': 'fft', 'layers': 3, 'features_per_layer': 8, 'parity': None},\n", "    {'mode': 'irreps', 'layers': 2, 'features_per_layer': 6, 'parity': 1},\n", "    {'mode': 'auto', 'layers': 1, 'features_per_layer': 16, 'parity': -1},\n", "]\n", "\n", "experiment_results = []\n", "for i, config in enumerate(configs):\n", "    print(f\"\\nExperiment {i+1}:\")\n", "    result = experiment_with_gcnn_config(**config)\n", "    experiment_results.append(result)\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Symmetry Operations Visualization\n", "\n", "Let's visualize how symmetry operations affect the input data and intermediate representations."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Symmetry Operations Visualization: Small Chain (L=4) for detailed analysis\n", "============================================================\n", "Original configuration: [ 1 -1  1 -1]\n", "Number of symmetries: 4\n", "\n", "Symmetry  0: [ 1 -1  1 -1]\n", "\n", "Symmetry  1: [-1  1 -1  1]\n", "\n", "Symmetry  2: [ 1 -1  1 -1]\n", "\n", "Symmetry  3: [-1  1 -1  1]\n", "\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABJoAAAMWCAYAAAC5vOyrAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3XlcVOX7//H3ICiICypumYohYKYGimK55Z4LikrpN3PXTBSXUsMszcj4WLhvuZWZVqZpaWraplmfckUzFRNKs1xKcWURkfn94Y/5NIEKOAvMvJ6Pxzxizrnn3NfFmencXtznHoPRaDQKAAAAAAAAuEcu9g4AAAAAAAAAjoFCEwAAAAAAACyCQhMAAAAAAAAsgkITAAAAAAAALIJCEwAAAAAAACyCQhMAAAAAAAAsgkITAAAAAAAALIJCEwAAAAAAACyCQhMAODGj0WjvEAAAsAmueYUL5wsovCg0AU5i3759ioyMVJMmTVS3bl21bt1aL730khITE3P1+nXr1ikgIEB//PFHrvvMz2ty448//lBAQIDWrVt317a//fabXnnlFbVp00b16tXTY489pjFjxig+Pt6iMf3Trl271L59e9WpU0eDBg3S3LlzFRAQYLX+8iM9PV0xMTHauHGjaVtUVJRatWplx6gAALk1fvx4BQQEaPHixdn29enTR3369DE9DwgI0Ny5c+94vIsXLyomJkZt2rRRnTp11KhRI/Xr109bt261eOz28NVXX+mFF16w+HGjoqIUEBBwx0dBubaePXtWTz/9tOrWratHHnlEqamp9g7pthYuXKhly5aZnhfEsRSA23O1dwAArG/x4sWaMWOGmjRpogkTJqhChQo6efKkPvjgA3Xr1k0xMTHq1KnTHY/x2GOPafXq1apQoUKu+83Payzpiy++0Lhx4+Tn56dhw4bp/vvv19mzZ/Xee+/piSee0Pz589W8eXOL9ztt2jRlZmZq8eLFKleunEqXLq1mzZpZvJ978ddff2n58uWKiYkxbYuIiFDfvn3tGBUAIDeuXbumbdu2yd/fXx999JGGDBkig8GQ7+OlpaWpd+/eysjI0JAhQ+Tj46OrV69qy5YtGjlypCZMmKD+/ftbLgE7WL58uVWOGxERoV69epmeL1iwQEeOHNG8efNM24oWLWqVvvPq3XffVVxcnN58801VrFhRHh4e9g7ptmbNmqURI0aYnj/xxBMFbiwF4PYoNAEO7ptvvtH06dMVERGhUaNGmbY3atRIYWFhev755xUVFSV/f3/5+fnd9jhly5ZV2bJl89R3fl5jKb///rvGjx+vZs2aadasWSpSpIhpX/v27fXUU08pKipKX3/9tdzd3S3a96VLl9SwYUM9+uijpm2VKlWyaB/WUK1aNXuHAADIhU2bNunmzZt66aWX1LdvX3333Xf39I/wzz//XImJifr8889Vo0YN0/Y2bdooLS1Nc+fOVZ8+fcyupbilWrVqZtfPsmXLqmjRogoMDLRfULdx6dIlVahQQR07drR3KHlWqVKlQjGWAnALt84BDm7evHmqUaOGRo4cmW2fm5ubpkyZoiJFimjJkiWm7QEBAZo3b5569OihBg0aaMGCBTneBrd+/Xp17NhRdevWVZcuXfTDDz+odu3aplva/v2aqKgo9e/fXx9//LHp1rIuXbpox44dZnHt2bNHgwYNUsOGDVWnTh21atVKc+fOVWZmZq7zfu+995Senq6XXnop28DY3d1dL7zwgsLDw3XlyhXT9u+//15PPfWUGjRooJCQED3//PM6c+aMaf+6detUu3ZtHTx4UD179lTdunX12GOPmX53Wbf0/fnnn/rkk08UEBCgXbt25Tjde9myZWrdurXq1aunXr166euvvza1l24/Rfyftz9k9ffOO++oQ4cOatSokel3/+WXX+qpp55SUFCQ6tSpo8cff1wrV640va5169aSpAkTJpim9P/71rmbN29q1apVCg0NNd12GBsbq+vXr5va5PacAgAs5+OPP1ZISIhCQkJUo0YNffjhh/d0vPPnz0vKeU2coUOHKiIiQunp6Tp+/LgCAgK0evVqszbnzp3Tgw8+qPXr15uuTVu3blVERIQCAwP16KOPasGCBbp27ZpefPFFNWjQQI8++qjefPNNU5/5fZ0kXb9+XW+88YZatGihOnXqKDQ0VJs3bzbt79Onj3bv3q3du3ebrrW7du1SQECAPvzwQ7Vs2VKPPvqovvzySwUEBOi7774zy+/AgQMKCAjQ7t278/07zqm/rH7WrFmj7t27KzAwUPXq1VPXrl3N4r/b+CPL5s2b1aVLF9WrV0+NGzfW2LFj9ddff0mSWrVqpXXr1un06dNmY4m//vpLEyZMUIsWLVSvXj2Fh4frq6++Mjvu7caFdevW1b59+9SjRw/VrVtX7du319dff61ff/1V/fr108MPP6y2bdtq06ZNZse72zgva/wzb9480885jYs2b96s7t27KygoSE2aNNGkSZN0+fJl0/65c+eqbdu22r59u0JDQ1WnTh21b99e69evNzvOe++9p8cff1x169ZVs2bN9Morr+jatWt5OLsA/o1CE+DAkpKS9PPPP6tly5a3nVJfpkwZPfroo9kGFQsXLlT79u01Y8YMU1Hinz755BNFRUWpfv36WrBggdq3b6+IiAjdvHnzjjH9/PPPWrZsmUaOHKn58+fL1dVVI0eONA0M4uPj1b9/f3l5eWnmzJlauHCh6tevr3nz5mUbqNzJzp07Vbt2bVWsWDHH/SEhIXruuedMt/V9+umnGjhwoCpWrKgZM2ZowoQJiouLU8+ePXXhwgXT6zIzMzV69Gh17NhRixcvVoMGDRQbG6udO3eqQoUKWr16tcqXL68WLVpo9erVeuihh7L1PW/ePMXGxqpDhw5asGCBHn74YY0ZMybXuf3bzJkzNWjQIL322mtq3Lixtm/fruHDh+uhhx7SggULNHfuXFWpUkXR0dHav3+/KlSoYJrSP2zYMLPp/f80adIkvf7662rVqpUWLlyo3r17a+XKlYqIiDAb4N/tnAIALCcxMVEHDx5Ut27dJEndu3fXN998o3PnzuX7mM2aNZOrq6v69eunefPm6cCBA7px44YkqV69eho0aJA8PDzk5+enhx9+WJ9++qnZ6z/99FO5u7urffv2pm0TJ06Uv7+/Fi5cqMaNG2v27NkKDw+Xu7u7Zs+erVatWmnp0qX6/PPPzY6V19cZjUYNHz5cH374oQYMGKCFCxcqKChIY8aM0SeffCJJmjx5smrXrq3atWtnuzbPnDlTL7zwgl544QWFhISoYsWK2fJbv369qlatqoYNG+b7d5xTf4GBgVq1apUmTZqk1q1ba9GiRXrzzTfl5uamcePG6fTp06bX3Wn8Id1ai3Ps2LFq166dlixZogkTJujHH3/U888/L+nW2KNFixYqX768Vq9erSeeeELnz59XeHi4du/erTFjxpjGC8OHD9eGDRvM4s5pXJiRkaHnnntOvXr10oIFC1SsWDGNHTtWzz77rB577DHNnj1b5cuX1wsvvKCzZ89Kyt04L6uQGR4enq2omWXBggUaM2aMHn74Yc2ZM0fDhw/X1q1b1adPH6WlpZna/f3333r11VfVt29fLV68WPfff7+ioqJMa5Ru2rRJ06ZNU+/evbVs2TINHz5cn376qV577bV7PteAM+PWOcCB/fnnn5Kk+++//47tqlevrq+++kqXL19W6dKlJd0aWD7zzDOmNocPHzZ7zezZs9WyZUvThbhZs2Zyc3PT9OnT79jX1atXtW7dOtM08+LFi+vpp5/Wjz/+qPbt2ys+Pt7010oXl1u18CZNmmj79u3as2ePQkNDc5V71l9XcyMzM1NvvvmmHn30Uc2cOdO0vX79+urYsaPefvttjRs3TtKtAW1ERISeeOIJSVKDBg30xRdfaPv27WrWrJkCAwNVtGhRlS1bNsdp8ykpKVqyZIl69+6tsWPHSpKaNm2q1NTU2w6m7qZdu3YKDw83Pd+8ebPCwsI0ceJE07agoCCFhIRoz549ql+/vul3U61aNdWuXTvbMRMSErR27VqNHj1aw4YNk3TrPFSoUEHjx4/Xt99+qxYtWki6+zkFAFjO2rVrVapUKbVp00aSFBYWplmzZmnNmjVma9rkRUBAgGbOnKkpU6Zo7ty5mjt3rtzd3RUcHKwePXqY3WrVo0cPTZo0SadOnVLVqlUl3frjU4cOHVS8eHElJSVJujUuGD16tCSpZs2a2rRpk8qVK6dJkyZJunVN2bJli/bv368OHTqYjp/X1/33v//Vzp07NXPmTFOczZo1U2pqqmJjY9W5c2fVrFlTJUqUkKRs1+ZevXrp8ccfNz0PCwvTe++9p+TkZHl6eio9PV1btmxRv3797mkdrNv1d+rUKQ0cOFDDhw83bbv//vvVvXt37d+/X/fdd5+ku48/9u3bp2LFimnIkCEqVqyYJMnLy0uHDh2S0WhU7dq1s93W9+abbyopKUlbtmwxncsWLVqof//+euONN9S5c2fTWCyncWFmZqaeffZZU0xXrlzRc889p379+mnAgAGSJG9vb/Xo0UM///yzKlWqlKtxXlZ8lSpVynEsdfnyZS1cuFBPPPGEJk+ebNru7++v3r17a926dXrqqackSampqZo6daoeeeQRSZKPj49atmypHTt2yNfXV7t27VKVKlXUu3dvubi4qFGjRipevLguXryY11ML4B+Y0QQ4sKxZJ25ubndsl3Vr2T9nqfj7+9+2/cmTJ3X69GmzgZKkuy4oLt1au+Cfaxlk3W+f9c0nYWFhWrJkiW7cuKHjx4/ryy+/1Ny5c3Xz5k3TX1dzw2Aw3HV2VZbffvtNf//9d7YiVrVq1RQUFGS6nS1LUFCQ6eesolJKSkqu+jpw4IDS0tKy/e46d+6cq9fn5N/navDgwZo2bZpSUlIUHx+vLVu2mL6VKLe/w6zbA/79O+nUqZOKFCli9ju52zkFAFhGRkaGNmzYoDZt2uj69eu6cuWK3N3dFRISojVr1uT6upeTdu3aafv27Vq6dKkGDhwoX19f/fe//9WYMWM0cuRI0xihU6dO8vDwMM36+emnn5SYmKju3bubHe+f18ry5ctLkh5++GHTNoPBoNKlS+vq1av39LoffvhBBoNBLVq0UEZGhunRqlUr/f333zp+/Pgd8/737Vg9evRQamqqvvjiC0m3bkW/cuWKwsLC7nic3Pp3f1FRURo3bpyuXr2qQ4cOaePGjVq1apWk7NfsO40/GjZsqLS0NIWGhmrmzJnat2+fmjZtqhEjRty2QLZ7924FBQWZikxZunTpor///lu//vqradvtxoX/jMnb21uSeTHPy8tLkkxLFVhinHfgwAGlp6dnG6MEBwerSpUq2cZt/4wna4yS9Xtr3LixTpw4oe7du5sWcg8NDVW/fv1yFQuAnDGjCXBgVapUkfS/mU23c+rUKRUvXtw0GJD+N1jISdZfK8uVK2e2PWtAeCf//oaTrMFP1n35aWlpio6O1qeffqqMjAzdf//9CgoKkqura45rR9xOlSpVzKac/1tGRoaSkpJUoUIFXbp0SVLOOXt7e+vIkSNm2/69eLiLi0uuY8v63f17kfQ7/b7v5t+vTUpK0uTJk/Xll1/KYDCoevXqatCggaSc19/ISdZtb/8+p66uripTpozZPwzudk4BAJaxfft2nT9/XuvWrTOtyfdP33zzjWmmU364ubmpWbNmpoXF//rrL7322mvaunWrtm/frpYtW6pEiRJ6/PHHtWHDBo0YMULr169X9erVFRwcbHasrBlE/5SbbznL6+suXboko9Go+vXr57j/r7/+uuMM53+PZapXr66GDRvqk08+UVhYmD755BM1btzYNKa6V//u7/fff9ekSZP0448/ytXVVQ888ICpGPXva/adxh9BQUFavHixli9frmXLlumtt95S+fLlNWTIkNsWTS5fvpzjrPesccU/17G83Tglp/N1py9ZscQ4L2uMcrtx27+Ll/98/2TNosrqq2PHjsrMzNT777+vefPmafbs2apSpYqef/75XP0BFUDOKDQBDqxcuXIKDAzUtm3bNHr06Bz/onXt2jV9//33Oa7DdDtZfw3659pFOT3Pj6lTp2rr1q2aNWuWHn30URUvXlySTFOec6tp06Z699139ffff+dYANu5c6eeffZZzZgxQ7Vq1ZL0v8VQ/+nvv/9WmTJl8pFJzrJ+d0lJSXrggQdM27MKUFmyztXNmzdNM86Sk5Nz1cfYsWOVmJiod955R/Xr11fRokWVmpqqNWvW5DrOrFso//77b7NB6I0bN3Tx4kWL/k4AALmzdu1aValSRTExMdn2jRw5Uh9++GG+Ck29evVSjRo1sh23QoUKpkJTQkKCWrZsKenWrJ/169frp59+Mq2LYy8lS5ZU8eLFtWLFihz3V69ePc/H7NGjhyZMmKDffvtN33//fY6/b0vIzMzUM888Izc3N3300UeqXbu2XF1dlZCQkG2NpNzIKhKmpqbqxx9/1IoVK/T6668rMDDQbFZYltKlS9927CPJKtd6S4zzssYo58+fl6+vr9m+v//+O9sMrbvp3LmzOnfurKtXr+q7777TkiVLNG7cOAUHB992rU8Ad8atc4CDGzFihH799VfNmjUr276bN29q8uTJSktL0+DBg3N9zEqVKqlatWqmaeVZtm7deq/hat++fQoJCVGbNm1Mg4+ff/5ZSUlJeZoh07t3b7m5uem1117LditBamqq5syZo9KlS6tly5aqUaOGypcvr40bN5q1O3XqlA4cOHDbv5LmR61atVSyZElt27bNbPu/f3dZfyH857fe7d+/P1d97Nu3T+3bt1fjxo1VtGhRSdK3334r6X+zjO72FdWNGjWSpGy/k6yv1M6aIQUAsI3z589r586d6tSpk+kb5/756Nixo77//nudOnUqz8euUqWKPv/88xxf+9tvv0kyv3WqYcOG8vHx0ZtvvqmLFy9a7Lay/GjUqJFSUlJkNBpVt25d0+P48eOaP3++MjIyJP1vJktutG/fXsWLF9ekSZPk7u6udu3aWSX2ixcv6rffflN4eLjq1asnV9dbcwD+fc3OjWnTpik8PFxGo1EeHh5q2bKlXnjhBUnmY4l/atiwoeLi4rKd9w0bNqh8+fL5KtLdTW7HeXc6Xw8//LCKFi2abYyyd+9enT59Ok/jttGjR5vWNitZsqQ6dOhg+nKbrG/sA5B3zGgCHFyzZs0UFRWlN954Q0eOHFG3bt1UoUIF/fHHH/rggw909OhRTZ061TSrJzcMBoNGjhypsWPHavLkyWrbtq3i4+M1f/58SXkbzP1bvXr1tGXLFn3wwQfy9fVVfHy8Fi5cKIPBkKc1f+6//3698sormjhxonr37q1evXqpcuXK+v3337V8+XKdPHlSS5YsMQ1ynnvuOU2YMEFjxoxRWFiYLl68qHnz5ql06dKmBS0toUSJEho8eLDmzJkjDw8PNWrUSLt379YHH3wg6X+/uxYtWigmJkYvv/yyhgwZorNnz2revHny9PS8ax/16tXTxo0b9dBDD6lSpUqKi4vTokWLzH6HJUuWlHRrbQtfX99sf+msWbOmunXrpnnz5iktLU0hISE6evSo5s2bp5CQENNtFQAA21i/fr0yMjJueztPt27d9P777+ujjz7K87HHjBmjXbt2KTw8XH379lVQUJBcXFx06NAhvf3222revLmaN29u9poePXpo+vTpatKkiSpXrpyvnCyhRYsWatiwoSIiIhQRESFfX1/99NNPmjt3rpo2bWq6Vb1UqVKKi4vTDz/8kOOXYPyTh4eHOnXqpNWrV+vJJ5+8461g96JcuXKqUqWKVq1apUqVKqlUqVL67rvv9O6770rK21qHjzzyiN555x1FRUWpS5cuunHjhpYuXSovLy81btw4x9cMGDBAGzZs0IABAzRixAiVKVNGn3zyiX788Ue9/vrr9zSeu53cjvOyzteePXuy3Zbp5eWlZ555RvPmzZObm5tat26tP/74Q7Nnz1bNmjWzrRd2J40bN9bkyZM1bdo0NW/eXFeuXNG8efPk4+OTp7ExAHMUmgAnMGDAAAUFBendd9/VtGnTlJSUpPLly6tJkyaaOnWqatasmedjhoaGKiUlRcuWLdPHH38sPz8/TZw4URMnTjQVb/IjKipKN27c0KxZs5Senq77779fw4YNU0JCgr7++us8LXTarVs3Va9eXe+++65mzZqlCxcuqHz58goKCjINRrJ0795dnp6eWrRokYYPH64SJUqoWbNmeu6553K19lReDB06VJmZmVq9erWWLVumhx9+WGPHjlVMTIzpd1ejRg1NmzZNCxcu1DPPPCNfX19FR0crOjr6rsf/z3/+Y9bWx8dHU6ZM0YYNG7R3715JtwpeAwYM0OrVq7V9+3Z9//332Y4zdepUVa9eXR9//LGWLVumChUqqE+fPho+fLhVBp8AgNtbv369/Pz8bvuP33r16umBBx7Qxx9/rGrVqt31i0D+6f7779f69eu1aNEibdy4UUuWLJHRaFT16tU1aNAg9e3bN9vt94899pimT5+ep3/UW4OLi4sWL16s2bNna9GiRbpw4YIqVqyo/v37m32TW+/evfXzzz9ryJAhiomJUYUKFe543JYtW2r16tVWz2/BggWaOnWqoqKiVLRoUdWsWVMLFy7U66+/rr179+b6tsTmzZsrNjZWb7/9tmkB8AYNGmjFihVma3D+U/ny5fXBBx9o+vTpmjp1qm7cuKFatWppwYIFeVpSIS9yM84rUqSInn32WS1YsEBDhgzR5s2bsx0nMjJS3t7eWrlypdasWSMvLy89/vjjGj16dK7WAsvSq1cv3bhxQx9++KHef/99ubu765FHHtG4cePy9BkCYM5gzMvqugDw/3322WeqXbu22TpD27dv19ChQ/Xpp5/yV6DbyMjI0GeffaaQkBCzvwCvWrVKr732mnbt2qVSpUrZMUIAAO5uyZIlWrp0qXbu3Gm6TduRvPLKK9q3b1+227MAAHfHjCYA+bJhwwbNnDlTo0ePVuXKlXXixAnNmTNHjRo1osh0B66urlqyZIneffddDRs2TGXKlFF8fLxmz56tsLAwikwAgAJt/fr1+uWXX/T+++/rmWeecbgi04oVK/Trr79q9erVVlsEHAAcHTOaAOTLxYsXNX36dH377bdKSkqSt7e32rdvr5EjR+ZqHSFndurUKc2YMUO7du3SlStXdN9996lLly4aOnQo07QBAAVaTEyM6dvtYmJiHK7QNHLkSO3cuVNPPPGEXnzxRXuHAwCFEoUmAAAAAAAAWIRDrOaalJSktm3bateuXbdtM3jwYNWtW1dBQUGmR9ZXhwIAABRWKSkpmjBhgkJCQtSgQQONHz9eycnJt20/efJk1alTx2xMtHr1ahtGDACAc8lNzWLHjh0KDQ1VYGCgOnTooG+++cZs/5IlS9S8eXMFBgaqT58++vXXX60ddr4V+kLTvn371LNnT/3+++93bPfzzz9r2bJliouLMz3+/TWtAAAAhU10dLTOnDmjrVu3atu2bTpz5oxiY2Nv2/7QoUOKjo42GxP17NnThhEDAOA8clOzOHHihCIjIzVq1Cjt3btXkZGRGj16tM6dOyfp1vp47733npYtW6Zdu3bpoYce0siRI1VQb1Ar1IWm9evXa+zYsRozZswd2506dUqXL19W7dq1bRQZAACA9aWmpmrjxo0aOXKkvLy8VK5cOY0dO1br1q1Tampqtvbp6en65ZdfVKdOHTtECwCAc8ltzWL9+vUKDg5WmzZt5Orqqo4dO6phw4amGccfffSRnnrqKfn5+alYsWJ6/vnndfr06TvOkLKnQl1oatq0qb744gt17Njxju0OHTokT09PjRkzRo0bN1bnzp21du1aG0UJAACQf2lpaTp58uRtHzdu3JC/v7+pva+vr9LS0nTixIlsx4qPj1dGRobmzJmjRx99VO3bt9fixYuVmZlpw4wAAHAOua1ZJCQkmF3LJalmzZqKj4/Pcb+bm5t8fHxM+wsaV3sHcC/Kly+fq3bp6ekKDAzUmDFj5Ofnp127dikyMlKenp7q0KFDrvszGo0yGAz5DRcAACDPDh48qL59++a4b9SoUZKk4sWLm7Z5eHhIUo7rNF29elWNGjVSnz59NGPGDB09elTDhw+Xi4uLBg8enOuYGBMBACztWYOP3jKesHo/RYMG5vu16XFv56l9bmsWycnJput3Fnd3d6WkpORqf0FTqAtNuRUWFqawsDDT86ZNmyosLExbtmzJU6EpKSlZLi53HlQVKeKiUqU8dOVKqm7edI6/DpIzOTsqciZnR0XOd8+5TBlPG0SVOyEhITp27FiO+44cOaLZs2crNTVVnp63Ys66Za5EiRLZ2jdp0kRNmjQxPa9Xr5769eunzZs356nQZDAY1GfCIh07cSYvqRQ4AT6V9V7MUIfKZdlTI3U2PtHe4dyTSrV8Nej9OYU+l6w8HOn95Ui5FPb3l+Q4nxXpVi7OzMPDQ2lpaWbb0tLSTNf2u+0vaJyi0LR27dpss5fS09NVrFixPB0nM9OozMzcLbZ182amMjKcY/CehZydAzk7B3J2DuRc+NWoUUNubm5KSEjQww8/LElKTEw0Tan/ty+//FLnz59Xr169TNvS09Pl7u6e576PnTijA/F3/jKWwsKRcjkbn6hTcYftHYZFOEoujvT+cqRcHOX9JTlWLrZgcCli7xCy8ff31+HD5ucwISHBtKain5+fjh8/rpYtW0qSbty4oRMnTmS73a6gKNRrNOXWtWvXFB0drSNHjigzM1Pbt2/XZ599xjesAACAQs3Dw0MdOnRQbGyskpKSlJSUpNjYWHXu3DnH4pHRaFRMTIx++OEHGY1GxcXFacWKFYyJAABOw+BSJN8Pa+nSpYt2796tzZs3KyMjQ5s3b9bu3bvVtWtXSVKPHj20cuVKxcfH6/r165o+fbq8vb0VHBxstZjuhcPOaAoKCtKUKVPUpUsX9evXTykpKRoxYoQuXLigqlWratq0aQX2pAAAAOTW5MmTNW3aNIWGhurGjRtq3bq1Xn75ZdP+Tp06KTQ0VM8++6zatm2rCRMm6JVXXtG5c+fk7e2tyMhI00AWAABHV1BmNP2zZuHr66v58+crNjZWEydOVJUqVTR37lzVqFFDkhQeHq6rV69q+PDhSkpKUt26dbVo0SK5ubnZOYucOUyh6d9rF8TFxZl+NhgMioiIUEREhK3DAgAAsKoSJUooOjpa0dHROe7ftGmT2fNevXqZ3ToHAIAzsVeh6U41C0lq1qyZmjVrluNrDQaDBg4cqIED87+QuS05xa1zAAAAAAAAsD6HmdEEAAAAAABwJ4YiBePWOUdGoQkAAAAAADgFlwKyRpMjo9AEAAAAAACcQkFZDNyRUWgCAAAAAABOgUKT9bEYOAAAAAAAACyCGU0AAAAAAMApGFyYb2NtFJoAAAAAAIBT4NY566PQBAAAAAAAnAKFJuuj0AQAAAAAAJwChSbro9AEAAAAAACcgqEIhSZrYxUsAAAAAAAAWAQzmgAAAAAAgFPg1jnro9AEAAAAAACcAoUm66PQBAAAAAAAnIILhSaro9AEAAAAAACcAjOarI/FwAEAAAAAAGARzGgCAAAAAABOgRlN1kehCQAAAAAAOAUKTdZHoQkAAAAAADgFCk3WR6EJAAAAAAA4BQpN1sdi4AAAAAAAALAIZjQBAAAAAACnYCjCjCZro9AEAAAAAACcArfOWR+FJgAAAAAA4BQoNFkfhSYAAAAAAOAUKDRZH4UmAAAAAADgFFxcDPYOweHxrXMAAAAAAACwCGY0AQAAAAAAp2BgRpPVUWgCAAAo5C5cuKCXX35Zu3fvVpEiRdSlSxe98MILcnXNPtTbsWOHYmNjderUKVWuXFnjx49Xy5Yt7RA1AAC2ZzDYrtCU2+vz4MGDtW/fPrNtKSkp6tmzp1599VVlZmaqQYMGMhqNZvF///33Kl68uE1yyQsKTQAAAIXc6NGjVbFiRe3cuVPnz5/XsGHDtHz5cg0ePNis3YkTJxQZGakZM2boscce07Zt2zR69Ght27ZNFStWtFP0AADYji3XaMrt9Xnp0qVmz9euXat58+ZpxIgRkqSEhATduHFD+/fvV9GiRW0Wf36xRhMAAEAhdvLkSe3evVvjxo2Th4eHqlatqoiICK1atSpb2/Xr1ys4OFht2rSRq6urOnbsqIYNG2r16tV2iBwAANszuBjy/ciLvFyf/+nXX39VdHS0YmNjVaFCBUnSoUOHFBAQUCiKTBKFJgAAgELt+PHj8vLyMpuR5Ovrq9OnT+vKlStmbRMSEuTv72+2rWbNmoqPj7dJrAAAOIu8XJ//acqUKQoLC1NwcLBp26FDh3T9+nX16NFDjRs3Vu/evbV//36rxn8vuHUOAACgEEtOTpaHh4fZtqznKSkpKlWq1B3buru7KyUlJc/9BvhUzke0BUtWDo6US6VavnaO5N5l5VDYc8mK35HeX46US2F/f0mO81mRbuVwKu6wTfqy1WLgebk+Z9m7d68OHjyo2NhYs+3u7u6qV6+eRo0apdKlS2vVqlUaNGiQNmzYoKpVq1oviXyi0AQAAFCIFS9eXKmpqWbbsp57enqabffw8FBaWprZtrS0tGztcuO9mKF5fk1B5Ui5DHp/jr1DsBhHycWR3l+OlIujvL8kx8llzwcbbNKPi40WA8/L9TnL6tWr1aFDB5UvX95se1RUlNnzQYMGad26ddqxY4eefvppC0ZtGRSaAAAACjE/Pz9dunRJ58+fl7e3tyQpMTFRlSpVUsmSJc3a+vv76/Bh878YJyQkqE6dOnnut8+ERTp24kz+Ay8AAnwq672YoQ6Vy7KnRupsfKK9w7knlWr5atD7cwp9Lll5ONL7y5FyKezvL8lxPiuSbWdl2WpGU16uz5KUkZGhr776SvPnz8+2b+bMmWrfvr1q165t2paenq5ixYpZL4F7QKEJAACgEPPx8VGDBg30+uuv69VXX9XFixe1YMEChYeHZ2vbpUsXvfPOO9q8ebPatWunbdu2affu3Zo4cWKe+z124owOxP9uiRTszpFyORufaLPbT6zNUXJxpPeXI+XiKO8vybFysQVbFZrycn2WpGPHjun69euqX79+tn2//PKL9u7dq1mzZql06dJavHixrl27prZt21o7jXxhMXAAAIBCbs6cOcrIyFDr1q315JNPqlmzZoqIiJAkBQUFacOGW7cj+Pr6av78+Vq0aJEaNmyoBQsWaO7cuapRo4Y9wwcAwCHl9vosSadOnVLp0qVznKUUExOjatWqqWvXrgoJCdHu3bv1zjvvyMvLy1ap5AkzmgAAAAo5b29vzZmT8xodcXFxZs+bNWumZs2a2SIsAAAKHBcbzWiS8nZ9fvzxx/X444/n2NbLy0sxMTEWj89aKDQBAAAAAACnYOC+Lquj0AQAAAAAAJyCwUbfOufMKDQBAAAAAACnYMtb55wVk8YAAAAAAABgEQ5RaEpKSlLbtm21a9eu27bZsWOHQkNDFRgYqA4dOuibb76xYYQAAAAAAMDeDC6GfD+QO4W+0LRv3z717NlTv//++23bnDhxQpGRkRo1apT27t2ryMhIjR49WufOnbNhpAAAAAAAwJ4oNFlfoS40rV+/XmPHjtWYMWPu2i44OFht2rSRq6urOnbsqIYNG2r16tU2ihQAAAAAANibi8GQ7wdyp1AvBt60aVOFhobK1dX1jsWmhIQE+fv7m22rWbOm4uPj89Sfi4vhrguHFSniYvZfZ0DOzoGcnQM5OwdyBgAAzoqZSdZXqAtN5cuXz1W75ORkeXh4mG1zd3dXSkpKnvorW9Yz11+FWKqUx90b/YNjFEfzlnNBYzTm/TV5Pc+OgJzvzDE+yxKfZ+dAzgAAwNlQaLK+Ql1oyi0PDw+lpaWZbUtLS5Onp2eejpOUlJyrGU2lSnnoypVU3byZmYej5y0WWN7Fi8m5bpv/81x4kXNuc+azXBDweb4zcr57zmXK8FkGAADID6coNPn7++vw4cNm2xISElSnTp08HScz06jMzNz9mfzmzUxlZDjH4N1R5Od8OeN5JmcUBnyec4ecAQCAs7nb5BHcO6dYqKBLly7avXu3Nm/erIyMDG3evFm7d+9W165d7R0aAAAAAACwEYPBkO8HcsdhC01BQUHasGGDJMnX11fz58/XokWL1LBhQy1YsEBz585VjRo17BwlAAAAAACwFYNL/h/IHYe5de7YsWNmz+Pi4syeN2vWTM2aNbNlSAAAAAAAoADh1jnroyYHAAAAAAAAi3CYGU0AAAAAAAB3YmBGk9VRaAIAAAAAAE6BRb2tj0ITAAAAAABwCqzRZH0UmgAAAAAAgFPg1jnrYzFwAAAAAAAAWAQzmgAAAAAAgFMowowmq6PQBAAAAAAAnAKFJuuj0AQAAAAAAJwChSbro9AEAAAAAACcAoUm66PQBAAAAAAAnAKFJuvjW+cAAAAAAABgEcxoAgAAAAAATsGVGU1WR6EJAAAAAAA4BW6dsz4KTQAAAAAAwClQaLI+Ck0AAAAAAMApFHFhqWpr4zcMAABQyF24cEEREREKDg5WSEiIpk6dqoyMjBzbDh48WHXr1lVQUJDp8e2339o4YgAAHJ8lr89LlixR8+bNFRgYqD59+ujXX3+1VRp5xowmAACAQm706NGqWLGidu7cqfPnz2vYsGFavny5Bg8enK3tzz//rGXLlqlRo0Z2iBQAAPuy5a1zlro+r1+/Xu+9956WLVumatWqaebMmRo5cqQ2btwog6Hg3QrIjCYAAIBC7OTJk9q9e7fGjRsnDw8PVa1aVREREVq1alW2tqdOndLly5dVu3ZtO0QKAID9FXEx5PuRF5a8Pn/00Ud66qmn5Ofnp2LFiun555/X6dOntWvXrnz9DqyNGU0AAACF2PHjx+Xl5aWKFSuatvn6+ur06dO6cuWKSpUqZdp+6NAheXp6asyYMTp06JC8vb3Vv39/hYeH57nfAJ/KFonfnrJycKRcKtXytXMk9y4rh8KeS1b8jvT+cqRcCvv7S3Kcz4p0K4dTcYdt0petZjRZ8vqckJCgIUOGmNq7ubnJx8dH8fHxaty4sU3yyQsKTQAAAIVYcnKyPDw8zLZlPU9JSTEbyKanpyswMFBjxoyRn5+fdu3apcjISHl6eqpDhw556ve9mKH3HnwB4Ui5DHp/jr1DsBhHycWR3l+OlIujvL8kx8ll2NFressG/RSx0a1mlrw+53Qsd3d3paSkWD+RfKDQBAAAUIgVL15cqampZtuynnt6epptDwsLU1hYmOl506ZNFRYWpi1btuS50LTsqZE6G5+Yv6ALiEq1fDXo/TkOlUufCYt07MQZe4dzTwJ8Kuu9mKGFPpesPBzp/eVIuRT295fkOJ8VyTFmy/2bJa/PHh4eSktLM3tNWlpatuMUFBSaAAAACjE/Pz9dunRJ58+fl7e3tyQpMTFRlSpVUsmSJc3arl27NtvspfT0dBUrVizP/Z6NT7TZbQ7W5ki5HDtxRgfif7d3GBbhKLk40vvLkXJxlPeX5Fi52IKtbp2z5PXZz89Px48fV8uWLSVJN27c0IkTJ+Tv72+TXPKKxcABAAAKMR8fHzVo0ECvv/66rl27plOnTmnBggU5rrt07do1RUdH68iRI8rMzNT27dv12WefqWfPnnaIHAAA27PVYuCWvD736NFDK1euVHx8vK5fv67p06fL29tbwcHBFvmdWBozmgAAAAq5OXPm6NVXX1Xr1q3l4uKisLAwRURESJKCgoI0ZcoUdenSRf369VNKSopGjBihCxcuqGrVqpo2bVqBHagCAGBprjaa0SRZ7vocHh6uq1evavjw4UpKSlLdunW1aNEiubm52SyXvKDQBAAAUMh5e3trzpycF4ONi4sz/WwwGBQREWEa5AIA4GxsdeucZLnrs8Fg0MCBAzVw4ECrxGlp3DoHAAAAAAAAi2BGEwAAAAAAcAq2nNHkrCg0AQAAAAAAp0ChyfooNAEAAAAAAKdAocn6KDQBAAAAAACnQKHJ+ig0AQAAWMGFCxc0c+ZM7du3Tzdu3JDRaDTb/9VXX9kpMgAAnBeFJuuj0AQAAGAFkyZN0t69exUWFqaSJUvaOxwAAACboNAEAABgBd9//73mz5+vJk2a2DsUAADw/zGjyfooNAEAAFhB8eLFVblyZXuHAQAA/oFCk/W52DsAAAAARxQWFqZly5bp5s2b9g4FAAD8f0VcDPl+IHeY0QQAAGAF58+f15YtW/TNN9+oWrVqKlq0qNn+FStW2CkyAACcFwUj66PQBAAAYAVFihRR586d7R0GAACATVFoAgAAsIKYmBh7hwAAAP6liIEZTdZGoQkAAMBKzp49q1WrVunYsWNydXWVn5+fevbsqfvuu8/eoQEA4JRcKDRZHYuBAwAAWMEvv/yiLl266NNPP1XRokVlNBq1bt06denSRcePH7d3eAAAOKUihvw/kDvMaAIAALCCN954Q40bN1ZsbKxpIfDr169r3Lhxio2N1aJFi+wcIQAAzseFxcCtrlDPaLpw4YIiIiIUHByskJAQTZ06VRkZGTm2HTx4sOrWraugoCDT49tvv7VxxAAAwFns27dPI0aMMPu2uWLFiikiIkL79u2zY2QAAADWU6hnNI0ePVoVK1bUzp07df78eQ0bNkzLly/X4MGDs7X9+eeftWzZMjVq1MgOkQIAAGfj6emp9PT0bNtz2gYAAGyDxcCtr9DOaDp58qR2796tcePGycPDQ1WrVlVERIRWrVqVre2pU6d0+fJl1a5d2w6RAgAAZ9S4cWO98cYbunTpkmlbUlKSYmNj1bhxY/sFBgCAE3MxGPL9QO4U2hlNx48fl5eXlypWrGja5uvrq9OnT+vKlSsqVaqUafuhQ4fk6empMWPG6NChQ/L29lb//v0VHh5uj9ABAIATGDt2rHr16qWWLVvKx8dHBoNBv/32m0qVKqWVK1faOzwAAJwSi3pbX6EtNCUnJ8vDw8NsW9bzlJQUs0JTenq6AgMDNWbMGPn5+WnXrl2KjIyUp6enOnTokOs+XVwMd104rEgRF7P/ovBwdc39OXPG80zOKEz4PN8ZOdtGpUqVtGnTJn366ac6fvy4jEajwsPDFRoaqpIlS9osDgAA8D8sBm59hbbQVLx4caWmpppty3ru6elptj0sLExhYWGm502bNlVYWJi2bNmSp0JT2bKeMuRyulypUh53b4QCpUwZz7s3+hdnPM/kjMKAz3PukLP1eXp66qmnnrJpnwAAAPZUaAtNfn5+unTpks6fPy9vb29JUmJioipVqpTtr4Rr167NNnspPT1dxYoVy1OfSUnJuZrRVKqUh65cSdXNm5l5OHre/1EEy7p4MTnXbfN/ngsvcs5tznyWCwI+z3dGznfPOT/FSklq3bq11q5dqzJlyqhVq1Z3/APVV199la8+AABA/rHWkvUV2kKTj4+PGjRooNdff12vvvqqLl68qAULFuS47tK1a9c0Y8YMVa9eXbVq1dK3336rzz77TMuWLctTn5mZRmVmGnPV9ubNTGVkOMfg3VHk53w543kmZxQGfJ5zh5wtr1u3bnJ3d5ckde/e3Wr9AACA/GGNJusrtIUmSZozZ45effVVtW7dWi4uLgoLC1NERIQkKSgoSFOmTFGXLl3Ur18/paSkaMSIEbpw4YKqVq2qadOmKTg42M4ZAAAARzJixAjTzyEhIQoMDJSbm5tZm+vXr2v79u02jgwAAEjMaLKFQl1o8vb21pw5c3LcFxcXZ/rZYDAoIiLCVIQCAACwtr59++r7779X2bJlzbYnJCRo3Lhxat++vZ0iAwDAeRVhMXCrK9SFJgAAgIJk+fLlmjZtmiTJaDSqSZMmObarV6+eLcMCAAD/HzOarI9CEwAAgIU8/fTT8vLyUmZmpl588UVNmDDB7EtKDAaDihcvrsaNG9sxSgAAAOuh0AQAAGAhrq6uCgsLk3SrqNSpUycVLVrUvkEBAAATFgO3PgpNAAAAVtCtWzclJSXpt99+U2bmrW+6MxqNSk9P18GDBzV8+HCL95mUlKSePXvqtddeU0hISI5tduzYodjYWJ06dUqVK1fW+PHj1bJlS4vHAgBAQWTLW+cuXLigl19+Wbt371aRIkXUpUsXvfDCC3J1zV6K+eCDD7R8+XL99ddfqlChgvr27avevXtLkjIzM9WgQQMZjUYZ/hH/999/r+LFi9ssn9yi0AQAAGAFmzZt0osvvqjr16/LYDCYDQ6rVKli8ULTvn37FBUVpd9///22bU6cOKHIyEjNmDFDjz32mLZt26bRo0dr27ZtqlixokXjAQCgILLlYuCjR49WxYoVtXPnTp0/f17Dhg3T8uXLNXjwYLN2X375pWbMmKElS5bo4Ycf1oEDB/TMM8/I29tb7du3V0JCgm7cuKH9+/cXipnSLvYOAAAAwBG99dZb6ty5sz7//HOVLFlSa9eu1fz581WhQgVFRkZatK/169dr7NixGjNmzF3bBQcHq02bNnJ1dVXHjh3VsGFDrV692qLxAABQULkY8v/Ii5MnT2r37t0aN26cPDw8VLVqVUVERGjVqlXZ2p47d05DhgxRYGCgDAaDgoKCFBISoj179kiSDh06pICAgEJRZJIoNAEAAFjFiRMnNGjQIPn4+OjBBx9UUlKSWrVqpYkTJ+rdd9+1aF9NmzbVF198oY4dO96xXUJCgvz9/c221axZU/Hx8RaNBwAAZ3f8+HF5eXmZzRj29fXV6dOndeXKFbO2vXv31jPPPGN6fuHCBe3Zs0d16tSRdKvQdP36dfXo0UONGzdW7969tX//ftskkg/cOgcAAGAFxYoVk5ubmyTJx8dHx48fV/PmzVWnTh2dPHnSon2VL18+V+2Sk5Pl4eFhts3d3V0pKSl57rNSLd88v6agycrBkXIJ8Kls50juXVYOhT2XrPgd6f3lSLkU9veX5DifFelWDgfib3/rtyUVsdEaTTldc7Oep6SkqFSpUjm+7u+//9bQoUNVp04dde7cWdKta3W9evU0atQolS5dWqtWrdKgQYO0YcMGVa1a1bqJ5AOFJgAAACuoV6+ePvzwQ40bN041a9bUN998o0GDBikhIcFUgLI1Dw8PpaWlmW1LS0uTp6dnno816P05lgrL7hwpl/dihto7BItxlFwc6f3lSLk4yvtLcpxcVn++yyb92Gox8OLFiys1NdVsW9bz2113Dxw4oFGjRik4OFgxMTGmRcOjoqLM2g0aNEjr1q3Tjh079PTTT1sh+ntDoQkAAMAKhg8frkGDBqls2bLq3r275s2bp06dOunMmTN3vcXNWvz9/XX48GGzbQkJCaap+Xmx7KmROhufaKnQ7KJSLV8Nen+OQ+XSZ8IiHTtxxt7h3JMAn8p6L2Zooc8lKw9Hen85Ui6F/f0lOc5nRbLtrKwiNlpAyM/PT5cuXdL58+fl7e0tSUpMTFSlSpVUsmTJbO3Xrl2r1157TSNHjtTAgQPN9s2cOVPt27dX7dq1TdvS09NVrFgx6yaRTxSaAAAArKB+/fraunWr0tPTVaZMGX3wwQd6//33VblyZfXp08cuMXXp0kXvvPOONm/erHbt2mnbtm3avXu3Jk6cmOdjnY1P1Km4w3dvWAg4Ui7HTpyx2e0n1uYouTjS+8uRcnGU95fkWLnYgq1mNPn4+KhBgwZ6/fXX9eqrr+rixYtasGCBwsPDs7XdunWrXnnlFS1cuFDNmjXLtv+XX37R3r17NWvWLJUuXVqLFy/WtWvX1LZtW1ukkmcsBg4AAGAFPXr00MWLF01rJzzwwAN66aWXNGjQIJt+a0xQUJA2bNgg6dYipPPnz9eiRYvUsGFDLViwQHPnzlWNGjVsFg8AAM5izpw5ysjIUOvWrfXkk0+qWbNmioiIkGR+fZ43b55u3rypkSNHKigoyPSYNGmSJCkmJkbVqlVT165dFRISot27d+udd96Rl5eXvVK7I6vNaDp9+rQSExPVsGFDJScnq1y5ctbqCgAAoMD5888/Vbx4cZv3e+zYMbPncXFxZs+bNWuW419LAQBwBrZaDFySvL29NWdOzmub/fP6vHHjxjsex8vLSzExMRaN7d8sWcOxeKEpPT1dL7zwgrZs2SIXFxdt3bpV06ZN09WrVzVv3rwc70UEAABwNEOGDNHEiRM1aNAgVatWTe7u7mb777vvPjtFBgCA87LVrXOFhTVqOBa/dW7hwoWKj4/Xu+++a1qYqm/fvvrzzz/15ptvWro7AACAAik2NlZ79uzRs88+q06dOql169Zq3bq1WrVqpdatW9s7PAAAnFIRl/w/HJE1ajgWn9G0adMmvfLKKwoJCTFta9SokaKjozVu3Di9+uqrlu4SAACgwFmxYoW9QwAAAP/CjCZz1qjhWLzQdO7cOVWrVi3b9sqVK+vKlSuW7g4AAKBAatSokenn9PR0my4ADgAAckadyZw1ajgWn/zl6+ur//73v9m2f/bZZ6pZs6aluwMAACiwPvjgA7Vq1UqBgYE6deqUJk+erHnz5tk7LAAAAEnWqeFYfEZTZGSkRo8erV9++UU3b97U+vXr9euvv2rbtm2aOXOmpbsDAAAokDZu3Kjp06erX79+Wrp0qaRbg7nY2FgVK1ZMQ4YMsXOEAAA4HxcxpemfrFHDsfiMppYtW2ru3Lk6evSoihQpomXLlumPP/7QzJkz1b59e0t3BwAAUCC9/fbbmjhxoiIjI+XicmvI1bdvX02ZMkVr1qyxc3QAADgngyH/D0dkjRqOxWc0SVLz5s3VvHlzaxwaAACgUPjtt98UHBycbXtwcLDOnj1rh4gAAICLgxaM7oWlazgWLzR98sknd9wfFhZm6S4BAAAKHG9vb/3666+qWrWq2fb9+/erQoUKdooKAADn5qgzk/LLGjUcixeaoqKictxerFgxVapUiUITAABwCj179tSUKVNMY6Nff/1VO3fu1OzZs9W/f3/7BgcAACDr1HAsXmiKj483e56RkaGTJ09q0qRJ6t27t6W7AwAAKJCGDBmiq1evaty4cbp+/bqGDh0qV1dX9erVS0OHDrV3eAAAOCUWAzdnjRqOVdZoMuvA1VW+vr6KiorS2LFj1bFjR2t3CQAAUCA899xzGjZsmBISEmQ0GvXAAw+oRIkS9g4LAACnxa1zd2aJGo7VC01ZihcvrjNnztiqOwAAALu7du2aNm/erF9++UUuLi566KGH9Pjjj6tYsWL2Dg0AAKfEYuC5cy81HIsXmvbs2WP23Gg06urVq1q+fLlq1apl6e4AAAAKpMTERPXr10/Jycny8fFRZmamPvroIy1YsEDvvvuuKlWqZO8QAQBwOtSZzFmjhmPxQlOfPn1kMBhkNBrNtletWlWxsbGW7g4AAKBAeu211/Tggw8qNjZWpUuXliQlJSVp7Nixeu211zRv3jw7RwgAAJydNWo4Fi80ffXVV9m2ubm58TW+AADAqRw4cEAfffSRqcgkSWXLltX48eP11FNP2TEyAACclwuLNJmxRg3H4oWmKlWqWPqQAAAAhU6FChV09uxZ+fn5mW2/du2aSpUqZaeoAABwbtSZzFmjhmORQlPfvn1z3XbFihWW6BIAAKBAe+GFFzRlyhRFRUWpUaNGcnV11aFDhzRlyhT169dPp0+fNrW977777BgpAADOw8XeARQA1q7hWKTQxCwmAAAAcxEREZKkESNGyPCPP58ajUZNmzZNb7zxhoxGowwGg44ePWqvMAEAcCoGpjRZvYZjkUJTTEyMJQ4DAADgMN59910GswAAoMCxdg3H4ms0Sbe+UeW3335TZmampFt/uUtPT9fBgwc1fPhwa3QJAABQoISEhOjmzZu6dOmSvLy8VKRIEXuHBACA03Phb0DZWLqGY/FC06ZNm/Tiiy/q+vXrpq/Iy/prXpUqVSg0AQAAh7dlyxa9++67+umnn0xjoTp16qhv377q3LmzvcMDAMBpMdnYnDVqOBZfB+utt95S586d9fnnn6tkyZJau3at5s+frwoVKigyMtLS3QEAABQor7/+usaMGSMPDw+NHj1ar776qp577jmVLl1a48aN0yuvvGLvEAEAcFou9/BwRNao4Vh8RtOJEyc0e/Zs+fj46MEHH1RSUpJatWqljIwMvfXWW+ratauluwQAACgQvv76a3344YdavHixmjdvbrZv8ODB+u677zRixAg1bdpUbdq0sVOUAAA4L9ZPNGeNGo7Fi3LFihWTm5ubJMnHx0fHjx+XJNWpU0cnT560dHcAAAAFxsqVK/Xss89mKzJladq0qZ599lm9//77No4MAABIt9Zoyu/DEVmjhmPxQlO9evX04YcfSpJq1qyp77//XpKUkJBgCh4AAMARHTlyRG3btr1jm7Zt2+rIkSM2iggAAOD2rFHDsfitc8OHD9egQYNUtmxZde/eXfPmzVOnTp105swZdezY0dLdAQAAFBhpaWkqXrz4HdsUK1ZM6enpNooIAAD8k4NOTMo3a9RwLFJomjBhgsLDw9WgQQM1aNBAW7duVXp6usqUKaP3339fH3zwgSpXrqw+ffpYojsAAIACqVq1atq/f7+qVKly2zb79u2Tj4+P7YICAAAmjnoLXF5Yu4ZjkULTgQMH9Mknn6h69ep64okn1K1bN1WsWFGS5Ovrq5deeskS3QAAABRonTp10pw5c9S8eXOVLl062/6///5bc+bMUd++fe0QHQAAYDFw69dwLLJG05YtW7R69Wo98sgjWrJkiZo3b67IyEjt2LFDRqPREl0AAAAUeP3791fJkiXVrVs3rVixQocOHdKpU6d09OhRLV++XN27d1elSpXUu3dve4cKAIBTYjFw69dwLLYYeL169TR58mTt3LlTM2bMUGZmpoYPH66WLVtq9uzZ+uOPPyzVlcmFCxcUERGh4OBghYSEaOrUqcrIyMix7Y4dOxQaGqrAwEB16NBB33zzjcXjAQAAzq1YsWJasWKFQkJC9MYbb+jJJ59Uu3bt1L17d02fPl2tW7fWkiVL5Opq8WUyJUlJSUlq27atdu3adds2gwcPVt26dRUUFGR6fPvtt1aJBwAAZ2bJmkVWQSgwMFB9+vTRr7/+ek+xWbOGY/FRjpubm9q1a6d27dopKSlJmzZt0qZNm7R48WKFhITo7bfftlhfo0ePVsWKFbVz506dP39ew4YN0/LlyzV48GCzdidOnFBkZKRmzJihxx57TNu2bdPo0aO1bds20/QwAAAASyhRooRiYmI0YcIE/fTTT7p06ZK8vLxUr149lSpVymr97tu3T1FRUfr999/v2O7nn3/WsmXL1KhRI6vFAgBAQWXLiUmWqlmsX79e7733npYtW6Zq1app5syZGjlypDZu3HjPtwJao4ZjsRlNOSlbtqy6dOmi8PBwBQQE6Mcff7TYsU+ePKndu3dr3Lhx8vDwUNWqVRUREaFVq1Zla7t+/XoFBwerTZs2cnV1VceOHdWwYUOtXr3aYvEAAAD8U6lSpdS0aVN17txZTZs2tWqRaf369Ro7dqzGjBlzx3anTp3S5cuXVbt2bavFAgBAQeZiMOT7kReWrFl89NFHeuqpp+Tn56dixYrp+eef1+nTp+84gzk/LFXDscq87fT0dH311VfauHGjdu7cqXLlyqlbt26aPXu2xfo4fvy4vLy8zGYk+fr66vTp07py5YrZYC4hIUH+/v5mr69Zs6bi4+Pz1KeLi0Eud7kxs0gRF7P/ovBwdc39OXPG80zOKEz4PN8ZOTuepk2bKjQ0VK6urncsNh06dEienp4aM2aMDh06JG9vb/Xv31/h4eF57rNSLd97CblAyMrBkXIJ8Kls50juXVYOhT2XrPgd6f3lSLkU9veX5DifFelWDgfi7zwj11JstRa4JWsWCQkJGjJkiGmfm5ubfHx8FB8fr8aNG99zrJau4Vis0GQ0GvXDDz9o48aN+uKLL5SWlqZWrVpp3rx5atasmVxcLDuwS05OloeHh9m2rOcpKSlmJy2ntu7u7kpJSclTn2XLeuZ6WlqpUh53b/QPrJleEHjm+RV5Os8O8u0G1vt7vI3k48OWl/PMZ7mg4POcG4X685zPD1ter8+FRfny5XPVLj09XYGBgRozZoz8/Py0a9cuRUZGytPTUx06dMhTn4Pen5OfUAskR8rlvZih9g7BYhwlF0d6fzlSLo7y/pIcJxfP/8TYpB+DjQbslqxZWKqm8U/WrOFYpNAUExOjTZs26cKFC/L19dXw4cPVtWtXlS1b1hKHz1Hx4sWVmppqti3ruaen+T8wPDw8lJaWZrYtLS0tW7u7SUpKztWMplKlPHTlSqpu3szM0/ELK3LOXc5lrBwTcufixeRct+W9Tc63w+fZ/vLyWZbyfp7LlMl7sbIwCAsLU1hYmOl506ZNFRYWpi1btuS50NRnwiIdO3HGwhHaVoBPZb0XM9Shcln21EidjU+0dzj3pFItXw16f06hzyUrD0d6fzlSLoX9/SU5zmdFcozZcv9myZqFpWoaWaxdw7FIoWnNmjXq2LGjwsPDFRgYaIlD3pWfn58uXbqk8+fPy9vbW5KUmJioSpUqqWTJkmZt/f39dfjwYbNtCQkJqlOnTp76zMw0KjMzd9XPmzczlZHhHP9Iy0LOKAzyc76c8TyTMwq6/J4rW57nb775Ri1atLD4rO57sXbt2myzl9LT01WsWLE8H+vYiTM2u83B2hwpl7PxiToVd/juDQsBR8nFkd5fjpSLo7y/JMfKxSaMthkHWLJm4efnp+PHj6tly5aSpBs3bujEiRPZbrfLLWvXcCwy8vn+++/12muv2azIJEk+Pj5q0KCBXn/9dV27dk2nTp3SggULclxjoEuXLtq9e7c2b96sjIwMbd68Wbt371bXrl1tFi8AAHAuo0ePVvPmzfXmm28qMbFg/KX52rVrio6O1pEjR5SZmant27frs88+U8+ePe0dGgAANmEwZub7kReWrFn06NFDK1euVHx8vK5fv67p06fL29tbwcHB+fodWLuGY5FC07/vFbSVOXPmKCMjQ61bt9aTTz6pZs2aKSIiQpIUFBSkDRs2SLq14Nb8+fO1aNEiNWzYUAsWLNDcuXNVo0YNu8QNAAAc3/fff6/IyEjt379fnTp10pNPPqkPP/xQ165ds2kc/xwT9evXT08//bRGjBihoKAgxcbGatq0afkeqAIAUOgYM/P/yCNL1SzCw8PVv39/DR8+XI0bN9aRI0e0aNEiubm55etXYO0ajlW+dc5WvL29NWdOzgvSxcXFmT1v1qyZmjVrZouwAAAAVKJECfXs2VM9e/bUyZMntXHjRr3//vv6z3/+ozZt2ig8PNwi3xTzb8eOHTN7/s8xkcFgUEREhGmQCwCA07Hht/dYqmZhMBg0cOBADRw40OIxWkPBWTQAAADAQd13330KCAiQn5+fJGnfvn0aPny4QkNDTV9dDAAAbMCGM5qcFYUmAAAAK9m/f78mT56spk2baty4cTIYDFq4cKG++eYb7dy5U76+vho9erS9wwQAALAYq9w6t2/fPu3bt083btyQ8V/T0kaMGGGNLgEAAAqUtm3b6o8//lDt2rU1atQohYaGmn3LTPHixdWhQwd9//33dowSAADnktdFvZ2BpWs4Fi80LV68WDNmzFDp0qXl6elpts9gMFBoAgAATqFly5YKDw+/41cPP/LII9q6dasNowIAwMlRaDJjjRqOxQtNK1eu1LBhwzRq1ChLHxoAAKDQ+Oqrr0xfS3w7pUqVslE0AABAEoWmf7FGDcfihabLly8rLCzM0ocFAAAoVK5fv271rw8GAAB5RKHJjDVqOBYvNDVo0ECHDh1S9erVLX1oAACAQqN3794aMWKEevfurWrVqsnd3d1sf8OGDe0UGQAAwC3WqOFYvNDUoUMHvfrqq/r555/1wAMPqGjRomb7me0EAACcwezZsyVJ0dHR2fYZDAYdPXrU1iEBAIBMZjT9kzVqOBYvNL388suSpOXLl2fbZzAYKDQBAACn8NVXX9k7BAAA8C9865w5a9RwLF5oio+Pt/QhAQAACp158+Zp4sSJKlGihNn2S5cuaeLEiZo/f76dIgMAwIlRaDJjjRqOxQtNAAAAzmrfvn06deqUJOmTTz7RQw89lK3QlJiYqP/+97/2CA8AABiN9o7A4Vmk0PTggw/qu+++U7ly5VSrVi0ZDIbbtmU9AgAA4KgMBoOioqJMP7/22mvZ2hQvXlyDBg2ydWgAAACSrF/DsUih6fXXX1fJkiVNP98pSAAAAEdVv3590xT0WrVq6bvvvpO3t7edowIAACbcOmf1Go5FCk3dunUz/dy9e3dLHBIAAKBQ++eaB+np6dm+xQUAANgei4Fbv4bjYvEjStqyZYt69eql+vXrq2HDhurXr5++++47a3QFAABQYH3wwQdq1aqVAgMDderUKU2ePFnz5s2zd1gAADgvY2b+Hw7K0jUcixea1q5dq+eff1733XefRo8ereHDh6t06dIaOnSovvzyS0t3BwAAUCBt3LhR06dPV7du3eTm5iZJ8vX11eLFi7VkyRI7RwcAgJOi0GTGGjUci3/r3JIlSzR+/Hj179/ftK1///5aunSp5syZozZt2li6SwAAgALn7bff1sSJE9WtWze9/fbbkqS+ffuqZMmSWrhwoYYMGWLnCAEAgLOzRg3H4jOazp49q8ceeyzb9rZt2+rkyZOW7g4AAKBA+u233xQcHJxte3BwsM6ePWuHiAAAADOazFmjhmPxQtMjjzyizZs3Z9v+3XffKSgoyNLdAQAAFEje3t769ddfs23fv3+/KlSoYIeIAACAwZiZ74cjskYNx+K3zgUFBWnBggU6fPiwGjZsKDc3Nx06dEifffaZunXrZrYA5ogRIyzdPQAAQIHQs2dPTZkyRVFRUZKkX3/9VTt37tTs2bPNpqcDAAAbynTMglF+WaOGY/FC0+rVq1WuXDkdPXpUR48eNW2vUKGCvv/+e9Nzg8FAoQkAADisIUOG6OrVqxo3bpyuX7+uoUOHytXVVb169dLQoUPtHR4AAM7JaLR3BAWKNWo4Fi80ff3115Y+JAAAQKH03HPPadiwYUpISJDRaNQDDzygEiVK2DssAACcl4PeApdf1qjhWLzQ9E9JSUnau3evvL29Vb9+fWt2BQAAUOBcvnxZJ0+e1PXr1yXJ7C+FDRs2tFdYAAAA2ViqhmOxQtP8+fO1YsUKffTRR6pevbr279+vZ555RteuXZN0a4GphQsXyt3d3VJdAgAAFFiffPKJJk+erPT0dBn/NU3fYDCYFZ0AAIBtOOqi3nllzRqORb51bvXq1Vq0aJF69uypcuXKSZJefPFFFS9eXJs2bdKOHTuUnJysRYsWWaI7AACAAm/WrFnq2rWrNm3apK+++srs8eWXX9o7PAAAnJMxM/8PB2HtGo5FCk1r1qxRVFSUnnvuOZUoUUI//fSTTpw4ob59+8rX11cVK1bUsGHDcvzKPAAAAEd0+fJlDRw4UA888ICqVKmS7QEAAOyAQpPVazgWKTQlJibq0UcfNT3/8ccfZTAY1KJFC9O2mjVr6vTp05boDgAAoMBr166dduzYYe8wAADAP2XezP/DQVi7hmOxNZoMBoPp53379qls2bLy8/MzbUtOTpaHh4elugMAACjQxo0bp06dOmnbtm2qWrWq2VhJkmJiYuwUGQAAcHbWrOFYpNAUEBCgPXv2qHr16rpy5Yp27dql9u3bm7XZsmWL/P39LdEdAABAgRcTE6Pk5GSlp6frzz//tHc4AABAkjHTcW6Byy9r13AsUmjq3bu3Jk2apGPHjikuLk7p6enq06ePJOmvv/7Sxo0btWzZMk2dOtUS3QEAABR4X3/9tebPn282Dd1a4uPjNW3aNB0+fFhubm5q0qSJoqKiVLZs2Wxtd+zYodjYWJ06dUqVK1fW+PHj1bJlS6vHCABAgVBAboFLSUlRdHS0vv76a2VkZKh169aaPHmyPD09c2y/detWLViwQKdOnZKXl5e6d++uiIgIubjcWhGpQ4cOOn36tOm5JK1du1a+vr7ZjmXtGo5F1mgKDQ3VxIkTtX//fhkMBs2aNUt16tSRJC1evFgzZ87UkCFD1LVrV0t0BwAAUOB5enqqWrVqVu8nLS1NgwcPVlBQkL777jt99tlnunTpkl588cVsbU+cOKHIyEiNGjVKe/fuVWRkpEaPHq1z585ZPU4AAAqEArJGU3R0tM6cOaOtW7dq27ZtOnPmjGJjY3Ns+/PPP2v8+PEaPXq09u7dqyVLlmjdunVavny5JOnatWv67bfftHnzZsXFxZkeORWZJOvXcCxSaJKk8PBwffzxx1qzZo3atWtn2j5kyBDt3LlTo0aNslRXAAAABd7QoUM1a9YsXbt2zar9nD59WrVq1dLw4cNVtGhRlSlTRj179tSePXuytV2/fr2Cg4PVpk0bubq6qmPHjmrYsKFWr15t1RgBACgojDdv5vthKampqdq4caNGjhwpLy8vlStXTmPHjtW6deuUmpqarf2ff/6pXr16qWXLlnJxcZGvr6/atm1rutb//PPP8vLyytO32lqzhmOxxcBvp2LFitbuAgAAoMD5+uuvtXfvXjVu3FjlypWTq6v5sOurr76ySD8PPPCAli5darZt69ateuihh7K1TUhIyLbeQs2aNRUfH2+RWAAAwC1paWm3nTGcmpqqGzdumF2TfX19lZaWphMnTujBBx80a9++fXuzNZTS0tK0fft2hYaGSpIOHTokDw8PPf300zp+/LiqVKmiyMjIfN0ab4kajtULTQAAAM6oQYMGatCggU37NBqNmjVrlr755hutXLky2/6cvkHG3d1dKSkpee4rwKdyvuMsKLJycKRcKtXK+TaJwiQrh8KeS1b8jvT+cqRcCvv7S3Kcz4p0K4dTcYdt05mNFgM/ePCg+vbtm+O+rNlCxYsXN23Luj4nJyff8bjXrl3TqFGj5O7urv79+0u69Q1ydevW1XPPPaf77rtPn3/+uSIjI7Vy5UoFBgbeezJ5RKEJAADACkaMGGHT/q5du6YJEybo8OHDWrlypQICArK18fDwUFpamtm2tLS02y48eifvxQzNd6wFjSPlMuj9OfYOwWIcJRdHen85Ui6O8v6SHCeXPR9ssE1HNloMPCQkRMeOHctx35EjRzR79mylpqaarsFZt8yVKFHitsf89ddfNXLkSJUrV04rVqwwtR08eLBZuy5duuizzz7T1q1bKTQBAAA4kh07duiXX37R9evXzbYbDAYNHz7cYv38/vvvGjJkiO677z6tXbs2x2+bkyR/f38dPmz+F+OEhATTAqB50WfCIh07cSZf8RYUAT6V9V7MUIfKZdlTI3U2PtHe4dyTSrV8Nej9OYU+l6w8HOn95Ui5FPb3l+Q4nxXJtrOyjAXgW+dq1KghNzc3JSQk6OGHH5YkJSYmys3NTT4+Pjm+ZseOHXruuef05JNP6vnnnze7JX/ZsmWqXbu2HnnkEdO29PR0FStWzKp53A6FJgAAACt47bXXtHLlSnl7e6to0aJm+yxZaLp8+bL69eunxo0ba+rUqWZfa/xvXbp00TvvvKPNmzerXbt22rZtm3bv3q2JEyfmud9jJ87oQPzv9xJ6geFIuZyNT7Td7SdW5ii5ONL7y5FycZT3l+RYudiEjW6duxMPDw916NBBsbGxmj17tiQpNjZWnTt3lru7e7b2Bw4c0PDhw/XKK68oPDw82/4zZ85ozZo1WrJkiSpXrqxPPvlEcXFxmjJlitVzyQmFJgAAACvYuHGjpkyZop49e1q1n3Xr1un06dPasmWLPv/8c7N9cXFxCgoK0pQpU9SlSxf5+vpq/vz5io2N1cSJE1WlShXNnTtXNWrUsGqMAAAUFAVhRpMkTZ48WdOmTVNoaKhu3Lih1q1b6+WXXzbt79Spk0JDQ/Xss8/qrbfeUkZGhqZOnaqpU6ea2jRo0EBLly7V+PHj5eLioqeeekpXr15VzZo1tXjxYlWvXt0eqVFoAgAAsAZXV1c1atTI6v0MGDBAAwYMuO3+uLg4s+fNmjVTs2bNrB0WAAC4gxIlSig6OlrR0dE57t+0aZPp57feeuuOxypatKhefPFFvfjiixaNMb9uP7caAAAA+fb0009r4cKFSk9Pt3coAAAgS+bN/D+QK8xoAgAAsIIOHTqoZ8+eatCggcqXLy+DwWC2/6uvvrJTZAAAOLECsEaTo6PQBAAAYAVRUVEqVaqUwsPD5eHhYe9wAACAJONNZiZZW6EtNKWkpCg6Olpff/21MjIy1Lp1a02ePFmenp45tp88ebI+/vhjubm5mbZFRUVZfYFOAADgnI4cOaKPPvpItWrVsncoAAAgC7fAWV2hLTRFR0frzJkz2rp1q27evKnRo0crNjZWkydPzrH9oUOHFB0drW7dutk4UgAA4IyqVq3K+kwAAMDpFMrFwFNTU7Vx40aNHDlSXl5eKleunMaOHat169YpNTU1W/v09HT98ssvqlOnjh2iBQAAzmjSpEl65ZVXtGPHDv3+++86ffq02QMAANgBi4FbXYGd0ZSWlqZz587luC81NVU3btyQv7+/aZuvr6/S0tJ04sQJPfjgg2bt4+PjlZGRoTlz5mjfvn0qWbKkevToocGDB8vFpVDW2gAAQAE3cOBA3bx5U0OHDjVbCNxoNMpgMOjo0aN2jA4AAOdkZDFwqyuwhaaDBw+qb9++Oe4bNWqUJKl48eKmbVmLbCYnJ2drf/XqVTVq1Eh9+vTRjBkzdPToUQ0fPlwuLi4aPHhwrmNycTHIxcVwxzZFiriY/dcZkDMKE1fX3J8zZzzP5IzCIi+fZck+5/mdd96xWV8AACCXmJlkdQW20BQSEqJjx47luO/IkSOaPXu2UlNTTYt/Z90yV6JEiWztmzRpoiZNmpie16tXT/369dPmzZvzVGgqW9Yz21cT306pUs737TLkjMKgTJmcvzDgTpzxPJMzCrr8fJYl257nX375RZ07d5aXl5fN+gQAAHdBocnqCmyh6U5q1KghNzc3JSQk6OGHH5YkJSYmys3NTT4+Ptnaf/nllzp//rx69epl2paeni53d/c89ZuUlJyrGU2lSnnoypVU3bzpHFPyyDl3OZexckzInYsXs896vB3e2+R8O3ye7S8vn2Up7+c5v4Wsf1qyZIneeOMNtWzZUj169FCzZs1y/QcrAACAwqpQFpo8PDzUoUMHxcbGavbs2ZKk2NhYde7cOcfikdFoVExMjKpXr67GjRvrwIEDWrFihSZMmJCnfjMzjcrMNOaq7c2bmcrIcI5/pGUhZxQG+TlfznieyRkFXX7PlS3P8/bt2/X999/rk08+0ciRI1WyZEmFhYWpW7dueuCBB2wSAwAAMMcaTdZXKAtNkjR58mRNmzZNoaGhunHjhlq3bq2XX37ZtL9Tp04KDQ3Vs88+q7Zt22rChAl65ZVXdO7cOXl7eysyMlJdu3a1YwYAAMCRGQwGNW3aVE2bNlVycrK2bdumbdu2qXv37qpVq5aeeOIJderUKc8zrAEAwD3g1jmrK7SFphIlSig6OlrR0dE57t+0aZPZ8169epndOgcAAGArKSkpunz5si5evKj09HS5uLho0aJFmjFjhmJjY/XII4/YO0QAAJwDhSarK7SFJgAAgILs+vXr2rZtmz799FP98MMP8vb2VlhYmN544w1Vq1ZNkjRlyhRFRUVpx44ddo4WAADnYLxJocnaKDQBAABYwSOPPKKMjAw99thjWrBggZo1ayYXF5dsbb766is7RQgAAGB5FJoAAACsYNSoUerSpYvKlLn99xS2atVK7dq1s2FUAAA4ORYDtzoKTQAAAFbQr18/089JSUnau3evypUrpwYNGpi2u7oyFAMAwKZYo8nqXO7eBAAAALk1f/58hYSE6OTJk5Kk/fv3q127dho5cqR69+6tAQMGKC0tzc5RAgDgnIyZN/P9QO5QaAIAALCQ1atXa9GiRerZs6fKlSsnSXrxxRdVvHhxbdq0STt27FBycrIWLVpk50gBAHBOxszMfD+QOxSaAAAALGTNmjWKiorSc889pxIlSuinn37SiRMn1LdvX/n6+qpixYoaNmyYNm/ebO9QAQBwSsabmfl+IHcoNAEAAFhIYmKiHn30UdPzH3/8UQaDQS1atDBtq1mzpk6fPm2P8AAAAKyOFSgBAAAsyGAwmH7et2+fypYtKz8/P9O25ORkeXh42CM0AACcHjOTrI8ZTQAAABYSEBCgPXv2SJKuXLmiXbt2qWnTpmZttmzZIn9/f3uEBwCA02ONJutjRhMAAICF9O7dW5MmTdKxY8cUFxen9PR09enTR5L0119/aePGjVq2bJmmTp1q50gBAHBOzGiyPgpNAAAAFhIaGqrr16/rgw8+kIuLi2bNmqU6depIkhYvXqwPP/xQQ4YMUdeuXe0cKQAAzolCk/VRaAIAALCg8PBwhYeHZ9s+ZMgQDR8+XGXKlLFDVAAAALZBoQkAAMAGKlasaO8QAABwepk3b9o7BIdHoQkAAAAAADgFFvW2PgpNAAAAAADAKbBGk/VRaAIAAAAAAE6BQpP1udg7AAAAANyb+Ph4DRgwQI0aNVKTJk00fvx4JSUl5dh28ODBqlu3roKCgkyPb7/91sYRAwDg3FJSUjRhwgSFhISoQYMGGj9+vJKTk2/bfvLkyapTp47Z9Xv16tWm/evXr1fbtm0VGBio7t27Ky4uzhZp5IhCEwAAQCGWlpamwYMHKygoSN99950+++wzXbp0SS+++GKO7X/++WctW7ZMcXFxpkfz5s1tHDUAAPZhzMzM98OSoqOjdebMGW3dulXbtm3TmTNnFBsbe9v2hw4dUnR0tNn1u2fPnpKkXbt2KTo6Wv/5z3+0Z88edenSRcOGDVNqaqpFY84tCk0AAACF2OnTp1WrVi0NHz5cRYsWVZkyZdSzZ0/t2bMnW9tTp07p8uXLql27th0iBQDA/jJvZub7YSmpqanauHGjRo4cKS8vL5UrV05jx47VunXrciwOpaen65dfflGdOnVyPN6aNWvUqVMnNWjQQG5uburfv7/KlCmjzZs3WyzmvGCNJgAAgELsgQce0NKlS822bd26VQ899FC2tocOHZKnp6fGjBmjQ4cOydvbW/3791d4eHie+w3wqZzvmAuKrBwcKZdKtXztHMm9y8qhsOeSFb8jvb8cKZfC/v6SHOezIt3K4VTcYZv0Zas1mtLS0nTu3Lkc96WmpurGjRvy9/c3bfP19VVaWppOnDihBx980Kx9fHy8MjIyNGfOHO3bt08lS5ZUjx49NHjwYLm4uCghIUE9evQwe03NmjUVHx9v+cRygUITAACAgzAajZo1a5a++eYbrVy5Mtv+9PR0BQYGasyYMfLz89OuXbsUGRkpT09PdejQIU99vRcz1FJh250j5TLo/Tn2DsFiHCUXR3p/OVIujvL+khwnl2FHr+ktG/Rjq0LTwYMH1bdv3xz3jRo1SpJUvHhx0zYPDw9JynGdpqtXr6pRo0bq06ePZsyYoaNHj2r48OFycXHR4MGDlZycbHp9Fnd3d6WkpFgqnTyh0AQAAOAArl27pgkTJujw4cNauXKlAgICsrUJCwtTWFiY6XnTpk0VFhamLVu25LnQtOypkTobn3ivYdtVpVq+GvT+HIfKpc+ERTp24oy9w7knAT6V9V7M0EKfS1YejvT+cqRcCvv7S3Kcz4rkGLPl/i0kJETHjh3Lcd+RI0c0e/ZspaamytPTU5JMt8yVKFEiW/smTZqoSZMmpuf16tVTv379tHnzZg0ePFgeHh5KS0sze01aWprKlCljqXTyhEITAABAIff7779ryJAhuu+++7R27VqVLVs2x3Zr167NNnspPT1dxYoVy3OfZ+MTbXabg7U5Ui7HTpzRgfjf7R2GRThKLo70/nKkXBzl/SU5Vi62YOlFvfOjRo0acnNzU0JCgh5++GFJUmJiotzc3OTj45Ot/Zdffqnz58+rV69epm3p6elyd3eXJPn5+en48eNmr0lISLDbl32wGDgAAEAhdvnyZfXr10/169fXsmXLbltkkm7NeoqOjtaRI0eUmZmp7du367PPPjN9aw0AAI7OeDMz3w9L8fDwUIcOHRQbG6ukpCQlJSUpNjZWnTt3NhWPzGI2GhUTE6MffvhBRqNRcXFxWrFihen6HR4ero0bN+rHH3/UjRs3tHz5cl24cEFt27a1WMx5wYwmAACAQmzdunU6ffq0tmzZos8//9xsX1xcnIKCgjRlyhR16dJF/fr1U0pKikaMGKELFy6oatWqmjZtmoKDg+0UPQAAtmWrNZruZvLkyZo2bZpCQ0N148YNtW7dWi+//LJpf6dOnRQaGqpnn31Wbdu21YQJE/TKK6/o3Llz8vb2VmRkpLp27SpJeuSRRzR58mTT/po1a2rJkiXy8vKyS24UmgAAAAqxAQMGaMCAAbfdHxcXZ/rZYDAoIiJCERERtggNAIACJ7MA3Don3VqLKTo6WtHR0Tnu37Rpk9nzXr16md06929du3Y1FZ7sjUITAAAAAABwCgVlRpMjY40mAAAAAAAAWAQzmgAAAAAAgFMw3rxp7xAcHoUmAAAAAADgFIwFZI0mR0ahCQAAAAAAOAXWaLI+Ck0AAAAAAMApUGiyPhYDBwAAAAAAgEUwowkAAAAAADiFTGY0WR2FJgAAAAAA4BRYDNz6KDQBAAAAAACnwBpN1kehCQAAAAAAOAXjTaO9Q3B4LAYOAAAAAAAAi2BGEwAAAAAAcAosBm59FJoAAAAAAIBTMGZy65y1UWgCAAAAAABOIZM1mqyOQhMAAAAAAHAKfOuc9RX6xcBTU1PVs2dPrVu37o7tDh48qCeeeEJBQUFq1aqV1qxZY6MIAQAAAABAQWC8acz3A7lTqAtNx48fV+/evXXgwIE7trt8+bKeeeYZhYWFac+ePZo6dapiYmL0008/2SZQAAAAAAAAJ1BoC00//PCD+vXrp27duum+++67Y9tt27bJy8tLvXv3lqurqx555BGFhoZq1apVNooWAAAAAADYW+ZNY74fyJ0Cu0ZTWlqazp07l+O+8uXLq1atWvrmm29UrFgxvfPOO3c81vHjx+Xv72+2rWbNmlq7dq3F4gUAAAAAAAUbazRZX4EtNB08eFB9+/bNcd/8+fPVpk2bXB8rOTlZHh4eZtvc3d2VkpKSp5hcXAxycTHcsU2RIi5m/3UG5IzCxNU19+fMGc8zOaOwyMtnWeI8AwCAWzIzmZlkbQW20BQSEqJjx45Z5FgeHh66evWq2ba0tDR5enrm6Thly3rKYLhzoSlLqVIed2/kYMj5Loz8D60gKJOP1/Dedg58nguX/HyWJed8bwMAgP9hUW/rK7CFJkvy9/fX999/b7YtISFBfn5+eTpOUlJyrmY0lSrloStXUnXTSabkkTM5OypyJmdHRc53z7lMmbz9MQoAAAC3OEWhqW3btnrzzTe1fPly9e7dW/v27dPGjRu1YMGCPB0nM9OY62l2N29mKiPDOQbvWcjZOZCzcyBn50DOAADA2WQ6yR/Z7MlhFyro1KmT3nrrLUlSmTJl9Pbbb+vzzz9XSEiIXnrpJb300ktq3LixnaMEAAAAAAC2YrxpzPcDueMQM5q+/vrrbNs2bdpk9rxu3br68MMPbRUSAAAAAAAoYCgYWZ9DFJoAAAAAAADuhlvnrM9hb50DAAAAAACAbTGjCQAAAAAAOAVjLr/gC/lHoQkAAKCQ++GHHzRjxgwlJibKw8NDjz/+uMaNGyd3d/dsbXfs2KHY2FidOnVKlStX1vjx49WyZUs7RA0AgO1lFpA1mlJSUhQdHa2vv/5aGRkZat26tSZPnixPT89sbSdNmqSNGzeabUtLS9Ojjz6qZcuWSZI6dOig06dPy8XlfzeurV27Vr6+vtZNJAfcOgcAAFCIJSUlaejQofq///s/7d27V+vXr9fu3bu1ePHibG1PnDihyMhIjRo1Snv37lVkZKRGjx6tc+fO2SFyAABsz3gzM98PS4qOjtaZM2e0detWbdu2TWfOnFFsbGyObV999VXFxcWZHnPnzlWpUqUUFRUlSbp27Zp+++03bd682aydPYpMEoUmAACAQq1s2bL673//q+7du8tgMOjSpUu6fv26ypYtm63t+vXrFRwcrDZt2sjV1VUdO3ZUw4YNtXr1ajtEDgCA7RlvGvP9sJTU1FRt3LhRI0eOlJeXl8qVK6exY8dq3bp1Sk1NveNrk5KSNHbsWE2cOFF+fn6SpJ9//lleXl6qUqWKxWK8F9w6BwAAUMiVKFFCktSiRQudO3dOwcHB6t69e7Z2CQkJ8vf3N9tWs2ZNxcfH2yROAACcRVpa2m1nDKempurGjRtm12RfX1+lpaXpxIkTevDBB2973NjYWNWpU0ddunQxbTt06JA8PDz09NNP6/jx46pSpYoiIyPtdms8hSYAAAAHsW3bNl2+fFljx47VyJEjtXTpUrP9ycnJ8vDwMNvm7u6ulJSUPPdVqZZ9puNbUlYOjpRLgE9lO0dy77JyKOy5ZMXvSO8vR8qlsL+/JMf5rEi3cjgQ/7tN+rLVGk0HDx5U3759c9w3atQoSVLx4sVN27Kuz8nJybc95qlTp7RhwwatWbPGbLvBYFDdunX13HPP6b777tPnn3+uyMhIrVy5UoGBgfeYSd5RaAIAAHAQ7u7ucnd317hx4/TEE0/o8uXLKl26tGm/h4eH0tLSzF6TlpaW48KjdzPo/Tn3HG9B4Ui5vBcz1N4hWIyj5OJI7y9HysVR3l+S4+Sy+vNdNunHmGnZtZZuJyQkRMeOHctx35EjRzR79mylpqaarsFZt8xlzVLOyccff6ygoKBsM54GDx5s9rxLly767LPPtHXrVgpNAAAAyJv9+/frxRdf1IYNG1S0aFFJUnp6utzc3LLNXvL399fhw4fNtiUkJKhOnTp57nfZUyN1Nj4x/4EXAJVq+WrQ+3McKpc+Exbp2Ikz9g7nngT4VNZ7MUMLfS5ZeTjS+8uRcins7y/JcT4rkm1nZRWEb52rUaOG3NzclJCQoIcffliSlJiYKDc3N/n4+Nz2ddu2bdPAgQOzbV+2bJlq166tRx55xLQtPT1dxYoVs3jsuUGhCQAAoBALCAhQWlqapk+frueff15///23pk2bpvDwcFPhKUuXLl30zjvvaPPmzWrXrp22bdum3bt3a+LEiXnu92x8ok7FHb57w0LAkXI5duKMzW4/sTZHycWR3l+OlIujvL8kx8rFFiy5qHd+eXh4qEOHDoqNjdXs2bMl3Vp7qXPnznJ3d8/xNRcvXlRiYqIaNmyYbd+ZM2e0Zs0aLVmyRJUrV9Ynn3yiuLg4TZkyxap53A7fOgcAAFCIeXp6aunSpTp+/LiaNGmiPn366NFHH9WLL74oSQoKCtKGDRsk3VpodP78+Vq0aJEaNmyoBQsWaO7cuapRo4Y9UwAAwGaMNzPz/bCkyZMny8fHR6GhoXr88cd1//33a9KkSab9nTp10ltvvWV6/scff0iSKlasmO1Y48ePV/PmzfXUU08pODhYH374oRYvXqzq1atbNObcYkYTAABAIVezZk29/fbbOe6Li4sze96sWTM1a9bMFmEBAIDbKFGihKKjoxUdHZ3j/k2bNpk9r1u37m3XfCpatKhefPFF0x+Z7I1CEwAAAAAAcAoFYY0mR0ehCQAAAAAAOIWCsEaTo6PQBAAAAAAAnEKmkUKTtVFoAgAAAAAATuEmhSar41vnAAAAAAAAYBHMaAIAAAAAAE6BJZqsj0ITAAAAAABwCtw6Z30UmgAAAAAAgFNgRpP1UWgCAAAAAABOgRlN1sdi4AAAAAAAALAIZjQBAAAAAACnwK1z1kehCQAAAAAAOAVunbM+Ck0AAAAAAMApMKPJ+ig0AQAAAAAAp0ChyfooNAEAAAAAAKfArXPWx7fOAQAAAAAAwCKY0QQAAAAAAJwCt85ZH4UmAAAAAADgFLh1zvooNAEAAAAAAKfAjCbro9AEAAAAAACcAjOarI/FwAEAAAAAAGARzGgCAAAAAABOgVvnrI9CEwAAAAAAcArcOmd9FJoAAAAAAIBTyLR3AE6AQhMAAAAAAHAKzGiyPhYDBwAAAAAAgEUwowkAAAAAADgFFgO3PgpNAAAAAADAKXDrnPVRaAIAAAAAAE6BGU3WR6EJAAAAAAA4BWY0WV+hXww8NTVVPXv21Lp16+7YbvLkyapTp46CgoJMj9WrV9soSgAAAOv54Ycf9MQTT6h+/fpq0qSJoqOjlZaWlmPbwYMHq27dumZjom+//dbGEQMAACn3NY2DBw/qiSeeUFBQkFq1aqU1a9aY7V+/fr3atm2rwMBAde/eXXFxcdYM+44KdaHp+PHj6t27tw4cOHDXtocOHVJ0dLTi4uJMj549e1o/SAAAACtKSkrS0KFD9X//93/au3ev1q9fr927d2vx4sU5tv/555+1bNkyszFR8+bNbRw1AAD2cdOY/4el5bamcfnyZT3zzDMKCwvTnj17NHXqVMXExOinn36SJO3atUvR0dH6z3/+oz179qhLly4aNmyYUlNTLR90LhTaQtMPP/ygfv36qVu3brrvvvvu2DY9PV2//PKL6tSpY6PoAAAAbKNs2bL673//q+7du8tgMOjSpUu6fv26ypYtm63tqVOndPnyZdWuXdsOkQIAYH83jcZ8PywpLzWNbdu2ycvLS71795arq6seeeQRhYaGatWqVZKkNWvWqFOnTmrQoIHc3NzUv39/lSlTRps3b7ZozLlVYNdoSktL07lz53LcV758edWqVUvffPONihUrpnfeeeeOx4qPj1dGRobmzJmjffv2qWTJkurRo4cGDx4sF5fc19pcXAxycTHcsU2RIi5m/3UG5OwcyNk5kLNzIGfHU6JECUlSixYtdO7cOQUHB6t79+7Z2h06dEienp4aM2aMDh06JG9vb/Xv31/h4eF57rNSLd97jtvesnJwpFwCfCrbOZJ7l5VDYc8lK35Hen85Ui6F/f0lOc5nRbqVw4H4323Sl60WA7dkTeP48ePy9/c321azZk2tXbtWkpSQkKAePXpk2x8fH38PGeRfgS00HTx4UH379s1x3/z589WmTZtcH+vq1atq1KiR+vTpoxkzZujo0aMaPny4XFxcNHjw4Fwfp1y5ErluW6qUR67bOgpydg7k7BzI2TmQs+PZtm2bLl++rLFjx2rkyJFaunSp2f709HQFBgZqzJgx8vPz065duxQZGSlPT0916NAhT30Nen+OJUO3K0fK5b2YofYOwWIcJRdHen85Ui6O8v6SHCcXW+XxlvGETfqxZE0jOTlZHh7mYxh3d3elpKTkar+tFdhCU0hIiI4dO2aRYzVp0kRNmjQxPa9Xr5769eunzZs356nQBAAAUJC5u7vL3d1d48aN0xNPPKHLly+rdOnSpv1hYWEKCwszPW/atKnCwsK0ZcuWPBeaAADA7VmypuHh4aGrV6+abUtLS5Onp6dp/7+/BCQtLU1lypSxSP955Zjzx//lyy+/1Icffmi2LT09Xe7u7naKCAAAwDL279+vxx9/XOnp6aZt6enpcnNzy/bXzbVr12rLli1m29LT01WsWDGbxAoAAPLO399fx48fN9uWkJAgPz8/SZKfn98d99uaUxSajEajYmJi9MMPP8hoNCouLk4rVqzgW+cAAEChFxAQoLS0NE2fPl3p6en6888/NW3aNIWHh6to0aJmba9du6bo6GgdOXJEmZmZ2r59uz777DPGRAAAFGBt27bV+fPntXz5ct24cUM//vijNm7caFqXKTw8XBs3btSPP/6oGzduaPny5bpw4YLatm1rl3gL7K1z96pTp04KDQ3Vs88+q7Zt22rChAl65ZVXdO7cOXl7eysyMlJdu3a1d5gAAAD3xNPTU0uXLtXrr7+uJk2aqGTJkgoNDdXw4cMlSUFBQZoyZYq6dOmifv36KSUlRSNGjNCFCxdUtWpVTZs2TcHBwXbOAgAA/NM/axplypTR22+/ralTp2rOnDkqW7asXnrpJTVu3FiS9Mgjj2jy5MmmmkfNmjW1ZMkSeXl52SV2g9Fo4e/oAwAAAAAAgFNyilvnAAAAAAAAYH0UmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmu5RSkqKJkyYoJCQEDVo0EDjx49XcnLybdtPnjxZderUUVBQkOmxevVqG0acPxcuXFBERISCg4MVEhKiqVOnKiMjI8e2O3bsUGhoqAIDA9WhQwd98803No7WMvKS8+DBg1W3bl2z8/rtt9/aOGLLSUpKUtu2bbVr167btnGU85wlNzk7ynmOj4/XgAED1KhRIzVp0kTjx49XUlJSjm0d5TznJWdHOc8//PCDnnjiCdWvX19NmjRRdHS00tLScmzrCOc5L/k6yjm2lcI+1nGkMYyjjU0cabzhCOMIRxofONJ135Gu51yrnYgR9yQqKsrYr18/48WLF43nz583Pv3008ZXXnnltu27detmXLdunQ0jtIynn37a+PzzzxtTUlKMv//+u7FTp07GJUuWZGv322+/GevWrWv84osvjDdu3DBu2rTJWK9ePePZs2ftEPW9yW3ORqPRGBISYty1a5eNI7SOvXv3Gtu0aWP09/c3/vjjjzm2caTzbDTmLmej0THOc2pqqrFJkybG2bNnG69fv25MSkoyDhkyxDh06NBsbR3lPOclZ6PRMc7zhQsXjHXr1jV+/PHHxps3bxrPnTtn7Ny5s3H27NnZ2jrCec5LvkajY5xjWyrsYx1HGsM40tjEkcYbjjCOcKTxgSNd9x3pes612rlQaLoHKSkpxoceesi4b98+07YDBw4Y69WrZ0xJScnW/vr168aHHnrI+Msvv9gyzHt24sQJo7+/v9n/pDZt2mR87LHHsrWdMWOGccCAAWbbBg0adNv/gRRUecn5999/N9aqVct49epVW4ZoFevWrTM+9thjxk2bNt1xsOQo59lozH3OjnKeExMTjYMGDTJmZGSYtn355ZfG+vXrZ2vrKOc5Lzk7ynk2Go2mHDIzM43Hjh0ztm3b1vjee+9la+co5zm3+TrSObaFwj7WcaQxjCONTRxpvOEo4whHGh842nXfka7nXKudB7fO3UVaWppOnjx528eNGzfk7+9vau/r66u0tDSdOHEi27Hi4+OVkZGhOXPm6NFHH1X79u21ePFiZWZm2jCjvDt+/Li8vLxUsWJF0zZfX1+dPn1aV65cMWubkJBg9vuQpJo1ayo+Pt4msVpKXnI+dOiQPD09NWbMGDVu3FidO3fW2rVrbR2yRTRt2lRffPGFOnbseMd2jnKepdzn7Cjn+YEHHtDSpUtVpEgR07atW7fqoYceytbWUc5zXnJ2lPMsSSVKlJAktWjRQqGhoSpfvry6d++erZ2jnOfc5utI59hSHHms40hjGEcamzjSeMNRxhGOND5wtOu+I13PuVY7D1d7B1DQHTx4UH379s1x36hRoyRJxYsXN23z8PCQpBzXLrh69aoaNWqkPn36aMaMGTp69KiGDx8uFxcXDR482ArRW0ZycrIpryxZz1NSUlSqVKk7tnV3d1dKSor1A7WgvOScnp6uwMBAjRkzRn5+ftq1a5ciIyPl6empDh062DTue1W+fPlctXOU8yzlPmdHOs9ZjEajZs2apW+++UYrV67Mtt+RznOWu+XsiOd527Ztunz5ssaOHauRI0dq6dKlZvsd7TzfLV9HPMf3ypHHOo40hnGksYkjjTcccRzhSOMDR7ruO9L1nGu146PQdBchISE6duxYjvuOHDmi2bNnKzU1VZ6enpKk1NRUSf+r1v5TkyZN1KRJE9PzevXqqV+/ftq8eXOBLjQVL17clFeWrOdZeWfx8PDItqBbWlpatnYFXV5yDgsLU1hYmOl506ZNFRYWpi1btjjs/wgd5TznhaOd52vXrmnChAk6fPiwVq5cqYCAgGxtHO085yZnRzvP0q1Bpru7u8aNG6cnnnhCly9fVunSpU37He083y1fRzzH98qRxzqONIZxxrFJQT8neVFYzokjjQ8c7brvSNdzrtWOj1vn7kGNGjXk5uamhIQE07bExES5ubnJx8cnW/svv/xSH374odm29PR0ubu7WzvUe+Ln56dLly7p/Pnzpm2JiYmqVKmSSpYsadbW399fx48fN9uWkJAgPz8/m8RqKXnJee3atdqyZYvZtvT0dBUrVswmsdqDo5znvHCk8/z777+rR48eunbtmtauXZvjwEtyrPOc25wd5Tzv379fjz/+uNLT003b0tPT5ebmlu2vnY5wnvOSr6OcY1sp7GMdRxrDOOPYpKCfk7woDOfEkcYHjnLdd6TrOddq50Kh6R54eHioQ4cOio2NVVJSkpKSkhQbG6vOnTvnOKAyGo2KiYnRDz/8IKPRqLi4OK1YsUI9e/a0Q/S55+PjowYNGuj111/XtWvXdOrUKS1YsEDh4eHZ2nbp0kW7d+/W5s2blZGRoc2bN2v37t3q2rWrHSLPv7zkfO3aNUVHR+vIkSPKzMzU9u3b9dlnnxX483ovHOU854WjnOfLly+rX79+ql+/vpYtW6ayZcvetq2jnOe85Owo5zkgIEBpaWmaPn260tPT9eeff2ratGkKDw9X0aJFzdo6wnnOS76Oco5tpbCPdRxpDOOMY5OCfk7yoqCfE0caHzjSdd+Rrudcq52M/dYhdwxXr141vvTSS8ZHH33U2LBhQ2NUVJQxOTnZtL9jx47GhQsXmp5/8MEHxnbt2hkffvhhY+vWrY0rV660R9h59vfffxsjIyONjRo1MjZu3Nj4n//8x/RNDoGBgcZPP/3U1Pbbb781dunSxRgYGGjs1KmTcfv27fYK+57kNufMzEzj/PnzjS1btjTWq1fP2KlTJ+OWLVvsGbpF/PubUxz1PP/TnXJ2lPP89ttvG/39/Y0PP/ywMTAw0OxhNDrmec5Lzo5yno1Go/H48ePGAQMGGIODg40tW7Y0zpgxw3j9+nWj0eiY5zm3+TrSObaVwj7WcaQxjCOOTRxpvFGYxxGOND5wtOu+I13PuVY7D4PRaDTau9gFAAAAAACAwo9b5wAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaADiljRs3qmfPngoKClJQUJB69OihDz/80LS/VatWmjt3riTJaDRq/fr1unDhgr3CBQAABQjjCAC4PVd7BwAAtrZ27Vq99tprevHFF9WwYUMZjUb98MMPmjp1qs6fP68RI0Zo7dq1KlasmCRpz549ioqK0ldffWXnyAEAgL0xjgCAO6PQBMDpvP/++woPD9eTTz5p2vbAAw/o7NmzWrFihUaMGKGyZcua9hmNRnuECQAACiDGEQBwZ9w6B8DpuLi4aP/+/bp8+bLZ9iFDhmj16tWS/jflfdeuXerbt68kqXXr1lq3bp0kaf/+/erdu7fq1aunxx57TFOmTNG1a9dsmwgAALA5xhEAcGcUmgA4nSFDhujo0aNq3ry5nnnmGS1evFg//fSTSpYsqRo1api1DQoKMq2xsGbNGnXs2FHx8fHq37+/mjRpog0bNig2NlaHDx/WwIED+aslAAAOjnEEANwZhSYATqd9+/ZavXq12rVrp0OHDmn69Ol64okn9Pjjj2vfvn1mbYsWLarSpUtLksqWLSt3d3ctW7ZMjzzyiCIiIuTj46Pg4GBNnz5dBw8e1O7du+2REgAAsBHGEQBwZ6zRBMAp1atXT2+++aaMRqN++eUX7dixQytWrNCQIUP0xRdf3PG1R44c0cmTJxUUFJRtX2JiokJCQqwVNgAAKAAYRwDA7VFoAuBUzp49qyVLluiZZ55RxYoVZTAYFBAQoICAALVu3VodO3bUnj177niMzMxMhYaG6tlnn82275+LfwIAAMfCOAIA7o5b5wA4laJFi2r16tXasGFDtn0lSpSQJHl7e5ttNxgMZs/9/Px0/PhxVa9e3fS4efOmYmJidObMGesFDwAA7IpxBADcHTOaADiVsmXLavDgwZo1a5auXbumxx9/XCVKlFBCQoIWLFigkJAQBQcHm72mePHikqT4+HiVKVNGAwcOVO/evTVp0iT17dtXycnJmjJlipKTk+Xj42OHrAAAgC0wjgCAu6PQBMDpjB49Wj4+Pvroo4+0atUqpaWlqXLlyurYsaOGDh2arb2/v79atGih0aNH67nnntPAgQO1dOlSzZ49W927d5eHh4caN26sF154QUWLFrVDRgAAwFYYRwDAnRmMfIcmAAAAAAAALIA1mgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAAAAAAABgERSaAAAAAAAAYBEUmgAAAAAAAGARFJoAOAWj0WjvEAAAgBNh7AFL4v2EwoRCExxanz591KdPn9vub9WqlaKiovJ0zKioKLVq1epeQ7O4zMxMrVmzRr1791ZISIjq16+vbt26acWKFUpPT7dKnxkZGZowYYLq16+v+vXr68cff1RAQIDmzp1rlf7ya9++fRo6dKjp+R9//KGAgACtW7fOjlFlFx0drZkzZ5qeHz16VEOGDFHjxo0VEhKigQMH6vDhw6b9SUlJatGihU6dOmWPcAEAyLdffvlFY8aMUZMmTVSnTh01bdpUo0eP1pEjR+wdmkUsXLhQy5Yts2ofiYmJio6OVvv27fXwww+rQYMG6tmzp1atWqUbN25YtW9Le/vttzV27Ngc9/3888966KGHLDJu27VrlwICArRr1657PpYt/XssmxvPP/+8li5daqWIgDtztXcAQGETERGhvn372jsMM6mpqXr22Wd18OBB/d///Z8GDx4sNzc37dq1S7GxsdqxY4cWLlyookWLWrTfnTt3at26dYqIiNCjjz6q2rVra/Xq1apUqZJF+7lXa9asUUJCgul5hQoVtHr1alWrVs2OUZn78ccftW3bNm3dulWS9Pvvv6t379566KGHNHXqVLm4uOjtt9/WU089pfXr1+uBBx5Q2bJl1b9/f7344otasWKFDAaDnbMAAODujh8/rp49e6pevXqaOHGivL29dfbsWa1cuVI9e/bUe++9p8DAQHuHeU9mzZqlESNGWO34mzdv1oQJE/TAAw9owIABqlGjhtLS0rRjxw7FxMTo22+/1VtvvVUoxgaJiYl66623tHHjxmz70tPTFRUVpYyMDDtEVnD8eyybG+PHj1doaKhatmwpX19fK0UG5IxCE5BHBak4kSUmJkb79+/PNjBr2rSpateurdGjR2vVqlUaMGCARfu9dOmSJKl79+6qWrWqJBWKgWHRokULXJwxMTHq27evihcvLklasWKF3N3dtWjRItO2xo0bq2XLllq5cqUmTZokSXrqqaf01ltv6csvv1Tbtm3tFj8AALn1zjvvyMvLS0uXLpWbm5tpe5s2bdShQwctWLBAixcvtmOEBVtiYqImTJigRx99VHPnzpWr6//+SdeiRQuFhIRo5Mj/x969x8WU/38Af83UqKmkyP1WktxCirBkrWsol9rl574pVqncr0sIrRVWclta1v3Oslh22cVa6xobtiiykXtLujfN/P7wbdbooqlppk6v5+Mxj20+53POvD8fZzun93w+n+OPo0ePom/fvjqMtHCWLl2K3r17o3r16rm2ffPNN3jz5o0Ooir7qlevjt69eyMkJARr167VdThUznDqHNE7PvnkE4SGhmLJkiXo0KEDWrRogdGjR+P+/fvKOu9PnZPL5Vi9ejU+/vhjtGzZEmPHjsXx48dha2uLhw8fAgBWrVoFW1vbXJ/3/jSzjIwMfP311+jcuTOaN28OV1dXHDt2rMCYExMTsX//fri7u+eZPHFxccHo0aNVRhm9efMGwcHB6NatG+zs7NC3b1/s27dPrb6YMWOGctpht27dlFMU329TbGwsvL290bp1a3To0AErVqzAzJkzVaY05jXd7v0+mzFjBkaOHInAwEA4OjpiwIABkMlkSExMxPz589GlSxc0b94cbdu2ha+vr7LvZ8yYgYMHD+LRo0fK6XJ5TZ2Li4uDv78/PvroI7Rq1QrDhw/H1atXldtz9jl+/Dj8/f1hb2+PNm3aYPbs2UhJSVHWu3XrFkaOHAkHBwfY29tj1KhRuHHjRoH/hr/99huio6NVbgYbNGgAT09PZZIJAKRSKWrUqIF//vlHWWZgYIAePXpg/fr1BX4GERFRafHixQsAudecMTIywsyZM+Hi4gIA2L59O2xtbVXuwwDg6NGjaNy4MR4+fIgDBw7Azs4OV69ehbu7O+zs7NCzZ0+cPn0a9+7dw8iRI9GyZUt0794dR48eVR6jqPsBQEJCAiZNmoS2bduiZcuWGDlypMqUv5z7l7CwMOXPq1atQvfu3REWFgYnJyd069YNX375JVq0aJErkfLtt9/C3t4eqampefbfxo0bIRaLsXDhQpUkU46ePXuif//+EIv/+1PP1tYWYWFhcHd3h4ODA9asWQOg8Pc/709be/9+ePjw4ZgxYwbWr1+Pjz76CK1bt8a4ceM+OL3/zp07+O233+Dq6pprW0REhMqXa4Vx584djB07Vrmsg6+vb6FiKGifnOl2Fy5cwPDhw9GiRQt8/PHH2Lt3L549e4bx48fD3t4enTt3xubNm1WO/erVK8ydOxcdOnSAnZ0dPvvsM1y4cEGljq2tLbZv347Zs2ejbdu2sLe3h7+/v/L/k7zuZYG3o9rc3NzQokULtGvXDlOmTMGzZ89Uju3m5oZff/0Vd+7cKXQfEmkCE01E79myZQvu3buH4OBgLFy4EDdv3ixwHaevv/4aa9asgbu7O8LCwmBmZobAwEC1P1ehUMDX1xe7du3C559/jrVr18Le3h4TJ07EoUOH8t3vwoULkMlk6NKlS751pk2bprxpS09Px5AhQ3D48GF4enpizZo1cHBwwOzZs7Fu3TqV/QrqCx8fH4wbNw7A2xupvNqcmJiIYcOG4fHjxwgODsaXX36Jn376CT/++KO63QMAuHLlCh48eIBVq1bB19cXenp6GDt2LM6fP4/JkycjPDwcPj4++OOPP5Q3JT4+PujcuTOqVq2K3bt34+OPP8513JiYGAwcOBDx8fH48ssvERISApFIhJEjR+LSpUsqdQMDA1G7dm2sWbMGXl5e2L9/v7LfkpOT4eXlBXNzc4SGhmLFihVIS0vD6NGjC/w27vDhw2jVqhVq1qypLBsyZAi8vLxU6t2/fx93796FjY2NSrmLiwsiIyNz3YgTERGVRh9//DESEhIwePBgbN++HbGxscqkU69evTBgwAAAgKurKwwMDPDDDz+o7H/w4EG0bdsWderUAfB2zchJkyZh8ODBWLNmDQwMDDBlyhR88cUX+Pjjj7Fy5UpUrVoV06dPx5MnT5THKcp+iYmJGDx4MG7duoU5c+Zg2bJlkMvlGDp0KGJjYwEAu3fvBgB4eHgofwbeJqh+/vlnLF++HBMmTMDnn3+OjIwM/PTTTyrtO3ToEHr16qXyZdO7Tp06hXbt2qFKlSr59vGSJUvQu3dvlbK1a9eiZ8+eWL58Obp27arW/U9hnDp1Cvv378fs2bOxYMECREVFYcSIEfkmzADgyJEjqFq1Klq3bq1Snp6ejhkzZmDs2LF5flmbl/v372Pw4MF4+fIlvvrqKyxatAjx8fH4v//7P7x8+bLY+0yaNAmffPIJ1q1bB0tLSwQGBmLEiBFo1KgRQkND0axZMwQHB+Ovv/4C8PYL5JEjR+LUqVOYOHEiwsLCUKNGDXh5eeVKNq1YsQJyuRzLly/HtGnT8Ntvv2Hx4sUA8r6XvXr1KqZMmYIePXpgw4YNmDlzJv78809MnjxZ5bj29vaoXr16ke+9iYqKU+eI3mNqaoo1a9ZAT08PwNu1clatWoV///0X5ubmKnVfv36Nbdu2YcSIEfDz8wMAdOrUCZ9//jn++OMPtT73jz/+wLlz57BixQrljUGnTp2QlpaGkJAQ9O3bN89vrXJufHJutj7kwIEDuHPnDnbs2AEHBwfl58hkMqxZswaDBw+GmZnZB/uiXr16ymmETZo0yfPzt27dipSUFBw6dEg5HLply5bo2bOnGj3zH5lMhvnz56N+/foAgKdPn0IqlWL69OlwdHQEADg5OeHhw4fYtWsXgLdTHStXrqwyXe79G56wsDBIJBJs2bIFFStWBPD2Jrhv375YunQp9u7dq6zbuXNnTJ8+HQDQvn17nD9/Hr/99hsmT56MmJgYJCYmYvjw4cq+bdCgAXbt2oXk5GTlsd/3559/ok+fPgW2PS0tDTNmzICBgUGuNcLs7OwAvE06WllZFdyJREREOjZkyBA8f/4c4eHhWLBgAQDA3NwcHTt2xPDhw9GyZUsAb+9DunfvjsOHDyMgIAAikQjPnj3DH3/8ofwjHHg7uvyLL77Ap59+CgBISkrCpEmTMHLkSOWyARYWFnB3d8fNmzeVo7yLst/333+PV69eYefOnahduzYAwNnZGb1798bKlSsRGhqqvN+oUaOGymhzmUyG6dOno0OHDsoye3t7/PDDD8oY/vrrL8TGxir75X2vX7/G69evYWlpmWvb++sYiUQi5T0cALRo0QJjxoxRvp8wYUKh738KIzU1Ffv371feHzZo0AADBgzAwYMHMXTo0Dz3+fPPP2FnZ5drLamQkBAYGRlh7NixKsnBgoSFhcHQ0BCbN2+GiYkJgLf3at26dcPGjRuV929F3cfd3V15XhgZGSnXGfP39wcANG/eHKdOncK1a9fQokUL/PDDD4iKisKePXuU57SzszOGDx+OkJAQ7N+/X3nsRo0aITg4WPn+r7/+UiYg87qX3bdvHwwMDODt7Q0DAwMAgJmZGSIjI6FQKJT9KRKJ0Lx581yJLaKSxhFNVO69f2Gzs7NTuSjn3IykpaXl2vf69evIyspC165dVcrd3NzUjuPChQsQiUTo3LkzZDKZ8vXJJ5/g+fPnuHv3bp775QyLlsvlhfqcS5cuoXbt2spEyLsxZ2RkqEzzUqcv8vLnn38qv0nJUbt2bdjb2xdq//cZGhqqrJFVvXp1bNmyBY6OjkhISMCFCxewbds2XLt2Ta2nrVy6dAldunRRSQTp6+ujT58+iIyMVJka9/70xBo1aigTVzY2NqhcuTLGjRuHwMBAnD59GlWrVsW0adNURiu9Ky0tDS9fviwwUZicnIwxY8bg5s2bWLZsWa5jVaxYEaampsrpgkRERKVdQEAAzp07h2XLlsHDwwMmJiY4cuQIBg0ahO+//15Zz8PDA48ePcKVK1cAAD/88AMMDQ1zfWn17r2FhYUFANVrds6XaElJScXa78KFC2jSpAmqV6+uvFcTi8VwdnYu1JeMjRo1Unnv7u6OK1euKK/hBw4cQL169ZRfoL0vv/u9Bw8eoFmzZiqv99dufP+z1bn/KQx7e3uV+7SmTZuibt26yn+7vMTHx+e6B7p48SJ2796N4ODgPL9kzc+ff/4JJycnGBoaKv9tTExM4OjomO+/jTr75HWu5CSQACi/kM4ZxX7hwgVUrVoVzZo1Ux47OzsbXbp0wc2bN/H69WvlvnndXxZ0v92mTRukp6fD1dUVK1aswNWrV9GxY0eMHz8+1982tWvX5j0iaR1HNJGgGRkZKReszktmZiakUqlK2fvvC0rk5FwgKleurFKe12KGH/Lq1SsoFIpcQ4dzPHv2DE2aNMlVnvNtWkJCQq4pVTmeP38Oc3Nz6Ovr4/Xr18qL47tyyt69AVOnL/KSmJiIZs2a5SqvWrUqnj9/XqhjvKtKlSq5Lp6HDx/G8uXL8fjxY5iZmaFx48YwNDRU67gF9YlCoUBycrKyLK8+yRnub2xsjO3bt2Pt2rU4duwYdu3aBalUCjc3N8yePVv5jdO7cvo7v+Hxjx8/xpgxYxAXF4dvvvkm3ymSUqlUJU4iIqLSrlKlSujbt69yjcLbt29j2rRpCAkJgZubG8zNzdGuXTvUqVMHhw4dQps2bXDo0CG4uLjkuh7njEZ5V2HuB9Td79WrV8qkTl7S0tJyxfau9+83evfujcWLF+Pw4cPw8vLC8ePHMXLkyHz3Nzc3h5GRER49eqRSXrNmTZX1NlevXp1rXZ73P1ud+5/CqFatWq6yKlWq5EruvSs5OVmlv1JSUjBz5kx4e3ujYcOGkMlkyvtOuVwOmUyWb/Lp1atXOHbsWJ7rm75/r16UffI6Vwr6t3716hWeP3+e77ny/PlzVKpUKc/jvHt/mRd7e3t8++232Lx5M8LDw7Fu3TpUrVoV3t7euc4fqVTKBdVJ65hoIkGzsLDId/G7zMxMJCYm5nmBLaycby5evHiBBg0aKMvfT27lJEeys7OVI4Te/5aoYsWKMDIywpYtW/L8rJzpYu9r164dJBIJzpw5g86dO+dZZ+zYsUhLS8Px48dRqVIlPHjwIFednMTP+9MDi6NGjRp5zonPqyw7O1vlfUHz+XNcuXIF06dPx7Bhw1QWPP/6669VFrL8kEqVKikXXHzXu33y/uKK+WnQoAGWLl2K7Oxs/PXXX/jhhx+wc+dO1KlTR2W4eo6c/s7rJiwqKgpeXl7IyMjAxo0b4eTklO/nJiUlafTfjoiIqCQ8ffoU7u7uCAgIUE4Xy5HzpNycxZjNzc0hEokwYMAAbNmyBUOHDkVMTEy+08q0oWLFimjbti2mTZuW5/YKFSqodTxjY2P06tULx48fR5MmTZCUlIT+/fsXuE/Xrl1x+vRpJCcnK5MfFSpUUE6lB/4biVWQwtz/5PxcmPu0vL7cffHiRYFPbDYzM1NJgty8eROPHj3C6tWrsXr1apW6s2fPxuzZsxEdHZ3nsSpWrIgOHTrk+ZTl/JJTRdmnsCpWrAhLS0uEhITkub2wy17kp1OnTsplNv78809s2bIFixcvRqtWrVRGWvEekXSBU+dI0Nq2bYuEhATlonzv+uWXX5CdnY127doV+fj29vaQSqW5vgU5ffq0yvucm4DHjx8ry65du5Yr1tTUVCgUCtjZ2Slfd+/exerVq3PNu89hamoKDw8P7NmzJ892/vjjj7h16xb69esH4O1Q20ePHuVKxBw+fBgSiQQtWrQoZOs/rE2bNoiIiFAZvfT8+XNcv35dpZ6JiUmu+ffv909eIiIiIJfL4e/vr0wyZWdnK4c653wD9u5TV/KL89dff1W50cnOzsbRo0dhZ2dX6JvGn376Ce3atcPz58+hp6cHe3t7zJs3D6ampvmuL1ChQgVUrVpV5dwA3p4rnp6eEIlE2LlzZ4FJplevXiEtLQ21atUqVJxERES6YmFhAX19fezYsQMZGRm5tt+7dw8GBgYqX7C5u7srn5hraWmZa/q/NrVt2xb379+HlZWVyv3a4cOHsXfvXuUXih+693iXh4cH7ty5g++++w7t2rX74PV87NixyM7OxqxZs5CZmZlre3p6+geftAYU7v4n5x723fuYrKysPO85IyIikJiYqHx/69YtPHz4EO3bt883htq1a6vcAzVr1gz79u1Tea1duxYAMH78+FxPSX5X27ZtERMTgyZNmij/XZo3b47Nmzfj559/1tg+hdW2bVs8fvwYVapUUTlXLly4gI0bN6osT/Eh759PS5YsgYeHBxQKBaRSKbp06aJcTyqve8qcGRBE2sIRTSRovXv3xvfffw9vb2+MHTsWzZo1g1wux7Vr17Bx40b06dMn36lqhWFiYgJfX18sW7YMUqkUH330Ec6dO5cr8dS5c2cEBwdjzpw58Pb2xpMnTxAWFgZjY2OVOm3atIGPjw98fHxgbW2Nv/76C6tWrULHjh3zHfILvH0KRmRkJEaOHImhQ4fCyckJMpkM586dw549e+Ds7Kx8gtnAgQOxY8cOjB8/Hv7+/qhbty5Onz6N/fv3Y/z48TA1NS1yf7xvxIgR2L59O0aPHg1fX18Ab4dyZ2ZmqkyB+/jjj3H06FG0aNECVlZWOHjwYJ6jrt6XkxRbsGAB3N3dkZSUhG3btiEqKgrA22/bTExMYGpqihcvXuDMmTN5Tj8cP348zp49ixEjRmDMmDGoUKECtm3bhvj4eGzcuLHQ7W3dujXkcjl8fX0xZswYGBsb4/jx43jz5g169OiR734fffRRrsTawoUL8fLlS8yfPx/JyckqyTkTExM0bNhQ+T4nadixY8dCx0pERKQLenp6mDdvHnx9feHu7o6hQ4fC2toaaWlpOH/+PLZv346AgADllCLg7bSwDh064Pfff8fEiRN1GD0watQo/PDDDxg1ahQ8PT1hbm6OY8eOYc+ePZg5c6aynqmpKSIiInD58uV811vK4eDggAYNGuDSpUv5jn55l42NDZYtW4bp06ejf//++Oyzz2BrawuZTIaIiAjs27cPL168yPX02vcV5v6nUqVKsLe3x7Zt21C/fn2Ym5tj69atSE9PzzXtPy0tDd7e3hg3bhxSUlKwYsUKNGrUSDk1Mi8fffQRduzYoVzA2sTERGVkFgDl+kK1a9fOte1dPj4+GDx4MMaOHYv/+7//g4GBAXbv3o1ffvkFoaGhGtunsAYOHIht27bh888/xxdffIGaNWvijz/+wIYNGzBs2DBIJJJCH+v9e9n27dtj06ZNmDFjBtzc3JCVlYWNGzfCzMxM5Ut0hUKBiIgIDB8+vFhtIVIXE00kaBKJBNu2bcO6deuwd+9ehIaGQiwWo379+pg4cSKGDRtW7M/w9vaGsbExvvvuO2zbtg0ODg744osvVIb7WllZYcmSJVi7di3GjBkDa2trBAUFISgoSFlHLBbj22+/xcqVK7F+/Xq8fPkS1atXx6hRo5RJmvyYmppi69at2LZtm3JtIIVCgfr162PmzJn49NNPlcN/pVIptm7dimXLliE0NBTJyclo0KABFi1aBA8Pj2L3x/txbdmyBYsWLcK0adNgbGyMIUOGwMjISOXmZObMmZDJZFi6dCn09fXRu3dvTJ48GV9++WWBx3dycsLcuXOxadMm/PTTT7CwsICTkxPCwsLg6+uLq1evonPnzhg4cCDOnDkDX19f+Pv753rcr42NDXbs2IHly5dj1qxZEIlEaNGihXKh8cKqVq0aNm7ciJUrV2L27NlIS0uDjY0NVq1aVeDIuZ49e+LIkSN49uwZqlWrhszMTPz2228AgMDAwFz127Zti61btyrfnz17Fi1atOC3VUREVCZ8/PHH2LNnj3JtmcTERFSoUAFNmzbFihUr8vxypkuXLvjjjz8+OK2spFWvXh27du3CsmXLMG/ePGRkZMDS0jLXfdQXX3yBNWvWwNvbO8/1f9738ccf4/nz57kW8M5Pt27dcPjwYezcuRP79u3Do0ePoFAoULduXfTu3RuDBw/O88l07yrs/c9XX32FoKAgzJkzByYmJvDw8IC9vX2up9I5OjqiXbt2mD17NgDgk08+wbRp0wocGd6jRw+sXr0akZGRxR5V37hxY2zfvh0rVqzAtGnToFAo0KhRI6xevTrXg3uKs09hGRkZYfv27Vi2bBmWLl2KN2/eoHbt2pg8eTI8PT3VOtb797JjxoxBSEgIvvvuO+UC4A4ODtiyZYvKtMm//voLr169Qq9evYrVFiJ1iRQFrTJGREVy4MABzJw5E6dOnSr2/Ouy7MaNG3j16pXK2lEymQwff/wx+vTpo/LNX3mmUCjQr18/9OzZ84NJxfelpKSgU6dO+Prrr9GtW7cSipCIiEi3vL29oaenh3Xr1uk6FI1TKBRwdXWFk5MT5syZo+twiiRnxMy7X4QV1hdffIHKlStj8eLFmg6r3Js5cyZev36NNWvW6DoUKme4RhMRlZiEhASMHTsWq1atwsWLF/Hbb79h/PjxePPmDT777DNdh1dqiEQiTJkyBTt37lT7CS87duxAo0aNiv2tGxERUWm0evVqTJkyBWfPnsXo0aN1HY5GJScnIywsDF988QXi4uIKfNqckE2cOBEnTpxAQkKCrkMRlISEBJw8eRIBAQG6DoXKIU6dI6IS4+LiglevXmHHjh0IDw+HRCJBy5YtsW3bNlhbW+s6vFLF2dkZXbt2xfr16zF58uRC7ZOYmIgtW7Zg27ZtKmteERERCcXp06fx4MEDTJ06FW3atNF1OBplaGiIXbt2QS6XY9GiRQU+nU3IbG1tMXbsWISEhGD58uW6DkcwQkJCMGbMGNja2uo6FCqHSu3UudTUVAQFBeH06dOQyWTo2rUrAgMDVRZPfldgYCD279+vsqjajBkzMGjQIADAwYMHsWbNGjx//hwNGjTAnDlzYG9vr5W2EBERlTeJiYkYNGgQFi5cmO9TE8+cOYOQkBDEx8ejZs2amDZtGrp06aLcvmHDBmzduhVJSUmws7PD/Pnz0aBBA201gYiIiIiKoNROnQsKCsLjx49x4sQJnDx5Eo8fPy7wKQyRkZEICgpCRESE8pWTZLp48SKCgoLw1Vdf4fLly3Bzc8O4ceOQlpamreYQERGVG1evXsWgQYPwzz//5FsnLi4Ofn5+CAgIwJUrV+Dn54cJEybg6dOnAN5+QbR161aEh4fj4sWLaNasGfz9/VFKvx8jIiIiov8plYmmtLQ0HDlyBP7+/jAzM0OVKlUwZcoUHDhwIM/kUGZmJu7cuYPmzZvneby9e/eiT58+cHBwgEQiwahRo5SPIiUiIiLNOXjwIKZMmfLBR5AfPHgQjo6O6Natm/Jpk23atMHu3bsBAHv27MGQIUNgY2MDAwMDTJ48GQkJCbh48aI2mkFERERERaSzRFN6ejoePHiQ7ysrKwuNGjVS1re2tkZ6ejri4uJyHSsqKgoymQyhoaHo0KEDevbsiW+//RZyuRwAEBMTo3IsAGjYsCGioqJKtI1ERETlTceOHfHzzz+jd+/eBdb70LX5/e0SiQSWlpa8dhMRlQGJiYno3r07vxwgKqd0thj4jRs3MGLEiDy35ayMb2RkpCyTSqUA3j7K+31v3rxB27ZtMXz4cCxfvhx///03fH19IRaL4eXlhZSUFOX+OQwNDZGamqqp5hARERGAqlWrFqreh67NvHYTEZVNV69exYwZMwqcPk1EwqazRJOTkxOio6Pz3Hb79m2sXLkSaWlpysW/c6bMmZiY5Kr/0Ucf4aOPPlK+b9GiBUaOHIljx47By8sLUqkU6enpKvukp6fD3NxcrZgVCgWf7ERERBr1hcgS6xRxJfoZFew9i7V/ZsR3GorkP/ldm3Ou+x/aTlSePX/+pkj7icUiVK5sjMTEFMjlXO/sfeyf/BW2b44f/xEbN66Dj48/AgNn4dWr1CKfr2UJz52CFad/qlatWEJRUUnSWaKpIFZWVpBIJIiJiUHLli0BALGxscph8+/75Zdf8OLFCwwePFhZlpmZCUNDQwCAjY0N7t69q7JPTEwMnJ2d1YpLJBJh+Mz1iI57rGaLSg9by5rYGjy2zLcD+K8t4UP88SQqVtfhFEuNxtYYvSNUUG0R0jkmpLYI6RwTSlu0QSTW08rnqKNRo0a4deuWSllMTIxyvcWca3fOU+iysrIQFxeXa7odERWeWCyCSCSCWCziH8N5YP/kr7B907ZtO3Tv3gv6+voIDJylxQh1i+dOwdg/5U+pTDRJpVK4uLggJCQEK1euBACEhISgb9++yuTRuxQKBYKDg1G/fn20a9cO169fx5YtWzBz5kwAgIeHB3x9feHi4gIHBwds374dL1++RPfu3dWOLTruMa5Hlf1hoEJpBwA8iYpFfMStD1csA4TUFiGdY0Jqi5DOMSG1paSVxkSTm5sbNm3ahGPHjqFHjx44efIkLl26hNmzZwMA3N3dsWrVKjg7O8PKygorVqyAhYUFHB0ddRw5ERHlp0oVC12HQESlQKlMNAFAYGAglixZAldXV2RlZaFr166YM2eOcnufPn3g6uqKL774At27d8fMmTMxb948PH36FBYWFvDz80O/fv0AAO3bt0dgYKBye8OGDbFhwwaYmZnpqHVERETlj729PebPnw83NzdYW1tj9erVCAkJwezZs1G7dm2sWrUKVlZWAN5+SfTmzRv4+voiMTERdnZ2WL9+PSQSiY5bQUREREQFKbWJJhMTEwQFBSEoKCjP7UePHlV5P3jwYJWpc+/r16+fMvFERERUnuhqRNP7azFGRESovO/UqRM6deqU574ikQienp7w9Cze+lJEREREpF2lNtFEREREmlEap84RERERkTAx0URERCRwIj0mmoiIiIhIO5hoIiIiEjgxRzQREVFRyOUQ338I/WfPkSXRg35WNuTVqkJuVQcQiwvc9fffr2gpSCIqbZhoIiIiIiIiov8oFNCPjIZ+3EMgNQ1ifX0oJHoQZ2WjwoME4NYdyCzrQGZnC4hEuo6WiEoZJpqIiIgEjms0ERFRoSkUkFyIgN4/CRDpiYH3pl+L9PSAjEzo346BKCUVWe3smWwiIhUFj3ckIiKiMk8k1ivWi4j+k5iYiO7du+PixYv51jlz5gxcXV3RqlUruLi44Ndff1XZvmHDBjg7O6NVq1YYPnw47t27V9JhExWafmT0f0mmAoj0xNB7kAD9m9EF1iOi8oeJJiIiIoETicXFehHRW1evXsWgQYPwzz//5FsnLi4Ofn5+CAgIwJUrV+Dn54cJEybg6dOnAICDBw9i69atCA8Px8WLF9GsWTP4+/tDoVBoqxlE+ZPLoR/38INJphwiPTH07z8E5PISDoyIyhLePRIREQkcRzQRFd/BgwcxZcoUTJw48YP1HB0d0a1bN+jr66N3795o06YNdu/eDQDYs2cPhgwZAhsbGxgYGGDy5MlISEgocIQUkbaI779dk0ktqWlv9yMi+h+u0URERCRwTBYRFV/Hjh3h6uoKfX39ApNNMTExaNSokUpZw4YNERUVpdzu7e2t3CaRSGBpaYmoqCi0a9euULGIxSKIxeqviaP3v1EqeoUcrVLesH8A/WfPIdbP/SdizhJMb/+reu6J9PVR4dlzyGwtSzy+0ornTsHYP+UPE01ERERERB9QtWrVQtVLSUmBVCpVKTM0NERqamqhthdG5crGEBVj8WVTU+mHK5Vj5bl/siR6UEjy/3JCXz/vbSKJHiTmxiUVVplRns+dwmD/lB9MNBEREQkcRzQRaY9UKkV6erpKWXp6OoyNjQu1vTASE1OKPKLJ1FSKpKQ0ZGdzTZ33sX8A/axsiLOyc5WLRG+TTDJZNvJaTkyelY3kf1O0EGHpxHOnYMXpH3MmMMskJpqIiIgETqTHRBORtjRq1Ai3bt1SKYuJiUHz5s0BADY2Nrh79y66dOkCAMjKykJcXFyu6XYFkcsVkMuLvnh4drYcMhn/GM5Pee4febWqqPAgIY/rxtvEpkKBXAvXK7KzkVmtKuTltM/eVZ7PncJg/5QfnCRJREQkcFwMnEh73NzccOnSJRw7dgwymQzHjh3DpUuX0K9fPwCAu7s7tm3bhqioKGRkZGDZsmWwsLCAo6OjjiMnAuRWdQAjNac3GUnf7kdE9D9MNBEREQkcE01EJcve3h6HDx8GAFhbW2P16tVYv3492rRpgzVr1mDVqlWwsrICAHh4eGDUqFHw9fVFu3btcPv2baxfvx4SiUSXTSB6SyyGzLIOFIWc3qSQyyGzqgOI+WclEf2HU+eIiIiIiNQQHR2t8j4iIkLlfadOndCpU6c89xWJRPD09ISnp2eJxUdUHDI7W4hSUqH3IAGiAp4SpsiWI7t+Lcia22oxOiIqC5hoIiIiEjgxRyUREVFhiUTIamcPhYkR9O8/BFLTINL/789GRXY2YCSFzKrO2yRTMZ6ASETCxEQTERGRwHH6GxERqUUkgsyuMWTNGkF8/yEqPHsOkUQP8qz/LfzN6XJEVAAmmoiIiASOiSYiIioSsRhy63qQ2VpCYm6M5H9T+HQ5IvogJpqIiIgEjokmIiIiItIWjnckIiIiIiIiIiKN4IgmIiIigeOIJiIiIiLSFiaaiIiIBI6JJiIiIiLSFiaaiIiIBE6kx0QTEREREWkHE01EREQCxxFNRERERKQtXAyciIiIiIiIiIg0giOaiIiIBI4jmoiIiIhIW5hoIiIiEjgmmoiIiIhIW5hoIiIiEjixWKTrEIiIiIionGCiiYiISOBETDQRERERkZYw0URERCRwIhETTURERESkHaU20ZSamoqgoCCcPn0aMpkMXbt2RWBgIIyNjfOsf+LECaxZswbx8fEwMzPDwIED4ePjA7H47YP1XFxckJCQoHwPAPv27YO1tbVW2kNERFQevHz5EnPmzMGlS5egp6cHNzc3TJ8+Hfr6qrccXl5euHr1qkpZamoqBg0ahAULFkAul8PBwQEKhUIlUXb+/HkYGRlppS1EREREpL5Sm2gKCgrC48ePceLECWRnZ2PChAkICQlBYGBgrro3b97EtGnT8M0336Bz5864f/8+vL29YWRkBE9PTyQnJ+P+/fs4deoUateurYPWEBER6Y4212iaMGECqlevjnPnzuHFixcYN24cNm/eDC8vL5V6GzduVHm/b98+hIWFYfz48QCAmJgYZGVl4dq1a6hQoYLW4iciIiKi4hF/uIr2paWl4ciRI/D394eZmRmqVKmCKVOm4MCBA0hLS8tV/9GjRxg8eDC6dOkCsVgMa2trdO/eHZcvXwbwNhFlZmbGJBMREZVLIrGoWK/CevDgAS5duoSpU6dCKpWibt268PHxwfbt2wvc7969ewgKCkJISAiqVasGAIiMjIStrS2TTERERERljM5GNKWnp+Pp06d5bktLS0NWVhYaNWqkLLO2tkZ6ejri4uLQpEkTlfo9e/ZEz549VY7922+/wdXVFcDbm1WpVIphw4bh7t27qF27Nvz8/NClSxe147a1rKn2PqVJTvxlvR3Af22o0bjsT3/MaYOQ2iKkc0xIbRHSOSaUtsRH3Crxz9HWYuB3796FmZkZqlevriyztrZGQkICkpKSYGpqmud+8+fPR//+/eHo6Kgsi4yMREZGBtzd3fHo0SNYW1tj8uTJaN26dYm3g4iIiIiKTmeJphs3bmDEiBF5bgsICAAAlTUYpFIpACAlJaXA4yYnJyMgIACGhoYYNWoUgLeLoNrZ2WHSpEmoVasWfvrpJ/j5+WHbtm1o1aqVWnFvDR6rVv3SSijtAIDRO0J1HYLGCKktQjrHhNQWIZ1jQmnL5Z2HS/wzxFpaDDwlJUV5vc6R8z41NTXPRNOVK1dw48YNhISEqJQbGhqiRYsWCAgIQKVKlbB9+3aMHj0ahw8fRt26dUuuEURERERULDpLNDk5OSE6OjrPbbdv38bKlSuRlpamXPw7Z8qciYlJvse8d+8e/P39UaVKFWzZskVZ9/11Idzc3PDjjz/ixIkTaieahs9cj+i4x2rtU5rYWtbE1uCxZb4dwH9tCR/ijydRsboOp1hqNLbG6B2hgmqLkM4xIbVFSOeYUNoiJEZGRrmmuOe8z+9hHrt374aLiwuqVq2qUj5jxgyV96NHj8aBAwdw5swZDBs2TINRExEREZEmlcrFwK2srCCRSBATE4OWLVsCAGJjYyGRSGBpaZnnPmfOnMGkSZPw2WefYfLkySpPtwkPD0fTpk3Rvn17ZVlmZiYMDAzUji067jGuR/2j9n6ljVDaAQBPomK1MvVEG4TUFiGdY0Jqi5DOMSG1paRpa+qcjY0NXr16hRcvXsDCwgLA2+t3jRo1ULFixVz1ZTIZTp06hdWrV+fatmLFCvTs2RNNmzZVlhX12k1ERERE2lMqFwOXSqVwcXFBSEgIEhMTkZiYiJCQEPTt2xeGhoa56l+/fh2+vr6YOXNmno9Qfvz4MebPn4/4+HjIZDLs27cPERERGDBggLaaREREpDPaWgzc0tISDg4OWLx4MZKTkxEfH481a9bAw8Mjz/rR0dHIyMjIc92lO3fuYNGiRXj+/DkyMzMRFhaG5ORkdO/evcj9QEREREQlr1QmmgAgMDAQlpaWcHV1Ra9evVCnTh3MnTtXub1Pnz5Yt24dAGDdunWQyWRYtGgR7O3tla+cKXPTpk2Ds7MzhgwZAkdHR+zatQvffvst6tevr5O2ERERaZNYLCrWSx2hoaGQyWTo2rUrPvvsM3Tq1Ak+Pj4AAHt7exw+/N+aVPHx8ahUqVKeo5SCg4NRr1499OvXD05OTrh06RI2bdoEMzOzYvUFEREREZWsUjl1Dni7FlNQUBCCgoLy3H706FHlzzkJp/xUqFABs2bNwqxZszQaIxERUVkg0uLXShYWFggNzXuh9oiICJX3vXr1Qq9evfKsa2ZmhuDgYI3HR0REREQlq9SOaCIiIiIiIiIiorKl1I5oIiIiIs0QibSzGDiRkL18+RJz5szBpUuXoKenBzc3tzzXBvXy8sLVq1dVylJTUzFo0CAsWLAAcrkcDg4OUCgUKv9vnj9/HkZGRlppCxERUUlioomIiEjg1F1niYhymzBhAqpXr45z587hxYsXGDduHDZv3qxcEzTHxo0bVd7v27cPYWFhGD9+PAAgJiYGWVlZuHbtGipUqKC1+ImIiLSFU+eIiIgETltPnSMSqgcPHuDSpUuYOnUqpFIp6tatCx8fH2zfvr3A/e7du4egoCCEhISgWrVqAIDIyEjY2toyyURERILFEU1EREQCx2QRUfHcvXsXZmZmqF69urLM2toaCQkJSEpKgqmpaZ77zZ8/H/3794ejo6OyLDIyEhkZGXB3d8ejR49gbW2NyZMno3Xr1oWOpyhPhAQAPT2xyn9JFfsnf+ybgrF/Csb+KX+YaCIiIhI4MddoIiqWlJQUSKVSlbKc96mpqXkmmq5cuYIbN24gJCREpdzQ0BAtWrRAQEAAKlWqhO3bt2P06NE4fPgw6tatW6h4Klc2Ltbaa6am0g9XKsfYP/lj3xSM/VMw9k/5wUQTEREREVEBjIyMkJaWplKW897Y2DjPfXbv3g0XFxdUrVpVpXzGjBkq70ePHo0DBw7gzJkzGDZsWKHiSUxMKfKIJlNTKZKS0pCdLVd7f6Fj/+SPfVMw9k/BitM/5uZ5/46l0o2JJiIiIoHj1Dmi4rGxscGrV6/w4sULWFhYAABiY2NRo0YNVKxYMVd9mUyGU6dOYfXq1bm2rVixAj179kTTpk2VZZmZmTAwMCh0PHK5AnK5oggteSs7Ww6ZjH8M54f9kz/2TcHYPwVj/5QfnCRJREQkcFwMnKh4LC0t4eDggMWLFyM5ORnx8fFYs2YNPDw88qwfHR2NjIyMPNddunPnDhYtWoTnz58jMzMTYWFhSE5ORvfu3Uu6GURERFrBRBMREZHA5SwcXNQXEQGhoaGQyWTo2rUrPvvsM3Tq1Ak+Pj4AAHt7exw+fFhZNz4+HpUqVcpzlFJwcDDq1auHfv36wcnJCZcuXcKmTZtgZmamraYQERGVKE6dIyIiErjiLBpMRG9ZWFggNDQ0z20REREq73v16oVevXrlWdfMzAzBwcEaj4+IiKi04IgmIiIiIiIiIiLSCI5oIiIiEjgRv1YiIiIiIi1hoomIiEjguM4SEREREWkLE01EREQCxyfHEREREZG2MNFEREQkcFwMnIiIiIi0has2EBERERERERGRRnBEExERkcBxjSYiIiIi0hYmmoiIiASOazQRERERkbYw0URERCRwekw0EREREZGWMNFEREQkcEw0EREREZG2cDFwIiIiIiIiIiLSCI5oIiIiEjiOaCIiIiIibWGiiYiISOCYaCIiIiIibWGiiYiISOCYaCIiIiIibWGiiYiISOD0mWgiIiIiIi1hoomIiEjgOKKJiIiIiLSFT50jIiIiIiIiIiKN4IgmIiIigeOIJiIiIiLSllI9ounly5fw8fGBo6MjnJycsGjRIshksjzrnjlzBq6urmjVqhVcXFzw66+/qmzfsGEDnJ2d0apVKwwfPhz37t3TRhOIiIh0Tk8sLtZLHepcu728vGBnZwd7e3vl6+zZs8rtvHYTERERlT2lOtE0YcIEGBkZ4dy5c9i3bx8uXLiAzZs356oXFxcHPz8/BAQE4MqVK/Dz88OECRPw9OlTAMDBgwexdetWhIeH4+LFi2jWrBn8/f2hUCi03CIiIiLt0xOLivVSR2Gv3QBw8+ZNhIeHIyIiQvlydnYGwGs3ERERUVlVahNNDx48wKVLlzB16lRIpVLUrVsXPj4+2L59e666Bw8ehKOjI7p16wZ9fX307t0bbdq0we7duwEAe/bswZAhQ2BjYwMDAwNMnjwZCQkJuHjxorabRUREpHXaSjSpc+2Oj4/H69ev0bRp0zyPxWs3ERERUdlUahNNd+/ehZmZGapXr64ss7a2RkJCApKSklTqxsTEoFGjRiplDRs2RFRUVJ7bJRIJLC0tlduJiIio+NS5dkdGRsLY2BgTJ05Eu3bt0LdvX+zbt0+5ndduIiIiorKp1C4GnpKSAqlUqlKW8z41NRWmpqYF1jU0NERqamqhtqvD1rKm2vuUJjnxl/V2AP+1oUZjax1HUnw5bRBSW4R0jgmpLUI6x4TSlviIWyX+OdpaDFyda3dmZiZatWqFiRMnwsbGBhcvXoSfnx+MjY3h4uKi0Ws3EREREWlPqU00GRkZIS0tTaUs572xsbFKuVQqRXp6ukpZenq6st6Htqtja/BYtfcpjYTSDgAYvSNU1yFojJDaIqRzTEhtEdI5JpS2XN55uMQ/Q0+knUSTOtfu/v37o3///sr3HTt2RP/+/XH8+HG4uLho9NpNRERERNpTahNNNjY2ePXqFV68eAELCwsAQGxsLGrUqIGKFSuq1G3UqBFu3VL9RjgmJgbNmzdXHuvu3bvo0qULACArKwtxcXG5ptsVxvCZ6xEd97goTSoVbC1rYmvw2DLfDuC/toQP8ceTqFhdh1MsNRpbY/SOUEG1RUjnmJDaIqRzTCht0QZtjWhS59q9b98+5eilHJmZmTAwMFAeS1PXbiIiIiLSnlKbaLK0tISDgwMWL16MBQsW4N9//8WaNWvg4eGRq66bmxs2bdqEY8eOoUePHjh58iQuXbqE2bNnAwDc3d2xatUqODs7w8rKCitWrICFhQUcHR3Vjis67jGuR/1T7PbpmlDaAQBPomK1MvVEG4TUFiGdY0Jqi5DOMSG1paRpK9GkzrU7OTkZy5cvR/369dG4cWOcPXsWP/74I8LDwwFo9tpNRERERNpTahcDB4DQ0FDIZDJ07doVn332GTp16gQfHx8AgL29PQ4ffjvdwNraGqtXr8b69evRpk0brFmzBqtWrYKVlRUAwMPDA6NGjYKvry/atWuH27dvY/369ZBIJDprGxERkRAV9to9cuRIDBs2DOPHj4e9vT1CQkKwZMkSZSKJ124qbV6+fAkfHx84OjrCyckJixYtgkwmy7Oul5cX7OzsYG9vr3ydPXtWuX3Dhg1wdnZGq1atMHz4cNy7d09bzSAiIipxpXZEEwBYWFggNDTvNTgiIiJU3nfq1AmdOnXKs65IJIKnpyc8PT01HiMREVFpp6+lEU1A4a/dIpEIPj4+yiTU+3jtptJmwoQJqF69Os6dO4cXL15g3Lhx2Lx5M7y8vHLVvXnzJsLDw9G2bdtc2w4ePIitW7ciPDwc9erVw4oVK+Dv748jR45ApKX11IiIiEpSqR7RRERERMWnJxYV60VU3j148ACXLl3C1KlTIZVKUbduXfj4+GD79u256sbHx+P169do2rRpnsfas2cPhgwZAhsbGxgYGGDy5MlISEjAxYsXS7oZREREWlGqRzQRERFR8TFZRFQ8d+/ehZmZGapXr64ss7a2RkJCApKSkmBqaqosj4yMhLGxMSZOnIjIyEhYWFhg1KhRyrXKYmJi4O3trawvkUhgaWmJqKgotGvXrlDxiMUiiIvw/7Wenljlv6SK/ZM/9k3B2D8FY/+UP0w0ERERCRwTTUTFk5KSAqlUqlKW8z41NVUl0ZSZmYlWrVph4sSJsLGxwcWLF+Hn56d8ymJexzI0NERqamqh46lc2bhY0+xMTaUfrlSOsX/yx74pGPunYOyf8oOJJiIiIoFjoomoeIyMjJCWlqZSlvPe2NhYpbx///7o37+/8n3Hjh3Rv39/HD9+HC4uLpBKpUhPT1fZJz09PddxCpKYmFLkEU2mplIkJaUhO1uu9v5Cx/7JH/umYOyfghWnf8zNC/+7kUoPJpqIiIiIiApgY2ODV69e4cWLF7CwsAAAxMbGokaNGqhYsaJK3X379ilHL+XIzMyEgYGB8lh3795Fly5dAABZWVmIi4tDo0aNCh2PXK6AXK4ocnuys+WQyfjHcH7YP/lj3xSM/VMw9k/5wUmSREREAsfFwImKx9LSEg4ODli8eDGSk5MRHx+PNWvWKNddeldycjKCgoJw+/ZtyOVy/Pbbb/jxxx8xaNAgAIC7uzu2bduGqKgoZGRkYNmyZbCwsICjo6O2m0VERFQiOKKJiIhI4JgsIiq+0NBQLFiwAF27doVYLEb//v3h4+MDALC3t8f8+fPh5uaGkSNHIjU1FePHj8fLly9Rt25dLFmyRJlI8vDwwJs3b+Dr64vExETY2dlh/fr1kEgkumweERGRxjDRREREJHBMNBEVn4WFBUJDQ/PcFhERofxZJBLBx8dHmYR6n0gkgqenJzw9PUskTiIiIl1joomIiEjgmGgiIiIiIm3hGk1ERERERERERKQRHNFEREQkcBzRRERERETawkQTERGRwDHRRERERETawkQTERGRwOmJmGgiIiIiIu1goomIiEjgxEw0EREREZGWcDFwIiIiIiIiIiLSCI5oIiIiEjg9DmgiIiIiIi1hoomIiEjgxFwMnIiIiIi0hFPniIiIBE5PJCrWi6i0u3z5Mnbt2oXk5GTExMQgKytL1yERERGVWxzRREREJHBcDJyEKjk5GV5eXrh+/TpEIhE++ugjhISEIC4uDps3b0aNGjV0HSIREVG5wxFNRERERFQmLV++HADw888/w9DQEAAwbdo0GBkZ4euvv9ZlaEREROUWE01EREQCpycq3ouotPr1118xbdo01K1bV1nWoEEDBAYG4sKFCzqMjIiIqPzi1DkiIiKB42LgJFSJiYmoWrVqrnITExOkpaXpICIiIiLiiCYiIiKBE4tExXoRlVZ2dnY4duxYrvItW7agadOmOoiIiIiIOKKJiIhI4Dj9jYRq0qRJ+PzzzxEREQGZTIa1a9ciJiYGt2/fRnh4uK7DIyIiKpc4oomIiEjgOKKJhKp169bYvXs3TE1NUb9+fVy/fh01a9bE9u3b4eTkpOvwiIiIyiWOaCIiIiKiMqtGjRrw8fGBpaUlAODYsWOoV6+eboMiIiIqx9RONCUkJORZLhKJIJFIULlyZYjFHChFRERUWuhxMXASqL/++gve3t4YOHAgpk+fDgBYunQpsrKy8N1336FRo0Y6jpCIiKj8UTvR9Mknn0BUwDD6ChUqoE+fPpg3bx4qVKhQrOCIiIio+Dj9jYTq66+/Ro8ePTBx4kRl2c8//4y5c+fiq6++wnfffafD6IiIiMontYceLVq0CKamppg9ezYOHjyIgwcPIjAwEObm5hg/fjwWLlyIq1evYtWqVSURLxEREalJT1S8F1FpdevWLYwZM0bly019fX2MGTMG169f111gRERE5ZjaI5o2b96MefPmwcXFRVnWuHFjWFhYYNWqVfjhhx9gYWGBWbNmYfLkyRoNloiIiNTHEU0kVCYmJvjnn39Qt25dlfInT57A0NBQR1ERERGVb2onmv755x80adIkV3nDhg1x//59AIClpSVevnxZ7OBevnyJOXPm4NKlS9DT04ObmxumT58Off3cYe/cuRObN2/Gs2fPUK1aNYwYMQJDhw4FAMjlcjg4OEChUKhM+zt//jyMjIyKHScRERG9xWs3aVPPnj0xb948zJ8/Hy1atIBIJEJkZCQWLFiA7t276zo8IiKickntRJO1tTX279+fa7TS/v37Ub9+fQDA33//jerVqxc7uAkTJqB69eo4d+4cXrx4gXHjxmHz5s3w8vJSqffLL79g+fLl2LBhA1q2bInr169jzJgxsLCwQM+ePRETE4OsrCxcu3aN60YREVG5o83FwHntJm2aPHky4uPj4enpqZKQ7N69O6ZNm6bDyIiIiMovtRNNkyZNwhdffIHLly/D3t4ecrkcN27cwM2bNxEWFoa///4b06dPx+eff16swB48eIBLly7h7NmzkEqlqFu3Lnx8fLB06dJcN6tPnz6Ft7c3WrVqBQCwt7eHk5MTLl++jJ49eyIyMhK2tra8USUionJJW3kmXrtJ26RSKdavX4/79+/jzp070NfXh7W1NSwtLXUdGhERUbmldqKpY8eO2Lt3LzZv3ozff/8d+vr6aNy4MYKCgmBjY4PIyEhMmzYNn376abECu3v3LszMzFRGRllbWyMhIQFJSUkwNTVVlucMs8/x8uVLXL58GTNnzgQAREZGIiMjA+7u7nj06BGsra0xefJktG7dWu24bC1rFrFFpUNO/GW9HcB/bajR2FrHkRRfThuE1BYhnWNCaouQzjGhtCU+4laJf46eltZoKq3XbhI+KysrWFlZqZQlJCSgVq1aGvsMTgslIiIqHLUTTQDQpEkTLFmyJM9tdnZ2sLOzK1ZQAJCSkgKpVKpSlvM+NTVV5Wb1Xc+fP8fYsWPRvHlz9O3bFwBgaGiIFi1aICAgAJUqVcL27dsxevRoHD58ONfikR+yNXhsEVpT+gilHQAwekeorkPQGCG1RUjnmJDaIqRzTChtubzzcIl/hrYWAy+t124SrocPH2LJkiWIjo5GdnY2AEChUCAzMxOJiYm4ffu2xj6L00KJiIgKp1CJppkzZ2L27NkwMTFRftOYn+DgYI0EZmRkhLS0NJWynPfGxsZ57nP9+nUEBATA0dERwcHBym+YZsyYoVJv9OjROHDgAM6cOYNhw4apFdfwmesRHfdYrX1KE1vLmtgaPLbMtwP4ry3hQ/zxJCpW1+EUS43G1hi9I1RQbRHSOSaktgjpHBNKW4SktF67SbgWLlyI+/fvw8XFBeHh4fD09MT9+/fx888/Y8GCBRr7HE4LJSIiKrxCJZoePnwIuVyu/FkbbGxs8OrVK7x48QIWFhYAgNjYWNSoUQMVK1bMVX/fvn1YuHAh/P394enpqbJtxYoV6NmzJ5o2baosy8zMhIGBgdpxRcc9xvWof9Ter7QRSjsA4ElUrFamnmiDkNoipHNMSG0R0jkmpLaUND2xdj6ntF67SbiuXLmCtWvXok2bNjh79iy6deuGFi1aYMWKFThz5gw+++wzjXwOp4USEREVXqESTXZ2dlAoFACAJUuWoEaNGhCLS/au1dLSEg4ODli8eDEWLFiAf//9F2vWrIGHh0euuidOnMC8efOwdu1adOrUKdf2O3fu4MqVK/jmm29QqVIlfPvtt0hOTuZjb4mIqFzQ1tQ5XrtJ2zIyMlCnTh0AQIMGDRAdHY0WLVqgf//+GD58uMY+p7RNCxWLRRAXYZV/vf9lnfW0lX0uY9g/+WPfFIz9UzD2T/lTqETTtm3bMHz4cFSqVAldu3bF+fPnUbly5ZKODaGhoViwYAG6du0KsViM/v37w8fHB8DbYcjz58+Hm5sbwsLCkJ2dDX9/f5X9XV1dsWDBAgQHB2PJkiXo168f0tLSYGdnh02bNsHMzKzE20BERKRr2loMHChb1+6EhATExsaiTZs2SElJQZUqVTR2bNKOunXr4s6dO6hZsyYsLS3x999/A3i74HZKSorGPqe0TQutXNlYZSFxdZmaSj9cqRxj/+SPfVMw9k/B2D/lR6ESTdWrV8eXX34Je3t7KBQKbNy4Md+nYowfP15jwVlYWCA0NO/FXiMiIpQ/HzlypMDjmJmZaWztKCIiorJGWyOagLJx7c7MzMT06dNx/PhxiMVinDhxAkuWLMGbN28QFhaW5zQ/Kp0GDhyIadOm4auvvkLnzp0xfPhw1KpVC+fPn4etra3GPqe0TQtNTEwp8ogmU1MpkpLSkJ0tV3t/oWP/5I99UzD2T8GK0z/m5nkn86l0K1Siae7cuVi+fDkOHjwIkUiEY8eO5Tl1TiQSaTTRRERERKRpa9euRVRUFL7//nt88cUXAIARI0Zg1qxZWLp0qUYXkaaS5eXlBX19fYhEIrRo0QLjx4/H2rVrUbNmTSxdulRjn1PapoXK5QrI5Yoityc7Ww6ZjH8M54f9kz/2TcHYPwVj/5QfhUo0derUSXmhbNy4Mfbv38/h5URERGUEl0RQdfToUcybNw9OTk7KsrZt2yIoKAhTp05loqmMGTVqlPJnb29veHt7l8jnlKVpoURERLpUqETTu6KiokoiDiIiIioh2pw6VxY8ffoU9erVy1Ves2ZNJCUl6SAiKo7r169j69atuHPnDvT09NCsWTOMGjUKNjY2Gv2csjAtlIiIqDRQO9H08uVLrFixAlevXkVWVpbyaXQ5Tp06pbHgiIiIqPiYZ1JlbW2NP/74A5999plK+Y8//oiGDRvqKCoqitOnT2P8+PFo0aIFOnToALlcjuvXr2PgwIHYtGkTHB0ddR0iERFRuaN2omnu3Lm4cuUK+vfvz8UyiYiIygAxmGl6l5+fHyZMmIA7d+4gOzsbBw8exL1793Dy5EmsWLFC1+GRGlasWIHRo0dj8uTJKuVLlizB0qVLsXv3bh1FRkREVH6pnWg6f/48Vq9ejY8++qgk4iEiIiIN44gmVV26dMGqVauwfv166OnpITw8HDY2NsqngVHZ8c8//8Dd3T1X+aBBg7Bjxw4dRERERERqJ5qMjIxQs2bNkoiFiIiISCucnZ3h7Oys6zComJo1a4YLFy7A0tJSpfzmzZuwtrbWTVBERETlnNqJpv79+yM8PBwLFiyAnp5eScREREREGiTmiCYVhw4dKnB7//79tRIHFZ+bmxuWLl2K+/fvo23bttDX10dkZCS+//57DBo0SOXfmv+uRERE2qF2ounFixc4fvw4fv31V9SrVw8VKlRQ2b5lyxaNBUdERETFx6lzqmbMmJFnuYGBAWrUqMGERBkyb948AG/vP9+/Bw0PD1f+LBKJ+O9KRESkJWonmvT09NC3b9+SiIWIiIhKABcDVxUVFaXyXiaT4cGDB5g7dy6GDh2qo6ioKN7/tyQiIiLdUzvRFBwcXBJxEBERUQnhiKaC6evrw9raGjNmzMCUKVPQu3dvXYdERSCTyRAdHQ0LCwtUr15d1+EQERGVW2onmgDgyZMn2L59O6Kjo6Gvrw8bGxsMGjQItWrV0nR8RERERFphZGSEx48f6zoMKoRDhw5hy5YtCAsLQ61atRAbGwtvb288fvwYIpEIAwYM4HqiREREOqJ2ounOnTsYNmwYDA0N0aJFC2RnZ+PAgQPYvn07du7cCRsbm5KIk4iIiIqIi4Grunz5ssp7hUKBN2/eYPPmzWjcuLGOoqLCOnHiBGbOnInevXvD0NAQwNt1t5KTk7F+/XoYGxtj9uzZ2LJlCz7//HMdR0tERFT+qJ1o+vrrr9GuXTuEhIQoFwLPyMjA1KlTERISgvXr12s8SCIiIio65plUDR8+HCKRCAqFQqW8bt26CAkJ0VFUVFhbt27F+PHj4evrC+Dtl6CRkZHw8fGBs7MzAGDChAlYvXo1E01EREQ6oHai6erVq9i9e7fK0+YMDAzg4+ODYcOGaTQ4IiIiKj4xF2lScerUqVxlEokE1apV00E0pK6oqCjMnz9f+f7ChQsQiUTo0qWLsqxJkyb4559/dBEeERFRuad2osnY2BiZmZm5yvMqIyIiIt1jnklV7dq1dR0CFUNWVpbKF55Xr16FiYkJmjdvriyTyWSQSCS6CI+IiKjcUzvR1K5dO3z99dcIDQ2FmZkZACAxMREhISFo166dpuMjIiIiKrYRI0YUuu6WLVtKMBIqLisrK9y8eRN169ZFeno6/vjjD3To0AGidzKqv/76KywtLXUXJBERUTmmdqJpypQpGDx4MLp06QJLS0uIRCLcv38fpqam2LZtW0nESERERMUg1nUApQBHMQmHu7s7Fi1ahKdPn+LPP/9EcnIy/u///g/A29FOp06dwtq1azFhwgTdBkpERFROqZ1oqlGjBo4ePYoffvgBd+/ehUKhgIeHB1xdXVGxYsWSiJGIiIiKQcS5cwgODtZ1CKQhw4cPR2JiItatWwexWIwZM2agffv2AN7+O+/YsQP9+vXD0KFDdRxp2XT37h2sXv0NoqOjIJFI0KaNE/z8JilnMhARlZR//03E118vQkTEVejp6aFHj96YN+9L6OurnbYgHSvSv5ixsTGGDBkCAHj27BkXzyQiIirFxMwz5ZKYmIj79+9DLpcDABQKBTIzM3Hjxg3l08yo9AoICEBAQECu8kGDBmHQoEGwtbXVQVRlX0ZGOqZM8Yeb2wAsXboSqakpWLgwEIsXz8fXX6/QdXhEJHBz585E1arVcOjQT3j58gVmzJiEzZs3w8vLS9ehkZoKPZr+woUL6N27N+7cuaNSHhgYiB49euDKlSsaD46IiIiKTyQq3ktojh49ii5dumDo0KEYMWIEhg8fjpEjR8Lb2xsHDx7UdXhUDLa2tkwyFcPTp0/QsGEjjBrlBYlEgkqVzNCv30DcuHFN16ERkcA9fBiPiIir8PHxh6GhIWrXroNRo7ywfft2XYdGRVCoRNPNmzcxduxY1KhRA1KpVGXb559/jrp168LLyytXEoqIiIh0T1zMl9CsW7cOffv2xU8//YSKFSti3759WL16NapVqwY/Pz9dh0ekM/XqWWLZslDo6ekpy3799RRsbZvoMCoiKg/u34+FqWklWFhUVZZZWjZAQkICkpKSdBgZFUWh7h/Xrl2L7t2747vvvkPdunVVtrVt2xYbN25Ehw4dEBYWViJBEhEREWlKXFwcRo8eDUtLSzRp0gSJiYn45JNPMHv2bHz//fe6Do+oVFAoFPj22zU4f/4cAgKm6DocIhK41NRUGBoaqpTlvE9NTdVFSFQMhUo03bhxA56envluF4lEGD16NK5fv66puIiIiEhDRCJRsV5CY2BgAIlEAgCwtLTE3bt3AQDNmzfHgwcPdBkaUamQkpKML7+chpMnj2P16g2wtm6o65CISOAMDaXIyEhXKUtPf/ve2NhYFyFRMRQq0ZSSkgJTU9MC61SvXp1D2oiIiEohsah4L6Fp0aIFdu3aBQBo2LAhzp8/DwCIiYlRJqCobPj111+VC7qTZjx69BBeXiOQkpKCjRu3MslERFrRoIE1Xr9+jcTEl8qyuLh7qFGjBp9uXwYVKtFUp04dREdHF1gnKioK1atX10hQREREpDmiYr6ExtfXF9u3b0d4eDj69u2LW7duoU+fPpgwYQK6deum6/BIDRMmTICzszOWLl2K2NhYXYdT5iUlJcHf/wvY2bXE8uVhMDMz03VIRFRO1K1bDy1atMLKlcuQmpqChIRH2Lx5Izw8PHQdGhWBfmEq9erVC6tWrUL79u3zHLaWmpqKVatW4ZNPPtF4gERERFQ8QhyVpK6ZM2fCw8MDDg4OcHBwwIkTJ5CZmQlzc3Ps2LEDO3fuRM2aNTF8+HBdh0pqOH/+PI4ePYpDhw4hPDwcLVq0wMCBA9G3b1+YmJjoOrwy59ixw3j69AlOn/4Zv/76i8q2n38+p6OoiKi8WLhwCZYv/xqffuoGkUiMXr36wMfHR9dhUREUKtHk6emJo0ePon///hg1ahRatmyJSpUq4d9//8X169exZcsWiMVijBkzpqTjJSIiIlLb9evXcejQIdSvXx+ffvopBgwYoByJbW1tjS+//FLHEVJRmJiYYNCgQRg0aBAePHiAI0eOYMeOHfjqq6/QrVs3eHh4oF27droOU/fkcojvP4T+s+fIkuhBPysb8mpVIbeqA4j/m+AwePAwDB48TIeBEpGgFPJ3T47Klatg4cIlKmXvPgWTyo5CJZqkUil27NiB+fPnIzg4GNnZ2cptenp66NWrF6ZPnw5zc/MSC5SIiIiKRogLeqvr+PHj+Ouvv3Dw4EFs2LABK1asQJcuXeDh4QFnZ2f2kQDUqlULtra2uH//Pv755x9cvXoVv/76K2rVqoWlS5eicePGug5R+xQK6EdGQz/uIZCaBrG+PhQSPYizslHhQQJw6w5klnUgs7MF+P8AEWkKf/eUe4VaowkAzMzMsGLFCpw9exYbNmxASEgIvvvuO/z5558ICQlB1apVNR7cy5cv4ePjA0dHRzg5OWHRokWQyWR51vXy8oKdnR3s7e2Vr7Nnzyq3b9iwAc7OzmjVqhWGDx+Oe/fuaTxeIiKi0kibi4Grc+0+c+YMXF1d0apVK7i4uODXX39V2a7pa3eLFi0QGBiIc+fOYfny5ZDL5fD19UWXLl2wcuVKPHz4sFjHJ924du0aAgMD0bFjR0ydOhUikQhr167Fr7/+inPnzsHa2hoTJkwo9ueU5nM7TwoFJBcioH87BqKMTIjeGxUg0tODKCMT+rdjIPkzAlAoNB8DEZU//N1DUCPRlKNy5cro2LEj+vTpg/bt25fo/PcJEybAyMgI586dw759+3DhwgVs3rw5z7o3b95EeHg4IiIilC9nZ2cAwMGDB7F161aEh4fj4sWLaNasGfz9/aHgSU1EROWANhcDL+y1Oy4uDn5+fggICMCVK1fg5+eHCRMm4OnTpwBK9totkUjQo0cPrF69GmfPnsXo0aNx4cIF9OzZE56ensU+PmlP9+7dMXToUNy8eRMBAQE4d+4cQkJC0L59ewCAkZERXFxc8PLlyw8c6cPKwrn9Lv3IaOj9kwCRXsG3+yI9MfQeJED/ZsEP/iEiKgz+7iGgCIkmbXnw4AEuXbqEqVOnQiqVom7duvDx8cH27dtz1Y2Pj8fr16/RtGnTPI+1Z88eDBkyBDY2NjAwMMDkyZORkJCAixcvlnQziIiIdE4sEhXrVVjqXLsPHjwIR0dHdOvWDfr6+ujduzfatGmD3bt3A9Detbty5cpwc3ODh4cHbG1t8eeff2r0+FSyunTpgh9++AH79+/HkCFD8nwEdvv27XHixIlifU6ZO7flcujHPfzgH3o5RHpi6N9/CMjlmouBiMof/u6h/ym1iaa7d+/CzMxMuVAn8HaxzoSEBCQlJanUjYyMhLGxMSZOnIh27dqhb9++2Ldvn3J7TEwMGjVqpHwvkUhgaWmJqKiokm8IERFROaHOtfv9azMANGzYUHltLulrd2ZmJo4fPw4fHx907NgRYWFh6Ny5c7ETEqRdp06dQlZWVoF1TE1NUbly5WJ9Tlk6twFAfP/tuihqSU17ux8RURHxdw/lKNRi4LqQkpICqVSqUpbzPjU1FaampsryzMxMtGrVChMnToSNjQ0uXrwIPz8/GBsbw8XFJc9jGRoaIjU1Ve24bC1rFqE1pUdO/GW9HcB/bajR2FrHkRRfThuE1BYhnWNCaouQzjGhtCU+4laJf4621tlU59r9oWuzJq/dORQKBS5cuIAjR47g559/Rnp6Oj755BOEhYWhU6dOEOfxBBwq3TIyMnKdJyWhtJ3bYrEI4gIWUNN/9hxi/dy3+Tm/C97+V3V/kb4+Kjx7DpmtZaHjEBq9/43C0CvkaIzyhH1TMPbPW/zdQzlKbaLJyMgIaWmq2dCc98bGxirl/fv3R//+/ZXvO3bsiP79++P48eNwcXGBVCpFenq6yj7p6em5jlMYW4PHqr1PaSSUdgDA6B2hug5BY4TUFiGdY0Jqi5DOMaG0ZdzfyVhXwp8h0tKahOpcuz90bdbktRsAgoODcfToUbx8+RLW1tbw9fVFv379ij3ShXRr6NChGD9+PIYOHYp69erB0NBQZXubNm008jml7dyuXNm4wCclZkn0oJDk/0hwff28t4kkepCYF+3/MSExNS355GVZxb4pWHnvH/7uoRxFSjSdOXMGGzduxP3797F7927s378f9erVU0n2FJeNjQ1evXqFFy9ewMLCAgAQGxuLGjVq5Jp/v2/fPuXopRyZmZkwMDBQHuvu3bvo0qULACArKwtxcXG5hjUXRvgQfzyJii1qs3SuRmNrjN4RWubbAfzXluEz1yM67rGuwykWW8ua2Bo8VlBtEdI5JqS2COkcE0pbtEKhnbUP1Ll2N2rUCLduqY7miomJQfPmzZXH0tS1GwD27t2L3r17w8PDA61atSrSMaj0WblyJQAgKCgo1zaRSIS///5bI59T2s7txMSUgkc0ZWVDnJWdq1wkevuHnkyWneeDnuRZ2Uj+N6XQcQiNnp4YpqZSJCWlITuba8a8i31TMPbPWyXxu8ecCagySe1E0/nz5zF+/Hj06dMHN27cgFwuR3Z2NmbNmoXs7Gy4u7trJDBLS0s4ODhg8eLFWLBgAf7991+sWbMGHh4eueomJydj+fLlqF+/Pho3boyzZ8/ixx9/RHh4OADA3d0dq1atgrOzM6ysrLBixQpYWFjA0dFR7bieRMVqZZpDSRNKOwAgOu4xrkf9o+swNEJIbRHSOSaktgjpHBNSW0qaSEuJJnWu3W5ubti0aROOHTuGHj164OTJk7h06RJmz54NQLPXbuDt/Ys2pliRdp06dUorn1Pazm25XAG5PP+RivJqVVHhQUKux4rnTFlRKJDrKXeK7GxkVqsKuaz8/pGcIztbDhn7IU/sm4KV9/7h7x7KofYk0lWrVmHy5Mn46quvoPe/E2jixImYPHkyNm3apNHgQkNDIZPJ0LVrV3z22Wfo1KkTfHx8AAD29vY4fPgwAGDkyJEYNmwYxo8fD3t7e4SEhGDJkiXKC7aHhwdGjRoFX19ftGvXDrdv38b69eshkUg0Gi8REVF5V9hrt7W1NVavXo3169ejTZs2WLNmDVatWgUrKysAmr92M8kkTGFhYahUqRJq166t8jI2NsbixYs1+lml9dzOi9yqDmCk5jlvJH27HxFREfF3D+VQe0RTdHQ0vv7661zlPXr0QGioZtfLsLCwyPeYERERyp9FIhF8fHyUF/v3iUQieHp6wtPTU6PxERERlQlaGtEEFP7aDQCdOnVCp06d8qzLazfl5+rVq4iPjwcAHDp0CM2aNYOJiYlKndjYWPzxxx8a/dwydW6LxZBZ1oH+7ZhCPWZcIZdDZlUH4IL4RFQc/N1D/6N2oqlixYp4+vQp6tWrp1J+9+5dVKpUSWOBERERkYZoaTFwIm0QiUSYMWOG8ueFCxfmqmNkZITRo0drO7RSRWZnC1FKKvQeJBT4B58iW47s+rUga26rxeiISKj4u4eAIiSaXF1dsWjRIixatAgikQgpKSk4c+YMgoKC0Lt375KIkYiIiIpDiyOaiEpa69atERUVBQBo3Lgxfv/9d+UC3fQOkQhZ7eyhMDGC/v2HQGoaRO88dlyRnQ0YSSGzqvP2D70CnmJHRFRo/N1DKEKiacKECXjy5Ily0e8BAwZAoVDg448/xsSJEzUeIBERERWPthYDL0uuXr2Kq1evIisrK9fCpOPHj9dRVKSunIQT8PaJwxUqVNBhNKWQSASZXWPImjWC+P5DVHj2HCKJHuRZ/1t8l1NWiKgk8HdPuad2okkikWDZsmXw9/fH33//DblcjkaNGqFhw4YlER8REREVFxNNKr799lssX74clSpVgrGx6mOTRSIRE01lzM6dO7FhwwY8efIEJ06cwMaNG1G1alX+O75LLIbcuh5ktpaQmBsj+d8UPuGJiEoef/eUW2onmnLUr18f9evX12QsRERERCVu27ZtGDduHAICAnQdChXTkSNHsGzZMowcORIbN24E8PapbyEhITAwMIC3t7eOIyQiIip/1E40NW7cGKIC5lH+/fffxQqIiIiINIwjmlS8fv0a/fv313UYpAHfffcdZs+ejQEDBuC7774DAIwYMQIVK1bE2rVrmWgiIiLSAbUTTYsXL1ZJNMlkMsTFxeHgwYPKJ4AQERFRKcJEkwoHBwdERkZyZLYA3L9/H46OjrnKHR0d8eTJEx1ERERERGonmgYOHJhneePGjfHDDz/Azc2t2EERERGRBsmZaHqXi4sLFixYgJs3b6JBgwa5FpDmaKeyw8LCAvfu3UPdunVVyq9du4Zq1arpKCoiIqLyrchrNL2vdevWmDNnjqYOR0RERBrCp86pyrlf2bx5c65tIpGIiaYyZNCgQZg/f75yVP29e/dw7tw5rFy5EqNGjdJtcEREROWUxhJNR48eRaVKlTR1OCIiIqISERUVpesQSEO8vb3x5s0bTJ06FRkZGRg7diz09fUxePBgjB07VtfhERERlUtqJ5o++eQTlTWaFAoFUlJSkJSUhIkTJ2o0OCIiItIAjmgiAZs0aRLGjRuHmJgYKBQKNGjQACYmJroOi4iIqNxSO9E0YMCAXE+dk0gkaN26Ndq0aaOxwIiIiEhDFApdR6BzTZo0we+//44qVarwCboC8/r1azx48AAZGRkAVP/9eG9KRESkfWonmvz8/EoiDiIiIiopHNGExYsXo2LFisqfC0o0Udlx6NAhBAYGIjMzE4r3EqoikYhJQyIiIh1QO9F06NChQtflYppERES6x8XA347IzpHfE3Sp7Pnmm2/Qr18/jBo1CgYGBroOh4iIiFCERNOaNWvw5MkTZGZmomLFiqhQoQJevnwJkUik8k0Sn9pCREREpdXx48fx/fff486dO9DT00PTpk3h7e2Njh076jo0UsPr16/h6ekJS0tLXYdCRERE/yNWdwdvb2/Ur18fhw4dwuXLl3H+/Hn88ssvaNWqFYKCghAVFYWoqCgOVSYiIiotFPLivQRm3759mDx5MmrVqoUJEybA19cXlSpVwtixY/HLL7/oOjxSQ48ePXDmzBldh0FERETvKNKIpmXLlqFx48bKsjp16uDLL7/E2LFj8emnn2o0QCIiIiomASaLimPDhg2YNm0aRo0apSwbNWoUNm7ciNDQUHTr1k13wZFapk6dij59+uDkyZOoW7durrW3goODdRQZERFR+aV2oun169eQSqW5yrOyspCamqqRoIiIiEiDmGhS8eTJE3z88ce5yrt3745Vq1ZpPyAqsuDgYKSkpCAzMxOPHj3SdThERESEIiSa2rdvj/nz52Pp0qWoW7cuACA2NhYLFixA9+7dNR4gERERFQ8XA1fVvn17HDt2DD4+Pirlv//+O+zt7XUUFRXF6dOnsXr1anTu3FnXoRAREdH/qJ1omjt3LkaPHo0ePXrA1NQUAJCUlISWLVti9uzZGg+QiIiIiknORNO77O3tsWbNGty6dQtt2rSBRCJBZGQkfvzxRwwYMABhYWHKuuPHj9dhpPQhxsbGqFevnq7DICIioneonWiqXr06Dh06hPPnzyMmJgYA0LRpU7Rv317jwRERERFp2u7du1GlShX8/fffKg8vqVatGs6fP698LxKJmGgq5caOHYtvvvkGixYtgomJia7DISIiIhQh0QQA+vr66Ny5M4cpExERlQUKha4jKFVOnz6t6xBIQ06fPo0rV66gXbt2qFKlCvT1VW9tT506paPIiIiIyq9CJZqaNGmC33//HVWqVEHjxo1zPdHjXe9+M0hERESlANdoyldiYiKuXLkCCwsLtG7dWtfhkJocHBzg4OCg6zCIiIjoHYVKNC1evBgVK1ZU/lxQoomIiIhKFy4G/tbq1auxZcsW7NmzB/Xr18e1a9cwZswYJCcnA3i7SPjatWthaGio40ipsDi1kYiIqPQpVKJpwIAByp8HDhxYYsEQERFRCWCiCbt378b69esxatQoVKlSBQAwa9YsGBkZYffu3TAxMYGfnx/Wr1+PgIAAHUdL6jhz5gzu3LmDjIwMlXKRSARfX18dRUVERFR+qb1Gk1wux48//oirV68iKysLinfWfRCJRFi8eLFGAyQiIiIqrr1792LGjBkYMmQIAOCvv/5CXFwcpkyZAmtrawDAuHHj8NVXXzHRVIYsXLgQ27Ztg4WFBSpUqKCyjYkmIiIi3VA70bRkyRJs2bIFjRs35tM9iIiIygKOaEJsbCw6dOigfP/nn39CJBKpPNikYcOGSEhI0EV4VERHjhzB/PnzMWjQoBL9nNTUVAQFBeH06dOQyWTo2rUrAgMDYWxsnGf9EydOYM2aNYiPj4eZmRkGDhwIHx8fiMViAICLiwsSEhKU7wFg3759yqQnERFRWaZ2oumHH37Al19+iaFDh5ZEPERERKRp8mxdR1AqvLvG5NWrV1G5cmXY2Ngoy1JSUiCVSnURGhWRvr4+2rZtW+KfExQUhMePH+PEiRPIzs7GhAkTEBISgsDAwFx1b968iWnTpuGbb75B586dcf/+fXh7e8PIyAienp5ITk7G/fv3cerUKdSuXbvEYyciItI28YerqMrIyECnTp1KIhYiIiIqAQq5vFgvIbC1tcXly5cBAElJSbh48SI6duyoUuf48eNo1KiRLsKjIho2bBjWrl2LzMzMEvuMtLQ0HDlyBP7+/jAzM0OVKlUwZcoUHDhwAGlpabnqP3r0CIMHD0aXLl0gFothbW2N7t27K8+/mzdvwszMjEkmIiISLLVHNHXq1Annzp3jiCYiIqKygiOaMHToUMydOxfR0dGIiIhAZmYmhg8fDgB49uwZjhw5gvDwcCxatEjHkZI6XFxcMGjQIDg4OKBq1aq5nox86tSpQh0nPT0dT58+zXNbWloasrKyVJKQ1tbWSE9PR1xcHJo0aaJSv2fPnujZs6fKsX/77Te4uroCACIjIyGVSjFs2DDcvXsXtWvXhp+fH7p06VKoWImIiEo7tRNNdnZ2+Prrr3HhwgVYW1tDIpGobNfkY2ZfvnyJOXPm4NKlS9DT04ObmxumT58OfX3VsL28vHD16lWVstTUVAwaNAgLFiyAXC6Hg4MDFAqFyg3I+fPnYWRkpLF4iYiIKH+6XOfG1dUVGRkZ2LlzJ8RiMb755hs0b94cAPDtt99i165d8Pb2Rr9+/Uqg5VRSZsyYAVNTU3h4eBRr2uONGzcwYsSIPLflLA7/7j1jzmelpKQUeNzk5GQEBATA0NAQo0aNAvB2CqednR0mTZqEWrVq4aeffoKfnx+2bduGVq1aFSpesVgEsVj04Yrv0dMTq/yXVLF/8se+KRj7p2Dsn/JH7UTTzp07UaVKFdy+fRu3b99W2SYSiTSaaJowYQKqV6+Oc+fO4cWLFxg3bhw2b94MLy8vlXobN25Ueb9v3z6EhYUpY4mJiUFWVhauXbuW64kkREREgldKRjTpep0bDw8PeHh45Cr39vaGr68vzM3Ni91G0q7bt29jz549aNy4cbGO4+TkhOjo6Hw/Y+XKlUhLS1MmRXOmzBX0YJx79+7B398fVapUwZYtW5R137+PdXNzw48//ogTJ04UOtFUubJxrtFb6jA15VpkBWH/5I99UzD2T8HYP+WH2omm06dPl0QcuTx48ACXLl3C2bNnIZVKUbduXfj4+GDp0qW5LtDvunfvHoKCghAeHo5q1aoBeDtE2dbWlkkmIiIqlxTZuk805axzs2XLFpiZmQEApkyZghEjRmDatGm5RqO8u84NAJV1bjw9PTW6zk316tWLfQzSjbp165bo+kwAYGVlBYlEgpiYGLRs2RLA26cYSiQSWFpa5rnPmTNnMGnSJHz22WeYPHmyymj88PBwNG3aFO3bt1eWZWZmwsDAoNAxJSamFHlEk6mpFElJacjOFsb6a5rE/skf+6Zg7J+CFad/zM3zHvVMpZvaiSZtuXv3LszMzFRu/qytrZGQkICkpCSYmprmud/8+fPRv39/ODo6KssiIyORkZEBd3d3PHr0CNbW1pg8eTJat26tdlw1Gpftx87mxF/W2wH81wZby5o6jqT4ctogpLYI6RwTUluEdI4JpS3Xo/4p+Q/S0oLeXOeGtG3u3LmYN28eAgICYGVllWt5hVq1ahX7M6RSKVxcXBASEoKVK1cCAEJCQtC3b18YGhrmqn/9+nX4+vpi3rx5eY6ge/z4Mfbu3YsNGzagZs2aOHToECIiIjB//vxCxySXKyCXK4rcpuxsOWQy/jGcH/ZP/tg3BWP/FIz9U34UKtH0ySef5Dk8V19fH2ZmZrCzs8OoUaNQp04djQWW1yOGc96npqbmmWi6cuUKbty4gZCQEJVyQ0NDtGjRAgEBAahUqRK2b9+O0aNH4/Dhw6hbt65acY3eEapmS0onobQDALYGj9V1CBojpLYI6RwTUluEdI4JpS27f7pY8h+ipalzZW2dGyr7PD09kZ2djbFjx6rcq+asy/n3339r5HMCAwOxZMkSuLq6IisrC127dsWcOXOU2/v06QNXV1d88cUXWLduHWQyGRYtWqSyuLyDgwM2btyIadOmQSwWY8iQIXjz5g0aNmyIb7/9FvXr19dIrERERLomUigUH/w6ZNWqVXkmmuRyOV69eoUrV67gyZMn2L17N6ysrDQS2M8//4wvv/wSFy/+dwMeHR0NNzc3XLlyBRUrVsy1z9SpU6Gvr4/g4OAPHr9Pnz74v//7PwwbNkytuMKH+ONJVKxa+5QmNRpbY/SO0DLfDuC/tgyfuR7RcY91HU6x2FrWxNbgsYJqi5DOMSG1RUjnmFDasvuni8iM+K5EP0d29Wix9td36FPsGG7fvo0BAwbg2rVrynVukpOT4eDggB9++CHfNXbeXecmNDQUlSpVyvczxowZA2tra0yfPr3Y8VLZcOnSpQK3t23bVkuRaNfz52+KtJ++vhjm5sb4998UjirIA/snf+ybgrF/Clac/qlaNfff/VT6FWpEk5+f3wfrTJo0CatWrcLy5cuLHRQA2NjY4NWrV3jx4gUsLCwAvJ0PX6NGjTyTTDKZDKdOncLq1atzbVuxYgV69uyJpk2bKsvUnQuf40lULOIjbqm9X2kjlHYAQHTcY+1MPdECIbVFSOeYkNoipHNMSG0paYpSsBh4aVznhsq+O3fuoG/fvsp1v4iIiEj3NPZ8wWHDhn3wWyV1WFpawsHBAYsXL0ZycjLi4+OxZs2aPOe6A29HO2VkZOS57tKdO3ewaNEiPH/+HJmZmQgLC0NycjK6d++usXiJiIhKLbm8eC8NeHedm8TERCQmJhZqnZuZM2di+vTpudbeefz4MebPn4/4+HjIZDLs27cPERERGDBggEbipbJhw4YNcHZ2RkBAAM6ePYtCDNQnIiKiEqaxRFONGjXw+vVrTR0OABAaGgqZTIauXbvis88+Q6dOneDj4wMAsLe3x+HDh5V14+PjUalSpTy/yQwODka9evXQr18/ODk54dKlS9i0aRO//SIionJBIc8u1ktTAgMDYWlpCVdXV/Tq1Qt16tTB3Llzldv79OmDdevWAYDKOjf29vbKV86TZ6dNmwZnZ2cMGTIEjo6O2LVrF9e5KYd+++03rFmzBhKJBP7+/nB2dsayZctw7949XYdGRERUbmnsqXNPnjxB5cqVNXU4AICFhQVCQ/NehDciIkLlfa9evdCrV68865qZmRVq3SYiIiJBKgVT5wDAxMQEQUFBCAoKynP70aP/rSWVk3DKT4UKFTBr1izMmjVLozFS2SISidCxY0d07NgRKSkpOHnyJE6ePImBAweicePG+PTTT9GnT588R80RERFRydDIiKbMzEysW7dOZZ0EIiIiKiVKwdQ5opKWmpqK169f499//0VmZibEYjHWr1+Prl274sKFC7oOj4iIqNwo1IimmTNn5lmuUCiQlJSEv/76CyKRCHv27NFocERERERE+cnIyMDJkyfxww8/4MKFC7CwsED//v3x9ddfo169egCA+fPnY8aMGThz5oyOoyUiIiofCpVoevjwYZ7lEokElSpVgqenJ9zd3Qt85DARERHphiK7dEydI9K09u3bQyaT4eOPP8aaNWvQqVMniMXiXHVOnTqlowiJiIjKn0IlmrZu3VrScRAREVFJKSVrNBFpWkBAANzc3GBubp5vnU8++QQ9evTQYlRERETlm8YWAyciIqJSiokmEqiRI0cqf05MTMSVK1dQpUoVODg4KMv19Xm7S0REpE0aWQyciIiISi+FXF6sF1Fps3r1ajg5OeHBgwcAgGvXrqFHjx7w9/fH0KFD8fnnnyM9PV3HURIREZVPTDQRERERUZmxe/durF+/HoMGDUKVKlUAALNmzYKRkRGOHj2KM2fOICUlBevXr9dxpEREROUTE01ERERCJ88u3ouoFNm7dy9mzJiBSZMmwcTEBH/99Rfi4uIwYsQIWFtbo3r16hg3bhyOHTum61CJiIjKJU5aJyIiEjomi0hAYmNj0aFDB+X7P//8EyKRCJ07d1aWNWzYEAkJCboIj4iIqNxjoomIiEjguM4SCY1IJFL+fPXqVVSuXBk2NjbKspSUFEilUl2ERkREVO5x6hwREZHQceocCYitrS0uX74MAEhKSsLFixfRsWNHlTrHjx9Ho0aNdBEeERFRuccRTURERERUZgwdOhRz585FdHQ0IiIikJmZieHDhwMAnj17hiNHjiA8PByLFi3ScaRERETlExNNREREQsdRSSQgrq6uyMjIwM6dOyEWi/HNN9+gefPmAIBvv/0Wu3btgre3N/r166fjSImIiMonJpqIiIgETpHNRBMJi4eHBzw8PHKVe3t7w9fXF+bm5jqIioiIiAAmmoiIiISPi4FTOVG9enVdh0BERFTuMdFEREQkdJw6R0RERERawkQTERGRwCmYaCIiIiIiLRHrOgAiIiIiIiIiIhIGjmgiIiISOAXXaCIiIiIiLWGiiYiISOAU2Uw0EREREZF2MNFEREQkcEw0EREREZG2MNFEREQkcJw6R0RERETawsXAiYiIiIgKkJqaipkzZ8LJyQkODg6YNm0aUlJS8q0fGBiI5s2bw97eXvnavXu3cvvBgwfRvXt3tGrVCgMHDkRERIQ2mkFERKQVTDQREREJnCJbXqwXUXkXFBSEx48f48SJEzh58iQeP36MkJCQfOtHRkYiKCgIERERytegQYMAABcvXkRQUBC++uorXL58GW5ubhg3bhzS0tK01RwiIqISxUQTERGRwDHRRFR0aWlpOHLkCPz9/WFmZoYqVapgypQpOHDgQJ7JoczMTNy5cwfNmzfP83h79+5Fnz594ODgAIlEglGjRsHc3BzHjh0r6aYQERFpBddoIiIiEjh5drauQyAq1dLT0/H06dM8t6WlpSErKwuNGjVSlllbWyM9PR1xcXFo0qSJSv2oqCjIZDKEhobi6tWrqFixItzd3eHl5QWxWIyYmBi4u7ur7NOwYUNERUVpvmFEREQ6wEQTERGRwHExcKKC3bhxAyNGjMhzW0BAAADAyMhIWSaVSgEgz3Wa3rx5g7Zt22L48OFYvnw5/v77b/j6+kIsFsPLywspKSnK/XMYGhoiNTW10PGKxSKIxaJC18+hpydW+S+pYv/kj31TMPZPwdg/5Q8TTURERERUrjk5OSE6OjrPbbdv38bKlSuRlpYGY2NjAFBOmTMxMclV/6OPPsJHH32kfN+iRQuMHDkSx44dg5eXF6RSKdLT01X2SU9Ph7m5eaHjrVzZGCKR+ommHKam0g9XKsfYP/lj3xSM/VMw9k/5wUQTERGRwHGdJaKis7KygkQiQUxMDFq2bAkAiI2NhUQigaWlZa76v/zyC168eIHBgwcryzIzM2FoaAgAsLGxwd27d1X2iYmJgbOzc6FjSkxMKfKIJlNTKZKS0pDN3wu5sH/yx74pGPunYMXpH3Nz4xKKikpSmUg0JSYmYtCgQVi4cCGcnJzyrHPmzBmEhIQgPj4eNWvWxLRp09ClSxfl9g0bNmDr1q1ISkqCnZ0d5s+fjwYNGmirCURERDpTWhJNqampCAoKwunTpyGTydC1a1cEBgYqR4m8LzAwEPv374dEIlGWzZgxQ/n0roMHD2LNmjV4/vw5GjRogDlz5sDe3l4rbaHyQyqVwsXFBSEhIVi5ciUAICQkBH379lUmj96lUCgQHByM+vXro127drh+/Tq2bNmCmTNnAgA8PDzg6+sLFxcXODg4YPv27Xj58iW6d+9e6JjkcgXkckWR25SdLYdMVjp+L5RG7J/8sW8Kxv4pGPun/Cj1kySvXr2KQYMG4Z9//sm3TlxcHPz8/BAQEIArV67Az88PEyZMUC7qePDgQWzduhXh4eG4ePEimjVrBn9/fygURb9AExERlRUKubxYL03hI+KprAoMDISlpSVcXV3Rq1cv1KlTB3PnzlVu79OnD9atWwcA6N69O2bOnIl58+bB3t4eU6dOhZ+fH/r16wcAaN++PQIDAzFv3jy0bdsWR48exYYNG2BmZqaLphEREWlcqR7RdPDgQYSGhmLq1KmYOHFigfUcHR3RrVs3AEDv3r1x4MAB7N69G/7+/tizZw+GDBkCGxsbAMDkyZOxZ88eXLx4Ee3atdNKW4iIiHRFXgpGNOU8In7Lli3KP6inTJmCESNGYNq0abkWR1bnEfEAMGrUKOzevRvHjh3L9UQvouIyMTFBUFAQgoKC8tx+9OhRlfeDBw9WmTr3vn79+ikTT0REREJTqkc0dezYET///DN69+5dYL2YmBiVR84Cqo+JfX97zpx6PkaWiIhIc9LT0/HgwYN8XwU9Iv597z4ivkOHDujZsye+/fZbyP83wupD134iIiIi0o1SPaKpatWqhar3ocfEauIxsjlqNLZWe5/SJCf+st4O4L822FrW1HEkxZfTBiG1RUjnmJDaIqRzTChtuR6V/9RwTdHWGk1l7RHxRERERKR5pTrRVFj5PSY2Z3HRD21Xx+gdoUUPtBQRSjsAYGvwWF2HoDFCaouQzjEhtUVI55hQ2rL7p4sl/hnaSjSVtUfEExEREZHmCSLR1KhRI9y6dUulLCYmRrmuQ85jZHOeQpeVlYW4uLhcQ+4LI3yIP55ExRY/aB2p0dgao3eElvl2AP+1ZfjM9YiOe6zrcIrF1rImtgaPFVRbhHSOCaktQjrHhNIWbdDkgt5FVRofEU9EREREmieIRJObmxs2bdqEY8eOoUePHjh58iQuXbqE2bNnAwDc3d2xatUqODs7w8rKCitWrICFhQUcHR3V/qwnUbGIj7j14YqlnFDaAQDRcY+1MvVEG4TUFiGdY0Jqi5DOMSG1paRpa0RTQUrjI+KJiIiISPPKbKLJ3t4e8+fPh5ubG6ytrbF69WqEhIRg9uzZqF27NlatWgUrKysAb29G37x5A19fXyQmJsLOzg7r16+HRCLRcSuIiIhKXmlINAFvHxG/ZMkSuLq6IisrC127dsWcOXOU2/v06QNXV1d88cUXKo+If/r0KSwsLPJ9RPzTp0/RsGFDPiKeiIiIqBQoM4mm99d8iIiIUHnfqVMndOrUKc99RSIRPD094enpWWLxERERUcH4iHgiIiIi4SsziSYiIiIqGnkpWKOJiIiIiMoHJpqIiIgErrRMnSMiIiIi4WOiiYiISOAU2dm6DoGIiIiIygkmmoiIiAROwalzRERERKQlYl0HQEREREREREREwsARTURERALHNZqIiIiISFuYaCIiIhI4JpqIiIiISFuYaCIiIhI4ORNNRERERKQlTDQREREJHBcDJyIiIiJt4WLgRERERERERESkERzRREREJHBco4mIiIiItIWJJiIiIoFTZCt0HQIRERERlRNMNBEREQkcFwMnIiIiIm1hoomIiEjgFHKOaCIiIiIi7eBi4EREREREREREpBEc0URERCRwcq7RRERERERawkQTERGRwPGpc0RERESkLUw0ERERCRyfOkdERERE2sJEExERkcBx6hwRERERaQsTTURERALHqXNExZOamoqgoCCcPn0aMpkMXbt2RWBgIIyNjXPVnTt3Lo4cOaJSlp6ejg4dOiA8PBwA4OLigoSEBIjF/z2XZ9++fbC2ti7ZhhAREWkBnzpHRERERFSAoKAgPH78GCdOnMDJkyfx+PFjhISE5Fl3wYIFiIiIUL5WrVoFU1NTzJgxAwCQnJyM+/fv49ixYyr1mGQiIiKhYKKJiIhI4ORyRbFeROVZWloajhw5An9/f5iZmaFKlSqYMmUKDhw4gLS0tAL3TUxMxJQpUzB79mzY2NgAAG7evAkzMzPUrl1bG+ETERFpHafOERERCRwXAycqWHp6Op4+fZrntrS0NGRlZaFRo0bKMmtra6SnpyMuLg5NmjTJ97ghISFo3rw53NzclGWRkZGQSqUYNmwY7t69i9q1a8PPzw9dunQpdLxisQhisajQ9XPo6YlV/kuq2D/5Y98UjP1TMPZP+cNEExERkcDJuUYTUYFu3LiBESNG5LktICAAAGBkZKQsk0qlAICUlJR8jxkfH4/Dhw9j7969KuUikQh2dnaYNGkSatWqhZ9++gl+fn7Ytm0bWrVqVah4K1c2hkikfqIph6mptMj7lgfsn/yxbwrG/ikY+6f8YKKJiIhI4DiiiahgTk5OiI6OznPb7du3sXLlSqSlpSkX/86ZMmdiYpLvMffv3w97e/tcI568vLxU3ru5ueHHH3/EiRMnCp1oSkxMKfKIJlNTKZKS0pDNBHQu7J/8sW8Kxv4pWHH6x9w890MXqPRjoomIiIiIKB9WVlaQSCSIiYlBy5YtAQCxsbGQSCSwtLTMd7+TJ0/C09MzV3l4eDiaNm2K9u3bK8syMzNhYGBQ6JiKu35adrYcMhn/GM4P+yd/7JuCsX8Kxv4pPzhJkoiISOAU2YpivYjKM6lUChcXF4SEhCAxMRGJiYkICQlB3759YWhomOc+//77L2JjY9GmTZtc2x4/foz58+cjPj4eMpkM+/btQ0REBAYMGFDSTSEiItIKjmgiIiISOK7RRFQ8gYGBWLJkCVxdXZGVlYWuXbtizpw5yu19+vSBq6srvvjiCwDAw4cPAQDVq1fPdaxp06ZBLBZjyJAhePPmDRo2bIhvv/0W9evX105jiIiIShgTTURERAKnKMYUGyJ6uxZTUFAQgoKC8tx+9OhRlfd2dnb5rvlUoUIFzJo1C7NmzdJ4nERERKUBE01EREQCJ+f0NyIiIiLSkjKRaEpMTMSgQYOwcOFCODk55Vln586d2Lx5M549e4Zq1aphxIgRGDp0KABALpfDwcEBCoVC5VGw58+fV3lULREREZWc1NRUBAUF4fTp05DJZOjatSsCAwOVT/J619y5c3HkyBGVsvT0dHTo0AHh4eEAABcXFyQkJEAs/m/JyX379sHa2rpkG0JERERE+Sr1iaarV69ixowZ+Oeff/Kt88svv2D58uXYsGEDWrZsievXr2PMmDGwsLBAz549ERMTg6ysLFy7dg0VKlTQYvRERES6pyglazQFBQXh8ePHOHHiBLKzszFhwgSEhIQgMDAwV90FCxZgwYIFyve///47Jk+ejBkzZgAAkpOTcf/+fZw6dQq1a9fWWhuIiIiIqGCl+qlzBw8exJQpUzBx4sQC6z19+hTe3t5o1aoVRCIR7O3t4eTkhMuXLwMAIiMjYWtryyQTERGVS6XhqXNpaWk4cuQI/P39YWZmhipVqmDKlCk4cOAA0tLSCtw3MTERU6ZMwezZs2FjYwMAuHnzJszMzJhkIiIiIiplSvWIpo4dO8LV1RX6+voFJptypsjlePnyJS5fvoyZM23aNqYAACTrSURBVGcCeJtoysjIgLu7Ox49egRra2tMnjwZrVu3VjumGo3L9nD8nPjLejuA/9pga1lTx5EUX04bhNQWIZ1jQmqLkM4xobTlelT+I3Y1RVtrNKWnp+Pp06d5bktLS0NWVhYaNWqkLLO2tkZ6ejri4uLQpEmTfI8bEhKC5s2bw83NTVkWGRkJqVSKYcOG4e7du6hduzb8/PzQpUsXzTWIiIiIiNRWqhNNVatWVXuf58+fY+zYsWjevDn69u0LADA0NESLFi0QEBCASpUqYfv27Rg9ejQOHz6MunXrqnX80TtC1Y6pNBJKOwBga/BYXYegMUJqi5DOMSG1RUjnmFDasvuniyX+GQq5dqbO3bhxAyNGjMhzW0BAAACorI0olUoBACkpKfkeMz4+HocPH8bevXtVykUiEezs7DBp0iTUqlULP/30E/z8/LBt2za0atWqmC0hIiIioqIq1YkmdV2/fh0BAQFwdHREcHAw9PXfNi9nPYcco0ePxoEDB3DmzBkMGzZMrc8IH+KPJ1GxGotZ22o0tsboHaFlvh3Af20ZPnM9ouMe6zqcYrG1rImtwWMF1RYhnWNCaouQzjGhtEUbtDWiycnJKd9Hut++fRsrV65EWlqacvHvnClzJiYm+R5z//79sLe3zzXiycvLS+W9m5sbfvzxR5w4cYKJJiIiIiIdEkyiad++fVi4cCH8/f3h6empsm3FihXo2bMnmjZtqizLzMyEgYGB2p/zJCoW8RG3ih2vrgmlHQAQHfdYK1NPtEFIbRHSOSaktgjpHBNSW8oDKysrSCQSxMTEoGXLlgCA2NhYSCQSWFpa5rvfyZMnc13XASA8PBxNmzZF+/btlWVFvbYTERERkeaU6sXAC+vEiROYN28eVq1alefN6J07d7Bo0SI8f/4cmZmZCAsLQ3JyMrp3766DaImIiLSrNCwGLpVK4eLigpCQECQmJiIxMREhISHo27cvDA0N89zn33//RWxsLNq0aZNr2+PHjzF//nzEx8dDJpNh3759iIiIwIABAzQSLxEREREVTZlNNNnb2+Pw4cMAgLCwMGRnZ8Pf3x/29vbK19y5cwEAwcHBqFevHvr16wcnJydcunQJmzZtgpmZmQ5bQEREpB2KbHmxXpoSGBgIS0tLuLq6olevXqhTp47yWg0Affr0wbp165TvHz58CACoXr16rmNNmzYNzs7OGDJkCBwdHbFr1y58++23qF+/vsbiJSIiIiL1lZmpc++v+RAREaH8+ciRIwXua2ZmhuDg4BKJi4iIqLTT1hpNH2JiYoKgoCAEBQXluf3o0aMq7+3s7PJd86lChQqYNWsWZs2apfE4iYiIiKjoykyiiYiIiIpGU9PfiIiIiIg+pMxOnSMiIiIiIiIiotKFI5qIiIgETq7giCYiIiIi0g4mmoiIiAQum4kmIiLSgqtXL2PdujA8eBAHQ0NDdOnSFT4+/jAwyPvpokQkTJw6R0REJHDZiuK9iIiIPuTff//F1KkTMGCAB3766Vd89912RERcxbZt3+s6NCLSMo5oIiIiEjiOaCIiopJmbm6OH388CSMjYygUCiQlvUJmZibMzMx0HRoRaRkTTURERERERFRsRkbGAICBA/vg+fNnaNnSHr17u+k4KiLSNk6dIyIiEjhOnSMiIm3atesADh06DrFYjC+/nK7rcIhIy5hoIiIiErhshaJYLyIiInUYGBjCwqIqxo3zw8WLfyApKUnXIRGRFjHRREREJHAc0URERCUtMvIGhgxxR1ZWlrIsKysLEokEUqlUh5ERkbYx0URERCRwHNFEREQlzdraBunp6Vi3bhWysrLw5MljhIV9gz59+kEikeg6PCLSIi4GTkRERERERMViZGSEZctWITR0GVxde8DExAQ9erhg1CgvXYdGRFrGRBMREZHAcfobEREViVwO8f2H0H/2HFkSPehnZUNerSrkVnUAce7JMVZWDbBixWodBEpEpQkTTURERALHRBMREalFoYB+ZDT04x4CqWkQ6+tDIdGDOCsbFR4kALfuQGZZBzI7W0Ak0nW0RFTKMNFEREQkcFxniYiICk2hgORCBPT+SYBITwzo6alsFunpARmZ0L8dA1FKKrLa2TPZREQquBg4ERGRwPGpc0SakZaWhkGDBuHAgQMF1rtx4wY+/fRT2Nvb45NPPsHevXtVth88eBDdu3dHq1atMHDgQERERJRk2ERq0Y+M/i/JVACRnhh6DxKgfzNaS5ERUVnBRBMREZHA8alzRMV39+5dDB06FNevXy+w3uvXrzFmzBj0798fly9fxqJFixAcHIy//voLAHDx4kUEBQXhq6++wuXLl+Hm5oZx48YhLS1NC60g+gC5HPpxDz+YZMoh0hND//5DQC4v4cCIqCxhoomIiIiIqAAXLlzAyJEjMWDAANSqVavAuidPnoSZmRmGDh0KfX19tG/fHq6urti+fTsAYO/evejTpw8cHBwgkUgwatQomJub49ixY9poClGBxPffrsmkltS0t/sREf0P12giIiISOE5/IypYeno6nj59mue2qlWronHjxvj1119hYGCATZs2FXisu3fvolGjRiplDRs2xL59+wAAMTExcHd3z7U9Kiqq0PGKxSKIxeqviaP3v1EqeoUcrVLesH8A/WfPIdbP/SdizhJMb/+reu6J9PVR4dlzyGwtSzy+0ornTsHYP+UPE01EREQCx+lvRAW7ceMGRowYkee21atXo1u3boU+VkpKCqRSqUqZoaEhUlNTC7W9MCpXNoaoGIsvm5pKP1ypHCvP/ZMl0YNCopfvdn39vLeJJHqQmBuXVFhlRnk+dwqD/VN+MNFEREQkcBzRRFQwJycnREdrZkFjqVSKN2/eqJSlp6fD2NhYuT09PT3XdnNz80J/RmJiSpFHNJmaSpGUlIbsbK6p8z72D6CflQ1xVnaucpHobZJJJstGXt9dyLOykfxvihYiLJ147hSsOP1jzgRmmcREExERkcBxRBOR9jRq1Ajnz59XKYuJiYGNjQ0AwMbGBnfv3s213dnZudCfIZcrIJcX/f/r7Gw5ZDL+MZyf8tw/8mpVUeFBAkR6749cepvYVCgAxXvXFEV2NjKrVYW8nPbZu8rzuVMY7J/yg5MkiYiIiIg0pHv37njx4gU2b96MrKws/Pnnnzhy5IhyXSYPDw8cOXIEf/75J7KysrB582a8fPkS3bt313HkRIDcqg5gpOb0JiPp2/2IiP6HiSYiIiKBkxfzRUQF69OnD9atWwcAMDc3x3fffYeffvoJTk5O+PLLL/Hll1+iXbt2AID27dsjMDAQ8+bNQ9u2bXH06FFs2LABZmZmOmwB0f+IxZBZ1oGikNObFHI5ZFZ1ADH/rCSi/3DqHBERkcBx6hyR5pw+fTpX2dGjR1Xe29nZYdeuXfkeo1+/fujXr5/GYyPSBJmdLUQpqdB7kABRAU8JU2TLkV2/FmTNbbUYHRGVBUw0ERERCRwXAyciokITiZDVzh4KEyPo338IpKZBpP/fn42K7GzASAqZVZ23SaZiPAGRiISJiSYiIiKB44gmIiJSi0gEmV1jyJo1gvj+Q1R49hwiiR7kWf9b+JvT5YioAEw0ERERERERUW5iMeTW9SCztYTE3BjJ/6bw6XJE9EFMNBEREQkcp84RERERkbaUifGOiYmJ6N69Oy5evJhvHS8vL9jZ2cHe3l75Onv2rHL7hg0b4OzsjFatWmH48OG4d++eNkInIiLSuWyFolgvTUtLS8OgQYNw4MCBAuvduHEDn376Kezt7fHJJ59g7969KtsPHjyI7t27o1WrVhg4cCAiIiI0HisRERERqafUJ5quXr2KQYMG4Z9//imw3s2bNxEeHo6IiAjly9nZGcDbG9Gt/9/enQZFdaV9AP+rrCIGUMslieLC4sLSgiDCaER0ZAcbh4xEQUfRKBoxakQtl6CCWwI66ptBDCIaRZQoUUY0AVMZBTEmamlQIGqMRidCZNgXOe+HFD1pQYSxG7qb/6+qP/S9595+nn66+p5z+t7bBw4gPj4eOTk5GD58OBYtWgTBe1YQEVEH8Ey82kOR8vPzERQUhO+//77ZdiUlJQgNDYWfnx9yc3OxceNGREVF4dq1awCAnJwcREZGIjo6Grm5ufDx8cG7776LyspKxQZMRERERK2i0hNNqampWLp0KcLDw5ttd//+fZSUlGDYsGFNrk9OTsa0adNgZmYGXV1dvP/++3j48GGzZ0gRERFpClU5o+nixYsIDg6Gv78/+vXr12zbjIwMGBkZISgoCFpaWnBycoK3tzcOHjwIADh69Cg8PT1hZ2cHbW1thISEwNjYGKdPn1ZYvERERETUeio90eTi4oKzZ8/Cw8Oj2XbXr1+HgYEBwsPDMXr0aHh5eSElJUW2vqCgAObm5rLn2traMDU1RV5entJiJyIiUhVtdUZTVVUV7t271+SjoqIClpaWyMzMxPTp09HpJX+HnZ+fL3fsBoAhQ4bIjt3PH9ufX09ERERE7UOlbwbeq1evFrWrqamBra0twsPDYWZmhpycHCxcuBAGBgZwd3dHeXk59PX15bbR09NDRUVFq2PqYzm41duokob41T0P4L85WJj2bedIXl1DDpqUiyZ9xjQpF036jGlKLt/nNX9puDq5evUqZsyY0eS6Xbt2wc3NrcX7etmxW5HHdiIiIiJSHJWeaGopPz8/+Pn5yZ67uLjAz88P6enpcHd3h76+PqqqquS2qaqqgoGBQatf62+HdrxquCpBU/IAgANRc9s7BIXRpFw06TOmSblo0mdMU3Jpizz+T9xV+msAgKOjI27duqWQfenr66O0tFRu2R+P3S86thsbGyvk9YlUWa9ehq+0vbFx6/vAHQnfnxfje9M8vj/N4/vTcaj0pXMtlZKSgvT0dLllNTU10NXVBQCYmZkhPz9ftq62thZ3795tdMo9ERERqQZzc3O5Yzfw++VyZmZmABof259fT0RERETtQyMmmsrKyhAZGYmbN2+ivr4eWVlZ+OKLLxAYGAgAkEqlSEpKQl5eHqqrq7F9+3b07NkT9vb27Rw5ERERNWXixIl48uQJEhISUFtbi+zsbKSlpUEqlQIAAgICkJaWhuzsbNTW1iIhIQFFRUWYOHFiO0dORERE1LGp7aVzEokE69evh4+PD4KDg1FRUYGwsDAUFRXhzTffxObNm2UTSQEBASgtLcWCBQtQXFwMKysrfPLJJ9DW1m7nLIiIiKiBp6cnvL29MW/ePBgbG2Pfvn3YuHEjduzYARMTE6xevRqjR48GADg5OWHt2rVYt24dHj9+jCFDhiAuLg5GRkbtmwQRERFRB9dJCAX+bzEREREREREREXVYGnHpHBERERERERERtT9ONBERERERERERkUJwoomIiIiIiIiIiBSCE01ERERERERERKQQnGh6gYqKCkRERMDR0RF2dnZYvnw5ysvLX9h+7dq1GDFiBCQSiexx5MiRNoz4v4qKijB//nzY29vD0dERGzduRF1dXZNtz58/D29vb9ja2sLd3R2ZmZltHG3zWpPL7NmzYWVlJVeDr7/+uo0jfrni4mJMnDgROTk5L2yj6nVp0JJcVL0ueXl5mDlzJhwcHODs7Izly5ejuLi4ybaqXpfW5KLqdbl48SKmTp2KkSNHwtnZGZGRkaiqqmqyrarXpTW5qHpdiEg9VVZWIjAwEMePH2+23dWrVzF16lRIJBK4urri6NGjbRRh+1Dn/r4yaNIYQhk0cVyiDJo01qFXIKhJK1asEMHBweK3334TT548Ee+8845Yt27dC9v7+/uL48ePt2GEL/bOO++I999/X1RUVIiffvpJeHp6iri4uEbt7ty5I6ysrMTZs2dFbW2tOHXqlLC2thaPHj1qh6ib1tJchBDC0dFR5OTktHGErXP58mXh5uYmzM3NRXZ2dpNt1KEuQrQsFyFUuy6VlZXC2dlZxMbGiurqalFcXCzmzJkj5s6d26itqtelNbkIodp1KSoqElZWVuLYsWPi2bNn4vHjx8LLy0vExsY2aqvqdWlNLkKodl2ISD3dvn1b+Pv7C3Nzc3Hs2LEXtnv69KlwcHAQSUlJora2Vly4cEFIJBJx9erVNoy2balzf18ZNGkMoQyaNi5RBk0a69Cr4RlNTaisrERaWhoWLVoEIyMj9OjRA0uXLsXx48dRWVnZqH1NTQ1u376NESNGtEO08u7du4dLly5h2bJl0NfXx5tvvon58+fj4MGDjdqmpqbC3t4ebm5u0NLSgoeHB0aNGqUyv8y0Jpf79++jpKQEw4YNa4dIWyY1NRVLly5FeHj4S9upcl2Aluei6nV5+PAhLC0tsWDBAujo6MDY2BiBgYHIzc1t1FbV69KaXFS9LiYmJrhw4QKmTJmCTp064enTp6iuroaJiUmjtqpel9bkoup1ISL1c/HiRQQHB8Pf3x/9+vVrtm1GRgaMjIwQFBQELS0tODk5wdvbu8l+lyZQ5/6+MmjSGEIZNG1cogyaNNahV6fV3gG0l6qqKjx+/LjJdZWVlaitrYW5ubls2eDBg1FVVYW7d+9i6NChcu3z8vJQV1eHHTt24Ntvv4WhoSGkUilmz56Nzp3bdi4vPz8fRkZG6N27t1zsDx8+xH/+8x90795dtrygoEAuRwAYMmQI8vLy2ize5rQml+vXr8PAwADh4eG4fv06evbsiZCQEAQEBLRH6E1ycXGBt7c3tLS0mv0CVvW6AC3PRdXrMmjQIOzdu1du2ZkzZzB8+PBGbVW9Lq3JRdXrAgDdunUDAIwbNw6PHz+Gvb09pkyZ0qidqtcFaHku6lAXIlItzfVne/XqBUtLS2RmZkJXVxeffvpps/vKz89v8vs0JSVFYfG2NU3t7yuDJo0hlEHTxiXKoEljHXp1HXai6erVq5gxY0aT69577z0AQNeuXWXL9PX1AaDJ67ZLS0vh4OCA6dOn46OPPsIPP/yABQsWoHPnzpg9e7YSon+x8vJyWawNGp5XVFTIfQk21VZPTw8VFRXKD7QFWpNLTU0NbG1tER4eDjMzM+Tk5GDhwoUwMDCAu7t7m8b9Ir169WpRO1WvC9DyXNShLg2EEIiJiUFmZiaSkpIarVeHujR4WS7qVJeMjAyUlJRg6dKlWLRoUaPJNHWqy8tyUae6EJFqaK4/u2vXLri5ubV4X+r0fdpSmtrfVwZNGkMog6aNS5RBk8Y69Oo67ESTo6Mjbt261eS6mzdvIjY2FpWVlTAwMAAA2Sm0Db9M/5GzszOcnZ1lz62trREcHIzTp0+3+YGna9eujU73bXjekEsDfX39RjekraqqatSuvbQmFz8/P/j5+cmeu7i4wM/PD+np6Wr3ha7qdWkNdalLWVkZIiIicOPGDSQlJcHCwqJRG3WpS0tyUZe6AL93PPT09LBs2TJMnToVJSUleO2112Tr1aUuwMtzUae6EJFqaK4/21r6+vooLS2VW6aq36ctpan9fWXQpDGEMnTUcYkydMTPT0ek/ud5KsHAgQOhra2NgoIC2bLCwkJoa2vD1NS0Uftz587h8OHDcstqamqgp6en7FAbMTMzw9OnT/HkyRPZssLCQvTp0weGhoZybc3NzZGfny+3rKCgAGZmZm0S68u0JpeUlBSkp6fLLaupqYGurm6bxKpIql6X1lCHuvz000+QSqUoKytDSkpKkxMzgHrUpaW5qHpdrly5gsmTJ6Ompka2rKamBtra2o1+AVP1urQmF1WvCxFpNlX/PlU0de7vK4MmjSGUoaOOS5ShI35+OiJONDVBX18f7u7u2LZtG4qLi1FcXIxt27bBy8uryYOJEAJRUVG4ePEihBD47rvvkJiYiMDAwDaP3dTUFHZ2dti0aRPKyspw//597N69u8lrgn18fHDp0iWcPn0adXV1OH36NC5dugRfX982j7sprcmlrKwMkZGRuHnzJurr65GVlYUvvviiXWrwqlS9Lq2h6nUpKSlBcHAwRo4cifj4+CZv0NxA1evSmlxUvS4WFhaoqqrC9u3bUVNTgwcPHmDz5s0ICAiAjo6OXFtVr0trclH1uhCRZps4cSKePHmChIQE1NbWIjs7G2lpaZBKpe0dmlKoc39fGTRpDKEMHXVcogwd8fPTIbXnX96pstLSUrF69WoxZswYMWrUKLFixQpRXl4uW+/h4SH27Nkje/7ZZ5+JSZMmCRsbGzFhwgSRlJTUHmELIYT49ddfxcKFC4WDg4MYPXq0iI6OFnV1dUIIIWxtbcWJEydkbb/++mvh4+MjbG1thaenp8jKymqvsJvU0lzq6+vFrl27xPjx44W1tbXw9PQU6enp7Rl6s57/y091q8sfNZeLqtdl3759wtzcXNjY2AhbW1u5hxDqVZfW5KLqdRFCiPz8fDFz5kxhb28vxo8fLz766CNRXV0thFCvugjR8lzUoS5EpL7Gjx8vjh07Jrfs+f7stWvXRGBgoJBIJGLChAmN2msade7vK4MmjSGUQVPHJcqgSWMd+t90EkKI9p7sIiIiIiIiIiIi9cdL54iIiIiIiIiISCE40URERERERERERArBiSYiIiIiIiIiIlIITjQREREREREREZFCcKKJiIiIiIiIiIgUghNNRERERERERESkEJxoIiIiIiIiIiIiheBEExERERERERERKQQnmoioxdLS0hAYGAiJRAKJRAKpVIrDhw/L1ru6umLnzp0AACEEUlNTUVRU1F7hEhEREbWZl/WT1Mm3336Ly5cvK3SfQggcOHAAvr6+sLa2hp2dHYKCgvDPf/5Toa9DRO1Pq70DICL1kJKSgg0bNmDlypUYNWoUhBC4ePEiNm7ciCdPniAsLAwpKSnQ1dUFAOTm5mLFihX48ssv2zlyIiIiIuVqST9JnUybNg1RUVGwt7dX2D537NiB5ORkrFy5ElZWVqiursaZM2ewePFiREVFwd/fX2GvRUTtixNNRNQihw4dQkBAAP7yl7/Ilg0aNAiPHj1CYmIiwsLCYGJiIlsnhGiPMImIiIjaXEv6SR3doUOHMG/ePHh6esqWmZmZ4ccff0RiYiInmog0CC+dI6IW6dy5M65cuYKSkhK55XPmzMGRI0cA/PfSuZycHMyYMQMAMGHCBBw/fhwAcOXKFQQFBcHa2hpvvfUW1q9fj7KysrZNhIiIiEjBWtJPSkhIgEQiQWVlpWx9fX09xo4di8TEROTk5GDYsGHIzs6Gh4cHrKysEBgYiDt37mDPnj0YM2YMHBwcEBkZKftBb+fOnQgJCUFiYiJcXFxga2uLJUuW4Ndff8Xy5cshkUgwbtw4pKamyl5TCIG4uDhMmDABNjY28PX1xcmTJ2XrLSwsAAARERFYsWIFfv75Z1hYWGD37t1wdnaGq6srNm7cCDc3N7lcS0tLYW1t/cKz2Tt37ozs7Gy5/AFg1apVslsvNLz+Z599hr/+9a+wtraGt7e33D6FENi7dy/c3d0xYsQI2NnZYe7cubh//76sTUVFBTZs2AAXFxdIJBIEBQXh2rVrsvXskxIpFyeaiKhF5syZgx9++AFjx45FaGgo/vGPf+DatWswNDTEwIED5dpKJBJZh+Ho0aPw8PBAXl4eQkJC4OzsjJMnT2Lbtm24ceMGZs2axbOfiIiISK21pJ/k4+OD2tpaZGRkyLa7cOECiouL4eXlBQB49uwZoqOjsWnTJiQnJ6OoqAhvv/02CgsLceDAASxZsgRJSUnIysqS7ePy5cu4fPky9u/fj5iYGJw5cwZeXl4YOnQojh07hrFjx2LNmjX47bffAAAff/wxDh06hNWrVyMtLQ0zZszAunXrcPDgQQDAN998AwBYuXIlVq1aJXudkydPYv/+/YiNjUVAQADu378vdx+n06dPo1u3bhg3blyT79HcuXORlZUFFxcXLFy4EAkJCbh16xZ69OiBN954Q67tli1b4OXlhc8//xzjxo1DWFgYrly5AgDYv38/PvnkEyxbtgxnzpzB7t27cefOHURHR8u2Dw8PR2ZmJjZt2oTPP/8cAwcOxN/+9jcUFxezT0rUBnjpHBG1yJ///GccOXIEBw4cwDfffIPz588DAExNTbFp0ybY2dnJ2uro6OC1114DAJiYmEBPTw/x8fFwcnLC/PnzZdtt374dbm5uuHTpEhwdHds+KSIiIiIFaEk/ycTEBK6urjh58iR8fX0BAKmpqXB1dZW7/cB7770HW1tbAMCkSZOQmJiIyMhI6OvrY/Dgwdi5cyfy8/Mxfvx4AL+fFbVhwwZ0794dgwcPxtChQ6GtrY2ZM2cCAEJCQpCcnIx79+5BV1cXCQkJ2LJli2z7/v3748GDB4iPj0dQUBB69eoFADA0NIShoaHsLK1p06ZhyJAhsjiHDx+OkydPyu7jlJqaCl9fX2hpNT3EDAkJgZmZGQ4fPowLFy7IJtysrKwQHR0tt2+pVIqgoCAAwNKlS5Gbm4ukpCSMHDkS/fv3R3R0NFxdXQEAr7/+Otzd3XHq1CkAwJ07d5CVlYW9e/fiT3/6EwBgzZo1MDAwwNOnT9knJWoDnGgiohaztrbG1q1bIYTA7du3cf78eSQmJmLOnDk4e/Zss9vevHkT9+7dg0QiabSusLCQB3UiIiJSay/rJ/Xo0QNSqRTz5s3D48ePYWBggHPnziE2NlZuP388U1xfXx89e/aEvr6+bJmuri6qq6tlz3v06IHu3bvLbdO3b1+59gBQXV2NgoICVFdX44MPPkBERISsTV1dHWpqalBVVQU9Pb0m8xswYIDcc6lUipiYGKxevRq//PILvvvuO3z44YfNvkfOzs5wdnbGs2fPcOPGDXz11VdISkrC7NmzkZGRAR0dHQCAg4OD3HY2Nja4cOECgN9v1XD16lXs2LED9+7dQ2FhIfLz89G7d28AwK1btwBANlkH/P4jaEO+7JMSKR8nmojopR49eoS4uDiEhoaid+/e6NSpEywsLGBhYYEJEybAw8MDubm5ze6jvr4e3t7emDdvXqN1f/wVj4iIiEidtLSfNHnyZLi4uKBXr144deoUjIyMYGhoKDvrpsHzZwR17tz83U60tbUbLXvRNg2XhsXExGDQoEGN1jdM9DTl+Qkob29vbN68GZmZmbh9+zasrKxgbm7e5LZ5eXk4cuQIIiIioKOjgy5dusDa2hrW1taQSCQIDQ3FrVu3YGVlBaDxe1BfXy/LKS4uDjt37sSUKVPg4OCA6dOn48svv5Sd0dSwbadOnZqMhX1SIuXjPZqI6KV0dHRw5MgRuRtFNujWrRsAoGfPnnLLnz+4m5mZIT8/HwMGDJA9nj17hqioKPzyyy/KC56IiIhIiVrTT+rSpQv8/PyQkZGBjIwM+Pr6okuXLm0W66BBg6ClpYWHDx/K9cnOnz+P+Pj4l05q/VH37t0xceJEWS5Tpkxptv2hQ4dw7ty5Rsu7deuGTp06oUePHrJl169fl2vz/fffY/jw4QCAPXv2ICwsDOvWrUNgYCBsbW1x9+5d2STa4MGDG+2jrq4Ob731Fk6dOsU+KVEb4BlNRPRSJiYmmD17NmJiYlBWVobJkyejW7duKCgowO7du+Ho6Ci7Pr9B165dAfz+C5axsTFmzZqFoKAgrFmzBjNmzEB5eTnWr1+P8vJymJqatkNWRERERK+utf0kqVSKuLg4aGtrY9myZW0aq6GhId5++23ExMTAwMAAdnZ2uHz5MrZu3Yo5c+bI2nXt2hWFhYWyG4i/iFQqxbvvvgshhOyG5k2xtLSEj48PVq1ahQcPHmD8+PHQ0tJCXl4ePv74Y/j7+6Nfv36y9vv378egQYMwYsQIJCcnIy8vDxs2bAAA9O3bF//617/g6uqKzp0748SJE8jIyJBN5g0cOBCTJk3C+vXrsXbtWvTp0wdxcXGoqamBk5MTXn/9dfZJiZSME01E1CKLFy+GqakpkpOTcfDgQVRVVaFv377w8PDA3LlzG7U3NzfHuHHjsHjxYixZsgSzZs3C3r17ERsbiylTpkBfXx+jR4/GBx980Oxp2kRERESqrjX9pAEDBsDW1hb19fWys2/aUkREBExMTLBjxw78+9//Rp8+fRAWFobQ0FBZm4Z+248//ij3z3PPc3JygrGxMUaOHCl3n6imREVFYcSIEThx4gT27NmD2tpa9O/fH1OnTkVwcLBc28DAQHz66afIz8+HpaUl4uPjYWlpCeD3f6T78MMPIZVKYWBgABsbG6xfvx7r1q3Dzz//jDfeeANRUVHYsmULwsPDUV1dDRsbG+zbtw8mJiYwMTFhn5RIyToJ/ocjERERERFRmxBCYNKkSQgNDcXUqVPbO5xXUlFRARcXF/z973/HmDFjFLJPCwsLREVFvfRSPCJSXTyjiYiIiIiISMlqa2vx1VdfITs7G2VlZfD09GzvkP5nJSUlyM7ORnp6Ovr16wcnJ6f2DomIVAgnmoiIiIiIiJRMW1tbdp+hrVu3yu5nqY7q6uqwatUqmJiYICYm5oX/8EZEHRMvnSMiIiIiIiIiIoVo+f9XEhERERERERERNYMTTUREREREREREpBCcaCIiIiIiIiIiIoXgRBMRERERERERESkEJ5qIiIiIiIiIiEghONFEREREREREREQKwYkmIiIiIiIiIiJSCE40ERERERERERGRQnCiiYiIiIiIiIiIFOL/AV6x4WjzGISNAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Summary:\n", "   Total symmetries: 4\n", "   Unique configurations: 2\n", "   Symmetry reduction factor: 2.0\n", "\n", "================================================================================\n", "\n", "🔄 Symmetry Operations Visualization: 1D Chain (L=6) with translation symmetry\n", "============================================================\n", "Original configuration: [ 1 -1  1 -1  1 -1]\n", "Number of symmetries: 6\n", "\n", "Symmetry  0: [ 1 -1  1 -1  1 -1]\n", "\n", "Symmetry  1: [-1  1 -1  1 -1  1]\n", "\n", "Symmetry  2: [ 1 -1  1 -1  1 -1]\n", "\n", "Symmetry  3: [-1  1 -1  1 -1  1]\n", "\n", "Symmetry  4: [ 1 -1  1 -1  1 -1]\n", "\n", "Symmetry  5: [-1  1 -1  1 -1  1]\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Summary:\n", "   Total symmetries: 6\n", "   Unique configurations: 2\n", "   Symmetry reduction factor: 3.0\n", "\n", "================================================================================\n", "\n"]}], "source": ["# =============================================================================\n", "# SYMMETRY OPERATIONS VISUALIZATION\n", "# =============================================================================\n", "\n", "def visualize_symmetry_operations(system_name='small_chain'):\n", "    \"\"\"Visualize how symmetry operations transform input data.\"\"\"\n", "    \n", "    system = quantum_systems[system_name]\n", "    graph = system['graph']\n", "    symmetries = system['symmetries']\n", "    \n", "    print(f\"🔄 Symmetry Operations Visualization: {system['description']}\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Create a test spin configuration\n", "    n_sites = graph.n_nodes\n", "    test_config = jnp.array([1, -1, 1, -1] if n_sites == 4 else \n", "                           [1, -1, 1, -1, 1, -1] if n_sites == 6 else\n", "                           [1, -1, 1, -1, 1, -1, 1, -1, 1])  # Alternating pattern\n", "    test_config = test_config[:n_sites]  # Trim to system size\n", "    \n", "    print(f\"Original configuration: {test_config}\")\n", "    print(f\"Number of symmetries: {len(symmetries)}\")\n", "    print()\n", "    \n", "    # Apply each symmetry operation\n", "    transformed_configs = []\n", "    for i, symm in enumerate(symmetries):\n", "        # Apply symmetry operation\n", "        transformed = symm @ test_config  # Matrix multiplication\n", "        transformed_configs.append(transformed)\n", "        \n", "        print(f\"Symmetry {i:2d}: {transformed}\")\n", "        \n", "        # Check if this is a translation\n", "        if hasattr(symm, 'shape') and len(symm.shape) == 2:\n", "            # Find which sites map to which\n", "            mapping = jnp.argmax(symm, axis=1)\n", "            if i < 5:  # Only show first few\n", "                print(f\"           Site mapping: {mapping}\")\n", "        print()\n", "    \n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "    \n", "    # Plot 1: Original configuration\n", "    ax1 = axes[0, 0]\n", "    ax1.bar(range(n_sites), test_config, color=['red' if x < 0 else 'blue' for x in test_config])\n", "    ax1.set_title('Original Configuration')\n", "    ax1.set_xlabel('Site')\n", "    ax1.set_ylabel('Spin Value')\n", "    ax1.set_ylim(-1.5, 1.5)\n", "    \n", "    # Plot 2: All transformed configurations\n", "    ax2 = axes[0, 1]\n", "    transformed_array = jnp.array(transformed_configs)\n", "    im = ax2.imshow(transformed_array, cmap='RdBu', vmin=-1, vmax=1, aspect='auto')\n", "    ax2.set_title('All Symmetry Transformations')\n", "    ax2.set_xlabel('Site')\n", "    ax2.set_ylabel('Symmetry Operation')\n", "    plt.colorbar(im, ax=ax2, label='Spin Value')\n", "    \n", "    # Plot 3: Unique configurations\n", "    ax3 = axes[1, 0]\n", "    unique_configs = []\n", "    unique_indices = []\n", "    for i, config in enumerate(transformed_configs):\n", "        is_unique = True\n", "        for unique_config in unique_configs:\n", "            if jnp.allclose(config, unique_config):\n", "                is_unique = False\n", "                break\n", "        if is_unique:\n", "            unique_configs.append(config)\n", "            unique_indices.append(i)\n", "    \n", "    if unique_configs:\n", "        unique_array = jnp.array(unique_configs)\n", "        im3 = ax3.imshow(unique_array, cmap='RdBu', vmin=-1, vmax=1, aspect='auto')\n", "        ax3.set_title(f'Unique Configurations ({len(unique_configs)})')\n", "        ax3.set_xlabel('Site')\n", "        ax3.set_ylabel('Unique Config')\n", "        plt.colorbar(im3, ax=ax3, label='Spin Value')\n", "    \n", "    # Plot 4: Symmetry group structure\n", "    ax4 = axes[1, 1]\n", "    # Create a simple visualization of the symmetry group\n", "    n_symm = len(symmetries)\n", "    angles = jnp.linspace(0, 2*jnp.pi, n_symm, endpoint=False)\n", "    x = jnp.cos(angles)\n", "    y = jnp.sin(angles)\n", "    \n", "    ax4.scatter(x, y, s=100, alpha=0.7)\n", "    for i, (xi, yi) in enumerate(zip(x, y)):\n", "        ax4.annotate(f'{i}', (xi, yi), xytext=(5, 5), textcoords='offset points')\n", "    \n", "    ax4.set_title(f'Symmetry Group ({n_symm} elements)')\n", "    ax4.set_xlabel('Symmetry Space')\n", "    ax4.set_ylabel('Symmetry Space')\n", "    ax4.set_aspect('equal')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\n📊 Summary:\")\n", "    print(f\"   Total symmetries: {len(symmetries)}\")\n", "    print(f\"   Unique configurations: {len(unique_configs)}\")\n", "    print(f\"   Symmetry reduction factor: {len(symmetries) / len(unique_configs):.1f}\")\n", "    \n", "    return transformed_configs, unique_configs\n", "\n", "# Visualize symmetries for different systems\n", "for system_name in ['small_chain', '1d_chain']:\n", "    if system_name in quantum_systems:\n", "        transformed, unique = visualize_symmetry_operations(system_name)\n", "        print(\"\\n\" + \"=\"*80 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Feature Map Analysis\n", "\n", "Let's examine how feature representations evolve through the GCNN layers."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Feature Map Analysis: FFT\n", "==================================================\n", "⚠️ No intermediate results available\n", "\n", "------------------------------------------------------------\n", "\n", "🔍 Feature Map Analysis: Irrep\n", "==================================================\n", "⚠️ No intermediate results available\n", "\n", "------------------------------------------------------------\n", "\n", "🔍 Feature Map Analysis: Parity_FFT\n", "==================================================\n", "⚠️ No intermediate results available\n", "\n", "------------------------------------------------------------\n", "\n", "🔍 Feature Map Analysis: Parity_Irrep\n", "==================================================\n", "⚠️ No intermediate results available\n", "\n", "------------------------------------------------------------\n", "\n"]}], "source": ["# =============================================================================\n", "# FEATURE MAP ANALYSIS\n", "# =============================================================================\n", "\n", "def analyze_feature_maps(model_name='FFT', input_configs=None):\n", "    \"\"\"Analyze how feature maps evolve through GCNN layers.\"\"\"\n", "    \n", "    if model_name not in results:\n", "        print(f\"❌ No results available for {model_name}\")\n", "        return\n", "    \n", "    print(f\"🔍 Feature Map Analysis: {model_name}\")\n", "    print(\"=\" * 50)\n", "    \n", "    model_results = results[model_name]\n", "    intermediates = model_results['intermediates']\n", "    \n", "    if not intermediates:\n", "        print(\"⚠️ No intermediate results available\")\n", "        return\n", "    \n", "    # Create visualization\n", "    n_stages = len(intermediates)\n", "    fig, axes = plt.subplots(2, 3, figsize=(15, 8))\n", "    axes = axes.flatten()\n", "    \n", "    stage_idx = 0\n", "    for stage_name, features in intermediates.items():\n", "        if stage_idx >= len(axes):\n", "            break\n", "            \n", "        ax = axes[stage_idx]\n", "        \n", "        # Handle different feature shapes\n", "        if features.ndim == 3:  # [batch, features, n_symm]\n", "            # Show first batch element\n", "            feature_map = features[0]  # [features, n_symm]\n", "            im = ax.imshow(feature_map, cmap='RdBu', aspect='auto')\n", "            ax.set_title(f'{stage_name}\\nShape: {features.shape}')\n", "            ax.set_xlabel('Symmetry Element')\n", "            ax.set_ylabel('Feature')\n", "            plt.colorbar(im, ax=ax, shrink=0.8)\n", "            \n", "        elif features.ndim == 2:  # [batch, features] or [batch, n_symm]\n", "            feature_map = features[0:1]  # Keep as 2D for imshow\n", "            im = ax.imshow(feature_map, cmap='RdBu', aspect='auto')\n", "            ax.set_title(f'{stage_name}\\nShape: {features.shape}')\n", "            ax.set_xlabel('Feature/Site')\n", "            ax.set_ylabel('Batch')\n", "            plt.colorbar(im, ax=ax, shrink=0.8)\n", "            \n", "        elif features.ndim == 1:  # [batch] - final output\n", "            ax.bar(range(len(features)), features.real, alpha=0.7, label='Real')\n", "            if jnp.iscomplexobj(features):\n", "                ax.bar(range(len(features)), features.imag, alpha=0.7, label='Imag')\n", "                ax.legend()\n", "            ax.set_title(f'{stage_name}\\nShape: {features.shape}')\n", "            ax.set_xlabel('Batch Element')\n", "            ax.set_ylabel('Output Value')\n", "        \n", "        stage_idx += 1\n", "    \n", "    # Hide unused subplots\n", "    for i in range(stage_idx, len(axes)):\n", "        axes[i].set_visible(False)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print numerical analysis\n", "    print(f\"\\n📊 Numerical Analysis:\")\n", "    for stage_name, features in intermediates.items():\n", "        if hasattr(features, 'shape'):\n", "            mean_val = jnp.mean(jnp.abs(features))\n", "            std_val = jnp.std(jnp.abs(features))\n", "            print(f\"   {stage_name:20}: Mean |value| = {mean_val:.4f}, Std = {std_val:.4f}\")\n", "\n", "# Analyze feature maps for available models\n", "for model_name in results.keys():\n", "    analyze_feature_maps(model_name)\n", "    print(\"\\n\" + \"-\"*60 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Su<PERSON><PERSON> and Insights for Polynomial Enhancement\n", "\n", "Based on our exploration, let's summarize key insights for implementing polynomial enhancements."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Summary: NetKet GCNN Architecture Insights\n", "============================================================\n", "\n", "📋 Key Architecture Components:\n", "   1. Input preprocessing: [batch, n_sites] → [batch, 1, n_sites]\n", "   2. Dense symmetry layer: Projects to [batch, features[0], n_symm]\n", "   3. Equivariant layers: Transform features while preserving symmetry\n", "   4. Activation functions: reim_selu (acts on real/imag separately)\n", "   5. LogSumExp reduction: [batch, features, n_symm] → [batch]\n", "   6. Character weighting: Implements irrep projections\n", "\n", "🔧 Implementation Variants:\n", "   • GCNN_FFT: Uses FFT for translation groups (efficient)\n", "   • GCNN_Irrep: Uses irrep matrices (general groups)\n", "   • Parity variants: Handle x → -x symmetry\n", "   • Mode 'auto': Automatically selects best implementation\n", "\n", "🚀 Polynomial Enhancement Strategy:\n", "   ✅ Subclass existing GCNN classes (GCNN_FFT, GCNN_Irrep, etc.)\n", "   ✅ Override __call__ method to add polynomial operations\n", "   ✅ Preserve all setup() infrastructure and symmetry operations\n", "   ✅ Apply polynomial transformations in equivariant layer loop\n", "   ✅ Maintain [batch, features, n_symm] tensor structure\n", "   ✅ Keep final logsumexp reduction unchanged\n", "\n", "💡 Key Insights for Polynomial Integration:\n", "   1. Symmetry structure: Always maintain [batch, features, n_symm] shape\n", "   2. Per-symmetry processing: Apply polynomials to each symmetry element\n", "   3. Residual connections: Add polynomial output to standard equivariant output\n", "   4. Character preservation: Don't modify final reduction or characters\n", "   5. Physics-agnostic: Polynomial enhancement works with any symmetry group\n", "\n", "🎨 Recommended Polynomial Integration Points:\n", "   • Between activation and equivariant layer: x = activation(x); x += poly(x)\n", "   • After equivariant layer: x = equivariant(x); x += poly_strength * poly(x)\n", "   • Parallel processing: x = equivariant(x) + poly_strength * poly(x_input)\n", "\n", "🔬 Next Steps:\n", "   1. Create PolynomialGCNN_FFT, PolynomialGCNN_Irrep subclasses\n", "   2. Implement _apply_polynomial_layer() method\n", "   3. Add polynomial parameters: degree, poly_class, poly_strength\n", "   4. Test on quantum systems from this notebook\n", "   5. Compare standard vs polynomial GCNN performance\n", "\n", "✨ Expected Benefits:\n", "   • Enhanced expressiveness for quantum correlations\n", "   • Better accuracy with same parameter count\n", "   • Physics-agnostic enhancement (works for any quantum system)\n", "   • Maintains all NetKet optimizations and compatibility\n", "\n", "🎉 Ready to implement polynomial GCNN enhancements!\n"]}], "source": ["# =============================================================================\n", "# SUMMARY AND INSIGHTS\n", "# =============================================================================\n", "\n", "print(\"🎯 Summary: NetKet GCNN Architecture Insights\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n📋 Key Architecture Components:\")\n", "print(\"   1. Input preprocessing: [batch, n_sites] → [batch, 1, n_sites]\")\n", "print(\"   2. Dense symmetry layer: Projects to [batch, features[0], n_symm]\")\n", "print(\"   3. Equivariant layers: Transform features while preserving symmetry\")\n", "print(\"   4. Activation functions: reim_selu (acts on real/imag separately)\")\n", "print(\"   5. LogSumExp reduction: [batch, features, n_symm] → [batch]\")\n", "print(\"   6. Character weighting: Implements irrep projections\")\n", "\n", "print(\"\\n🔧 Implementation Variants:\")\n", "print(\"   • GCNN_FFT: Uses FFT for translation groups (efficient)\")\n", "print(\"   • GCNN_Irrep: Uses irrep matrices (general groups)\")\n", "print(\"   • Parity variants: Handle x → -x symmetry\")\n", "print(\"   • Mode 'auto': Automatically selects best implementation\")\n", "\n", "print(\"\\n🚀 Polynomial Enhancement Strategy:\")\n", "print(\"   ✅ Subclass existing GCNN classes (GCNN_FFT, GCNN_Irrep, etc.)\")\n", "print(\"   ✅ Override __call__ method to add polynomial operations\")\n", "print(\"   ✅ Preserve all setup() infrastructure and symmetry operations\")\n", "print(\"   ✅ Apply polynomial transformations in equivariant layer loop\")\n", "print(\"   ✅ Maintain [batch, features, n_symm] tensor structure\")\n", "print(\"   ✅ Keep final logsumexp reduction unchanged\")\n", "\n", "print(\"\\n💡 Key Insights for Polynomial Integration:\")\n", "print(\"   1. Symmetry structure: Always maintain [batch, features, n_symm] shape\")\n", "print(\"   2. Per-symmetry processing: Apply polynomials to each symmetry element\")\n", "print(\"   3. Residual connections: Add polynomial output to standard equivariant output\")\n", "print(\"   4. Character preservation: Don't modify final reduction or characters\")\n", "print(\"   5. Physics-agnostic: Polynomial enhancement works with any symmetry group\")\n", "\n", "print(\"\\n🎨 Recommended Polynomial Integration Points:\")\n", "print(\"   • Between activation and equivariant layer: x = activation(x); x += poly(x)\")\n", "print(\"   • After equivariant layer: x = equivariant(x); x += poly_strength * poly(x)\")\n", "print(\"   • Parallel processing: x = equivariant(x) + poly_strength * poly(x_input)\")\n", "\n", "print(\"\\n🔬 Next Steps:\")\n", "print(\"   1. Create PolynomialGCNN_FFT, PolynomialGCNN_Irrep subclasses\")\n", "print(\"   2. Implement _apply_polynomial_layer() method\")\n", "print(\"   3. Add polynomial parameters: degree, poly_class, poly_strength\")\n", "print(\"   4. Test on quantum systems from this notebook\")\n", "print(\"   5. Compare standard vs polynomial GCNN performance\")\n", "\n", "print(\"\\n✨ Expected Benefits:\")\n", "print(\"   • Enhanced expressiveness for quantum correlations\")\n", "print(\"   • Better accuracy with same parameter count\")\n", "print(\"   • Physics-agnostic enhancement (works for any quantum system)\")\n", "print(\"   • Maintains all NetKet optimizations and compatibility\")\n", "\n", "print(\"\\n🎉 Ready to implement polynomial GCNN enhancements!\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}