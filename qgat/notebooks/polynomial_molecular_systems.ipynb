{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial Neural Networks for Molecular Systems\n", "\n", "This notebook demonstrates polynomial neural networks for quantum molecular systems, focusing on small molecules like H₂, LiH, and BeH₂.\n", "\n", "## Features:\n", "- **Molecular Hamiltonians**: Work with realistic molecular systems\n", "- **Polynomial Enhancements**: Test how polynomial networks handle molecular correlations\n", "- **Chemical Accuracy**: Target chemical accuracy (~1 mHa) for molecular energies\n", "- **Basis Set Effects**: Compare different basis sets and their impact"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from typing import Dict, List, Any\n", "\n", "# Import QGAT components\n", "from models import ModelFactory\n", "from physics.molecular_systems import MolecularSystemFactory\n", "from ground_state.exact_solver import ExactGroundStateSolver\n", "\n", "print(\"✅ Imports successful!\")\n", "print(f\"JAX devices: {jax.devices()}\")\n", "print(f\"NetKet version: {nk.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Configure Molecular System\n", "\n", "Choose your molecular system and computational parameters:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# =============================================================================\n", "# MOLECULAR SYSTEM CONFIGURATION\n", "# =============================================================================\n", "\n", "# Molecular system options\n", "MOLECULE_CONFIG = {\n", "    'molecule': 'H2',           # 'H2', 'LiH', 'BeH2', 'H2O'\n", "    'bond_length': 1.4,         # Bond length in Bohr\n", "    'basis_set': 'sto-3g',      # 'sto-3g', '6-31g', 'cc-pvdz'\n", "    'charge': 0,                # Molecular charge\n", "    'spin': 0                   # Spin multiplicity - 1\n", "}\n", "\n", "# Models optimized for molecular systems\n", "MOLECULAR_MODELS = [\n", "    {\n", "        'name': 'Standard_RBM_Molecular',\n", "        'type': 'rbm',\n", "        'config': {\n", "            'n_hidden': 16,  # Larger for molecular systems\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_Molecular',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 12,  # Slightly smaller due to polynomial enhancement\n", "            'degree': 3,     # Higher degree for molecular correlations\n", "            'poly_class': 'CP',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_RBM_Sparse_Molecular',\n", "        'type': 'poly_rbm',\n", "        'config': {\n", "            'n_hidden': 12,\n", "            'degree': 4,     # Even higher degree with sparsity\n", "            'poly_class': 'CP_sparse_degree',\n", "            'param_dtype': 'float64'\n", "        }\n", "    },\n", "    {\n", "        'name': 'Polynomial_GCNN_Molecular',\n", "        'type': 'poly_gcnn',\n", "        'config': {\n", "            'layers': 3,     # More layers for molecular complexity\n", "            'features': 12,  # More features\n", "            'degree': 3,\n", "            'poly_class': 'CP_sparse_LU',\n", "            'param_dtype': 'float64'\n", "        }\n", "    }\n", "]\n", "\n", "# Training configuration for molecular systems\n", "MOLECULAR_TRAINING = {\n", "    'n_optimization_steps': 1000,  # More steps for convergence\n", "    'learning_rate': 0.0005,       # Lower learning rate for stability\n", "    'n_chains': 32,                # More chains for better sampling\n", "    'n_samples': 2048              # More samples\n", "}\n", "\n", "print(f\"🧪 Configured molecular system: {MOLECULE_CONFIG['molecule']}\")\n", "print(f\"   Bond length: {MOLECULE_CONFIG['bond_length']} Bohr\")\n", "print(f\"   Basis set: {MOLECULE_CONFIG['basis_set']}\")\n", "print(f\"   Models to test: {len(MOLECULAR_MODELS)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Create Molecular System\n", "\n", "Generate the molecular Hamiltonian and compute exact solution:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create molecular system\n", "try:\n", "    molecular_system = MolecularSystemFactory.create(\n", "        molecule=MOLECULE_CONFIG['molecule'],\n", "        bond_length=MOLECULE_CONFIG['bond_length'],\n", "        basis_set=MOLECULE_CONFIG['basis_set'],\n", "        charge=MOLECULE_CONFIG['charge'],\n", "        spin=MOLECULE_CONFIG['spin']\n", "    )\n", "    \n", "    print(f\"✅ Created {MOLECULE_CONFIG['molecule']} molecular system:\")\n", "    print(f\"   Hilbert space: {molecular_system['hilbert']}\")\n", "    print(f\"   Number of orbitals: {molecular_system['n_orbitals']}\")\n", "    print(f\"   Number of electrons: {molecular_system['n_electrons']}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Failed to create molecular system: {e}\")\n", "    print(\"   Falling back to simple H2 model...\")\n", "    \n", "    # Fallback to simple H2 model\n", "    molecular_system = {\n", "        'hilbert': nk.hilbert.Spin(s=1/2, N=4),  # Simple 4-site model\n", "        'hamiltonian': nk.operator.Ising(nk.hilbert.Spin(s=1/2, N=4), graph=nk.graph.Chain(4), h=1.0),\n", "        'problem_name': 'H2_simple',\n", "        'n_orbitals': 4,\n", "        'n_electrons': 2\n", "    }\n", "\n", "# Compute exact ground state\n", "print(\"\\n🔍 Computing exact molecular ground state...\")\n", "exact_solver = ExactGroundStateSolver()\n", "exact_result = exact_solver.solve(molecular_system['hamiltonian'])\n", "\n", "print(f\"✅ Exact ground state energy: {exact_result['energy']:.8f} Hartree\")\n", "print(f\"   Hilbert space size: {exact_result['hilbert_size']}\")\n", "\n", "# Chemical accuracy threshold\n", "chemical_accuracy = 0.001  # 1 mHa\n", "print(f\"🎯 Chemical accuracy target: {chemical_accuracy:.3f} Hartree\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Create Molecular Models\n", "\n", "Build models specifically designed for molecular systems:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create molecular models\n", "molecular_models_info = []\n", "system_size = molecular_system['n_orbitals']\n", "\n", "print(\"🧬 Creating Molecular Models:\")\n", "print(\"=\" * 50)\n", "\n", "for model_config in MOLECULAR_MODELS:\n", "    try:\n", "        # Create model\n", "        model, param_count = ModelFactory.create(\n", "            type_name=model_config['type'],\n", "            config=model_config['config'],\n", "            physics_type='molecular',\n", "            system_size=system_size,\n", "            problem_name=molecular_system['problem_name'],\n", "            hilbert=molecular_system['hilbert'],\n", "            random_seed=42\n", "        )\n", "        \n", "        molecular_models_info.append({\n", "            'name': model_config['name'],\n", "            'type': model_config['type'],\n", "            'model': model,\n", "            'param_count': param_count,\n", "            'config': model_config['config']\n", "        })\n", "        \n", "        print(f\"✅ {model_config['name']}: {param_count:,} parameters\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Failed to create {model_config['name']}: {e}\")\n", "\n", "print(f\"\\n📊 Successfully created {len(molecular_models_info)} molecular models\")\n", "\n", "# Molecular model comparison\n", "if molecular_models_info:\n", "    fig, ax = plt.subplots(1, 1, figsize=(12, 6))\n", "    \n", "    names = [info['name'] for info in molecular_models_info]\n", "    param_counts = [info['param_count'] for info in molecular_models_info]\n", "    colors = ['blue' if 'Standard' in name else 'red' for name in names]\n", "    \n", "    bars = ax.bar(range(len(names)), param_counts, color=colors, alpha=0.7)\n", "    ax.set_xlabel('Molecular Model')\n", "    ax.set_ylabel('Parameter Count')\n", "    ax.set_title(f'Molecular Model Comparison - {MOLECULE_CONFIG[\"molecule\"]} System')\n", "    ax.set_xticks(range(len(names)))\n", "    ax.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "    \n", "    # Add value labels\n", "    for bar, count in zip(bars, param_counts):\n", "        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(param_counts)*0.01,\n", "                f'{count}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Molecular Training Function\n", "\n", "Specialized training function for molecular systems:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_molecular_model(model_info, molecular_system, exact_energy, training_config):\n", "    \"\"\"Train a molecular model with specialized settings.\"\"\"\n", "    \n", "    print(f\"\\n🧬 Training {model_info['name']} on molecular system...\")\n", "    \n", "    # Create sampler optimized for molecular systems\n", "    sampler = nk.sampler.MetropolisLocal(\n", "        hilbert=molecular_system['hilbert'],\n", "        n_chains=training_config['n_chains']\n", "    )\n", "    \n", "    # Create variational state\n", "    vs = nk.vqs.MCState(\n", "        sampler=sampler,\n", "        model=model_info['model'],\n", "        n_samples=training_config['n_samples']\n", "    )\n", "    \n", "    # Create optimizer with molecular-specific settings\n", "    optimizer = nk.optimizer.<PERSON>(\n", "        learning_rate=training_config['learning_rate'],\n", "        b1=0.9,  # Standard momentum\n", "        b2=0.999  # Standard RMSprop parameter\n", "    )\n", "    \n", "    # Create VMC solver\n", "    gs = nk.VMC(\n", "        hamiltonian=molecular_system['hamiltonian'],\n", "        optimizer=optimizer,\n", "        variational_state=vs\n", "    )\n", "    \n", "    # Training history\n", "    history = {\n", "        'steps': [],\n", "        'energies': [],\n", "        'errors': [],\n", "        'variances': [],\n", "        'chemical_accuracy_achieved': False,\n", "        'chemical_accuracy_step': None\n", "    }\n", "    \n", "    # Training loop\n", "    try:\n", "        for step in range(training_config['n_optimization_steps']):\n", "            # Run optimization step\n", "            gs.run(n_iter=1)\n", "            \n", "            # Get current energy\n", "            energy = gs.energy.mean.real\n", "            variance = gs.energy.variance.real\n", "            error = abs(energy - exact_energy)\n", "            \n", "            # Store history\n", "            history['steps'].append(step)\n", "            history['energies'].append(energy)\n", "            history['errors'].append(error)\n", "            history['variances'].append(variance)\n", "            \n", "            # Check for chemical accuracy\n", "            if error < chemical_accuracy and not history['chemical_accuracy_achieved']:\n", "                history['chemical_accuracy_achieved'] = True\n", "                history['chemical_accuracy_step'] = step\n", "                print(f\"   🎯 Chemical accuracy achieved at step {step}!\")\n", "            \n", "            # Print progress\n", "            if step % 100 == 0 or step == training_config['n_optimization_steps'] - 1:\n", "                status = \"✅\" if error < chemical_accuracy else \"🔄\"\n", "                print(f\"   {status} Step {step:4d}: Energy = {energy:10.8f}, Error = {error:8.6f}\")\n", "    \n", "    except Exception as e:\n", "        print(f\"   ❌ Training failed: {e}\")\n", "        return None\n", "    \n", "    final_error = history['errors'][-1]\n", "    best_error = min(history['errors'])\n", "    \n", "    print(f\"   📊 Final error: {final_error:.6f}\")\n", "    print(f\"   🏆 Best error: {best_error:.6f}\")\n", "    \n", "    if history['chemical_accuracy_achieved']:\n", "        print(f\"   🎯 Chemical accuracy: ✅ (step {history['chemical_accuracy_step']})\")\n", "    else:\n", "        print(f\"   🎯 Chemical accuracy: ❌ (target: {chemical_accuracy:.3f})\")\n", "    \n", "    return history\n", "\n", "print(\"✅ Molecular training function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train Molecular Models\n", "\n", "Run training for all molecular models:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train all molecular models\n", "molecular_results = {}\n", "exact_energy = exact_result['energy']\n", "\n", "print(f\"🧬 Training {len(molecular_models_info)} models on {MOLECULE_CONFIG['molecule']} system\")\n", "print(f\"   Target energy: {exact_energy:.8f} Hartree\")\n", "print(f\"   Chemical accuracy: {chemical_accuracy:.3f} Hartree\")\n", "print(\"=\" * 70)\n", "\n", "for model_info in molecular_models_info:\n", "    history = train_molecular_model(model_info, molecular_system, exact_energy, MOLECULAR_TRAINING)\n", "    if history is not None:\n", "        molecular_results[model_info['name']] = {\n", "            'history': history,\n", "            'model_info': model_info\n", "        }\n", "\n", "print(f\"\\n✅ Molecular training complete! {len(molecular_results)} models trained successfully.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Molecular Results Analysis\n", "\n", "Comprehensive analysis of molecular training results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Molecular results visualization\n", "if molecular_results:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Energy convergence with chemical accuracy line\n", "    for name, result in molecular_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax1.plot(history['steps'], history['energies'], \n", "                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "    \n", "    ax1.axhline(y=exact_energy, color='black', linestyle=':', alpha=0.7, label='Exact Energy')\n", "    ax1.axhline(y=exact_energy + chemical_accuracy, color='red', linestyle=':', alpha=0.5, label='Chemical Accuracy')\n", "    ax1.axhline(y=exact_energy - chemical_accuracy, color='red', linestyle=':', alpha=0.5)\n", "    ax1.set_xlabel('Training Step')\n", "    ax1.set_ylabel('Energy (Hartree)')\n", "    ax1.set_title(f'{MOLECULE_CONFIG[\"molecule\"]} Energy Convergence')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 2. Error convergence with chemical accuracy threshold\n", "    for name, result in molecular_results.items():\n", "        history = result['history']\n", "        color = 'blue' if 'Standard' in name else 'red'\n", "        linestyle = '-' if 'RBM' in name else '--'\n", "        ax2.semilogy(history['steps'], history['errors'], \n", "                    label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)\n", "        \n", "        # Mark chemical accuracy achievement\n", "        if history['chemical_accuracy_achieved']:\n", "            step = history['chemical_accuracy_step']\n", "            ax2.scatter(step, history['errors'][step], color=color, s=100, marker='*', \n", "                       edgecolor='black', linewidth=1, zorder=5)\n", "    \n", "    ax2.axhline(y=chemical_accuracy, color='red', linestyle=':', alpha=0.7, label='Chemical Accuracy')\n", "    ax2.set_xlabel('Training Step')\n", "    ax2.set_ylabel('Energy Error (log scale)')\n", "    ax2.set_title('Error Convergence (⭐ = Chemical Accuracy)')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 3. Chemical accuracy achievement\n", "    names = list(molecular_results.keys())\n", "    achieved = [molecular_results[name]['history']['chemical_accuracy_achieved'] for name in names]\n", "    steps_to_accuracy = [molecular_results[name]['history']['chemical_accuracy_step'] \n", "                        if molecular_results[name]['history']['chemical_accuracy_achieved'] \n", "                        else MOLECULAR_TRAINING['n_optimization_steps'] for name in names]\n", "    \n", "    colors = ['green' if ach else 'red' for ach in achieved]\n", "    bars = ax3.bar(range(len(names)), steps_to_accuracy, color=colors, alpha=0.7)\n", "    ax3.set_xlabel('Model')\n", "    ax3.set_ylabel('Steps to Chemical Accuracy')\n", "    ax3.set_title('Chemical Accuracy Achievement')\n", "    ax3.set_xticks(range(len(names)))\n", "    ax3.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "    \n", "    # Add labels\n", "    for bar, steps, ach in zip(bars, steps_to_accuracy, achieved):\n", "        label = f'{steps}' if ach else 'Failed'\n", "        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,\n", "                label, ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 4. Best error comparison\n", "    best_errors = [min(molecular_results[name]['history']['errors']) for name in names]\n", "    colors = ['green' if error < chemical_accuracy else 'red' for error in best_errors]\n", "    \n", "    bars = ax4.bar(range(len(names)), best_errors, color=colors, alpha=0.7)\n", "    ax4.axhline(y=chemical_accuracy, color='red', linestyle=':', alpha=0.7, label='Chemical Accuracy')\n", "    ax4.set_xlabel('Model')\n", "    ax4.set_ylabel('Best Energy Error (Hartree)')\n", "    ax4.set_title('Best Error Achieved')\n", "    ax4.set_xticks(range(len(names)))\n", "    ax4.set_xticklabels([name.replace('_', '\\n') for name in names], rotation=45, ha='right')\n", "    ax4.set_yscale('log')\n", "    ax4.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary table\n", "    print(f\"\\n📊 MOLECULAR RESULTS SUMMARY - {MOLECULE_CONFIG['molecule']}\")\n", "    print(\"=\" * 90)\n", "    print(f\"{'Model':<30} {'Parameters':<12} {'Best Error':<15} {'Chemical Acc.':<15} {'Steps':<10}\")\n", "    print(\"-\" * 90)\n", "    \n", "    for name, result in molecular_results.items():\n", "        history = result['history']\n", "        param_count = result['model_info']['param_count']\n", "        best_error = min(history['errors'])\n", "        achieved = \"✅\" if history['chemical_accuracy_achieved'] else \"❌\"\n", "        steps = history['chemical_accuracy_step'] if history['chemical_accuracy_achieved'] else \"N/A\"\n", "        \n", "        print(f\"{name:<30} {param_count:<12,} {best_error:<15.6f} {achieved:<15} {steps:<10}\")\n", "    \n", "    # Count successes\n", "    successful_models = sum(1 for result in molecular_results.values() \n", "                           if result['history']['chemical_accuracy_achieved'])\n", "    \n", "    print(f\"\\n🎯 Chemical Accuracy Summary:\")\n", "    print(f\"   Successful models: {successful_models}/{len(molecular_results)}\")\n", "    print(f\"   Success rate: {successful_models/len(molecular_results)*100:.1f}%\")\n", "    \n", "    if successful_models > 0:\n", "        print(\"   ✅ Polynomial networks show promise for molecular systems!\")\n", "    else:\n", "        print(\"   ⚠️ Consider longer training or different hyperparameters\")\n", "\n", "else:\n", "    print(\"❌ No successful molecular training results to display\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Polynomial Enhancement Analysis\n", "\n", "Analyze the specific benefits of polynomial enhancements for molecular systems:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Polynomial vs standard analysis for molecular systems\n", "if molecular_results:\n", "    standard_molecular = {name: result for name, result in molecular_results.items() if 'Standard' in name}\n", "    polynomial_molecular = {name: result for name, result in molecular_results.items() if 'Polynomial' in name}\n", "    \n", "    print(\"\\n🧬 P<PERSON><PERSON><PERSON><PERSON><PERSON>L ENHANCEMENT ANALYSIS FOR MOLECULAR SYSTEMS\")\n", "    print(\"=\" * 70)\n", "    \n", "    if standard_molecular and polynomial_molecular:\n", "        # Compare chemical accuracy achievement\n", "        standard_success = sum(1 for result in standard_molecular.values() \n", "                             if result['history']['chemical_accuracy_achieved'])\n", "        polynomial_success = sum(1 for result in polynomial_molecular.values() \n", "                               if result['history']['chemical_accuracy_achieved'])\n", "        \n", "        print(f\"Chemical Accuracy Achievement:\")\n", "        print(f\"  Standard models: {standard_success}/{len(standard_molecular)}\")\n", "        print(f\"  Polynomial models: {polynomial_success}/{len(polynomial_molecular)}\")\n", "        \n", "        # Compare best errors\n", "        standard_best = [min(result['history']['errors']) for result in standard_molecular.values()]\n", "        polynomial_best = [min(result['history']['errors']) for result in polynomial_molecular.values()]\n", "        \n", "        avg_standard = np.mean(standard_best)\n", "        avg_polynomial = np.mean(polynomial_best)\n", "        \n", "        improvement = (avg_standard - avg_polynomial) / avg_standard * 100\n", "        \n", "        print(f\"\\nBest Error Comparison:\")\n", "        print(f\"  Standard average: {avg_standard:.6f} Hartree\")\n", "        print(f\"  Polynomial average: {avg_polynomial:.6f} Hartree\")\n", "        print(f\"  Improvement: {improvement:+.1f}%\")\n", "        \n", "        # Parameter efficiency\n", "        standard_params = [result['model_info']['param_count'] for result in standard_molecular.values()]\n", "        polynomial_params = [result['model_info']['param_count'] for result in polynomial_molecular.values()]\n", "        \n", "        avg_standard_params = np.mean(standard_params)\n", "        avg_polynomial_params = np.mean(polynomial_params)\n", "        \n", "        param_efficiency = (avg_standard_params - avg_polynomial_params) / avg_standard_params * 100\n", "        \n", "        print(f\"\\nParameter Efficiency:\")\n", "        print(f\"  Standard average: {avg_standard_params:.0f} parameters\")\n", "        print(f\"  Polynomial average: {avg_polynomial_params:.0f} parameters\")\n", "        print(f\"  Parameter reduction: {param_efficiency:+.1f}%\")\n", "        \n", "        # Overall assessment\n", "        print(f\"\\n🎯 Overall Assessment:\")\n", "        if polynomial_success > standard_success:\n", "            print(\"   ✅ Polynomial models achieve chemical accuracy more reliably\")\n", "        if improvement > 0:\n", "            print(\"   ✅ Polynomial models show better accuracy on average\")\n", "        if param_efficiency > 0:\n", "            print(\"   ✅ Polynomial models are more parameter efficient\")\n", "        \n", "        if polynomial_success > standard_success or improvement > 0:\n", "            print(\"   🏆 Polynomial enhancements are beneficial for molecular systems!\")\n", "        else:\n", "            print(\"   ⚠️ Standard models performed better in this case\")\n", "    \n", "    else:\n", "        print(\"⚠️ Need both standard and polynomial models for comparison\")\n", "\n", "print(\"\\n🧬 Molecular Experiment Complete!\")\n", "print(\"\\nTo explore different molecules:\")\n", "print(\"1. Change MOLECULE_CONFIG['molecule'] to 'LiH', 'BeH2', etc.\")\n", "print(\"2. Adjust bond_length for different geometries\")\n", "print(\"3. Try different basis sets: 'sto-3g', '6-31g', 'cc-pvdz'\")\n", "print(\"4. Experiment with higher polynomial degrees for complex molecules\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}