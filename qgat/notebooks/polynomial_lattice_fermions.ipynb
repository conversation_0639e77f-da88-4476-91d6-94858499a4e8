# Import required libraries
import sys
import os
sys.path.append('..')

import jax
import jax.numpy as jnp
import netket as nk
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any

# Import QGAT components
from models import ModelFactory
from physics.lattice_fermions import FermionSystemFactory
from ground_state.exact_solver import ExactGroundStateSolver

print("✅ Imports successful!")
print(f"JAX devices: {jax.devices()}")
print(f"NetKet version: {nk.__version__}")

# =============================================================================
# FERMIONIC LATTICE SYSTEM CONFIGURATION
# =============================================================================

# Lattice system configuration
FERMION_CONFIG = {
    'model': 'hubbard',         # 'hubbard', 't_j', 'extended_hubbard'
    'lattice': '1d_chain',      # '1d_chain', '2d_square', 'triangular'
    'L': 6,                     # Lattice size
    't': 1.0,                   # Hopping parameter
    'U': 2.0,                   # On-site interaction (Hubbard U)
    'n_particles': 3,           # Number of fermions
    'pbc': True                 # Periodic boundary conditions
}

# Models optimized for fermionic systems
FERMION_MODELS = [
    {
        'name': 'Standard_RBM_Fermion',
        'type': 'rbm',
        'config': {
            'n_hidden': 12,  # Larger for fermionic correlations
            'param_dtype': 'float64'
        }
    },
    {
        'name': 'Polynomial_RBM_Fermion_CP',
        'type': 'poly_rbm',
        'config': {
            'n_hidden': 10,
            'degree': 3,     # Higher degree for fermionic correlations
            'poly_class': 'CP',
            'param_dtype': 'float64'
        }
    },
    {
        'name': 'Polynomial_RBM_Fermion_Sparse',
        'type': 'poly_rbm',
        'config': {
            'n_hidden': 10,
            'degree': 4,     # Even higher degree with sparsity
            'poly_class': 'CP_sparse_degree',
            'param_dtype': 'float64'
        }
    },
    {
        'name': 'Polynomial_GCNN_Fermion',
        'type': 'poly_gcnn',
        'config': {
            'layers': 3,
            'features': 10,
            'degree': 3,
            'poly_class': 'CP_sparse_LU',
            'param_dtype': 'float64'
        }
    }
]

# Training configuration for fermionic systems
FERMION_TRAINING = {
    'n_optimization_steps': 800,
    'learning_rate': 0.001,     # Moderate learning rate
    'n_chains': 24,             # More chains for fermionic sampling
    'n_samples': 1536           # More samples for better statistics
}

print(f"🔬 Configured fermionic system: {FERMION_CONFIG['model']}")
print(f"   Lattice: {FERMION_CONFIG['lattice']} with L={FERMION_CONFIG['L']}")
print(f"   Parameters: t={FERMION_CONFIG['t']}, U={FERMION_CONFIG['U']}")
print(f"   Particles: {FERMION_CONFIG['n_particles']}")
print(f"   Models to test: {len(FERMION_MODELS)}")

# Create fermionic lattice system
try:
    fermion_system = FermionSystemFactory.create(
        model=FERMION_CONFIG['model'],
        lattice=FERMION_CONFIG['lattice'],
        L=FERMION_CONFIG['L'],
        t=FERMION_CONFIG['t'],
        U=FERMION_CONFIG['U'],
        n_particles=FERMION_CONFIG['n_particles'],
        pbc=FERMION_CONFIG['pbc']
    )
    
    print(f"✅ Created {FERMION_CONFIG['model']} fermionic system:")
    print(f"   Hilbert space: {fermion_system['hilbert']}")
    print(f"   Lattice sites: {fermion_system['n_sites']}")
    print(f"   Particles: {fermion_system['n_particles']}")
    print(f"   Filling: {fermion_system['n_particles']/fermion_system['n_sites']:.2f}")
    
except Exception as e:
    print(f"❌ Failed to create fermionic system: {e}")
    print("   Falling back to simple fermionic model...")
    
    # Fallback to simple fermionic model
    fermion_system = {
        'hilbert': nk.hilbert.Fock(n_max=1, N=FERMION_CONFIG['L']),
        'hamiltonian': nk.operator.BoseHubbard(
            hilbert=nk.hilbert.Fock(n_max=1, N=FERMION_CONFIG['L']),
            graph=nk.graph.Chain(FERMION_CONFIG['L'], pbc=FERMION_CONFIG['pbc']),
            U=FERMION_CONFIG['U'], t=FERMION_CONFIG['t']
        ),
        'problem_name': 'fermion_simple',
        'n_sites': FERMION_CONFIG['L'],
        'n_particles': FERMION_CONFIG['n_particles']
    }

# Compute exact ground state
print("\n🔍 Computing exact fermionic ground state...")
exact_solver = ExactGroundStateSolver()
exact_result = exact_solver.solve(fermion_system['hamiltonian'])

print(f"✅ Exact ground state energy: {exact_result['energy']:.8f}")
print(f"   Hilbert space size: {exact_result['hilbert_size']}")

# Calculate energy per particle
energy_per_particle = exact_result['energy'] / fermion_system['n_particles']
print(f"   Energy per particle: {energy_per_particle:.6f}")

# Create fermionic models
fermion_models_info = []
system_size = fermion_system['n_sites']

print("🔬 Creating Fermionic Models:")
print("=" * 50)

for model_config in FERMION_MODELS:
    try:
        # Create model
        model, param_count = ModelFactory.create(
            type_name=model_config['type'],
            config=model_config['config'],
            physics_type='fermion',
            system_size=system_size,
            problem_name=fermion_system['problem_name'],
            hilbert=fermion_system['hilbert'],
            random_seed=42
        )
        
        fermion_models_info.append({
            'name': model_config['name'],
            'type': model_config['type'],
            'model': model,
            'param_count': param_count,
            'config': model_config['config']
        })
        
        print(f"✅ {model_config['name']}: {param_count:,} parameters")
        
    except Exception as e:
        print(f"❌ Failed to create {model_config['name']}: {e}")

print(f"\n📊 Successfully created {len(fermion_models_info)} fermionic models")

# Fermionic model comparison
if fermion_models_info:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    names = [info['name'] for info in fermion_models_info]
    param_counts = [info['param_count'] for info in fermion_models_info]
    colors = ['blue' if 'Standard' in name else 'red' for name in names]
    
    # Parameter comparison
    bars = ax1.bar(range(len(names)), param_counts, color=colors, alpha=0.7)
    ax1.set_xlabel('Fermionic Model')
    ax1.set_ylabel('Parameter Count')
    ax1.set_title(f'Fermionic Model Comparison - {FERMION_CONFIG["model"].upper()}')
    ax1.set_xticks(range(len(names)))
    ax1.set_xticklabels([name.replace('_', '\n') for name in names], rotation=45, ha='right')
    
    # Add value labels
    for bar, count in zip(bars, param_counts):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(param_counts)*0.01,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    # Model architecture breakdown
    model_types = {}
    for info in fermion_models_info:
        model_type = info['type']
        if model_type not in model_types:
            model_types[model_type] = []
        model_types[model_type].append(info['param_count'])
    
    # Box plot of parameter counts by type
    type_names = list(model_types.keys())
    type_params = [model_types[t] for t in type_names]
    
    ax2.boxplot(type_params, labels=type_names)
    ax2.set_ylabel('Parameter Count')
    ax2.set_title('Parameter Distribution by Model Type')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def train_fermionic_model(model_info, fermion_system, exact_energy, training_config):
    """Train a fermionic model with particle number conservation."""
    
    print(f"\n🔬 Training {model_info['name']} on fermionic system...")
    
    # Create sampler for fermionic systems
    # Use exchange sampler for better fermionic sampling
    try:
        sampler = nk.sampler.MetropolisExchange(
            hilbert=fermion_system['hilbert'],
            n_chains=training_config['n_chains'],
            graph=nk.graph.Chain(fermion_system['n_sites'], pbc=True)
        )
    except:
        # Fallback to local sampler
        sampler = nk.sampler.MetropolisLocal(
            hilbert=fermion_system['hilbert'],
            n_chains=training_config['n_chains']
        )
    
    # Create variational state
    vs = nk.vqs.MCState(
        sampler=sampler,
        model=model_info['model'],
        n_samples=training_config['n_samples']
    )
    
    # Create optimizer with fermionic-specific settings
    optimizer = nk.optimizer.Adam(
        learning_rate=training_config['learning_rate'],
        b1=0.9,
        b2=0.999
    )
    
    # Create VMC solver
    gs = nk.VMC(
        hamiltonian=fermion_system['hamiltonian'],
        optimizer=optimizer,
        variational_state=vs
    )
    
    # Training history
    history = {
        'steps': [],
        'energies': [],
        'errors': [],
        'variances': [],
        'energy_per_particle': []
    }
    
    # Training loop
    try:
        for step in range(training_config['n_optimization_steps']):
            # Run optimization step
            gs.run(n_iter=1)
            
            # Get current energy
            energy = gs.energy.mean.real
            variance = gs.energy.variance.real
            error = abs(energy - exact_energy)
            energy_pp = energy / fermion_system['n_particles']
            
            # Store history
            history['steps'].append(step)
            history['energies'].append(energy)
            history['errors'].append(error)
            history['variances'].append(variance)
            history['energy_per_particle'].append(energy_pp)
            
            # Print progress
            if step % 100 == 0 or step == training_config['n_optimization_steps'] - 1:
                print(f"   🔄 Step {step:4d}: E = {energy:8.6f}, E/N = {energy_pp:6.4f}, Error = {error:8.6f}")
    
    except Exception as e:
        print(f"   ❌ Training failed: {e}")
        return None
    
    final_error = history['errors'][-1]
    best_error = min(history['errors'])
    final_energy_pp = history['energy_per_particle'][-1]
    
    print(f"   📊 Final error: {final_error:.6f}")
    print(f"   🏆 Best error: {best_error:.6f}")
    print(f"   ⚛️ Final energy per particle: {final_energy_pp:.6f}")
    
    return history

print("✅ Fermionic training function defined")

# Train all fermionic models
fermion_results = {}
exact_energy = exact_result['energy']
exact_energy_pp = exact_energy / fermion_system['n_particles']

print(f"🔬 Training {len(fermion_models_info)} models on {FERMION_CONFIG['model']} system")
print(f"   Target energy: {exact_energy:.8f}")
print(f"   Target energy per particle: {exact_energy_pp:.6f}")
print(f"   System: L={FERMION_CONFIG['L']}, N={fermion_system['n_particles']}, U/t={FERMION_CONFIG['U']/FERMION_CONFIG['t']:.1f}")
print("=" * 80)

for model_info in fermion_models_info:
    history = train_fermionic_model(model_info, fermion_system, exact_energy, FERMION_TRAINING)
    if history is not None:
        fermion_results[model_info['name']] = {
            'history': history,
            'model_info': model_info
        }

print(f"\n✅ Fermionic training complete! {len(fermion_results)} models trained successfully.")

# Fermionic results visualization
if fermion_results:
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Energy convergence
    for name, result in fermion_results.items():
        history = result['history']
        color = 'blue' if 'Standard' in name else 'red'
        linestyle = '-' if 'RBM' in name else '--'
        ax1.plot(history['steps'], history['energies'], 
                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)
    
    ax1.axhline(y=exact_energy, color='black', linestyle=':', alpha=0.7, label='Exact Energy')
    ax1.set_xlabel('Training Step')
    ax1.set_ylabel('Total Energy')
    ax1.set_title(f'{FERMION_CONFIG["model"].upper()} Energy Convergence')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Energy per particle convergence
    for name, result in fermion_results.items():
        history = result['history']
        color = 'blue' if 'Standard' in name else 'red'
        linestyle = '-' if 'RBM' in name else '--'
        ax2.plot(history['steps'], history['energy_per_particle'], 
                label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)
    
    ax2.axhline(y=exact_energy_pp, color='black', linestyle=':', alpha=0.7, label='Exact E/N')
    ax2.set_xlabel('Training Step')
    ax2.set_ylabel('Energy per Particle')
    ax2.set_title('Energy per Particle Convergence')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Error convergence (log scale)
    for name, result in fermion_results.items():
        history = result['history']
        color = 'blue' if 'Standard' in name else 'red'
        linestyle = '-' if 'RBM' in name else '--'
        ax3.semilogy(history['steps'], history['errors'], 
                    label=name.replace('_', ' '), color=color, linestyle=linestyle, alpha=0.8)
    
    ax3.set_xlabel('Training Step')
    ax3.set_ylabel('Energy Error (log scale)')
    ax3.set_title('Error Convergence')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Final performance comparison
    names = list(fermion_results.keys())
    best_errors = [min(fermion_results[name]['history']['errors']) for name in names]
    param_counts = [fermion_results[name]['model_info']['param_count'] for name in names]
    
    # Scatter plot: parameters vs best error
    for i, name in enumerate(names):
        color = 'blue' if 'Standard' in name else 'red'
        marker = 'o' if 'RBM' in name else 's'
        ax4.loglog(param_counts[i], best_errors[i], 
                  marker=marker, color=color, markersize=10, alpha=0.8, label=name.replace('_', ' '))
    
    ax4.set_xlabel('Parameter Count')
    ax4.set_ylabel('Best Energy Error')
    ax4.set_title('Parameter Efficiency')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Summary table
    print(f"\n📊 FERMIONIC RESULTS SUMMARY - {FERMION_CONFIG['model'].upper()}")
    print("=" * 90)
    print(f"{'Model':<30} {'Parameters':<12} {'Best Error':<15} {'Final E/N':<15} {'Quality':<10}")
    print("-" * 90)
    
    for name, result in fermion_results.items():
        history = result['history']
        param_count = result['model_info']['param_count']
        best_error = min(history['errors'])
        final_energy_pp = history['energy_per_particle'][-1]
        
        if best_error < 0.001:
            quality = "Excellent"
        elif best_error < 0.01:
            quality = "Good"
        elif best_error < 0.1:
            quality = "Fair"
        else:
            quality = "Poor"
        
        print(f"{name:<30} {param_count:<12,} {best_error:<15.6f} {final_energy_pp:<15.6f} {quality:<10}")
    
    # Find best model
    best_model = min(fermion_results.items(), key=lambda x: min(x[1]['history']['errors']))
    print(f"\n🏆 Best Model: {best_model[0]}")
    print(f"   Best error: {min(best_model[1]['history']['errors']):.6f}")
    print(f"   Parameters: {best_model[1]['model_info']['param_count']:,}")

else:
    print("❌ No successful fermionic training results to display")

# Fermionic correlation and polynomial enhancement analysis
if fermion_results:
    standard_fermion = {name: result for name, result in fermion_results.items() if 'Standard' in name}
    polynomial_fermion = {name: result for name, result in fermion_results.items() if 'Polynomial' in name}
    
    print("\n🔬 FERMIONIC CORRELATION ANALYSIS")
    print("=" * 60)
    
    # System parameters
    filling = fermion_system['n_particles'] / fermion_system['n_sites']
    interaction_strength = FERMION_CONFIG['U'] / FERMION_CONFIG['t']
    
    print(f"System Parameters:")
    print(f"  Lattice sites: {fermion_system['n_sites']}")
    print(f"  Particles: {fermion_system['n_particles']}")
    print(f"  Filling: {filling:.2f}")
    print(f"  U/t ratio: {interaction_strength:.1f}")
    
    # Analyze correlation regime
    if interaction_strength < 1.0:
        regime = "Weakly Correlated (Metallic)"
    elif interaction_strength < 4.0:
        regime = "Moderately Correlated"
    else:
        regime = "Strongly Correlated (Mott Insulator)"
    
    print(f"  Correlation regime: {regime}")
    
    if standard_fermion and polynomial_fermion:
        # Compare performance in this correlation regime
        standard_best = [min(result['history']['errors']) for result in standard_fermion.values()]
        polynomial_best = [min(result['history']['errors']) for result in polynomial_fermion.values()]
        
        avg_standard = np.mean(standard_best)
        avg_polynomial = np.mean(polynomial_best)
        
        improvement = (avg_standard - avg_polynomial) / avg_standard * 100
        
        print(f"\nPerformance in {regime}:")
        print(f"  Standard models: {avg_standard:.6f} average error")
        print(f"  Polynomial models: {avg_polynomial:.6f} average error")
        print(f"  Improvement: {improvement:+.1f}%")
        
        # Analyze polynomial degree effects
        print(f"\nPolynomial Degree Analysis:")
        for name, result in polynomial_fermion.items():
            config = result['model_info']['config']
            degree = config.get('degree', 'N/A')
            poly_class = config.get('poly_class', 'N/A')
            best_error = min(result['history']['errors'])
            
            print(f"  {name}: degree={degree}, class={poly_class}, error={best_error:.6f}")
        
        # Recommendations based on correlation regime
        print(f"\n💡 Recommendations for {regime}:")
        
        if interaction_strength < 1.0:
            print("  - Lower polynomial degrees (2-3) may be sufficient")
            print("  - Standard models might perform well")
        elif interaction_strength < 4.0:
            print("  - Moderate polynomial degrees (3-4) recommended")
            print("  - Sparse polynomial classes may help with efficiency")
        else:
            print("  - Higher polynomial degrees (4+) may be needed")
            print("  - Polynomial enhancements likely crucial")
        
        if improvement > 10:
            print("  ✅ Polynomial networks show significant advantage!")
        elif improvement > 0:
            print("  ✅ Polynomial networks show modest improvement")
        else:
            print("  ⚠️ Standard networks performed better in this case")
    
    else:
        print("⚠️ Need both standard and polynomial models for comparison")

print("\n🔬 Fermionic Experiment Complete!")
print("\nTo explore different fermionic systems:")
print("1. Change FERMION_CONFIG['model'] to 't_j' or 'extended_hubbard'")
print("2. Vary U/t ratio to explore different correlation regimes")
print("3. Try different fillings by changing n_particles")
print("4. Experiment with 2D lattices for more complex physics")
print("5. Use higher polynomial degrees for strongly correlated systems")