# Import necessary libraries
import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
from flax import nnx

# Enable 64-bit precision for quantum chemistry accuracy
jax.config.update("jax_enable_x64", True)

# Import QGAT components
import sys
import os
sys.path.append(os.path.join(os.getcwd(), '..'))  # Add parent directory to path

try:
    from physics.molecular.molecular_gat import NetKetMolecularGAT
    from physics.molecular.hamiltonians import create_molecular_hamiltonian
    from physics.molecular.utils import create_h2_adjacency, create_water_adjacency
    from visualization.plotting import plot_energy_convergence, plot_model_comparison
    from visualization.utils import setup_publication_style
    
    # Set up plotting style
    setup_publication_style()
    
    print("✅ Molecular QGAT environment setup complete!")
    print(f"JAX version: {jax.__version__}")
    print(f"NetKet version: {nk.__version__}")
    print("🧪 Ready for quantum chemistry calculations!")
    
except ImportError as e:
    print(f"⚠️ Import error: {e}")
    print("Please ensure you're running this notebook from the qgat/notebooks/ directory")
    print("and that the QGAT package is properly installed.")

# H₂ molecule parameters
n_atoms = 2
n_orbitals = 4  # Minimal basis: 1s orbital per H atom, spin up/down
n_electrons = 2  # Two electrons total
bond_length = 0.74  # Angstroms (equilibrium bond length)

print("🧪 Setting up H₂ molecule")
print(f"Number of atoms: {n_atoms}")
print(f"Number of orbitals: {n_orbitals}")
print(f"Number of electrons: {n_electrons}")
print(f"Bond length: {bond_length} Å")

# Create fermionic Hilbert space
hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=n_electrons)

print(f"\nHilbert space:")
print(f"  Size: {hilbert.size}")
print(f"  Local dimension: {hilbert.local_size}")
print(f"  Fermion constraint: {n_electrons} electrons in {n_orbitals} orbitals")

# Create molecular Hamiltonian
hamiltonian, exact_energy = create_molecular_hamiltonian("h2", hilbert, bond_length=bond_length)

print(f"\nMolecular Hamiltonian:")
print(f"  Exact ground state energy: {exact_energy:.6f} Ha")
print(f"  Energy in eV: {exact_energy * 27.211:.3f} eV")
print(f"  Energy in kcal/mol: {exact_energy * 627.5:.1f} kcal/mol")

# Create molecular adjacency matrix
molecular_adjacency = create_h2_adjacency()

print("🔗 Molecular graph structure:")
print(f"Adjacency matrix shape: {molecular_adjacency.shape}")
print(f"Adjacency matrix:")
print(molecular_adjacency)

# Visualize molecular graph
plt.figure(figsize=(10, 4))

# Plot adjacency matrix
plt.subplot(1, 2, 1)
plt.imshow(molecular_adjacency, cmap='Blues', aspect='auto')
plt.colorbar(label='Connection Strength')
plt.title('H₂ Molecular Adjacency Matrix')
plt.xlabel('Atom Index')
plt.ylabel('Atom Index')

# Add text annotations
for i in range(molecular_adjacency.shape[0]):
    for j in range(molecular_adjacency.shape[1]):
        plt.text(j, i, f'{molecular_adjacency[i, j]:.1f}',
                ha="center", va="center", color="black", fontsize=12)

# Plot molecular structure
plt.subplot(1, 2, 2)
# H₂ atoms at positions
atom_positions = jnp.array([[0, 0], [bond_length, 0]])
plt.scatter(atom_positions[:, 0], atom_positions[:, 1], s=200, c='lightblue', 
           edgecolors='black', linewidth=2, label='H atoms')
plt.plot(atom_positions[:, 0], atom_positions[:, 1], 'k-', linewidth=3, alpha=0.7, label='H-H bond')

# Add atom labels
for i, pos in enumerate(atom_positions):
    plt.text(pos[0], pos[1] + 0.1, f'H{i+1}', ha='center', va='bottom', fontsize=14, fontweight='bold')

plt.xlim(-0.2, bond_length + 0.2)
plt.ylim(-0.3, 0.3)
plt.xlabel('Distance (Å)')
plt.title('H₂ Molecular Structure')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n📊 Graph properties:")
print(f"  Number of nodes: {molecular_adjacency.shape[0]}")
print(f"  Number of edges: {jnp.sum(molecular_adjacency > 0) // 2}")
print(f"  Graph density: {jnp.mean(molecular_adjacency):.3f}")

# Create Molecular GAT model
model = NetKetMolecularGAT(
    n_atoms=n_atoms,
    n_orbitals=n_orbitals,
    molecular_adjacency=molecular_adjacency,
    hidden_features=[32, 16],  # Two GAT layers
    n_heads=[8, 2],           # Multi-head attention
    use_transcorrelated=False, # Start with standard GAT
    rngs=nnx.Rngs(42)         # Fixed seed for reproducibility
)

print("🧠 Molecular GAT model created!")
print(f"Architecture:")
print(f"  Input size: {n_orbitals} orbitals")
print(f"  Hidden layers: {model.gat_model.hidden_features}")
print(f"  Attention heads: {model.gat_model.n_heads}")
print(f"  Output: Scalar wavefunction amplitude")

# Test the model with sample configurations
test_configurations = [
    jnp.array([1, 1, 0, 0]),  # Both electrons in first two orbitals
    jnp.array([1, 0, 1, 0]),  # Electrons in first and third orbitals
    jnp.array([0, 1, 0, 1]),  # Electrons in second and fourth orbitals
    jnp.array([1, 0, 0, 1]),  # Electrons in first and fourth orbitals
]

print(f"\n🧪 Testing model with sample configurations:")
for i, config in enumerate(test_configurations):
    output = model(config)
    config_str = ''.join([str(int(x)) for x in config])
    print(f"  Config {i+1}: |{config_str}⟩ → {output:.6f}")

print(f"\n✅ Model successfully processes fermionic configurations!")

# Set up VMC for molecular system
sampler = nk.sampler.MetropolisLocal(hilbert, n_chains=8)
vs = nk.vqs.MCState(sampler, model, n_samples=1000)

# Optimizer with smaller learning rate for molecular systems
optimizer = nk.optimizer.Sgd(learning_rate=0.005)
vmc = nk.VMC(hamiltonian, optimizer, variational_state=vs)

print("⚙️ VMC setup for H₂ molecule:")
print(f"Sampler: {type(sampler).__name__}")
print(f"Number of chains: {sampler.n_chains}")
print(f"Samples per step: {vs.n_samples}")
print(f"Optimizer: {type(optimizer).__name__} (lr={optimizer.lr})")
print(f"Target accuracy: Chemical accuracy (~1 kcal/mol = 0.0016 Ha)")

# Run VMC optimization
n_steps = 60
energies = []
variances = []
errors = []

print(f"🚀 Starting VMC optimization for H₂ ({n_steps} steps)")
print("Step | Energy (Ha)  | Variance     | Error (Ha)   | Error (kcal/mol) | Status")
print("-" * 80)

for step in range(n_steps):
    vmc.advance()
    energy = vs.expect(hamiltonian)
    
    energies.append(energy.mean)
    variances.append(energy.variance)
    error = abs(energy.mean - exact_energy)
    errors.append(error)
    
    if step % 10 == 0 or step == n_steps - 1:
        error_kcal = error * 627.5  # Convert to kcal/mol
        status = "🎯" if error_kcal < 1.0 else "📈" if error < errors[0] else "📊"
        print(f"{step:4d} | {energy.mean:11.6f} | {energy.variance:11.6f} | {error:11.6f} | {error_kcal:11.2f} | {status}")

final_energy = energies[-1]
final_error = errors[-1]
final_error_kcal = final_error * 627.5
chemical_accuracy = final_error_kcal < 1.0

print(f"\n✅ H₂ optimization complete!")
print(f"Final energy: {final_energy:.6f} Ha")
print(f"Exact energy: {exact_energy:.6f} Ha")
print(f"Absolute error: {final_error:.6f} Ha")
print(f"Error in kcal/mol: {final_error_kcal:.2f} kcal/mol")
print(f"Chemical accuracy achieved: {'✅ Yes' if chemical_accuracy else '❌ No'}")
print(f"Relative error: {final_error/abs(exact_energy)*100:.3f}%")

# Plot H₂ optimization results
results = {
    'energies': energies,
    'variances': variances
}

fig = plot_energy_convergence(results, exact_energy, figsize=(14, 6))
plt.suptitle('H₂ Molecule: Molecular GAT Convergence', fontsize=16)
plt.show()

# Additional analysis plots
plt.figure(figsize=(15, 5))

# Error in kcal/mol
plt.subplot(1, 3, 1)
errors_kcal = [e * 627.5 for e in errors]
plt.plot(errors_kcal, 'r-', linewidth=2, label='Energy Error')
plt.axhline(1.0, color='green', linestyle='--', alpha=0.7, label='Chemical Accuracy')
plt.xlabel('Optimization Step')
plt.ylabel('Error (kcal/mol)')
plt.title('Chemical Accuracy Progress')
plt.legend()
plt.grid(True, alpha=0.3)
plt.yscale('log')

# Energy distribution
plt.subplot(1, 3, 2)
plt.hist(energies[-20:], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
plt.axvline(exact_energy, color='red', linestyle='--', linewidth=2, label='Exact')
plt.axvline(np.mean(energies[-20:]), color='green', linestyle='-', linewidth=2, label='Mean')
plt.xlabel('Energy (Ha)')
plt.ylabel('Frequency')
plt.title('Final Energy Distribution')
plt.legend()
plt.grid(True, alpha=0.3)

# Variance evolution
plt.subplot(1, 3, 3)
plt.plot(variances, 'purple', linewidth=2, label='Energy Variance')
plt.xlabel('Optimization Step')
plt.ylabel('Energy Variance')
plt.title('Variance Evolution')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("📊 Statistical Analysis:")
print(f"Mean final energy (last 10 steps): {np.mean(energies[-10:]):.6f} Ha")
print(f"Standard deviation: {np.std(energies[-10:]):.6f} Ha")
print(f"Energy improvement: {energies[0] - energies[-1]:.6f} Ha")
print(f"Variance reduction: {variances[0] - variances[-1]:.6f}")