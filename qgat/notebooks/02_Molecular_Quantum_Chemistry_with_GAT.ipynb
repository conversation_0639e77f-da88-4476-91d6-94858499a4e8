{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Molecular Quantum Chemistry with Graph Attention Networks\n", "\n", "**A comprehensive tutorial for quantum chemists and computational chemistry researchers**\n", "\n", "This notebook demonstrates how to use Quantum Graph Attention Networks (QGAT) for solving molecular quantum chemistry problems. We'll cover the mathematical foundations of molecular Hamiltonians, transcorrelated methods, and how GAT can capture electron correlation effects.\n", "\n", "## Learning Objectives\n", "\n", "By the end of this tutorial, you will:\n", "- Understand the mathematical foundations of molecular Hamiltonians and electron correlation\n", "- Implement QGAT for molecular systems from geometry to ground state energy\n", "- Compare QGAT with traditional quantum chemistry methods\n", "- Apply transcorrelated techniques with graph attention networks\n", "- Visualize molecular graphs and attention patterns\n", "\n", "## Table of Contents\n", "\n", "1. [Mathematical Foundations](#1-mathematical-foundations)\n", "2. [Environment Setup](#2-environment-setup)\n", "3. [H₂ Molecule with QGAT](#3-h2-molecule-with-qgat)\n", "4. [Transcorrelated Methods](#4-transcorrelated-methods)\n", "5. [Water Molecule Example](#5-water-molecule-example)\n", "6. [Comparison with Traditional Methods](#6-comparison-with-traditional-methods)\n", "7. [Advanced Applications](#7-advanced-applications)\n", "8. [Exercises and Extensions](#8-exercises-and-extensions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Mathematical Foundations\n", "\n", "### 1.1 <PERSON> Hamiltonian\n", "\n", "The electronic Hamiltonian for a molecule in the <PERSON><PERSON><PERSON><PERSON> approximation is:\n", "\n", "$$H = -\\frac{1}{2}\\sum_i \\nabla_i^2 - \\sum_{i,A} \\frac{Z_A}{|\\vec{r}_i - \\vec{R}_A|} + \\sum_{i<j} \\frac{1}{|\\vec{r}_i - \\vec{r}_j|} + \\sum_{A<B} \\frac{Z_A Z_B}{|\\vec{R}_A - \\vec{R}_B|}$$\n", "\n", "Where:\n", "- First term: kinetic energy of electrons\n", "- Second term: electron-nucleus attraction\n", "- Third term: electron-electron repulsion\n", "- Fourth term: nuclear repulsion (constant)\n", "\n", "### 1.2 Second Quantization\n", "\n", "In second quantization, the Hamiltonian becomes:\n", "\n", "$$H = \\sum_{pq} h_{pq} a_p^\\dagger a_q + \\frac{1}{2}\\sum_{pqrs} g_{pqrs} a_p^\\dagger a_q^\\dagger a_s a_r$$\n", "\n", "Where $h_{pq}$ are one-electron integrals and $g_{pqrs}$ are two-electron integrals.\n", "\n", "### 1.3 Graph Representation\n", "\n", "For molecular systems, we represent:\n", "- **Nodes**: Atomic orbitals or atoms\n", "- **Edges**: Chemical bonds or orbital overlaps\n", "- **Node Features**: Orbital occupations, atomic properties\n", "- **Edge Features**: Bond strengths, overlap integrals\n", "\n", "### 1.4 Transcorrelated Methods\n", "\n", "Transcorrelated methods use a similarity transformation:\n", "\n", "$$\\bar{H} = e^{-T} H e^T$$\n", "\n", "where $T$ is typically a <PERSON><PERSON>row factor that explicitly includes electron correlation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Environment Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Molecular QGAT environment setup complete!\n", "JAX version: 0.5.3\n", "NetKet version: 3.19.0\n", "🧪 Ready for quantum chemistry calculations!\n"]}], "source": ["# Import necessary libraries\n", "import jax\n", "import jax.numpy as jnp\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from flax import nnx\n", "\n", "# Enable 64-bit precision for quantum chemistry accuracy\n", "jax.config.update(\"jax_enable_x64\", True)\n", "\n", "# Import QGAT components\n", "import sys\n", "import os\n", "sys.path.append(os.path.join(os.getcwd(), '..'))  # Add parent directory to path\n", "\n", "try:\n", "    from physics.molecular.molecular_gat import NetKetMolecularGAT\n", "    from physics.molecular.hamiltonians import create_molecular_hamiltonian\n", "    from physics.molecular.utils import create_h2_adjacency, create_water_adjacency\n", "    from visualization.plotting import plot_energy_convergence, plot_model_comparison\n", "    from visualization.utils import setup_publication_style\n", "    \n", "    # Set up plotting style\n", "    setup_publication_style()\n", "    \n", "    print(\"✅ Molecular QGAT environment setup complete!\")\n", "    print(f\"JAX version: {jax.__version__}\")\n", "    print(f\"NetKet version: {nk.__version__}\")\n", "    print(\"🧪 Ready for quantum chemistry calculations!\")\n", "    \n", "except ImportError as e:\n", "    print(f\"⚠️ Import error: {e}\")\n", "    print(\"Please ensure you're running this notebook from the qgat/notebooks/ directory\")\n", "    print(\"and that the QGAT package is properly installed.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. H₂ Molecule with QGAT\n", "\n", "Let's start with the simplest molecular system: the hydrogen molecule (H₂)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Problem Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Setting up H₂ molecule\n", "Number of atoms: 2\n", "Number of orbitals: 4\n", "Number of electrons: 2\n", "Bond length: 0.74 Å\n", "\n", "Hilbert space:\n", "  Size: 4\n", "  Local dimension: 2\n", "  Fermion constraint: 2 electrons in 4 orbitals\n", "\n", "Molecular Hamiltonian:\n", "  Exact ground state energy: -1.137200 Ha\n", "  Energy in eV: -30.944 eV\n", "  Energy in kcal/mol: -713.6 kcal/mol\n"]}], "source": ["# H₂ molecule parameters\n", "n_atoms = 2\n", "n_orbitals = 4  # Minimal basis: 1s orbital per H atom, spin up/down\n", "n_electrons = 2  # Two electrons total\n", "bond_length = 0.74  # Angstroms (equilibrium bond length)\n", "\n", "print(\"🧪 Setting up H₂ molecule\")\n", "print(f\"Number of atoms: {n_atoms}\")\n", "print(f\"Number of orbitals: {n_orbitals}\")\n", "print(f\"Number of electrons: {n_electrons}\")\n", "print(f\"Bond length: {bond_length} Å\")\n", "\n", "# Create fermionic Hilbert space\n", "hilbert = nk.hilbert.SpinOrbitalFermions(n_orbitals=n_orbitals, n_fermions=n_electrons)\n", "\n", "print(f\"\\nHilbert space:\")\n", "print(f\"  Size: {hilbert.size}\")\n", "print(f\"  Local dimension: {hilbert.local_size}\")\n", "print(f\"  Fermion constraint: {n_electrons} electrons in {n_orbitals} orbitals\")\n", "\n", "# Create molecular Hamiltonian\n", "hamiltonian, exact_energy = create_molecular_hamiltonian(\"h2\", hilbert, bond_length=bond_length)\n", "\n", "print(f\"\\nMolecular Hamiltonian:\")\n", "print(f\"  Exact ground state energy: {exact_energy:.6f} Ha\")\n", "print(f\"  Energy in eV: {exact_energy * 27.211:.3f} eV\")\n", "print(f\"  Energy in kcal/mol: {exact_energy * 627.5:.1f} kcal/mol\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Molecular Graph Construction"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔗 Molecular graph structure:\n", "Adjacency matrix shape: (2, 2)\n", "Adjacency matrix:\n", "[[0. 1.]\n", " [1. 0.]]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/jq/mmntqs2d7wq778myd68k_jsh0000gp/T/ipykernel_85562/1465621047.py:45: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Times New Roman.\n", "  plt.tight_layout()\n", "/Users/<USER>/miniconda3/envs/quantum/lib/python3.13/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 8322 (\\N{SUBSCRIPT TWO}) missing from font(s) Times New Roman.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Graph properties:\n", "  Number of nodes: 2\n", "  Number of edges: 1\n", "  Graph density: 0.500\n"]}], "source": ["# Create molecular adjacency matrix\n", "molecular_adjacency = create_h2_adjacency()\n", "\n", "print(\"🔗 Molecular graph structure:\")\n", "print(f\"Adjacency matrix shape: {molecular_adjacency.shape}\")\n", "print(f\"Adjacency matrix:\")\n", "print(molecular_adjacency)\n", "\n", "# Visualize molecular graph\n", "plt.figure(figsize=(10, 4))\n", "\n", "# Plot adjacency matrix\n", "plt.subplot(1, 2, 1)\n", "plt.imshow(molecular_adjacency, cmap='Blues', aspect='auto')\n", "plt.colorbar(label='Connection Strength')\n", "plt.title('H₂ Molecular Adjacency Matrix')\n", "plt.xlabel('Atom Index')\n", "plt.ylabel('Atom Index')\n", "\n", "# Add text annotations\n", "for i in range(molecular_adjacency.shape[0]):\n", "    for j in range(molecular_adjacency.shape[1]):\n", "        plt.text(j, i, f'{molecular_adjacency[i, j]:.1f}',\n", "                ha=\"center\", va=\"center\", color=\"black\", fontsize=12)\n", "\n", "# Plot molecular structure\n", "plt.subplot(1, 2, 2)\n", "# H₂ atoms at positions\n", "atom_positions = jnp.array([[0, 0], [bond_length, 0]])\n", "plt.scatter(atom_positions[:, 0], atom_positions[:, 1], s=200, c='lightblue', \n", "           edgecolors='black', linewidth=2, label='H atoms')\n", "plt.plot(atom_positions[:, 0], atom_positions[:, 1], 'k-', linewidth=3, alpha=0.7, label='H-H bond')\n", "\n", "# Add atom labels\n", "for i, pos in enumerate(atom_positions):\n", "    plt.text(pos[0], pos[1] + 0.1, f'H{i+1}', ha='center', va='bottom', fontsize=14, fontweight='bold')\n", "\n", "plt.xlim(-0.2, bond_length + 0.2)\n", "plt.ylim(-0.3, 0.3)\n", "plt.xlabel('Distance (Å)')\n", "plt.title('H₂ Molecular Structure')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n📊 Graph properties:\")\n", "print(f\"  Number of nodes: {molecular_adjacency.shape[0]}\")\n", "print(f\"  Number of edges: {jnp.sum(molecular_adjacency > 0) // 2}\")\n", "print(f\"  Graph density: {jnp.mean(molecular_adjacency):.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Creating the Molecular GAT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Molecular GAT model created!\n", "Architecture:\n", "  Input size: 4 orbitals\n", "  Hidden layers: [32, 16]\n", "  Attention heads: [8, 2]\n", "  Output: Scalar wavefunction amplitude\n", "\n", "🧪 Testing model with sample configurations:\n", "  Config 1: |1100⟩ → 0.130394\n", "  Config 2: |1010⟩ → 0.288693\n", "  Config 3: |0101⟩ → 0.061356\n", "  Config 4: |1001⟩ → 0.175024\n", "\n", "✅ Model successfully processes fermionic configurations!\n"]}], "source": ["# Create Molecular GAT model\n", "model = NetKetMolecularGAT(\n", "    n_atoms=n_atoms,\n", "    n_orbitals=n_orbitals,\n", "    molecular_adjacency=molecular_adjacency,\n", "    hidden_features=[32, 16],  # Two GAT layers\n", "    n_heads=[8, 2],           # Multi-head attention\n", "    use_transcorrelated=False, # Start with standard GAT\n", "    rngs=nnx.Rngs(42)         # Fixed seed for reproducibility\n", ")\n", "\n", "print(\"🧠 Molecular GAT model created!\")\n", "print(f\"Architecture:\")\n", "print(f\"  Input size: {n_orbitals} orbitals\")\n", "print(f\"  Hidden layers: {model.gat_model.hidden_features}\")\n", "print(f\"  Attention heads: {model.gat_model.n_heads}\")\n", "print(f\"  Output: Scalar wavefunction amplitude\")\n", "\n", "model = nk.models.GCNN(\n", "            symmetries=g,\n", "            layers=layers,\n", "            features=features,\n", "            mode=mode,\n", "            complex_output=False,\n", "            param_dtype=dtype\n", "        )\n", "\n", "# Test the model with sample configurations\n", "test_configurations = [\n", "    jnp.array([1, 1, 0, 0]),  # Both electrons in first two orbitals\n", "    jnp.array([1, 0, 1, 0]),  # Electrons in first and third orbitals\n", "    jnp.array([0, 1, 0, 1]),  # Electrons in second and fourth orbitals\n", "    jnp.array([1, 0, 0, 1]),  # Electrons in first and fourth orbitals\n", "]\n", "\n", "print(f\"\\n🧪 Testing model with sample configurations:\")\n", "for i, config in enumerate(test_configurations):\n", "    output = model(config)\n", "    config_str = ''.join([str(int(x)) for x in config])\n", "    print(f\"  Config {i+1}: |{config_str}⟩ → {output:.6f}\")\n", "\n", "print(f\"\\n✅ Model successfully processes fermionic configurations!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 Variational Monte Carlo Optimization"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚙️ VMC setup for H₂ molecule:\n", "Sampler: MetropolisSampler\n", "Number of chains: 8\n", "Samples per step: 1000\n", "Optimizer: GradientTransformationExtraArgs (lr=0.005)\n", "Target accuracy: Chemical accuracy (~1 kcal/mol = 0.0016 Ha)\n"]}], "source": ["# Set up VMC for molecular system\n", "sampler = nk.sampler.MetropolisLocal(hilbert, n_chains=8)\n", "vs = nk.vqs.MCState(sampler, model, n_samples=1000)\n", "\n", "# Optimizer with smaller learning rate for molecular systems\n", "learning_rate = 0.005 \n", "optimizer = nk.optimizer.Sgd(learning_rate=learning_rate)\n", "vmc = nk.VMC(hamiltonian, optimizer, variational_state=vs)\n", "\n", "print(\"⚙️ VMC setup for H₂ molecule:\")\n", "print(f\"Sampler: {type(sampler).__name__}\")\n", "print(f\"Number of chains: {sampler.n_chains}\")\n", "print(f\"Samples per step: {vs.n_samples}\")\n", "print(f\"Optimizer: {type(optimizer).__name__} (lr={learning_rate})\")\n", "print(f\"Target accuracy: Chemical accuracy (~1 kcal/mol = 0.0016 Ha)\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Starting VMC optimization for H₂ (60 steps)\n", "Step | Energy (Ha)  | Variance     | Error (Ha)   | Error (kcal/mol) | Status\n", "--------------------------------------------------------------------------------\n", "   0 |   -6.148769 |    0.361265 |    5.011569 |     3144.76 | 📊\n", "  10 |   -6.122535 |    0.246303 |    4.985335 |     3128.30 | 📈\n", "  20 |   -6.143580 |    0.226171 |    5.006380 |     3141.50 | 📈\n", "  30 |   -6.137499 |    0.226658 |    5.000299 |     3137.69 | 📈\n", "  40 |   -6.145503 |    0.226644 |    5.008303 |     3142.71 | 📈\n", "  50 |   -6.158188 |    0.214737 |    5.020988 |     3150.67 | 📊\n", "  59 |   -6.143659 |    0.208339 |    5.006459 |     3141.55 | 📈\n", "\n", "✅ H₂ optimization complete!\n", "Final energy: -6.143659 Ha\n", "Exact energy: -1.137200 Ha\n", "Absolute error: 5.006459 Ha\n", "Error in kcal/mol: 3141.55 kcal/mol\n", "Chemical accuracy achieved: ❌ No\n", "Relative error: 440.244%\n"]}], "source": ["# Run VMC optimization\n", "n_steps = 60\n", "energies = []\n", "variances = []\n", "errors = []\n", "\n", "print(f\"🚀 Starting VMC optimization for H₂ ({n_steps} steps)\")\n", "print(\"Step | Energy (Ha)  | Variance     | Error (Ha)   | Error (kcal/mol) | Status\")\n", "print(\"-\" * 80)\n", "\n", "for step in range(n_steps):\n", "    vmc.advance()\n", "    energy = vs.expect(hamiltonian)\n", "    \n", "    energies.append(energy.mean)\n", "    variances.append(energy.variance)\n", "    error = abs(energy.mean - exact_energy)\n", "    errors.append(error)\n", "    \n", "    if step % 10 == 0 or step == n_steps - 1:\n", "        error_kcal = error * 627.5  # Convert to kcal/mol\n", "        status = \"🎯\" if error_kcal < 1.0 else \"📈\" if error < errors[0] else \"📊\"\n", "        print(f\"{step:4d} | {energy.mean:11.6f} | {energy.variance:11.6f} | {error:11.6f} | {error_kcal:11.2f} | {status}\")\n", "\n", "final_energy = energies[-1]\n", "final_error = errors[-1]\n", "final_error_kcal = final_error * 627.5\n", "chemical_accuracy = final_error_kcal < 1.0\n", "\n", "print(f\"\\n✅ H₂ optimization complete!\")\n", "print(f\"Final energy: {final_energy:.6f} Ha\")\n", "print(f\"Exact energy: {exact_energy:.6f} Ha\")\n", "print(f\"Absolute error: {final_error:.6f} Ha\")\n", "print(f\"Error in kcal/mol: {final_error_kcal:.2f} kcal/mol\")\n", "print(f\"Chemical accuracy achieved: {'✅ Yes' if chemical_accuracy else '❌ No'}\")\n", "print(f\"Relative error: {final_error/abs(exact_energy)*100:.3f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 Results Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot H₂ optimization results\n", "results = {\n", "    'energies': energies,\n", "    'variances': variances\n", "}\n", "\n", "fig = plot_energy_convergence(results, exact_energy, figsize=(14, 6))\n", "plt.suptitle('H₂ Molecule: Molecular GAT Convergence', fontsize=16)\n", "plt.show()\n", "\n", "# Additional analysis plots\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Error in kcal/mol\n", "plt.subplot(1, 3, 1)\n", "errors_kcal = [e * 627.5 for e in errors]\n", "plt.plot(errors_kcal, 'r-', linewidth=2, label='Energy Error')\n", "plt.axhline(1.0, color='green', linestyle='--', alpha=0.7, label='Chemical Accuracy')\n", "plt.xlabel('Optimization Step')\n", "plt.ylabel('Error (kcal/mol)')\n", "plt.title('Chemical Accuracy Progress')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.yscale('log')\n", "\n", "# Energy distribution\n", "plt.subplot(1, 3, 2)\n", "plt.hist(energies[-20:], bins=10, alpha=0.7, color='skyblue', edgecolor='black')\n", "plt.axvline(exact_energy, color='red', linestyle='--', linewidth=2, label='Exact')\n", "plt.axvline(np.mean(energies[-20:]), color='green', linestyle='-', linewidth=2, label='Mean')\n", "plt.xlabel('Energy (Ha)')\n", "plt.ylabel('Frequency')\n", "plt.title('Final Energy Distribution')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Variance evolution\n", "plt.subplot(1, 3, 3)\n", "plt.plot(variances, 'purple', linewidth=2, label='Energy Variance')\n", "plt.xlabel('Optimization Step')\n", "plt.ylabel('Energy Variance')\n", "plt.title('Variance Evolution')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 Statistical Analysis:\")\n", "print(f\"Mean final energy (last 10 steps): {np.mean(energies[-10:]):.6f} Ha\")\n", "print(f\"Standard deviation: {np.std(energies[-10:]):.6f} Ha\")\n", "print(f\"Energy improvement: {energies[0] - energies[-1]:.6f} Ha\")\n", "print(f\"Variance reduction: {variances[0] - variances[-1]:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "quantum", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}