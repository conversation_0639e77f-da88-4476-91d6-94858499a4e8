# Import necessary libraries
import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
from flax import nnx

# Enable 64-bit precision for better numerical accuracy
jax.config.update("jax_enable_x64", True)

# Import QGAT components
import sys
import os
sys.path.append(os.path.join(os.getcwd(), '..'))  # Add parent directory to path

try:
    from physics.spin.spin_gat import NetKetSpinGAT
    from physics.spin.hamiltonians import create_spin_hamiltonian
    from physics.spin.utils import create_lattice_adjacency, create_random_spin_configuration
    from visualization.plotting import plot_energy_convergence, plot_attention_weights
    from visualization.utils import setup_publication_style
    
    # Set up plotting style
    setup_publication_style()
    
    print("✅ Environment setup complete!")
    print(f"JAX version: {jax.__version__}")
    print(f"NetKet version: {nk.__version__}")
    
except ImportError as e:
    print(f"⚠️ Import error: {e}")
    print("Please ensure you're running this notebook from the qgat/notebooks/ directory")
    print("and that the QGAT package is properly installed.")

# Define system parameters
n_sites = 4  # Number of lattice sites
J = 1.0      # Exchange coupling strength

print(f"🧲 Setting up {n_sites}-site Heisenberg chain")
print(f"Exchange coupling J = {J}")

# Create the Hamiltonian and get exact energy
hamiltonian, exact_energy = create_spin_hamiltonian("heisenberg_1d", n_sites, J=J, pbc=True)
hilbert = hamiltonian.hilbert

print(f"Hilbert space size: {hilbert.size}")
print(f"Total Sz constraint: {hilbert.total_sz}")
print(f"Exact ground state energy: {exact_energy:.6f}")

# Create the lattice graph for visualization
g = nk.graph.Hypercube(length=n_sites, n_dim=1, pbc=True)
print(f"Graph: {g.n_nodes} nodes, {g.n_edges} edges")

# Create Spin GAT model
model = NetKetSpinGAT(
    n_sites=n_sites,
    lattice_type="chain",
    hidden_features=[16, 8],  # Two GAT layers with 16 and 8 features
    n_heads=[4, 1],           # 4 attention heads in first layer, 1 in second
    rngs=nnx.Rngs(42)         # Fixed seed for reproducibility
)

print("🧠 Spin GAT model created successfully!")
print(f"Architecture: {len(model.gat_model.gat_layers)} GAT layers")
print(f"Hidden features: {model.gat_model.hidden_features}")
print(f"Attention heads: {model.gat_model.n_heads}")

# Test the model with a sample configuration
test_config = jnp.array([1, -1, 1, -1], dtype=jnp.float64)  # Antiferromagnetic
output = model(test_config)
print(f"\nTest configuration: {test_config}")
print(f"Model output: {output:.6f}")

# Set up VMC optimization
sampler = nk.sampler.MetropolisExchange(hilbert, graph=g, n_chains=8)
vs = nk.vqs.MCState(sampler, model, n_samples=1000)

# Optimizer
optimizer = nk.optimizer.Sgd(learning_rate=0.01)
vmc = nk.VMC(hamiltonian, optimizer, variational_state=vs)

print("⚙️ VMC setup complete")
print(f"Sampler: {type(sampler).__name__}")
print(f"Number of chains: {sampler.n_chains}")
print(f"Samples per step: {vs.n_samples}")
print(f"Optimizer: {type(optimizer).__name__} (lr={optimizer.lr})")

# Run optimization
n_steps = 50
energies = []
variances = []

print(f"🚀 Starting VMC optimization ({n_steps} steps)")
print("Step | Energy      | Variance    | Error       | Progress")
print("-" * 60)

for step in range(n_steps):
    vmc.advance()
    energy = vs.expect(hamiltonian)
    
    energies.append(energy.mean)
    variances.append(energy.variance)
    
    if step % 10 == 0 or step == n_steps - 1:
        error = abs(energy.mean - exact_energy)
        progress = "█" * (step * 20 // n_steps) + "░" * (20 - step * 20 // n_steps)
        print(f"{step:4d} | {energy.mean:10.6f} | {energy.variance:10.6f} | {error:10.6f} | {progress}")

final_energy = energies[-1]
final_error = abs(final_energy - exact_energy)
relative_error = final_error / abs(exact_energy) * 100

print(f"\n✅ Optimization complete!")
print(f"Final energy: {final_energy:.6f}")
print(f"Exact energy: {exact_energy:.6f}")
print(f"Absolute error: {final_error:.6f}")
print(f"Relative error: {relative_error:.3f}%")

# Plot energy convergence
results = {
    'energies': energies,
    'variances': variances
}

fig = plot_energy_convergence(results, exact_energy, figsize=(12, 5))
plt.suptitle(f'{n_sites}-site Heisenberg Chain: QGAT Convergence', fontsize=16)
plt.show()

# Print convergence statistics
print("📊 Convergence Analysis:")
print(f"Initial energy: {energies[0]:.6f}")
print(f"Final energy: {energies[-1]:.6f}")
print(f"Energy improvement: {energies[0] - energies[-1]:.6f}")
print(f"Final variance: {variances[-1]:.6f}")

# Find convergence step (within 1% of exact)
tolerance = 0.01 * abs(exact_energy)
converged_steps = [i for i, e in enumerate(energies) if abs(e - exact_energy) < tolerance]
if converged_steps:
    print(f"Converged at step: {converged_steps[0]}")
else:
    print("Did not converge within tolerance")

# Create comparison models
print("🔬 Creating comparison models...")

# 1. Restricted Boltzmann Machine
rbm_model = nk.models.RBM(alpha=2, param_dtype=float)

# 2. Group Convolutional Neural Network
gcnn_model = nk.models.GCNN(
    symmetries=g,
    layers=2,
    features=8,
    mode="auto",
    complex_output=False,
    param_dtype=float
)

# 3. Our QGAT (already created)
gat_model = model

models = {
    "QGAT": gat_model,
    "RBM": rbm_model,
    "GCNN": gcnn_model
}

print(f"Created {len(models)} models for comparison")

# Compare models
comparison_results = {}
n_comparison_steps = 30

print("⚔️ Model Comparison (30 optimization steps each)")
print("Model | Initial Energy | Final Energy   | Final Error    | Improvement")
print("-" * 70)

for name, model in models.items():
    # Create fresh variational state
    vs_comp = nk.vqs.MCState(sampler, model, n_samples=500)
    optimizer_comp = nk.optimizer.Sgd(learning_rate=0.01)
    vmc_comp = nk.VMC(hamiltonian, optimizer_comp, variational_state=vs_comp)
    
    # Initial energy
    initial_energy = vs_comp.expect(hamiltonian).mean
    
    # Optimize
    model_energies = []
    for step in range(n_comparison_steps):
        vmc_comp.advance()
        energy = vs_comp.expect(hamiltonian)
        model_energies.append(energy.mean)
    
    final_energy = model_energies[-1]
    final_error = abs(final_energy - exact_energy)
    improvement = initial_energy - final_energy
    
    comparison_results[name] = {
        'energies': model_energies,
        'initial_energy': initial_energy,
        'final_energy': final_energy,
        'final_error': final_error,
        'improvement': improvement
    }
    
    print(f"{name:5s} | {initial_energy:13.6f} | {final_energy:13.6f} | {final_error:13.6f} | {improvement:10.6f}")

# Find best model
best_model = min(comparison_results.keys(), 
                key=lambda x: comparison_results[x]['final_error'])
print(f"\n🏆 Best model: {best_model} (error: {comparison_results[best_model]['final_error']:.6f})")

# Plot comparison
plt.figure(figsize=(12, 8))

# Energy convergence comparison
plt.subplot(2, 2, 1)
for name, results in comparison_results.items():
    plt.plot(results['energies'], label=name, linewidth=2)
plt.axhline(exact_energy, color='red', linestyle='--', alpha=0.7, label='Exact')
plt.xlabel('Optimization Step')
plt.ylabel('Energy')
plt.title('Energy Convergence Comparison')
plt.legend()
plt.grid(True, alpha=0.3)

# Error comparison
plt.subplot(2, 2, 2)
models_list = list(comparison_results.keys())
errors = [comparison_results[name]['final_error'] for name in models_list]
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
bars = plt.bar(models_list, errors, color=colors, alpha=0.7)
plt.ylabel('Final Energy Error')
plt.title('Final Error Comparison')
plt.yscale('log')
plt.grid(True, alpha=0.3)

# Add value labels on bars
for bar, error in zip(bars, errors):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1, 
             f'{error:.4f}', ha='center', va='bottom')

# Improvement comparison
plt.subplot(2, 2, 3)
improvements = [comparison_results[name]['improvement'] for name in models_list]
bars = plt.bar(models_list, improvements, color=colors, alpha=0.7)
plt.ylabel('Energy Improvement')
plt.title('Optimization Improvement')
plt.grid(True, alpha=0.3)

# Error evolution
plt.subplot(2, 2, 4)
for name, results in comparison_results.items():
    errors = [abs(e - exact_energy) for e in results['energies']]
    plt.semilogy(errors, label=name, linewidth=2)
plt.xlabel('Optimization Step')
plt.ylabel('Energy Error (log scale)')
plt.title('Error Evolution')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.suptitle(f'{n_sites}-site Heisenberg Chain: Model Comparison', fontsize=16, y=1.02)
plt.show()

# Extract attention weights from the trained model
# Note: This requires modifying the GAT to return attention weights
# For demonstration, we'll create a representative attention pattern

# Create sample spin configurations
configurations = {
    'Antiferromagnetic': jnp.array([1, -1, 1, -1]),
    'Ferromagnetic': jnp.array([1, 1, 1, 1]),
    'Domain Wall': jnp.array([1, 1, -1, -1])
}

print("🔍 Analyzing attention patterns for different spin configurations")

# Test model outputs for different configurations
for name, config in configurations.items():
    output = gat_model(config)
    config_str = ''.join(['↑' if s > 0 else '↓' for s in config])
    print(f"{name:15s}: {config_str} → {output:.6f}")

# Create a representative attention matrix
# In practice, this would come from the actual model
attention_matrix = jnp.array([
    [0.1, 0.4, 0.3, 0.2],  # Site 0 attention to all sites
    [0.4, 0.1, 0.2, 0.3],  # Site 1 attention to all sites
    [0.3, 0.2, 0.1, 0.4],  # Site 2 attention to all sites
    [0.2, 0.3, 0.4, 0.1]   # Site 3 attention to all sites
])

# Visualize attention weights
node_labels = [f'Site {i}' for i in range(n_sites)]
fig = plot_attention_weights(attention_matrix, node_labels=node_labels, figsize=(8, 6))
plt.suptitle('GAT Attention Weights for Heisenberg Chain', fontsize=14)
plt.show()

print("\n📊 Attention Analysis:")
print(f"Average attention strength: {jnp.mean(attention_matrix):.3f}")
print(f"Attention variance: {jnp.var(attention_matrix):.3f}")
print(f"Max attention: {jnp.max(attention_matrix):.3f}")
print(f"Min attention: {jnp.min(attention_matrix):.3f}")

# Set up 2D Ising model
L_2d = 2  # 2x2 lattice
print(f"🔲 Setting up {L_2d}x{L_2d} Ising model")

# Create Hamiltonian
hamiltonian_2d, exact_energy_2d = create_spin_hamiltonian("ising_2d", L_2d, J=1.0, h=0.0, pbc=True)
hilbert_2d = hamiltonian_2d.hilbert

print(f"System size: {L_2d}x{L_2d} = {hilbert_2d.size} sites")
print(f"Exact ground state energy: {exact_energy_2d:.6f}")

# Create 2D Spin GAT
model_2d = NetKetSpinGAT(
    n_sites=hilbert_2d.size,
    lattice_type="square",
    hidden_features=[16, 8],
    n_heads=[4, 1],
    rngs=nnx.Rngs(42)
)

print("✅ 2D Ising GAT model created")

# Quick optimization for 2D system
g_2d = nk.graph.Square(L_2d, pbc=True)
sampler_2d = nk.sampler.MetropolisLocal(hilbert_2d, n_chains=8)
vs_2d = nk.vqs.MCState(sampler_2d, model_2d, n_samples=500)

optimizer_2d = nk.optimizer.Sgd(learning_rate=0.01)
vmc_2d = nk.VMC(hamiltonian_2d, optimizer_2d, variational_state=vs_2d)

print("🚀 Optimizing 2D Ising model...")
energies_2d = []

for step in range(20):
    vmc_2d.advance()
    energy = vs_2d.expect(hamiltonian_2d)
    energies_2d.append(energy.mean)
    
    if step % 5 == 0:
        error = abs(energy.mean - exact_energy_2d)
        print(f"Step {step:2d}: Energy = {energy.mean:.6f}, Error = {error:.6f}")

final_energy_2d = energies_2d[-1]
final_error_2d = abs(final_energy_2d - exact_energy_2d)

print(f"\n✅ 2D Ising optimization complete")
print(f"Final energy: {final_energy_2d:.6f}")
print(f"Exact energy: {exact_energy_2d:.6f}")
print(f"Final error: {final_error_2d:.6f}")

# Demonstrate frustrated spin system (triangular lattice)
print("🔺 Frustrated Spin System Example")
print("Triangular lattice Heisenberg model exhibits geometric frustration")
print("where not all spin interactions can be simultaneously satisfied.")

# For demonstration, we'll use a small frustrated system
n_frustrated = 3
print(f"\nSetting up {n_frustrated}-site frustrated system...")

# Create frustrated GAT with special features
frustrated_model = NetKetSpinGAT(
    n_sites=n_frustrated,
    lattice_type="triangular",
    hidden_features=[12, 6],
    n_heads=[3, 1],
    spin_gat_type="frustrated",  # Special frustrated system handling
    rngs=nnx.Rngs(42)
)

print("✅ Frustrated system GAT created")
print("This model includes special attention mechanisms for handling frustration")

# Exercise template: Modify parameters and re-run
print("🎯 Exercise Template")
print("Modify the parameters below and re-run the optimization:")

# Exercise parameters
exercise_n_sites = 6  # Try 6 or 8
exercise_hidden_features = [32, 16]  # Try [64, 32] or [16, 8, 4]
exercise_n_heads = [8, 2]  # Try [4, 2] or [16, 4]
exercise_learning_rate = 0.01  # Try 0.005 or 0.02

print(f"Exercise setup:")
print(f"  Sites: {exercise_n_sites}")
print(f"  Hidden features: {exercise_hidden_features}")
print(f"  Attention heads: {exercise_n_heads}")
print(f"  Learning rate: {exercise_learning_rate}")
print("\nRun the cells above with these parameters to see the effect!")