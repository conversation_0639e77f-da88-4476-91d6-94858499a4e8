#!/usr/bin/env python3
"""
Test Polynomial Models

Simple test script to debug polynomial model implementations.
"""

import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx
import numpy as np

from core.polynomial_layers import CP, CP_sparse_LU, get_polynomial_class
from models.baselines.poly_gcnn_wrapper import PolyGCNNModelWrapper
from models.baselines.poly_rbm_wrapper import PolyRBMModelWrapper


def test_polynomial_layers():
    """Test basic polynomial layer functionality."""
    print("🧪 Testing Polynomial Layers")
    print("=" * 50)
    
    # Test parameters
    degree = 2
    input_dim = 4
    rank = 2
    output_dim = 4
    batch_size = 2
    
    # Create test input
    key = jax.random.PRNGKey(42)
    rngs = nnx.Rngs(42)
    x = jax.random.normal(key, (batch_size, input_dim))
    
    print(f"Input shape: {x.shape}")
    print(f"Input values: {x}")
    
    # Test CP layer
    print("\n🔬 Testing CP Layer:")
    try:
        cp_layer = CP(degree, input_dim, rank, output_dim, rngs=rngs)
        cp_output = cp_layer(x)
        print(f"✅ CP output shape: {cp_output.shape}")
        print(f"   Output values: {cp_output}")
        print(f"   Contains NaN: {jnp.any(jnp.isnan(cp_output))}")
        print(f"   Contains Inf: {jnp.any(jnp.isinf(cp_output))}")
    except Exception as e:
        print(f"❌ CP layer failed: {e}")
    
    # Test CP_sparse_LU layer
    print("\n🔬 Testing CP_sparse_LU Layer:")
    try:
        sparse_layer = CP_sparse_LU(degree, input_dim, rank, output_dim, rngs=rngs)
        sparse_output = sparse_layer(x)
        print(f"✅ CP_sparse_LU output shape: {sparse_output.shape}")
        print(f"   Output values: {sparse_output}")
        print(f"   Contains NaN: {jnp.any(jnp.isnan(sparse_output))}")
        print(f"   Contains Inf: {jnp.any(jnp.isinf(sparse_output))}")
    except Exception as e:
        print(f"❌ CP_sparse_LU layer failed: {e}")


def test_poly_gcnn():
    """Test Polynomial GCNN model."""
    print("\n🧪 Testing Polynomial GCNN")
    print("=" * 50)
    
    # Create simple test configuration
    config = {
        'layers': 2,
        'features': 4,
        'degree': 2,
        'poly_class': 'CP',
        'poly_layers': 1,
        'param_dtype': 'float64'
    }
    
    # Create simple Hilbert space
    hilbert = nk.hilbert.Spin(s=1/2, N=4)
    
    try:
        model, param_count = PolyGCNNModelWrapper.from_config(
            config=config,
            physics_type="spin",
            system_size=4,
            problem_name="test",
            hilbert=hilbert,
            random_seed=42
        )
        
        print(f"✅ Model created successfully")
        print(f"   Parameter count: {param_count}")
        
        # Test forward pass
        test_input = jnp.ones((1, 4))
        print(f"   Test input shape: {test_input.shape}")
        
        try:
            output = model(test_input)
            print(f"✅ Forward pass successful")
            print(f"   Output shape: {output.shape}")
            print(f"   Output values: {output}")
            print(f"   Contains NaN: {jnp.any(jnp.isnan(output))}")
            print(f"   Contains Inf: {jnp.any(jnp.isinf(output))}")
        except Exception as e:
            print(f"❌ Forward pass failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()


def test_poly_rbm():
    """Test Polynomial RBM model."""
    print("\n🧪 Testing Polynomial RBM")
    print("=" * 50)
    
    # Create simple test configuration
    config = {
        'n_hidden': 4,
        'degree': 2,
        'poly_class': 'CP',
        'param_dtype': 'float64'
    }
    
    # Create simple Hilbert space
    hilbert = nk.hilbert.Spin(s=1/2, N=4)
    
    try:
        model, param_count = PolyRBMModelWrapper.from_config(
            config=config,
            physics_type="spin",
            system_size=4,
            problem_name="test",
            hilbert=hilbert,
            random_seed=42
        )
        
        print(f"✅ Model created successfully")
        print(f"   Parameter count: {param_count}")
        
        # Test forward pass
        test_input = jnp.ones((1, 4))
        print(f"   Test input shape: {test_input.shape}")
        
        try:
            output = model(test_input)
            print(f"✅ Forward pass successful")
            print(f"   Output: {output}")
            print(f"   Contains NaN: {jnp.any(jnp.isnan(output))}")
            print(f"   Contains Inf: {jnp.any(jnp.isinf(output))}")
        except Exception as e:
            print(f"❌ Forward pass failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests."""
    print("🚀 Testing Polynomial Model Implementation")
    print("=" * 60)
    
    # Test individual layers first
    test_polynomial_layers()
    
    # Test complete models
    test_poly_gcnn()
    test_poly_rbm()
    
    print("\n🎯 Testing Complete!")


if __name__ == "__main__":
    main()
