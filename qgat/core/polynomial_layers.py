"""
Polynomial Neural Network Layers for Quantum Systems

Implements various polynomial network architectures inspired by CP decomposition
and adapted for quantum neural networks using JAX and NetKet's nnx framework.
"""

import jax
import jax.numpy as jnp
from flax import nnx
from typing import Optional, Callable, Any
import math


class CP(nnx.Module):
    """
    Canonical Polyadic (CP) decomposition polynomial network.
    
    Implements polynomial transformations using CP decomposition for efficient
    computation of high-degree polynomial features.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int, 
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        """
        Initialize CP polynomial network.
        
        Args:
            degree: Polynomial degree
            input_dim: Input feature dimension
            rank: CP decomposition rank (hidden dimension)
            output_dim: Output feature dimension
            param_dtype: Parameter data type
            rngs: Random number generators
        """
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        
        # Create U matrices for each degree
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(
                input_dim, rank, use_bias=False, 
                param_dtype=param_dtype, rngs=rngs
            ))
        
        # Final combination layer
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)
    
    def __call__(self, x):
        """Forward pass through CP polynomial network."""
        # Store original shape for batch processing
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        
        # Start with first degree
        out = getattr(self, 'U1')(x_flat)
        
        # Apply polynomial combinations
        for i in range(2, self.degree + 1):
            ui_out = getattr(self, f'U{i}')(x_flat)
            out = ui_out * out + out
        
        # Final linear combination
        result = self.layer_C(out)
        
        # Restore original shape
        return result.reshape(*original_shape, self.output_dim)


class CP_sparse_LU(nnx.Module):
    """
    Sparse CP decomposition with LU-style masking patterns.
    
    Uses structured sparsity to reduce parameters while maintaining
    polynomial expressiveness.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        
        # Create masked U matrices
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(
                input_dim, rank, use_bias=False,
                param_dtype=param_dtype, rngs=rngs
            ))
        
        # Generate LU-style masks
        self.mask1, self.mask2 = self._generate_lu_masks()
        
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)
    
    def _generate_lu_masks(self):
        """Generate upper and lower triangular-style masks."""
        # Create alternating upper/lower triangular patterns
        mask1 = jnp.triu(jnp.ones((self.rank, self.input_dim)))
        mask2 = jnp.tril(jnp.ones((self.rank, self.input_dim)))
        return mask1, mask2
    
    def __call__(self, x):
        """Forward pass with sparse LU masking."""
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        
        # Apply first layer with mask1
        u1_weight = getattr(self, 'U1').kernel * self.mask1.T
        out = jnp.dot(x_flat, u1_weight)
        
        # Apply remaining layers with alternating masks
        for i in range(2, self.degree + 1, 2):
            # Even degrees use mask2
            ui_weight = getattr(self, f'U{i}').kernel * self.mask2.T
            ui_out = jnp.dot(x_flat, ui_weight)
            out = ui_out * out + out
            
            # Odd degrees use mask1 (if exists)
            if i + 1 <= self.degree:
                ui1_weight = getattr(self, f'U{i+1}').kernel * self.mask1.T
                ui1_out = jnp.dot(x_flat, ui1_weight)
                out = ui1_out * out + out
        
        result = self.layer_C(out)
        return result.reshape(*original_shape, self.output_dim)


class CP_sparse_degree(nnx.Module):
    """
    Degree-specific sparse CP decomposition.
    
    Uses different sparsity patterns for different polynomial degrees
    to optimize parameter efficiency.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 param_dtype: Any = jnp.float64, rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        
        # Create U matrices
        for i in range(1, degree + 1):
            setattr(self, f'U{i}', nnx.Linear(
                input_dim, rank, use_bias=False,
                param_dtype=param_dtype, rngs=rngs
            ))
        
        # Generate degree-specific masks
        self.masks = self._generate_degree_masks()
        
        self.layer_C = nnx.Linear(rank, output_dim, param_dtype=param_dtype, rngs=rngs)
    
    def _generate_degree_masks(self):
        """Generate different sparsity patterns for each degree."""
        masks = {}
        for i in range(1, self.degree + 1):
            # Create degree-specific sparsity pattern
            # Higher degrees get sparser patterns
            sparsity_ratio = 1.0 - (i - 1) * 0.1  # Decrease density with degree
            mask = jax.random.bernoulli(
                jax.random.PRNGKey(i), 
                sparsity_ratio, 
                (self.rank, self.input_dim)
            )
            masks[f'mask{i}'] = mask
        return masks
    
    def __call__(self, x):
        """Forward pass with degree-specific sparsity."""
        original_shape = x.shape[:-1]
        x_flat = x.reshape(-1, self.input_dim)
        
        # Apply first layer
        u1_weight = getattr(self, 'U1').kernel * self.masks['mask1'].T
        out = jnp.dot(x_flat, u1_weight)
        
        # Apply remaining layers with degree-specific masks
        for i in range(2, self.degree + 1):
            ui_weight = getattr(self, f'U{i}').kernel * self.masks[f'mask{i}'].T
            ui_out = jnp.dot(x_flat, ui_weight)
            out = ui_out * out + out
        
        result = self.layer_C(out)
        return result.reshape(*original_shape, self.output_dim)


class QuantumPolynomialLayer(nnx.Module):
    """
    Quantum-specific polynomial layer with symmetry preservation.
    
    Designed specifically for quantum many-body systems with built-in
    symmetry constraints and quantum-aware initialization.
    """
    
    def __init__(self, degree: int, input_dim: int, rank: int, output_dim: int,
                 preserve_symmetries: bool = True, param_dtype: Any = jnp.float64,
                 rngs: Optional[nnx.Rngs] = None):
        if rngs is None:
            rngs = nnx.Rngs(0)
            
        self.degree = degree
        self.input_dim = input_dim
        self.rank = rank
        self.output_dim = output_dim
        self.preserve_symmetries = preserve_symmetries
        
        # Use quantum-aware initialization
        self.polynomial_net = CP(degree, input_dim, rank, output_dim, 
                               param_dtype, rngs)
        
        # Add symmetry preservation if requested
        if preserve_symmetries:
            self.symmetry_projection = nnx.Linear(
                output_dim, output_dim, param_dtype=param_dtype, rngs=rngs
            )
    
    def __call__(self, x):
        """Forward pass with optional symmetry preservation."""
        # Apply polynomial transformation
        out = self.polynomial_net(x)
        
        # Apply symmetry preservation if enabled
        if self.preserve_symmetries:
            out = self.symmetry_projection(out)
        
        return out


# Registry of available polynomial classes
POLYNOMIAL_CLASSES = {
    'CP': CP,
    'CP_sparse_LU': CP_sparse_LU,
    'CP_sparse_degree': CP_sparse_degree,
    'QuantumPolynomial': QuantumPolynomialLayer
}


def get_polynomial_class(name: str):
    """Get polynomial class by name."""
    if name not in POLYNOMIAL_CLASSES:
        raise ValueError(f"Unknown polynomial class: {name}. "
                        f"Available: {list(POLYNOMIAL_CLASSES.keys())}")
    return POLYNOMIAL_CLASSES[name]
