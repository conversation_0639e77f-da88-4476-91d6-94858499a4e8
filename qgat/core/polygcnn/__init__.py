"""
PolyGCNN: Polynomial Graph Convolutional Neural Networks for Quantum Systems

A package for polynomial enhancements to quantum neural networks,
built on top of NetKet's symmetry-preserving framework.

This package implements polynomial versions of NetKet's GCNN layers that perform
true polynomial recursion without intermediate activations, enabling enhanced
expressiveness for quantum many-body systems.
"""

from .poly_dense_equivariant import PolyDenseEquivariantFFT, PolyDenseEquivariantIrrep
from .poly_gcnn_fft import PolyGCNN_FFT
from .poly_gcnn_irrep import PolyGCNN_Irrep
from .poly_gcnn_parity import PolyGCNN_Parity_FFT, PolyGCNN_Parity_Irrep
from .factory import PolyGCNN, create_poly_gcnn_for_spin_system

__all__ = [
    # Layers
    'PolyDenseEquivariantFFT',
    'PolyDenseEquivariantIrrep',

    # Models
    'PolyGCNN_FFT',
    'PolyGCNN_Irrep',
    'PolyGCNN_Parity_FFT',
    'PolyGCNN_Parity_Irrep',
    'PolyGCNN',

    # Factory functions
    'create_poly_gcnn_for_spin_system',
]

__version__ = "0.1.0"
