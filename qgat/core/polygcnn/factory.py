"""
Factory functions for Polynomial GCNN models.

Provides NetKet-style factory functions for creating polynomial GCNN variants.
"""

from typing import Any
import numpy as np
import netket as nk
from netket.utils import HashableArray

from .poly_gcnn_fft import PolyGCNN_FFT
from .poly_gcnn_irrep import PolyGCNN_Irrep
from .poly_gcnn_parity import PolyGCNN_Parity_FFT, PolyGCNN_Parity_Irrep


def PolyGCNN(
    symmetries=None,
    product_table=None,
    irreps=None,
    degree=2,
    poly_degrees=None,
    no_activation=False,
    poly_output=False,
    parity=None,
    mode="auto",
    **gcnn_kwargs
):
    """
    Factory function for Polynomial GCNN.

    Creates polynomial GCNN models with configurable polynomial degrees
    and output modes. Supports all NetKet GCNN variants with polynomial enhancement.

    Args:
        symmetries: Symmetry group for the model
        product_table: Product table for FFT mode (optional)
        irreps: Irreducible representation matrices for irrep mode (optional)
        degree: Default polynomial degree for all layers (default: 2)
        poly_degrees: Tuple of per-layer polynomial degrees (optional)
        no_activation: If True, no activations between layers (default: False)
        poly_output: If True, simple sum output instead of logsumexp (default: False)
        parity: Parity eigenvalue for parity-symmetric models (optional)
        mode: Implementation mode - "fft", "irreps", or "auto" (default: "auto")
        **gcnn_kwargs: All standard GCNN parameters (layers, features, etc.)

    Returns:
        Polynomial GCNN model instance

    Example:
        >>> # Basic polynomial GCNN with degree-2 polynomials
        >>> model = PolyGCNN(
        ...     symmetries=symmetries,
        ...     layers=2,
        ...     features=[8, 16],
        ...     degree=2,
        ...     mode="fft"
        ... )

        >>> # Polynomial GCNN with parity symmetry
        >>> parity_model = PolyGCNN(
        ...     symmetries=symmetries,
        ...     layers=2,
        ...     features=[8, 16],
        ...     degree=2,
        ...     parity=1,
        ...     mode="fft"
        ... )

        >>> # Irrep-based polynomial GCNN
        >>> irrep_model = PolyGCNN(
        ...     symmetries=symmetries,
        ...     layers=2,
        ...     features=[8, 16],
        ...     degree=2,
        ...     mode="irreps"
        ... )
    """

    # Extract required parameters from symmetries if it's a graph
    # Follow NetKet's exact pattern from equivariant.py lines 706-723
    if hasattr(symmetries, 'translation_group'):
        # It's a graph object - follow NetKet's pattern exactly
        graph = symmetries

        # Check if it's a Lattice with point group (like honeycomb)
        if isinstance(graph, nk.graph.Lattice) and hasattr(graph, '_point_group'):
            # Use NetKet's exact pattern for complex lattices
            shape = tuple(graph.extent)
            symmetries = graph.space_group(graph._point_group)  # Use space_group, not automorphisms!
        else:
            # For simple graphs, use translation group
            symmetries = graph.translation_group()
            # Determine shape from graph
            if hasattr(graph, 'extent'):
                shape = tuple(graph.extent)
            else:
                shape = (graph.n_nodes,)
    else:
        # It's already a symmetry group
        if 'shape' not in gcnn_kwargs:
            raise ValueError("shape must be provided when passing symmetries directly")
        shape = gcnn_kwargs.pop('shape')

    # Set default characters if not provided
    if 'characters' not in gcnn_kwargs:
        characters = np.ones(len(symmetries))
    else:
        characters = gcnn_kwargs.pop('characters')

    # Set product table for FFT mode
    if product_table is None and mode == "fft":
        # Use the product table from the symmetry group as NetKet does
        # This is the key fix: use sg.product_table, not np.asarray(sg)
        if hasattr(symmetries, 'product_table'):
            product_table = HashableArray(symmetries.product_table)
        else:
            # Fallback for simple symmetry groups
            product_table = HashableArray(np.asarray(symmetries))

    # Set irreps for irrep mode
    if irreps is None and mode in ["irreps", "auto"]:
        # Use symmetries as irreps for auto mode
        irreps = tuple(np.eye(len(symmetries), dtype=complex) for _ in range(len(symmetries)))

    # Determine implementation based on mode and parity
    if mode == "fft":
        if parity is not None:
            return PolyGCNN_Parity_FFT(
                symmetries=symmetries,
                product_table=product_table,
                shape=shape,
                characters=characters,
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                parity=parity,
                **gcnn_kwargs
            )
        else:
            return PolyGCNN_FFT(
                symmetries=symmetries,
                product_table=product_table,
                shape=shape,
                characters=characters,
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                **gcnn_kwargs
            )
    elif mode in ["irreps", "auto"]:
        if parity is not None:
            return PolyGCNN_Parity_Irrep(
                symmetries=symmetries,
                irreps=irreps,
                characters=characters,
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                parity=parity,
                **gcnn_kwargs
            )
        else:
            return PolyGCNN_Irrep(
                symmetries=symmetries,
                irreps=irreps,
                characters=characters,
                degree=degree,
                poly_degrees=poly_degrees,
                no_activation=no_activation,
                poly_output=poly_output,
                **gcnn_kwargs
            )
    else:
        raise ValueError(f"Unknown mode={mode}. Valid modes are 'fft', 'irreps', or 'auto'.")


def create_poly_gcnn_for_spin_system(
    system_size,
    layers=2,
    features=None,
    degree=2,
    poly_degrees=None,
    no_activation=False,
    poly_output=False,
    dimension=1,
    pbc=True,
    **kwargs
):
    """
    Convenience function to create polynomial GCNN for spin systems.
    
    Args:
        system_size: Number of spins in the system
        layers: Number of layers (default: 2)
        features: Features per layer (default: [8, 16])
        degree: Polynomial degree (default: 2)
        poly_degrees: Per-layer degrees (optional)
        no_activation: No activations between layers (default: False)
        poly_output: Simple sum output (default: False)
        dimension: Spatial dimension (1 or 2, default: 1)
        pbc: Periodic boundary conditions (default: True)
        **kwargs: Additional GCNN parameters
        
    Returns:
        Polynomial GCNN model configured for spin system
    """
    import netket as nk
    
    # Set default features
    if features is None:
        features = [8, 16] if layers == 2 else [8] * layers
    
    # Create graph and symmetries
    if dimension == 1:
        g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=pbc)
    elif dimension == 2:
        L = int(np.sqrt(system_size))
        if L * L != system_size:
            raise ValueError(f"For 2D systems, system_size must be a perfect square. Got {system_size}")
        g = nk.graph.Square(L, pbc=pbc)
    else:
        raise ValueError(f"Dimension {dimension} not supported. Use 1 or 2.")
    
    symmetries = g.translation_group()
    
    # Create polynomial GCNN
    return PolyGCNN(
        symmetries=symmetries,
        product_table=symmetries,
        shape=(system_size,) if dimension == 1 else (L, L),
        layers=layers,
        features=tuple(features),
        characters=np.ones(len(symmetries)),
        degree=degree,
        poly_degrees=poly_degrees,
        no_activation=no_activation,
        poly_output=poly_output,
        mode="fft",
        **kwargs
    )
