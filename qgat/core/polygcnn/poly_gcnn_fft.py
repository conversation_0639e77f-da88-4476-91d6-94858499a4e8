"""
Polynomial GCNN using FFT implementation.

Implements PolyGCNN_FFT that uses PolyDenseEquivariantFFT layers
with configurable activation and output modes.
"""

import jax.numpy as jnp
import numpy as np
from flax import linen as nn
from typing import Any

from netket.utils import HashableArray
from netket.utils.types import NNInitFunc, Array
from netket.nn.activation import reim_selu
from netket.nn.symmetric_linear import DenseSymmFFT
from netket.jax import logsumexp_cplx
from jax.scipy.special import logsumexp
from jax.nn.initializers import zeros, lecun_normal

from .poly_dense_equivariant import PolyDenseEquivariantFFT

# Use NetKet's default initializer
default_gcnn_initializer = lecun_normal(in_axis=1, out_axis=0)


def identity(x):
    """Identity activation function."""
    return x


class PolyGCNN_FFT(nn.Module):
    """
    Polynomial GCNN using FFT implementation.
    
    Uses PolyDenseEquivariantFFT layers in the standard GCNN framework
    with configurable activation and output modes.
    
    Key Features:
    - no_activation: Performs multi-layer loop without activations
    - poly_output: Only sums output, skips logsumexp reduction
    - per-layer polynomial degrees
    
    Args:
        symmetries: Group of symmetry operations
        product_table: Product table describing symmetry group algebra
        shape: Shape of the translation group
        layers: Number of layers
        features: Number of features in each layer
        characters: Characters of desired symmetry representation
        degree: Default polynomial degree for all layers
        poly_degrees: Per-layer polynomial degrees (optional)
        no_activation: If True, no activations between layers
        poly_output: If True, simple sum instead of logsumexp
        param_dtype: Data type for parameters
        activation: Activation function between layers
        output_activation: Activation before output
        use_bias: Whether to use bias
        precision: Numerical precision
        kernel_init: Kernel initializer
        bias_init: Bias initializer
        complex_output: Use complex-valued logsumexp
        equal_amplitudes: Force equal amplitudes for all basis states
        input_mask: Optional mask for input layer
        hidden_mask: Optional mask for hidden layers
    """
    
    # Standard GCNN parameters
    symmetries: HashableArray
    """A group of symmetry operations over which the network should be equivariant."""
    product_table: HashableArray
    """Product table describing the algebra of the symmetry group"""
    shape: tuple
    """Shape of the translation group"""
    layers: int
    """Number of layers (not including sum layer over output)."""
    features: tuple
    """Number of features in each layer starting from the input."""
    characters: HashableArray
    """Array specifying the characters of the desired symmetry representation"""
    
    # NEW: Polynomial parameters
    degree: int = 2
    """Default polynomial degree for all layers"""
    poly_degrees: tuple | None = None
    """Per-layer polynomial degrees (optional)"""
    
    # NEW: Control flags
    no_activation: bool = False
    """If True, performs multi-layer loop without activations between layers"""
    poly_output: bool = False
    """If True, only sums output, skips logsumexp reduction"""
    
    # Standard GCNN parameters
    param_dtype: Any = np.float64
    """The dtype of the weights."""
    activation: Any = reim_selu
    """The nonlinear activation function between hidden layers."""
    output_activation: Any = identity
    """The nonlinear activation before the output."""
    use_bias: bool = True
    """If True uses a bias in all layers."""
    precision: Any = None
    """Numerical precision of the computation."""
    kernel_init: NNInitFunc = default_gcnn_initializer
    """Initializer for the kernels of all layers."""
    bias_init: NNInitFunc = zeros
    """Initializer for the biases of all layers."""
    complex_output: bool = True
    """Use complex-valued logsumexp."""
    equal_amplitudes: bool = False
    """If True forces all basis states to have the same amplitude."""
    input_mask: Array = None
    """Optional mask for input layer."""
    hidden_mask: Array = None
    """Optional mask for hidden layers."""
    
    def setup(self):
        """Setup polynomial GCNN layers efficiently."""
        self.n_symm = np.asarray(self.symmetries).shape[0]

        # Standard dense_symm layer (unchanged)
        self.dense_symm = DenseSymmFFT(
            space_group=self.symmetries,
            shape=self.shape,
            features=self.features[0],
            param_dtype=self.param_dtype,
            use_bias=self.use_bias,
            kernel_init=self.kernel_init,
            bias_init=self.bias_init,
            precision=self.precision,
            mask=self.input_mask,
        )

        # Create polynomial equivariant layers using NetKet pattern
        self.poly_equivariant_layers = [
            PolyDenseEquivariantFFT(
                product_table=self.product_table,
                shape=self.shape,
                features=self.features[layer + 1],
                degree=(self.poly_degrees[layer]
                       if self.poly_degrees is not None and layer < len(self.poly_degrees)
                       else self.degree),  # Polynomial degree
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.hidden_mask,
            )
            for layer in range(self.layers - 1)
        ]

    @nn.compact
    def __call__(self, x):
        """
        Forward pass with configurable activation and output modes.

        Modes:
        1. Standard: activations between layers, logsumexp output
        2. no_activation: no activations between layers (pure polynomial)
        3. poly_output: sum output instead of logsumexp

        Args:
            x: Input tensor of shape [batch, n_sites]

        Returns:
            Output tensor (scalar per batch element for standard mode)
        """

        # Standard GCNN preprocessing
        if x.ndim < 3:
            x = jnp.expand_dims(x, -2)
        x = self.dense_symm(x)

        # Apply polynomial equivariant layers
        for layer in range(self.layers - 1):
            # Apply activation (unless no_activation flag is set)
            if not self.no_activation:
                x = self.activation(x)

            # Apply polynomial equivariant layer
            x = self.poly_equivariant_layers[layer](x)

        # Output activation
        x = self.output_activation(x)

        # Handle different output modes
        if self.poly_output:
            # Simple sum over features and symmetries
            return jnp.sum(x, axis=(-2, -1))

        # Standard GCNN output with logsumexp
        if self.complex_output:
            x = logsumexp_cplx(x, axis=(-2, -1), b=jnp.asarray(self.characters))
        else:
            x = logsumexp(x, axis=(-2, -1), b=jnp.asarray(self.characters))

        if self.equal_amplitudes:
            return 1j * jnp.imag(x)
        else:
            return x
