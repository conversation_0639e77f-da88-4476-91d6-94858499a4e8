"""
Polynomial Dense Equivariant Layers

Implements polynomial versions of NetKet's DenseEquivariant layers that perform
true polynomial recursion without intermediate activations.
"""

import jax.numpy as jnp
import numpy as np
from flax import linen as nn
from typing import Any
from jax.nn.initializers import zeros, lecun_normal

from netket.utils import HashableArray
from netket.utils.types import NNInitFunc, Array, DType
from netket.nn.symmetric_linear import DenseEquivariantFFT, DenseEquivariantIrrep

# Use NetKet's default initializer
default_poly_equivariant_initializer = lecun_normal(in_axis=1, out_axis=0)


class PolyDenseEquivariantFFT(nn.Module):
    """
    Polynomial Dense Equivariant layer using FFT implementation.
    
    Creates degree-many DenseEquivariantFFT instances and combines them
    using polynomial recursion: out = U_degree(x) * out + out
    
    This preserves all NetKet symmetry operations while adding polynomial
    interactions without intermediate activations.
    
    Args:
        product_table: Product table for space group
        features: Number of output features
        shape: Shape of the translation group
        degree: Polynomial degree (default: 2)
        use_bias: Whether to add bias (default: True)
        mask: Optional mask for restricting kernel
        param_dtype: Data type for parameters
        precision: Numerical precision
        kernel_init: Kernel initializer
        bias_init: Bias initializer
    """
    
    # Inherit all DenseEquivariantFFT parameters
    product_table: HashableArray
    """Product table for space group."""
    features: int
    """The number of output features."""
    shape: tuple
    """Tuple that corresponds to shape of lattice"""
    degree: int = 2
    """Polynomial degree (NEW parameter)"""
    use_bias: bool = True
    """Whether to add a bias to the output."""
    mask: HashableArray | None = None
    """Optional mask for restricting the convolutional kernel."""
    param_dtype: DType = jnp.float64
    """The dtype of the weights."""
    precision: Any = None
    """Numerical precision of the computation."""
    kernel_init: NNInitFunc = default_poly_equivariant_initializer
    """Initializer for the kernels."""
    bias_init: NNInitFunc = zeros
    """Initializer for the biases."""
    
    def setup(self):
        """Create degree-many DenseEquivariantFFT instances efficiently."""

        # Create degree-many equivariant layers for polynomial recursion
        self.equivariant_layers = [
            DenseEquivariantFFT(
                product_table=self.product_table,
                shape=self.shape,
                features=self.features,
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.mask,
            )
            for i in range(1, self.degree + 1)
        ]

    @nn.compact
    def __call__(self, x: Array) -> Array:
        """
        Apply polynomial recursion using pre-created equivariant layers.

        Implements the polynomial pattern:
        out = U_1(x)
        for i in range(2, degree + 1):
            out = U_i(x) * out + out

        This creates true polynomial interactions while preserving
        all symmetry properties of the input.

        Args:
            x: Input tensor of shape [batch, features, n_symm]

        Returns:
            Output tensor of shape [batch, features, n_symm] with polynomial interactions
        """

        # Start with first degree (linear term)
        out = self.equivariant_layers[0](x)  # U_1(x)

        # Apply polynomial recursion (no activations!)
        for i in range(1, self.degree):
            ui_out = self.equivariant_layers[i](x)  # U_i(x)
            out = ui_out * out + out  # Pure polynomial combination

        return out


class PolyDenseEquivariantIrrep(nn.Module):
    """
    Polynomial Dense Equivariant layer using Irrep implementation.

    Creates degree-many DenseEquivariantIrrep instances and combines them
    using polynomial recursion: out = U_degree(x) * out + out

    This preserves all NetKet symmetry operations while adding polynomial
    interactions without intermediate activations, using irreducible
    representations for general symmetry groups.

    Args:
        irreps: Irreducible representation matrices
        features: Number of output features
        degree: Polynomial degree (default: 2)
        use_bias: Whether to add bias (default: True)
        mask: Optional mask for restricting kernel
        param_dtype: Data type for parameters
        precision: Numerical precision
        kernel_init: Kernel initializer
        bias_init: Bias initializer
    """

    irreps: tuple[HashableArray, ...]
    """Irrep matrices of the symmetry group."""
    features: int
    """The number of output features."""
    degree: int = 2
    """Polynomial degree (NEW parameter)"""
    use_bias: bool = True
    """Whether to add a bias to the output."""
    mask: HashableArray | None = None
    """Optional mask for restricting the convolutional kernel."""
    param_dtype: DType = jnp.float64
    """The dtype of the weights."""
    precision: Any = None
    """Numerical precision of the computation."""
    kernel_init: NNInitFunc = default_poly_equivariant_initializer
    """Initializer for the kernels."""
    bias_init: NNInitFunc = zeros
    """Initializer for the biases."""

    def setup(self):
        """Create degree-many DenseEquivariantIrrep instances efficiently."""

        # Create degree-many equivariant layers for polynomial recursion
        self.equivariant_layers = [
            DenseEquivariantIrrep(
                irreps=self.irreps,
                features=self.features,
                use_bias=self.use_bias,
                param_dtype=self.param_dtype,
                precision=self.precision,
                kernel_init=self.kernel_init,
                bias_init=self.bias_init,
                mask=self.mask,
            )
            for i in range(1, self.degree + 1)
        ]

    @nn.compact
    def __call__(self, x: Array) -> Array:
        """
        Apply polynomial recursion using pre-created irrep-based equivariant layers.

        Implements the polynomial pattern:
        out = U_1(x)
        for i in range(2, degree + 1):
            out = U_i(x) * out + out

        This creates true polynomial interactions while preserving
        all symmetry properties of the input using irreducible representations.

        Args:
            x: Input tensor of shape [batch, features, n_symm]

        Returns:
            Output tensor of shape [batch, features, n_symm] with polynomial interactions
        """

        # Start with first degree (linear term)
        out = self.equivariant_layers[0](x)  # U_1(x)

        # Apply polynomial recursion (no activations!)
        for i in range(1, self.degree):
            ui_out = self.equivariant_layers[i](x)  # U_i(x)
            out = ui_out * out + out  # Pure polynomial combination

        return out
