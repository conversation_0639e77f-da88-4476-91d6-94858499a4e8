"""
Model Registry and Factory System

Provides a unified interface for creating and managing different model types
including QGAT, RBM, GCNN, and other neural network architectures.
"""

from .registry import ModelFactory, OptimizerFactory, SamplerFactory, PhysicsFactory
from .gat import QGATModelWrapper
from .baselines import RB<PERSON>odelWrapper, GCNNModelWrapper
from .baselines.polynomial_gcnn_wrapper import PolynomialGCNNModelWrapper
from .baselines.poly_gcnn_wrapper import PolyGCNNModelWrapper
from .baselines.poly_rbm_wrapper import PolyRBMModelWrapper

# Register standard model types
ModelFactory.register("gat", QGATModelWrapper)
ModelFactory.register("rbm", RBMModelWrapper)
ModelFactory.register("gcnn", GCNNModelWrapper)

# Register polynomial model types (new subclassing approach)
ModelFactory.register("poly_gcnn", PolyGCNNModelWrapper)
ModelFactory.register("poly_rbm", PolyRBMModelWrapper)

# Register backward compatibility aliases
ModelFactory.register("polynomial_gcnn", PolynomialGCNNModelWrapper)

__all__ = [
    'ModelFactory',
    'OptimizerFactory',
    'SamplerFactory',
    'PhysicsFactory',
    'QGATModelWrapper',
    'RBMModelWrapper',
    'GCNNModelWrapper',
    'PolynomialGCNNModelWrapper',
    'PolyGCNNModelWrapper',
    'PolyRBMModelWrapper'
]
