"""
RBM Model Wrapper

Provides a unified interface for creating RBM models.
"""

from typing import Dict, Any
import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx


class RBMModelWrapper:
    """Wrapper for RBM models with unified configuration interface."""
    
    @staticmethod
    def from_config(config: Dict[str, Any], **kwargs):
        """Create RBM model from configuration."""
        
        # Get RBM parameters
        alpha = config.get('alpha', 2)
        param_dtype = config.get('param_dtype', 'float64')
        
        # Convert param_dtype string to actual dtype
        if param_dtype == 'float64':
            dtype = jnp.float64
        elif param_dtype == 'float32':
            dtype = jnp.float32
        else:
            dtype = float
        
        # Get hilbert space for proper initialization
        hilbert = kwargs.get('hilbert')
        if hilbert is None:
            raise ValueError("Hilbert space required for RBM creation")
        
        # Create RBM using NetKet with NNX bridge pattern
        rbm_linen = nk.models.RBM(alpha=alpha, param_dtype=dtype)
        
        # Initialize with proper NNX bridge
        key = jax.random.PRNGKey(42)
        rbm_nnx = nnx.bridge.ToNNX(rbm_linen, rngs=nnx.Rngs(key)).lazy_init(
            jnp.ones((1, hilbert.size))
        )

        # Get parameter count
        params = nnx.state(rbm_nnx)
        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params)
                         if hasattr(x, 'size'))

        return rbm_nnx, param_count
