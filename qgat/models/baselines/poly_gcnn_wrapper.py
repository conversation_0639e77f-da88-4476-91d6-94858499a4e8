"""
Polynomial GCNN Wrapper

Extends the standard GCNN with polynomial layers following the subclassing
approach from Polyformer. Replaces the final layers with polynomial networks
while preserving all base GCNN functionality.
"""

import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx
from typing import Dict, Any, Optional, <PERSON><PERSON>
import numpy as np

from core.polynomial_layers import get_polynomial_class, CP
from .gcnn_wrapper import GCNNModelWrapper


class PolyGCNN(nnx.Module):
    """
    Polynomial Graph Convolutional Neural Network.

    A simplified polynomial neural network for quantum systems that uses
    polynomial layers for enhanced expressiveness.
    """

    def __init__(self, layers: int = 2, features: int = 8,
                 degree: int = 2, poly_class: str = 'CP',
                 param_dtype: Any = jnp.float64,
                 rngs: Optional[nnx.Rngs] = None):
        """
        Initialize Polynomial GCNN.

        Args:
            layers: Number of layers
            features: Feature dimension
            degree: Polynomial degree
            poly_class: Type of polynomial network ('CP', 'CP_sparse_LU', etc.)
            param_dtype: Parameter data type
            rngs: Random number generators
        """
        if rngs is None:
            rngs = nnx.Rngs(0)

        self.layers = layers
        self.features = features
        self.degree = degree

        # Get polynomial class
        PolyClass = get_polynomial_class(poly_class)

        # Create input projection layer
        self.input_layer = nnx.Linear(
            features, features, param_dtype=param_dtype, rngs=rngs
        )

        # Create polynomial layers
        self.poly_layers_list = []
        for i in range(layers):
            # Calculate rank for polynomial layer (hidden dimension)
            rank = max(features // 2, 4)  # Ensure minimum rank

            poly_layer = PolyClass(
                degree=degree,
                input_dim=features,
                rank=rank,
                output_dim=features,
                param_dtype=param_dtype,
                rngs=rngs
            )
            self.poly_layers_list.append(poly_layer)

        # Final output layer
        self.output_layer = nnx.Linear(
            features, 1, param_dtype=param_dtype, rngs=rngs  # Output single value
        )
    
    def __call__(self, x):
        """Forward pass through Polynomial GCNN."""
        # Ensure input has correct feature dimension
        if x.shape[-1] != self.features:
            # Pad or project input to correct feature dimension
            if x.shape[-1] < self.features:
                # Pad with zeros
                padding = jnp.zeros((*x.shape[:-1], self.features - x.shape[-1]))
                x = jnp.concatenate([x, padding], axis=-1)
            else:
                # Project down
                x = x[..., :self.features]

        # Apply input layer
        x = self.input_layer(x)
        x = jnp.tanh(x)  # Activation

        # Apply polynomial layers
        for poly_layer in self.poly_layers_list:
            # Apply polynomial transformation
            poly_out = poly_layer(x)
            # Residual connection for stability
            x = x + poly_out
            # Bounded activation for quantum systems
            x = jnp.tanh(x)

        # Final output layer (sum over features for single output)
        x = self.output_layer(x)

        # Return log-amplitude (sum over spatial dimensions)
        return jnp.sum(x, axis=-2)  # Sum over spatial dimension, keep batch


class PolyGCNNModelWrapper:
    """
    Model wrapper for Polynomial GCNN that extends standard GCNN functionality.
    
    Follows the same interface as GCNNModelWrapper but adds polynomial capabilities.
    """
    
    @staticmethod
    def from_config(config: Dict[str, Any], physics_type: str, system_size: int,
                   problem_name: str, hilbert, random_seed: int = 42, **kwargs) -> Tuple[Any, int]:
        """
        Create Polynomial GCNN model from configuration.
        
        Args:
            config: Model configuration dictionary
            physics_type: Type of physics problem
            system_size: Size of the quantum system
            problem_name: Name of the specific problem
            hilbert: Hilbert space
            random_seed: Random seed for initialization
            **kwargs: Additional arguments
            
        Returns:
            Tuple of (model, parameter_count)
        """
        # Set random seed
        key = jax.random.PRNGKey(random_seed)
        rngs = nnx.Rngs(random_seed)
        
        # Extract polynomial-specific parameters
        degree = config.get('degree', 2)
        poly_class = config.get('poly_class', 'CP')
        poly_layers = config.get('poly_layers', 1)
        
        # Extract standard GCNN parameters
        layers = config.get('layers', 2)
        features = config.get('features', 8)
        param_dtype = getattr(jnp, config.get('param_dtype', 'float64'))
        
        # Create the polynomial GCNN model (simplified, no symmetries for now)
        model = PolyGCNN(
            layers=layers,
            features=features,
            degree=degree,
            poly_class=poly_class,
            param_dtype=param_dtype,
            rngs=rngs
        )
        
        # Initialize model with dummy input to get parameter count
        dummy_input = jnp.ones((1, system_size), dtype=param_dtype)
        
        # Get parameter count
        params = nnx.state(model)
        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params) 
                         if hasattr(x, 'size'))
        
        print(f"✅ Created Polynomial GCNN:")
        print(f"   - Total layers: {layers}")
        print(f"   - Features: {features}")
        print(f"   - Polynomial degree: {degree}")
        print(f"   - Polynomial class: {poly_class}")
        print(f"   - Parameters: {param_count:,}")
        
        return model, param_count
    
    @staticmethod
    def create_variational_state(model, sampler, **kwargs):
        """Create variational state for the polynomial GCNN model."""
        return nk.vqs.MCState(sampler, model, **kwargs)


# For backward compatibility, also create a direct polynomial GCNN wrapper
# that can be used as a drop-in replacement for the existing polynomial_gcnn_wrapper
class PolynomialGCNNWrapper(PolyGCNNModelWrapper):
    """Alias for backward compatibility with existing polynomial GCNN wrapper."""
    pass
