"""
GCNN Model Wrapper

Provides a unified interface for creating GCNN models.
"""

from typing import Dict, Any
import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx
import numpy as np


class GCNNModelWrapper:
    """Wrapper for GCNN models with unified configuration interface."""
    
    @staticmethod
    def from_config(config: Dict[str, Any], physics_type: str = "spin", **kwargs):
        """Create GCNN model from configuration."""

        # GCNN supports spin and lattice_fermion systems
        if physics_type not in ["spin", "lattice_fermion"]:
            raise ValueError(f"GCNN is currently only supported for spin and lattice_fermion systems, got {physics_type}")
        
        # Get GCNN parameters
        layers = config.get('layers', 2)
        features = config.get('features', 8)
        mode = config.get('mode', 'auto')
        param_dtype = config.get('param_dtype', 'float64')
        
        # Convert param_dtype string to actual dtype
        if param_dtype == 'float64':
            dtype = jnp.float64
        elif param_dtype == 'float32':
            dtype = jnp.float32
        else:
            dtype = float
        
        # Get system information
        system_size = kwargs.get('system_size')
        problem_name = kwargs.get('problem_name', 'heisenberg_1d')
        
        # Create appropriate graph
        if "1d" in problem_name:
            g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=True)
        elif "2d" in problem_name:
            L = int(np.sqrt(system_size))
            g = nk.graph.Square(L, pbc=True)
        else:
            g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=True)
        
        # Create GCNN using NetKet with NNX bridge pattern
        gcnn_linen = nk.models.GCNN(
            symmetries=g,
            layers=layers,
            features=features,
            mode=mode,
            complex_output=False,
            param_dtype=dtype
        )
        
        # Initialize with proper NNX bridge
        key = jax.random.PRNGKey(43)
        gcnn_nnx = nnx.bridge.ToNNX(gcnn_linen, rngs=nnx.Rngs(key)).lazy_init(
            jnp.ones((1, system_size))
        )

        # Get parameter count
        params = nnx.state(gcnn_nnx)
        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params)
                         if hasattr(x, 'size'))

        return gcnn_nnx, param_count
