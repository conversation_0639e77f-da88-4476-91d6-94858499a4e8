"""
Polynomial RBM Wrapper

Extends the standard RBM with polynomial interactions following the subclassing
approach. Adds polynomial terms to the RBM energy function for enhanced
expressiveness in quantum many-body systems.
"""

import jax
import jax.numpy as jnp
import netket as nk
from flax import nnx
from typing import Dict, Any, Optional, <PERSON><PERSON>
import numpy as np

from core.polynomial_layers import get_polynomial_class, CP
from .rbm_wrapper import RBMModelWrapper


class PolyRBM(nnx.Module):
    """
    Polynomial Restricted Boltzmann Machine.

    Extends standard RBM by adding polynomial interaction terms
    to capture higher-order correlations in quantum many-body systems.
    """

    def __init__(self, n_visible: int, n_hidden: int, degree: int = 2,
                 poly_class: str = 'CP', use_visible_bias: bool = True,
                 use_hidden_bias: bool = True, param_dtype: Any = jnp.float64,
                 rngs: Optional[nnx.Rngs] = None):
        """
        Initialize Polynomial RBM.

        Args:
            n_visible: Number of visible units
            n_hidden: Number of hidden units
            degree: Polynomial degree for interactions
            poly_class: Type of polynomial network
            use_visible_bias: Whether to use visible bias terms
            use_hidden_bias: Whether to use hidden bias terms
            param_dtype: Parameter data type
            rngs: Random number generators
        """
        if rngs is None:
            rngs = nnx.Rngs(0)

        self.n_visible = n_visible
        self.n_hidden = n_hidden
        self.degree = degree

        # Standard RBM components
        self.W = nnx.Param(
            jax.random.normal(rngs(), (n_visible, n_hidden), dtype=param_dtype) * 0.1
        )

        if use_visible_bias:
            self.visible_bias = nnx.Param(
                jnp.zeros(n_visible, dtype=param_dtype)
            )
        else:
            self.visible_bias = None

        if use_hidden_bias:
            self.hidden_bias = nnx.Param(
                jnp.zeros(n_hidden, dtype=param_dtype)
            )
        else:
            self.hidden_bias = None

        # Polynomial interaction terms
        PolyClass = get_polynomial_class(poly_class)

        # Polynomial layer for visible-hidden interactions
        poly_rank = max(min(n_visible, n_hidden) // 2, 4)
        self.poly_vh_interaction = PolyClass(
            degree=degree,
            input_dim=n_visible,
            rank=poly_rank,
            output_dim=n_hidden,
            param_dtype=param_dtype,
            rngs=rngs
        )

        # Polynomial layer for visible-visible interactions (if degree > 2)
        if degree > 2:
            self.poly_vv_interaction = PolyClass(
                degree=degree - 1,  # One degree lower for self-interactions
                input_dim=n_visible,
                rank=poly_rank,
                output_dim=n_visible,
                param_dtype=param_dtype,
                rngs=rngs
            )
        else:
            self.poly_vv_interaction = None

    def __call__(self, x):
        """
        Compute log-amplitude for given visible configuration.

        Args:
            x: Visible configuration (batch_size, n_visible)

        Returns:
            Log-amplitude of the wavefunction
        """
        # Ensure input is properly shaped
        if x.ndim == 1:
            x = x[None, :]  # Add batch dimension

        batch_size = x.shape[0]

        # Standard RBM visible bias term
        visible_term = 0.0
        if self.visible_bias is not None:
            visible_term = jnp.sum(x * self.visible_bias[None, :], axis=1)

        # Standard RBM hidden term
        hidden_input = jnp.dot(x, self.W)
        if self.hidden_bias is not None:
            hidden_input += self.hidden_bias[None, :]

        # Add polynomial visible-hidden interactions
        poly_vh_term = self.poly_vh_interaction(x)
        hidden_input += poly_vh_term

        # Hidden term (log-cosh for complex amplitudes)
        hidden_term = jnp.sum(jnp.logaddexp(0, hidden_input), axis=1)

        # Polynomial visible-visible interactions
        poly_vv_term = 0.0
        if self.poly_vv_interaction is not None:
            poly_vv_output = self.poly_vv_interaction(x)
            poly_vv_term = jnp.sum(x * poly_vv_output, axis=1)

        # Total log-amplitude
        log_amplitude = visible_term + hidden_term + poly_vv_term

        # Return scalar for single configuration, array for batch
        if batch_size == 1:
            return log_amplitude[0]
        return log_amplitude


class PolyRBMModelWrapper:
    """
    Model wrapper for Polynomial RBM that extends standard RBM functionality.
    """

    @staticmethod
    def from_config(config: Dict[str, Any], physics_type: str, system_size: int,
                   problem_name: str, hilbert, random_seed: int = 42, **kwargs) -> Tuple[Any, int]:
        """
        Create Polynomial RBM model from configuration.

        Args:
            config: Model configuration dictionary
            physics_type: Type of physics problem
            system_size: Size of the quantum system
            problem_name: Name of the specific problem
            hilbert: Hilbert space
            random_seed: Random seed for initialization
            **kwargs: Additional arguments

        Returns:
            Tuple of (model, parameter_count)
        """
        # Set random seed
        rngs = nnx.Rngs(random_seed)

        # Extract polynomial-specific parameters
        degree = config.get('degree', 2)
        poly_class = config.get('poly_class', 'CP')

        # Extract standard RBM parameters
        n_hidden = config.get('n_hidden', system_size)
        use_visible_bias = config.get('use_visible_bias', True)
        use_hidden_bias = config.get('use_hidden_bias', True)
        param_dtype = getattr(jnp, config.get('param_dtype', 'float64'))

        # Number of visible units equals system size
        n_visible = system_size

        # Create the polynomial RBM model
        model = PolyRBM(
            n_visible=n_visible,
            n_hidden=n_hidden,
            degree=degree,
            poly_class=poly_class,
            use_visible_bias=use_visible_bias,
            use_hidden_bias=use_hidden_bias,
            param_dtype=param_dtype,
            rngs=rngs
        )

        # Get parameter count
        params = nnx.state(model)
        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params)
                         if hasattr(x, 'size'))

        print(f"✅ Created Polynomial RBM:")
        print(f"   - Visible units: {n_visible}")
        print(f"   - Hidden units: {n_hidden}")
        print(f"   - Polynomial degree: {degree}")
        print(f"   - Polynomial class: {poly_class}")
        print(f"   - Parameters: {param_count:,}")

        return model, param_count

    @staticmethod
    def create_variational_state(model, sampler, **kwargs):
        """Create variational state for the polynomial RBM model."""
        return nk.vqs.MCState(sampler, model, **kwargs)
    
    def __call__(self, x):
        """
        Compute log-amplitude for given visible configuration.
        
        Args:
            x: Visible configuration (batch_size, n_visible)
            
        Returns:
            Log-amplitude of the wavefunction
        """
        # Ensure input is properly shaped
        if x.ndim == 1:
            x = x[None, :]  # Add batch dimension
        
        batch_size = x.shape[0]
        
        # Standard RBM visible bias term
        visible_term = 0.0
        if self.visible_bias is not None:
            visible_term = jnp.sum(x * self.visible_bias[None, :], axis=1)
        
        # Standard RBM hidden term
        hidden_input = jnp.dot(x, self.W)
        if self.hidden_bias is not None:
            hidden_input += self.hidden_bias[None, :]
        
        # Add polynomial visible-hidden interactions
        poly_vh_term = self.poly_vh_interaction(x)
        hidden_input += poly_vh_term
        
        # Hidden term (log-cosh for complex amplitudes)
        hidden_term = jnp.sum(jnp.logaddexp(0, hidden_input), axis=1)
        
        # Polynomial visible-visible interactions
        poly_vv_term = 0.0
        if self.poly_vv_interaction is not None:
            poly_vv_output = self.poly_vv_interaction(x)
            poly_vv_term = jnp.sum(x * poly_vv_output, axis=1)
        
        # Total log-amplitude
        log_amplitude = visible_term + hidden_term + poly_vv_term
        
        # Return scalar for single configuration, array for batch
        if batch_size == 1:
            return log_amplitude[0]
        return log_amplitude
    
    def conditional_hidden(self, visible):
        """Compute conditional probability of hidden units given visible."""
        hidden_input = jnp.dot(visible, self.W)
        if self.hidden_bias is not None:
            hidden_input += self.hidden_bias
        
        # Add polynomial contribution
        poly_contribution = self.poly_vh_interaction(visible)
        hidden_input += poly_contribution
        
        return jax.nn.sigmoid(hidden_input)
    
    def conditional_visible(self, hidden):
        """Compute conditional probability of visible units given hidden."""
        visible_input = jnp.dot(hidden, self.W.T)
        if self.visible_bias is not None:
            visible_input += self.visible_bias
        
        return jax.nn.sigmoid(visible_input)


class PolyRBMModelWrapper:
    """
    Model wrapper for Polynomial RBM that extends standard RBM functionality.
    """
    
    @staticmethod
    def from_config(config: Dict[str, Any], physics_type: str, system_size: int,
                   problem_name: str, hilbert, random_seed: int = 42, **kwargs) -> Tuple[Any, int]:
        """
        Create Polynomial RBM model from configuration.
        
        Args:
            config: Model configuration dictionary
            physics_type: Type of physics problem
            system_size: Size of the quantum system
            problem_name: Name of the specific problem
            hilbert: Hilbert space
            random_seed: Random seed for initialization
            **kwargs: Additional arguments
            
        Returns:
            Tuple of (model, parameter_count)
        """
        # Set random seed
        rngs = nnx.Rngs(random_seed)
        
        # Extract polynomial-specific parameters
        degree = config.get('degree', 2)
        poly_class = config.get('poly_class', 'CP')
        
        # Extract standard RBM parameters
        n_hidden = config.get('n_hidden', system_size)
        use_visible_bias = config.get('use_visible_bias', True)
        use_hidden_bias = config.get('use_hidden_bias', True)
        param_dtype = getattr(jnp, config.get('param_dtype', 'float64'))
        
        # Number of visible units equals system size
        n_visible = system_size
        
        # Create the polynomial RBM model
        model = PolyRBM(
            n_visible=n_visible,
            n_hidden=n_hidden,
            degree=degree,
            poly_class=poly_class,
            use_visible_bias=use_visible_bias,
            use_hidden_bias=use_hidden_bias,
            param_dtype=param_dtype,
            rngs=rngs
        )
        
        # Get parameter count
        params = nnx.state(model)
        param_count = sum(x.size for x in jax.tree_util.tree_leaves(params) 
                         if hasattr(x, 'size'))
        
        print(f"✅ Created Polynomial RBM:")
        print(f"   - Visible units: {n_visible}")
        print(f"   - Hidden units: {n_hidden}")
        print(f"   - Polynomial degree: {degree}")
        print(f"   - Polynomial class: {poly_class}")
        print(f"   - Parameters: {param_count:,}")
        
        return model, param_count
    
    @staticmethod
    def create_variational_state(model, sampler, **kwargs):
        """Create variational state for the polynomial RBM model."""
        return nk.vqs.MCState(sampler, model, **kwargs)
