"""
Fix for VMC hashing issue with Polynomial GCNN models.

The issue occurs because PolyGCNN models contain unhashable attributes 
(like NetKet symmetry groups) that cause problems when creating MCState.
"""

import sys
import os

# Add the qgat directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
qgat_dir = os.path.dirname(current_dir)
sys.path.insert(0, qgat_dir)

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

from core.polygcnn.factory import create_poly_gcnn_for_spin_system

def create_spin_system(system_size=4):
    """Create a simple spin system."""
    g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=True)
    hilbert = nk.hilbert.Spin(s=1/2, N=system_size)
    ha = nk.operator.Heisenberg(hilbert=hilbert, graph=g)
    return g, hilbert, ha

def setup_vmc_fixed(model, ha, hilbert, n_samples=500, n_chains=8):
    """Fixed VMC setup that avoids hashing issues."""
    
    # Create sampler
    sampler = nk.sampler.MetropolisLocal(hilbert=hilbert, n_chains=n_chains)
    
    # Method 1: Use seed parameter to avoid hashing
    try:
        vs = nk.vqs.MCState(sampler, model, n_samples=n_samples, seed=42)
        print("✅ Method 1 (seed parameter) worked!")
        return vs
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    # Method 2: Initialize model parameters separately
    try:
        # Initialize model parameters
        key = jax.random.PRNGKey(42)
        test_input = jnp.ones((1, hilbert.size))
        params = model.init(key, test_input)
        
        # Create MCState with pre-initialized parameters
        vs = nk.vqs.MCState(sampler, model, n_samples=n_samples, seed=42)
        vs.parameters = params
        print("✅ Method 2 (pre-initialized params) worked!")
        return vs
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    # Method 3: Use a wrapper function
    try:
        def model_wrapper(x):
            return model.apply(params, x)
        
        # This won't work directly with NetKet, but shows the concept
        print("❌ Method 3 requires more complex wrapper implementation")
        return None
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
    
    return None

def test_vmc_comparison():
    """Test VMC comparison with fixed setup."""
    
    print("🧪 Testing VMC Setup Fix")
    print("=" * 50)
    
    # Create system
    g, hilbert, ha = create_spin_system(4)
    
    # Create models
    poly_model = create_poly_gcnn_for_spin_system(
        system_size=4,
        layers=2,
        features=[8, 16],
        degree=2
    )
    
    baseline_model = nk.models.GCNN(
        symmetries=g,
        layers=2,
        features=[8, 16],
        mode="fft"
    )
    
    print("🔄 Testing PolyGCNN VMC setup...")
    poly_vs = setup_vmc_fixed(poly_model, ha, hilbert)
    
    print("🔄 Testing Baseline GCNN VMC setup...")
    baseline_vs = setup_vmc_fixed(baseline_model, ha, hilbert)
    
    if poly_vs is not None and baseline_vs is not None:
        print("✅ Both models can be used with VMC!")
        
        # Quick energy evaluation test
        try:
            poly_energy = poly_vs.expect(ha).mean.real
            baseline_energy = baseline_vs.expect(ha).mean.real
            
            print(f"✅ Energy evaluations:")
            print(f"   PolyGCNN: {poly_energy:.6f}")
            print(f"   Baseline: {baseline_energy:.6f}")
            
        except Exception as e:
            print(f"❌ Energy evaluation failed: {e}")
    
    else:
        print("❌ VMC setup failed for one or both models")

def recommended_vmc_setup():
    """Show the recommended VMC setup for the notebook."""
    
    print("\n🎯 Recommended VMC Setup for Notebook:")
    print("=" * 50)
    
    code = '''
def setup_vmc_safe(model, ha, hilbert, n_samples=500, n_chains=8):
    """Safe VMC setup that avoids hashing issues."""
    
    # Create sampler
    sampler = nk.sampler.MetropolisLocal(hilbert=hilbert, n_chains=n_chains)
    
    # Create variational state with explicit seed
    vs = nk.vqs.MCState(sampler, model, n_samples=n_samples, seed=42)
    
    # Create optimizer
    optimizer = nk.optimizer.Adam(learning_rate=0.01)
    
    # Create VMC driver
    vmc = nk.VMC(ha, optimizer, variational_state=vs)
    
    return vmc, vs

def run_vmc_comparison_safe(poly_model, baseline_model, ha, hilbert, n_iter=20):
    """Safe VMC comparison with reduced iterations."""
    
    print(f"🏃 Running VMC Comparison ({n_iter} iterations)")
    print("=" * 60)
    
    try:
        # Setup VMC for both models
        poly_vmc, poly_vs = setup_vmc_safe(poly_model, ha, hilbert)
        baseline_vmc, baseline_vs = setup_vmc_safe(baseline_model, ha, hilbert)
        
        # Run optimization with reduced iterations
        print("🔄 Running PolyGCNN optimization...")
        poly_vmc.run(n_iter=n_iter, out=None)
        
        print("🔄 Running Baseline GCNN optimization...")
        baseline_vmc.run(n_iter=n_iter, out=None)
        
        # Get final energies
        poly_energy = poly_vs.expect(ha).mean.real
        baseline_energy = baseline_vs.expect(ha).mean.real
        
        return poly_energy, baseline_energy, poly_vs, baseline_vs
        
    except Exception as e:
        print(f"❌ VMC comparison failed: {e}")
        print("💡 Try reducing n_samples, n_chains, or n_iter")
        return None, None, None, None
'''
    
    print(code)
    
    print("\n💡 Key fixes:")
    print("   • Add explicit seed=42 to MCState")
    print("   • Reduce n_samples and n_chains for stability")
    print("   • Use fewer iterations for quick testing")
    print("   • Add try-except blocks for error handling")

if __name__ == "__main__":
    test_vmc_comparison()
    recommended_vmc_setup()
