#!/usr/bin/env python3
"""
Investigate Identical Errors

Analyzes why systems with 5 and 7 sites show identical error patterns
and investigates potential optimization or convergence issues.
"""

import json
import numpy as np
from pathlib import Path
import sys
from collections import defaultdict


def investigate_identical_errors(results_dir: Path):
    """Investigate why 5-site and 7-site systems show identical errors."""
    
    # Load results
    result_files = list((results_dir / "results").glob("result_*.json"))
    results = []
    
    for result_file in result_files:
        try:
            with open(result_file, 'r') as f:
                result_data = json.load(f)
                if result_data.get('success', False):
                    results.append(result_data)
        except Exception as e:
            print(f"Warning: Could not load {result_file}: {e}")
    
    print("🔍 INVESTIGATING IDENTICAL ERROR PATTERNS")
    print("=" * 60)
    
    # Group results by system size
    by_size = defaultdict(list)
    for result in results:
        by_size[result['system_size']].append(result)
    
    # Check exact energies first
    print("📊 EXACT GROUND STATE ENERGIES:")
    for size in sorted(by_size.keys()):
        exact_energy = by_size[size][0]['exact_energy']
        print(f"   {size} sites: {exact_energy:.8f} Ha")
    print()
    
    # Analyze convergence patterns
    print("🔬 CONVERGENCE ANALYSIS BY SYSTEM SIZE:")
    print("-" * 60)
    
    for size in sorted(by_size.keys()):
        print(f"\n📈 {size}-SITE SYSTEM:")
        print(f"   Exact Energy: {by_size[size][0]['exact_energy']:.8f} Ha")
        
        # Group by model type
        model_results = defaultdict(list)
        for result in by_size[size]:
            model_type = result['model_name'].split('_')[0]  # GCNN or PGCNN
            model_results[model_type].append(result)
        
        for model_type, model_list in model_results.items():
            print(f"\n   🧠 {model_type} Models:")
            
            # Check for identical final energies
            final_energies = [r['final_energy'] for r in model_list]
            unique_energies = set(final_energies)
            
            if len(unique_energies) < len(final_energies):
                print(f"   ⚠️  WARNING: {len(final_energies) - len(unique_energies)} models converged to identical energies!")
                
                # Find which energies are repeated
                from collections import Counter
                energy_counts = Counter(final_energies)
                for energy, count in energy_counts.items():
                    if count > 1:
                        print(f"      {count} models converged to: {energy:.6f} Ha")
            
            # Analyze convergence steps
            convergence_steps = [r['convergence_step'] for r in model_list if r['converged']]
            if convergence_steps:
                avg_convergence = np.mean(convergence_steps)
                print(f"      Average convergence step: {avg_convergence:.1f}")
                if avg_convergence < 50:
                    print(f"      ⚠️  WARNING: Very early convergence suggests local minima!")
            
            # Check energy ranges
            energy_errors = [r['energy_error'] for r in model_list]
            min_error = min(energy_errors)
            max_error = max(energy_errors)
            print(f"      Error range: {min_error:.6f} - {max_error:.6f} Ha")
            
            # List individual results
            for result in sorted(model_list, key=lambda x: x['energy_error']):
                model_name = result['model_name']
                final_energy = result['final_energy']
                energy_error = result['energy_error']
                converged = result['converged']
                conv_step = result['convergence_step']
                
                print(f"      • {model_name}: {final_energy:.6f} Ha "
                      f"(error: {energy_error:.6f}, conv: {'✅' if converged else '❌'} @ {conv_step})")
    
    # Compare 5-site vs 7-site specifically
    print("\n" + "=" * 60)
    print("🎯 SPECIFIC COMPARISON: 5-SITE vs 7-SITE")
    print("=" * 60)
    
    if 5 in by_size and 7 in by_size:
        size5_results = by_size[5]
        size7_results = by_size[7]
        
        print(f"5-site exact energy: {size5_results[0]['exact_energy']:.8f} Ha")
        print(f"7-site exact energy: {size7_results[0]['exact_energy']:.8f} Ha")
        print()
        
        # Compare model by model
        for i, (r5, r7) in enumerate(zip(size5_results, size7_results)):
            if r5['model_name'].replace('5sites', 'Xsites') == r7['model_name'].replace('7sites', 'Xsites'):
                print(f"Model {i+1}: {r5['model_name'].split('_')[0]}_{r5['model_name'].split('_')[1]}")
                print(f"   5-site: {r5['final_energy']:.6f} Ha (error: {r5['energy_error']:.6f})")
                print(f"   7-site: {r7['final_energy']:.6f} Ha (error: {r7['energy_error']:.6f})")
                
                # Check if errors are suspiciously similar
                error_ratio = r5['energy_error'] / r7['energy_error'] if r7['energy_error'] != 0 else float('inf')
                if 0.9 < error_ratio < 1.1:
                    print(f"   ⚠️  SUSPICIOUS: Error ratio = {error_ratio:.3f} (too similar!)")
                
                # Check if final energies are identical
                if abs(r5['final_energy'] - r7['final_energy']) < 1e-6:
                    print(f"   🚨 CRITICAL: Identical final energies!")
                
                print()
    
    # Identify potential causes
    print("🔍 POTENTIAL CAUSES OF IDENTICAL ERRORS:")
    print("-" * 60)
    
    # Check for models converging to wrong solutions
    wrong_convergence_count = 0
    for result in results:
        exact_energy = result['exact_energy']
        final_energy = result['final_energy']
        
        # Check if converged to obviously wrong energy (like -1.0, 0.0, 1.0)
        if abs(final_energy - (-1.0)) < 0.1 or abs(final_energy) < 0.1 or abs(final_energy - 1.0) < 0.1:
            wrong_convergence_count += 1
    
    if wrong_convergence_count > 0:
        print(f"1. {wrong_convergence_count} models converged to obviously wrong energies (near -1, 0, or 1)")
    
    # Check for early convergence
    early_convergence_count = sum(1 for r in results if r['converged'] and r['convergence_step'] < 50)
    if early_convergence_count > 0:
        print(f"2. {early_convergence_count} models converged very early (< 50 steps) - likely local minima")
    
    # Check for optimization issues
    print(f"3. Possible optimization issues:")
    print(f"   - Learning rate too high (0.01) causing instability")
    print(f"   - Early stopping too aggressive (patience=25, tolerance=1e-6)")
    print(f"   - Models getting stuck in local minima")
    print(f"   - Insufficient parameter initialization diversity")
    
    print("\n💡 RECOMMENDATIONS:")
    print("-" * 60)
    print("1. Reduce learning rate to 0.005 or 0.001")
    print("2. Increase early stopping patience to 50-100")
    print("3. Use different random seeds for each experiment")
    print("4. Add parameter initialization diversity")
    print("5. Monitor training curves for early detection of local minima")


def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python investigate_identical_errors.py <results_directory>")
        return 1
    
    results_dir = Path(sys.argv[1])
    if not results_dir.exists():
        print(f"Error: Directory {results_dir} does not exist")
        return 1
    
    investigate_identical_errors(results_dir)
    return 0


if __name__ == "__main__":
    exit(main())
