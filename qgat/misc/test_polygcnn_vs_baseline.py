"""
Comparison tests between Polynomial GCNN and baseline GCNN.

Tests that polynomial GCNN produces reasonable results compared to
standard NetKet GCNN on simple quantum systems.
"""

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
import time

import sys
import os

# Add the qgat directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
qgat_dir = os.path.dirname(current_dir)
sys.path.insert(0, qgat_dir)

from core.polygcnn import PolyGCNN
from core.polygcnn.factory import create_poly_gcnn_for_spin_system


def create_test_system(system_size=4):
    """Create a simple test system for comparison."""
    # 1D Heisenberg chain
    g = nk.graph.Hypercube(length=system_size, n_dim=1, pbc=True)
    hilbert = nk.hilbert.Spin(s=1/2, N=system_size)
    
    # Simple Heisenberg Hamiltonian
    ha = nk.operator.Heisenberg(hilbert=hilbert, graph=g)
    
    return g, hilbert, ha


def create_baseline_gcnn(g, layers=2, features=None):
    """Create baseline NetKet GCNN for comparison."""
    if features is None:
        features = [8, 16]

    # Create standard NetKet GCNN using the graph directly
    baseline_gcnn = nk.models.GCNN(
        symmetries=g,  # Pass the graph directly
        layers=layers,
        features=tuple(features),
        mode="fft"
    )

    return baseline_gcnn


def test_parameter_count_comparison():
    """Compare parameter counts between polynomial and baseline GCNN."""
    print("🔍 Parameter Count Comparison")
    print("=" * 50)
    
    # Create test system
    g, hilbert, ha = create_test_system(system_size=4)
    
    # Create models
    baseline_gcnn = create_baseline_gcnn(g, layers=2, features=[8, 16])
    poly_gcnn = create_poly_gcnn_for_spin_system(
        system_size=4,
        layers=2,
        features=[8, 16],
        degree=2
    )
    
    # Initialize both models
    key = jax.random.PRNGKey(42)
    test_input = jnp.ones((1, 4))  # Single spin configuration
    
    baseline_params = baseline_gcnn.init(key, test_input)
    poly_params = poly_gcnn.init(key, test_input)
    
    # Count parameters
    baseline_count = sum(x.size for x in jax.tree_util.tree_leaves(baseline_params))
    poly_count = sum(x.size for x in jax.tree_util.tree_leaves(poly_params))
    
    print(f"Baseline GCNN parameters: {baseline_count:,}")
    print(f"Polynomial GCNN parameters: {poly_count:,}")
    print(f"Ratio (Poly/Baseline): {poly_count/baseline_count:.2f}")
    
    # Polynomial GCNN should have more parameters due to degree-many layers
    assert poly_count > baseline_count, "Polynomial GCNN should have more parameters"
    
    return baseline_count, poly_count


def test_forward_pass_comparison():
    """Compare forward pass outputs between models."""
    print("\n🚀 Forward Pass Comparison")
    print("=" * 50)
    
    # Create test system
    g, hilbert, ha = create_test_system(system_size=4)
    
    # Create models
    baseline_gcnn = create_baseline_gcnn(g, layers=2, features=[8, 16])
    poly_gcnn = create_poly_gcnn_for_spin_system(
        system_size=4,
        layers=2,
        features=[8, 16],
        degree=2
    )
    
    # Test inputs
    test_configs = jnp.array([
        [1, -1, 1, -1],   # Alternating
        [1, 1, -1, -1],   # Domain wall
        [1, 1, 1, 1],     # All up
        [-1, -1, -1, -1]  # All down
    ], dtype=jnp.float64)
    
    # Initialize models
    key = jax.random.PRNGKey(42)
    baseline_params = baseline_gcnn.init(key, test_configs)
    poly_params = poly_gcnn.init(key, test_configs)
    
    # Forward passes
    baseline_output = baseline_gcnn.apply(baseline_params, test_configs)
    poly_output = poly_gcnn.apply(poly_params, test_configs)
    
    print(f"Baseline outputs: {baseline_output}")
    print(f"Polynomial outputs: {poly_output}")
    
    # Both should produce finite outputs
    assert jnp.all(jnp.isfinite(baseline_output)), "Baseline outputs should be finite"
    assert jnp.all(jnp.isfinite(poly_output)), "Polynomial outputs should be finite"
    
    # Outputs should be different (different models)
    assert not jnp.allclose(baseline_output, poly_output), "Outputs should be different"
    
    return baseline_output, poly_output


def test_performance_comparison():
    """Compare computational performance."""
    print("\n⏱️ Performance Comparison")
    print("=" * 50)
    
    # Create test system
    g, hilbert, ha = create_test_system(system_size=6)  # Slightly larger system
    
    # Create models
    baseline_gcnn = create_baseline_gcnn(g, layers=2, features=[16, 32])
    poly_gcnn = create_poly_gcnn_for_spin_system(
        system_size=6,
        layers=2,
        features=[16, 32],
        degree=2
    )
    
    # Test input (larger batch)
    batch_size = 100
    test_input = jax.random.choice(
        jax.random.PRNGKey(42),
        jnp.array([-1., 1.]),
        shape=(batch_size, 6)
    )
    
    # Initialize models
    key = jax.random.PRNGKey(42)
    baseline_params = baseline_gcnn.init(key, test_input[:1])
    poly_params = poly_gcnn.init(key, test_input[:1])
    
    # Compile functions
    baseline_fn = jax.jit(baseline_gcnn.apply)
    poly_fn = jax.jit(poly_gcnn.apply)
    
    # Warm up
    _ = baseline_fn(baseline_params, test_input[:10])
    _ = poly_fn(poly_params, test_input[:10])
    
    # Time baseline
    start_time = time.time()
    for _ in range(10):
        _ = baseline_fn(baseline_params, test_input)
    baseline_time = (time.time() - start_time) / 10
    
    # Time polynomial
    start_time = time.time()
    for _ in range(10):
        _ = poly_fn(poly_params, test_input)
    poly_time = (time.time() - start_time) / 10
    
    print(f"Baseline GCNN time: {baseline_time*1000:.2f} ms")
    print(f"Polynomial GCNN time: {poly_time*1000:.2f} ms")
    print(f"Slowdown factor: {poly_time/baseline_time:.2f}x")
    
    return baseline_time, poly_time


def test_different_polynomial_modes():
    """Test different polynomial GCNN modes."""
    print("\n🎛️ Different Polynomial Modes")
    print("=" * 50)
    
    # Create test system
    g, hilbert, ha = create_test_system(system_size=4)
    test_input = jnp.array([[1, -1, 1, -1]], dtype=jnp.float64)
    
    # Test different modes
    modes = [
        {"degree": 2, "no_activation": False, "poly_output": False},
        {"degree": 2, "no_activation": True, "poly_output": False},
        {"degree": 2, "no_activation": False, "poly_output": True},
        {"degree": 3, "no_activation": False, "poly_output": False},
        {"poly_degrees": (2, 3), "no_activation": False, "poly_output": False},
    ]
    
    key = jax.random.PRNGKey(42)
    
    for i, mode_config in enumerate(modes):
        print(f"\nMode {i+1}: {mode_config}")
        
        poly_gcnn = create_poly_gcnn_for_spin_system(
            system_size=4,
            layers=2,
            features=[8, 16],
            **mode_config
        )
        
        params = poly_gcnn.init(key, test_input)
        output = poly_gcnn.apply(params, test_input)
        
        print(f"Output: {output}")
        assert jnp.all(jnp.isfinite(output)), f"Mode {i+1} should produce finite output"


def test_symmetry_preservation():
    """Test that polynomial GCNN preserves symmetries like baseline."""
    print("\n🔄 Symmetry Preservation Test")
    print("=" * 50)
    
    # Create test system
    g, hilbert, ha = create_test_system(system_size=4)
    
    # Create models
    baseline_gcnn = create_baseline_gcnn(g, layers=2, features=[8, 16])
    poly_gcnn = create_poly_gcnn_for_spin_system(
        system_size=4,
        layers=2,
        features=[8, 16],
        degree=2
    )
    
    # Test symmetric configuration (all spins up)
    symmetric_config = jnp.array([[1, 1, 1, 1]], dtype=jnp.float64)
    
    # Initialize models
    key = jax.random.PRNGKey(42)
    baseline_params = baseline_gcnn.init(key, symmetric_config)
    poly_params = poly_gcnn.init(key, symmetric_config)
    
    # Test outputs
    baseline_output = baseline_gcnn.apply(baseline_params, symmetric_config)
    poly_output = poly_gcnn.apply(poly_params, symmetric_config)
    
    print(f"Symmetric config baseline output: {baseline_output}")
    print(f"Symmetric config polynomial output: {poly_output}")
    
    # Both should handle symmetric inputs properly
    assert jnp.all(jnp.isfinite(baseline_output))
    assert jnp.all(jnp.isfinite(poly_output))
    
    print("✅ Both models handle symmetric configurations properly")


if __name__ == "__main__":
    print("🧪 Polynomial GCNN vs Baseline Comparison Tests")
    print("=" * 60)
    
    # Run all comparison tests
    try:
        test_parameter_count_comparison()
        test_forward_pass_comparison()
        test_performance_comparison()
        test_different_polynomial_modes()
        test_symmetry_preservation()
        
        print("\n🎉 All comparison tests passed!")
        print("✅ Polynomial GCNN implementation is working correctly")
        print("✅ Performance characteristics are reasonable")
        print("✅ Different modes work as expected")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
