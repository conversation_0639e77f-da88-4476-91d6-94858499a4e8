#!/usr/bin/env python3
"""
Scaling Study Visualization

Creates comprehensive heatmaps and contour plots to analyze the performance
scaling of GCNN vs Polynomial GCNN across architectures and system sizes.
"""

import json
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path
import argparse
import pandas as pd
from matplotlib.colors import LogNorm


def load_scaling_results(results_dir: Path):
    """Load and organize scaling study results."""
    results = []
    
    # Find all result files
    result_files = list(results_dir.glob("result_*.json"))
    
    for result_file in result_files:
        try:
            with open(result_file, 'r') as f:
                result_data = json.load(f)
                if result_data.get('success', False):
                    results.append(result_data)
        except Exception as e:
            print(f"Warning: Could not load {result_file}: {e}")
    
    return results


def organize_data_for_heatmap(results):
    """Organize results into matrices for heatmap visualization."""
    
    # Extract model types and system sizes
    gcnn_data = []
    pgcnn_data = []
    
    for result in results:
        model_name = result['model_name']
        system_size = result['system_size']
        energy_error = result['energy_error']
        n_parameters = result['n_parameters']
        
        # Extract layer count from model name
        if 'GCNN_' in model_name and 'PGCNN_' not in model_name:
            layers = int(model_name.split('Layer')[0].split('_')[-1])
            gcnn_data.append({
                'layers': layers,
                'system_size': system_size,
                'energy_error': energy_error,
                'n_parameters': n_parameters,
                'model_type': 'GCNN'
            })
        elif 'PGCNN_' in model_name:
            layers = int(model_name.split('Layer')[0].split('_')[-1])
            pgcnn_data.append({
                'layers': layers,
                'system_size': system_size,
                'energy_error': energy_error,
                'n_parameters': n_parameters,
                'model_type': 'PGCNN'
            })
    
    return gcnn_data, pgcnn_data


def create_heatmap_matrix(data, metric='energy_error'):
    """Create matrix for heatmap from data."""
    
    # Get unique values
    layers = sorted(list(set([d['layers'] for d in data])))
    system_sizes = sorted(list(set([d['system_size'] for d in data])))
    
    # Create matrices
    matrix = np.full((len(layers), len(system_sizes)), np.nan)
    param_matrix = np.full((len(layers), len(system_sizes)), np.nan)
    
    # Fill matrices
    for d in data:
        layer_idx = layers.index(d['layers'])
        size_idx = system_sizes.index(d['system_size'])
        matrix[layer_idx, size_idx] = d[metric]
        param_matrix[layer_idx, size_idx] = d['n_parameters']
    
    return matrix, param_matrix, layers, system_sizes


def create_scaling_visualization(results, output_dir: Path):
    """Create comprehensive scaling visualization."""
    
    # Organize data
    gcnn_data, pgcnn_data = organize_data_for_heatmap(results)
    
    if not gcnn_data or not pgcnn_data:
        print("Error: Insufficient data for both model types")
        return
    
    # Create matrices
    gcnn_matrix, gcnn_params, gcnn_layers, system_sizes = create_heatmap_matrix(gcnn_data)
    pgcnn_matrix, pgcnn_params, pgcnn_layers, _ = create_heatmap_matrix(pgcnn_data)
    
    # Set up the figure with subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Color map for energy errors (log scale)
    vmin = min(np.nanmin(gcnn_matrix), np.nanmin(pgcnn_matrix))
    vmax = max(np.nanmax(gcnn_matrix), np.nanmax(pgcnn_matrix))
    
    # 1. GCNN Energy Error Heatmap
    sns.heatmap(gcnn_matrix, 
                xticklabels=system_sizes, 
                yticklabels=[f"{l} layers" for l in gcnn_layers],
                annot=True, fmt='.3f', cmap='viridis_r', 
                norm=LogNorm(vmin=max(vmin, 1e-6), vmax=vmax),
                ax=ax1, cbar_kws={'label': 'Energy Error (Ha)'})
    ax1.set_title('GCNN: Energy Error vs System Size & Layers', fontsize=14, fontweight='bold')
    ax1.set_xlabel('System Size (sites)', fontweight='bold')
    ax1.set_ylabel('Architecture', fontweight='bold')
    
    # 2. Polynomial GCNN Energy Error Heatmap
    sns.heatmap(pgcnn_matrix, 
                xticklabels=system_sizes, 
                yticklabels=[f"{l} layers" for l in pgcnn_layers],
                annot=True, fmt='.3f', cmap='viridis_r',
                norm=LogNorm(vmin=max(vmin, 1e-6), vmax=vmax),
                ax=ax2, cbar_kws={'label': 'Energy Error (Ha)'})
    ax2.set_title('Polynomial GCNN: Energy Error vs System Size & Layers', fontsize=14, fontweight='bold')
    ax2.set_xlabel('System Size (sites)', fontweight='bold')
    ax2.set_ylabel('Architecture', fontweight='bold')
    
    # 3. Parameter Count Comparison
    sns.heatmap(gcnn_params, 
                xticklabels=system_sizes, 
                yticklabels=[f"{l} layers" for l in gcnn_layers],
                annot=True, fmt='.0f', cmap='Blues',
                ax=ax3, cbar_kws={'label': 'Parameter Count'})
    ax3.set_title('GCNN: Parameter Count', fontsize=14, fontweight='bold')
    ax3.set_xlabel('System Size (sites)', fontweight='bold')
    ax3.set_ylabel('Architecture', fontweight='bold')
    
    sns.heatmap(pgcnn_params, 
                xticklabels=system_sizes, 
                yticklabels=[f"{l} layers" for l in pgcnn_layers],
                annot=True, fmt='.0f', cmap='Oranges',
                ax=ax4, cbar_kws={'label': 'Parameter Count'})
    ax4.set_title('Polynomial GCNN: Parameter Count', fontsize=14, fontweight='bold')
    ax4.set_xlabel('System Size (sites)', fontweight='bold')
    ax4.set_ylabel('Architecture', fontweight='bold')
    
    plt.tight_layout()
    
    # Save the plot
    output_file = output_dir / "scaling_study_heatmaps.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 Scaling study heatmaps saved to: {output_file}")
    
    # Create performance comparison plot
    create_performance_comparison(gcnn_data, pgcnn_data, output_dir)
    
    # Create efficiency analysis
    create_efficiency_analysis(gcnn_data, pgcnn_data, output_dir)
    
    return fig


def create_performance_comparison(gcnn_data, pgcnn_data, output_dir: Path):
    """Create direct performance comparison plots."""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Convert to DataFrames for easier plotting
    gcnn_df = pd.DataFrame(gcnn_data)
    pgcnn_df = pd.DataFrame(pgcnn_data)
    
    # 1. Error vs System Size (by layer count)
    for layers in sorted(gcnn_df['layers'].unique()):
        gcnn_subset = gcnn_df[gcnn_df['layers'] == layers]
        ax1.semilogy(gcnn_subset['system_size'], gcnn_subset['energy_error'], 
                    'o-', label=f'GCNN {layers}L', linewidth=2, markersize=8)
    
    for layers in sorted(pgcnn_df['layers'].unique()):
        pgcnn_subset = pgcnn_df[pgcnn_df['layers'] == layers]
        ax1.semilogy(pgcnn_subset['system_size'], pgcnn_subset['energy_error'], 
                    's--', label=f'PGCNN {layers}L', linewidth=2, markersize=8)
    
    ax1.axhline(y=0.001, color='red', linestyle=':', alpha=0.7, linewidth=2,
                label='Chemical Accuracy')
    ax1.set_xlabel('System Size (sites)', fontweight='bold')
    ax1.set_ylabel('Energy Error (Ha)', fontweight='bold')
    ax1.set_title('Performance vs System Size', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Parameter Efficiency (Error vs Parameters)
    ax2.loglog(gcnn_df['n_parameters'], gcnn_df['energy_error'], 
              'o', label='GCNN', markersize=8, alpha=0.7)
    ax2.loglog(pgcnn_df['n_parameters'], pgcnn_df['energy_error'], 
              's', label='PGCNN', markersize=8, alpha=0.7)
    
    ax2.axhline(y=0.001, color='red', linestyle=':', alpha=0.7, linewidth=2,
                label='Chemical Accuracy')
    ax2.set_xlabel('Parameter Count', fontweight='bold')
    ax2.set_ylabel('Energy Error (Ha)', fontweight='bold')
    ax2.set_title('Parameter Efficiency', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    output_file = output_dir / "performance_comparison.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 Performance comparison saved to: {output_file}")


def create_efficiency_analysis(gcnn_data, pgcnn_data, output_dir: Path):
    """Create efficiency analysis showing PGCNN advantages."""
    
    # Calculate efficiency metrics
    efficiency_data = []
    
    # Group by system size
    system_sizes = sorted(list(set([d['system_size'] for d in gcnn_data + pgcnn_data])))
    
    for size in system_sizes:
        gcnn_size_data = [d for d in gcnn_data if d['system_size'] == size]
        pgcnn_size_data = [d for d in pgcnn_data if d['system_size'] == size]
        
        if gcnn_size_data and pgcnn_size_data:
            # Find best performance for each model type
            best_gcnn = min(gcnn_size_data, key=lambda x: x['energy_error'])
            best_pgcnn = min(pgcnn_size_data, key=lambda x: x['energy_error'])
            
            error_improvement = (best_gcnn['energy_error'] - best_pgcnn['energy_error']) / best_gcnn['energy_error']
            param_efficiency = (best_gcnn['n_parameters'] - best_pgcnn['n_parameters']) / best_gcnn['n_parameters']
            
            efficiency_data.append({
                'system_size': size,
                'error_improvement': error_improvement * 100,  # Percentage
                'param_efficiency': param_efficiency * 100,   # Percentage
                'gcnn_best_error': best_gcnn['energy_error'],
                'pgcnn_best_error': best_pgcnn['energy_error']
            })
    
    # Create efficiency plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    sizes = [d['system_size'] for d in efficiency_data]
    error_improvements = [d['error_improvement'] for d in efficiency_data]
    param_efficiencies = [d['param_efficiency'] for d in efficiency_data]
    
    # Error improvement
    bars1 = ax1.bar(sizes, error_improvements, color='green', alpha=0.7, 
                   label='PGCNN Advantage')
    ax1.set_xlabel('System Size (sites)', fontweight='bold')
    ax1.set_ylabel('Error Improvement (%)', fontweight='bold')
    ax1.set_title('PGCNN Error Improvement over GCNN', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars1, error_improvements):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Parameter efficiency
    bars2 = ax2.bar(sizes, param_efficiencies, color='blue', alpha=0.7,
                   label='Parameter Reduction')
    ax2.set_xlabel('System Size (sites)', fontweight='bold')
    ax2.set_ylabel('Parameter Reduction (%)', fontweight='bold')
    ax2.set_title('PGCNN Parameter Efficiency vs GCNN', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars2, param_efficiencies):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    output_file = output_dir / "efficiency_analysis.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"📊 Efficiency analysis saved to: {output_file}")
    
    return efficiency_data


def main():
    """Main function for scaling study visualization."""
    parser = argparse.ArgumentParser(description="Visualize scaling study results")
    parser.add_argument("results_dir", type=str, help="Path to experiment results directory")
    
    args = parser.parse_args()
    
    results_dir = Path(args.results_dir)
    if not results_dir.exists():
        print(f"Error: Results directory {results_dir} does not exist")
        return 1
    
    # Load results
    print(f"📊 Loading scaling study results from: {results_dir}")
    results = load_scaling_results(results_dir / "results")
    
    if len(results) < 30:
        print(f"Warning: Expected 30 results, found {len(results)}")
    
    print(f"✅ Loaded {len(results)} successful experiments")
    
    # Create visualizations
    output_dir = results_dir / "analysis"
    output_dir.mkdir(exist_ok=True)
    
    create_scaling_visualization(results, output_dir)
    
    print(f"🎯 All visualizations saved to: {output_dir}")
    
    return 0


if __name__ == "__main__":
    exit(main())
