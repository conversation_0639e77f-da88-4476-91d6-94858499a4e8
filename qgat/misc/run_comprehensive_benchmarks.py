#!/usr/bin/env python3
"""
Comprehensive QGAT Benchmarking Runner

This script runs the complete benchmarking suite for the QGAT framework,
including multi-system size analysis, detailed error analysis, and
publication-ready visualizations.

Usage:
    python run_comprehensive_benchmarks.py [--quick] [--problem-type TYPE]
    
Options:
    --quick: Run quick benchmarks (fewer optimization steps)
    --problem-type: Run only specific problem type (molecular, spin, lattice_fermion)
"""

import sys
import os
import argparse
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import jax
import jax.numpy as jnp
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Enable 64-bit precision
jax.config.update("jax_enable_x64", True)


def main():
    """Run comprehensive QGAT benchmarking analysis."""
    parser = argparse.ArgumentParser(description="Comprehensive QGAT Benchmarking")
    parser.add_argument("--quick", action="store_true", 
                       help="Run quick benchmarks (fewer steps)")
    parser.add_argument("--problem-type", choices=["molecular", "spin", "lattice_fermion"],
                       help="Run only specific problem type")
    parser.add_argument("--output-dir", default="benchmark_results",
                       help="Output directory for results")
    
    args = parser.parse_args()
    
    print("🚀 QGAT COMPREHENSIVE BENCHMARKING SYSTEM")
    print("=" * 80)
    print(f"Configuration:")
    print(f"  Quick mode: {'✅' if args.quick else '❌'}")
    print(f"  Problem type filter: {args.problem_type or 'All'}")
    print(f"  Output directory: {args.output_dir}")
    print()
    
    try:
        from benchmarks.comprehensive_benchmark import ComprehensiveBenchmark
        
        # Initialize benchmark system
        benchmark = ComprehensiveBenchmark(output_dir=args.output_dir)
        
        # Set optimized parameters based on mode
        if args.quick:
            n_optimization_steps = 50
            n_samples = 1500
            learning_rate = None  # Will be set optimally per model
            print("⚡ Quick mode: 50 steps, 1500 samples, optimized learning rates")
        else:
            n_optimization_steps = 150
            n_samples = 3000
            learning_rate = None  # Will be set optimally per model
            print("🔬 Full mode: 150 steps, 3000 samples, optimized learning rates")
        
        print()
        
        # Run comprehensive analysis
        if args.problem_type:
            # Run only specific problem type
            print(f"🎯 Running benchmarks for {args.problem_type} systems only")
            results = run_filtered_analysis(
                benchmark, args.problem_type, 
                n_optimization_steps, n_samples, learning_rate
            )
        else:
            # Run full analysis
            results = benchmark.run_comprehensive_analysis(
                n_optimization_steps=n_optimization_steps,
                n_samples=n_samples,
                learning_rate=learning_rate
            )
        
        # Generate summary report
        summary_file = benchmark.generate_summary_report(results)
        
        # Print final summary
        print_final_summary(results, args.output_dir)
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all QGAT components are properly installed.")
        return False
    except Exception as e:
        print(f"❌ Benchmarking failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_filtered_analysis(benchmark, problem_type, n_optimization_steps, n_samples, learning_rate):
    """Run analysis for specific problem type only."""
    all_results = {
        "molecular": [],
        "spin": [],
        "lattice_fermion": []
    }
    
    problem_configs = benchmark.get_problem_configurations()
    
    if problem_type not in problem_configs:
        raise ValueError(f"Unknown problem type: {problem_type}")
    
    problems = problem_configs[problem_type]
    
    print(f"📊 Analyzing {problem_type.upper()} systems")
    print("-" * 50)
    
    for problem_name, config in problems.items():
        print(f"\n🔬 Problem: {config['description']}")
        
        for system_size in config["sizes"]:
            print(f"\n  📏 System size: {system_size}")
            
            try:
                # Create problem setup
                hilbert, hamiltonian, exact_energy = benchmark.create_problem_setup(
                    problem_type, problem_name, system_size
                )
                
                print(f"    Exact energy: {exact_energy:.6f}")
                
                # Create models
                models = benchmark.create_models(problem_type, problem_name, system_size, hilbert)
                print(f"    Models: {list(models.keys())}")
                
                # Run benchmarks for each model
                for model_name, model in models.items():
                    result = benchmark.run_single_benchmark(
                        problem_type=problem_type,
                        problem_name=problem_name,
                        system_size=system_size,
                        model_name=model_name,
                        model=model,
                        hilbert=hilbert,
                        hamiltonian=hamiltonian,
                        exact_energy=exact_energy,
                        n_optimization_steps=n_optimization_steps,
                        n_samples=n_samples,
                        learning_rate=learning_rate
                    )
                    
                    all_results[problem_type].append(result)
                    
                    if result.success:
                        print(f"    ✅ {model_name}: Error = {result.energy_error:.6f}")
                    else:
                        print(f"    ❌ {model_name}: Failed")
            
            except Exception as e:
                print(f"    ❌ Failed to analyze size {system_size}: {e}")
    
    # Save results and generate analysis
    benchmark.save_results(all_results)
    benchmark.generate_analysis_files(all_results)
    
    return all_results


def print_final_summary(results, output_dir):
    """Print final summary of benchmarking results."""
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE BENCHMARKING COMPLETE")
    print("=" * 80)
    
    total_benchmarks = 0
    total_successful = 0
    
    for problem_type, result_list in results.items():
        if not result_list:
            continue
            
        successful = [r for r in result_list if r.success]
        total = len(result_list)
        
        total_benchmarks += total
        total_successful += len(successful)
        
        success_rate = len(successful) / total * 100 if total > 0 else 0
        
        print(f"\n📊 {problem_type.upper()} SYSTEMS:")
        print(f"  Total benchmarks: {total}")
        print(f"  Successful: {len(successful)} ({success_rate:.1f}%)")
        print(f"  Failed: {total - len(successful)}")
        
        if successful:
            # Find best performing models
            best_models = {}
            for result in successful:
                problem_key = f"{result.problem_name}_{result.system_size}"
                if problem_key not in best_models or result.energy_error < best_models[problem_key].energy_error:
                    best_models[problem_key] = result
            
            print(f"  Best performers:")
            for problem_key, best_result in best_models.items():
                print(f"    {problem_key}: {best_result.model_name} (error: {best_result.energy_error:.6f})")
    
    overall_success_rate = total_successful / total_benchmarks * 100 if total_benchmarks > 0 else 0
    
    print(f"\n🏆 OVERALL RESULTS:")
    print(f"  Total benchmarks: {total_benchmarks}")
    print(f"  Overall success rate: {total_successful}/{total_benchmarks} ({overall_success_rate:.1f}%)")
    
    print(f"\n📁 OUTPUTS GENERATED:")
    output_path = Path(output_dir)
    
    # Count generated files
    json_files = list(output_path.rglob("*.json"))
    png_files = list(output_path.rglob("*.png"))
    csv_files = list(output_path.rglob("*.csv"))
    md_files = list(output_path.rglob("*.md"))
    
    print(f"  📊 Visualization files: {len(png_files)} PNG plots")
    print(f"  💾 Data files: {len(json_files)} JSON, {len(csv_files)} CSV")
    print(f"  📄 Reports: {len(md_files)} Markdown")
    print(f"  📁 Location: {output_path.absolute()}")
    
    print(f"\n🎉 BENCHMARKING ANALYSIS COMPLETE!")
    print("All results, visualizations, and analysis files have been generated.")
    print(f"Check the '{output_dir}' directory for detailed results.")
    
    if overall_success_rate >= 80:
        print("\n✅ EXCELLENT: High success rate achieved!")
    elif overall_success_rate >= 60:
        print("\n⚠️ GOOD: Reasonable success rate, some issues to investigate.")
    else:
        print("\n❌ NEEDS ATTENTION: Low success rate, significant issues detected.")


def check_dependencies():
    """Check if all required dependencies are available."""
    required_modules = [
        "jax", "netket", "numpy", "matplotlib", "pandas", "seaborn"
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"❌ Missing required modules: {', '.join(missing)}")
        print("Please install missing dependencies and try again.")
        return False
    
    return True


if __name__ == "__main__":
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Run benchmarking
    success = main()
    sys.exit(0 if success else 1)
