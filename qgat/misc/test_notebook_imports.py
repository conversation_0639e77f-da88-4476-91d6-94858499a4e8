"""
Test script to verify that all imports for the polynomial GCNN notebook work correctly.
"""

import sys
import os

# Add the qgat directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
qgat_dir = os.path.dirname(current_dir)
sys.path.insert(0, qgat_dir)

print("🧪 Testing Polynomial GCNN Notebook Imports")
print("=" * 50)

try:
    import jax
    import jax.numpy as jnp
    import netket as nk
    import numpy as np
    print("✅ Basic scientific libraries imported successfully")
except ImportError as e:
    print(f"❌ Failed to import basic libraries: {e}")
    sys.exit(1)

try:
    from core.polygcnn import PolyGCNN, PolyGCNN_FFT, PolyDenseEquivariantFFT
    from core.polygcnn.factory import create_poly_gcnn_for_spin_system
    print("✅ Polynomial GCNN modules imported successfully")
except ImportError as e:
    print(f"❌ Failed to import polynomial GCNN modules: {e}")
    sys.exit(1)

# Test basic functionality
print("\n🚀 Testing Basic Functionality")
print("=" * 50)

try:
    # Create a simple test system
    g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
    hilbert = nk.hilbert.Spin(s=1/2, N=4)
    
    # Create polynomial GCNN
    poly_model = create_poly_gcnn_for_spin_system(
        system_size=4,
        layers=2,
        features=[8, 16],
        degree=2
    )
    
    # Test initialization
    key = jax.random.PRNGKey(42)
    test_input = jnp.array([[1, -1, 1, -1]], dtype=jnp.float64)
    params = poly_model.init(key, test_input)
    
    # Test forward pass
    output = poly_model.apply(params, test_input)
    
    print(f"✅ Model created and tested successfully")
    print(f"   Input shape: {test_input.shape}")
    print(f"   Output shape: {output.shape}")
    print(f"   Output value: {output}")
    
except Exception as e:
    print(f"❌ Basic functionality test failed: {e}")
    sys.exit(1)

# Test different configurations
print("\n🎛️ Testing Different Configurations")
print("=" * 50)

configurations = [
    {"degree": 2, "no_activation": False, "poly_output": False},
    {"degree": 2, "no_activation": True, "poly_output": False},
    {"degree": 2, "no_activation": False, "poly_output": True},
    {"degree": 3, "no_activation": False, "poly_output": False},
]

for i, config in enumerate(configurations):
    try:
        model = create_poly_gcnn_for_spin_system(
            system_size=4,
            layers=2,
            features=[8, 16],
            **config
        )
        
        params = model.init(key, test_input)
        output = model.apply(params, test_input)
        
        print(f"✅ Configuration {i+1}: {config}")
        print(f"   Output: {output}")
        
    except Exception as e:
        print(f"❌ Configuration {i+1} failed: {e}")

print("\n🎉 All notebook import tests passed!")
print("✅ The polynomial GCNN implementation is ready for use in Jupyter notebooks")
print("✅ All configurations work correctly")
print("✅ Ready for quantum many-body applications!")
