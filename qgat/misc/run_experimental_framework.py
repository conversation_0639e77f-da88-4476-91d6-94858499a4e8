#!/usr/bin/env python3
"""
Run Experimental Framework

Main script for running experiments using the new modular experimental framework.
Supports YAML configuration files and comprehensive result tracking.
"""

import argparse
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from experiments import ExperimentRunner, load_experiment_config


def main():
    """Main entry point for experimental framework."""
    parser = argparse.ArgumentParser(
        description="Run QGAT experiments using the modular experimental framework"
    )
    
    parser.add_argument(
        "config",
        type=str,
        help="Path to experiment configuration YAML file"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default=None,
        help="Output directory for results (default: auto-generated)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show experiment plan without running"
    )
    
    parser.add_argument(
        "--sequential",
        action="store_true",
        help="Force sequential execution (disable parallel processing)"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        default=None,
        help="Maximum number of parallel workers"
    )
    
    args = parser.parse_args()
    
    # Load experiment configuration
    try:
        config = load_experiment_config(args.config)
        print(f"✅ Loaded experiment configuration: {config.name}")
        print(f"   Description: {config.description}")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return 1
    
    # Override execution settings if specified
    if args.sequential:
        config.execution.parallel = False
    
    if args.max_workers is not None:
        config.execution.max_workers = args.max_workers
    
    # Show experiment plan
    combinations = config.generate_combinations()
    total_experiments = config.get_total_experiments()
    
    print(f"\n📊 Experiment Plan:")
    print(f"   Models: {len(config.models)}")
    print(f"   Optimizers: {len(config.optimizers)}")
    print(f"   Samplers: {len(config.samplers)}")
    print(f"   Physics systems: {len(config.physics_systems)}")
    print(f"   Combinations: {len(combinations)}")
    print(f"   Runs per combination: {config.statistical.n_runs}")
    print(f"   Total experiments: {total_experiments}")
    print(f"   Parallel execution: {config.execution.parallel}")
    if config.execution.parallel:
        print(f"   Max workers: {config.execution.max_workers}")
    
    # Show model details
    print(f"\n🧠 Models:")
    for model in config.models:
        print(f"   • {model.name} ({model.type})")
        for key, value in model.config.items():
            print(f"     - {key}: {value}")
    
    # Show physics systems
    print(f"\n⚛️ Physics Systems:")
    for physics in config.physics_systems:
        print(f"   • {physics.name} ({physics.type})")
        print(f"     - sizes: {physics.sizes}")
        for key, value in physics.config.items():
            print(f"     - {key}: {value}")
    
    if args.dry_run:
        print(f"\n🔍 Dry run complete - no experiments executed")
        return 0
    
    # Create and run experiment
    try:
        runner = ExperimentRunner(config, output_dir=args.output_dir)
        results = runner.run_experiment()
        
        # Print summary
        successful = sum(1 for r in results if r.success)
        print(f"\n🎯 Experiment Summary:")
        print(f"   Total experiments: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {len(results) - successful}")
        print(f"   Success rate: {successful / len(results) * 100:.1f}%")
        
        # Show best results
        print(f"\n🏆 Best Results:")
        best_by_model = {}
        for result in results:
            if not result.success:
                continue
            
            model_name = result.model_name
            if model_name not in best_by_model or result.energy_error < best_by_model[model_name].energy_error:
                best_by_model[model_name] = result
        
        for model_name, result in best_by_model.items():
            print(f"   • {model_name}: Error = {result.energy_error:.6f}, "
                  f"Converged = {result.converged}, "
                  f"Quality = {result.get_convergence_quality()}")
        
        # Check chemical accuracy
        chemical_accurate = [r for r in results if r.is_within_chemical_accuracy()]
        if chemical_accurate:
            print(f"\n🎯 Chemical Accuracy Achieved:")
            print(f"   {len(chemical_accurate)} experiments within 0.001 Hartree")
            for result in chemical_accurate:
                print(f"   • {result.model_name}: {result.energy_error:.6f}")
        else:
            print(f"\n⚠️ No experiments achieved chemical accuracy (< 0.001 Hartree)")
        
        print(f"\n📁 Results saved to: {runner.output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
