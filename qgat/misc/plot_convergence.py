#!/usr/bin/env python3
"""
Convergence Plot Generator

Creates convergence plots from experiment results showing training progress
for multiple models in a single comparison plot.
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import argparse
import sys


def load_experiment_results(results_dir: Path):
    """Load all experiment results from a results directory."""
    results = []
    
    # Find all result files
    result_files = list(results_dir.glob("result_*.json"))
    
    for result_file in result_files:
        try:
            with open(result_file, 'r') as f:
                result_data = json.load(f)
                if result_data.get('success', False):
                    results.append(result_data)
        except Exception as e:
            print(f"Warning: Could not load {result_file}: {e}")
    
    return results


def create_convergence_plot(results, output_file: Path = None, show_plot: bool = True):
    """Create convergence plot comparing multiple models."""

    plt.figure(figsize=(14, 10))

    # Color palette for different models
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    # Track best errors for each model
    best_errors = {}
    exact_energy = results[0]['exact_energy']  # Same for all results

    for i, result in enumerate(results):
        model_name = result['model_name']
        energies = result['energies']
        n_parameters = result['n_parameters']
        final_error = result['energy_error']

        # Calculate energy errors relative to exact ground state
        energy_errors = [abs(e - exact_energy) for e in energies]
        steps = list(range(len(energies)))

        # Find best error achieved during training
        best_error = min(energy_errors)
        best_step = energy_errors.index(best_error)
        best_errors[model_name] = {'error': best_error, 'step': best_step}

        # Plot convergence
        color = colors[i % len(colors)]
        plt.semilogy(steps, energy_errors,
                    color=color, linewidth=2.5, alpha=0.8,
                    label=f'{model_name}\n({n_parameters:,} params, best: {best_error:.3f}, final: {final_error:.3f})')

        # Mark final point
        plt.scatter(steps[-1], energy_errors[-1],
                   color=color, s=100, zorder=5, alpha=0.9)

        # Mark best point with a star
        plt.scatter(best_step, best_error,
                   color=color, s=150, marker='*', zorder=6, alpha=1.0,
                   edgecolors='black', linewidth=1)

        # Add horizontal dashed line for best error achieved
        plt.axhline(y=best_error, color=color, linestyle=':', alpha=0.6, linewidth=1.5)
    
    # Add exact solution line (zero error)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=2,
                label=f'Exact Solution ({exact_energy:.6f} Ha)')

    # Add chemical accuracy line
    plt.axhline(y=0.001, color='red', linestyle='--', alpha=0.7, linewidth=2,
                label='Chemical Accuracy (0.001 Ha)')

    # Formatting
    plt.xlabel('Optimization Step', fontsize=14, fontweight='bold')
    plt.ylabel('Energy Error (Hartree)', fontsize=14, fontweight='bold')
    plt.title('Training Convergence Comparison\n8-site Heisenberg 1D Model',
              fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=10, framealpha=0.9, loc='upper right')
    plt.grid(True, alpha=0.3)
    
    # Set y-axis limits for better visualization
    all_errors = []
    for result in results:
        exact_energy_local = result['exact_energy']
        all_errors.extend([abs(e - exact_energy_local) for e in result['energies']])

    min_error = min(all_errors)
    max_error = max(all_errors)

    # Set limits to show exact solution line and chemical accuracy
    y_min = max(1e-8, min_error * 0.1)  # Don't go below 1e-8 for log scale
    y_max = max_error * 2
    plt.ylim(y_min, y_max)

    # Add text box with experiment info and best errors
    best_error_text = "\n".join([f"{name}: {data['error']:.6f}"
                                for name, data in best_errors.items()])
    info_text = (f"System: 8-site Heisenberg 1D\n"
                f"Exact Energy: {exact_energy:.6f} Ha\n\n"
                f"Best Errors Achieved:\n{best_error_text}")

    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             fontsize=9, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # Save plot
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"📊 Convergence plot saved to: {output_file}")
    
    # Show plot
    if show_plot:
        plt.show()
    
    return plt


def main():
    """Main function for convergence plotting."""
    parser = argparse.ArgumentParser(description="Generate convergence plots from experiment results")
    parser.add_argument("results_dir", type=str, help="Path to experiment results directory")
    parser.add_argument("--output", type=str, default=None, help="Output file for plot (optional)")
    parser.add_argument("--no-show", action="store_true", help="Don't display plot")
    
    args = parser.parse_args()
    
    results_dir = Path(args.results_dir)
    if not results_dir.exists():
        print(f"Error: Results directory {results_dir} does not exist")
        return 1
    
    # Load results
    print(f"📊 Loading experiment results from: {results_dir}")
    results = load_experiment_results(results_dir / "results")
    
    if not results:
        print(f"Error: No successful results found in {results_dir}")
        return 1
    
    print(f"✅ Loaded {len(results)} successful experiments")
    for result in results:
        print(f"   • {result['model_name']}: {result['n_parameters']:,} params, "
              f"error = {result['energy_error']:.6f}")
    
    # Create plot
    output_file = Path(args.output) if args.output else results_dir / "analysis" / "convergence_plot.png"
    create_convergence_plot(results, output_file=output_file, show_plot=not args.no_show)
    
    return 0


if __name__ == "__main__":
    exit(main())
