#!/usr/bin/env python3
"""
Test Optimization Fixes

Systematically test each optimization fix to resolve local minima issues.
Runs A/B testing comparing each fix against the problematic baseline.
"""

import subprocess
import sys
from pathlib import Path
import json
import time
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import numpy as np


class OptimizationFixTester:
    """Test optimization fixes systematically."""
    
    def __init__(self):
        self.results_dir = Path("experiments/results")
        self.configs_dir = Path("experiments/configs/optimization_fixes")
        self.test_results = {}
        
    def run_all_tests(self):
        """Run all optimization fix tests."""
        
        print("🔬 SYSTEMATIC OPTIMIZATION FIX TESTING")
        print("=" * 60)
        
        # Define test configurations in order of complexity
        test_configs = [
            ("baseline_problematic.yaml", "Baseline (Problematic)"),
            ("learning_rate_fix.yaml", "Learning Rate Reduction"),
            ("early_stopping_fix.yaml", "Early Stopping Adjustment"),
            ("random_seed_fix.yaml", "Random Seed Diversity"),
            ("tolerance_fix.yaml", "Tolerance Relaxation"),
            ("multiple_restarts.yaml", "Multiple Restarts"),
            ("combined_fixes.yaml", "Combined Fixes")
        ]
        
        for config_file, description in test_configs:
            print(f"\n🧪 Testing: {description}")
            print("-" * 40)
            
            success = self.run_single_test(config_file, description)
            if not success:
                print(f"❌ Test failed: {description}")
                continue
                
            print(f"✅ Test completed: {description}")
        
        # Generate comparative analysis
        self.generate_comparative_analysis()
        
    def run_single_test(self, config_file: str, description: str) -> bool:
        """Run a single optimization fix test."""
        
        config_path = self.configs_dir / config_file
        if not config_path.exists():
            print(f"❌ Config file not found: {config_path}")
            return False
        
        try:
            # Run the experiment
            cmd = [
                sys.executable, 
                "run_experimental_framework.py", 
                str(config_path)
            ]
            
            print(f"   Running: {' '.join(cmd)}")
            start_time = time.time()
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=1800  # 30 minute timeout
            )
            
            duration = time.time() - start_time
            
            if result.returncode != 0:
                print(f"   ❌ Experiment failed:")
                print(f"   STDOUT: {result.stdout[-500:]}")  # Last 500 chars
                print(f"   STDERR: {result.stderr[-500:]}")
                return False
            
            print(f"   ✅ Completed in {duration:.1f} seconds")
            
            # Extract experiment ID from output
            experiment_id = self.extract_experiment_id(result.stdout)
            if experiment_id:
                self.analyze_test_results(experiment_id, description)
            
            return True
            
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Test timed out after 30 minutes")
            return False
        except Exception as e:
            print(f"   ❌ Test failed with error: {e}")
            return False
    
    def extract_experiment_id(self, stdout: str) -> str:
        """Extract experiment ID from stdout."""
        lines = stdout.split('\n')
        for line in lines:
            if "Experiment ID:" in line:
                return line.split(":")[-1].strip()
            if "Results:" in line and "experiments/results/" in line:
                # Extract from path like: experiments/results/Test_Name_abc123/results
                path_part = line.split("experiments/results/")[-1]
                return path_part.split("/")[0].split("_")[-1]
        return None
    
    def analyze_test_results(self, experiment_id: str, description: str):
        """Analyze results from a test."""
        
        # Find the results directory
        result_dirs = list(self.results_dir.glob(f"*{experiment_id}*"))
        if not result_dirs:
            print(f"   ⚠️  No results directory found for {experiment_id}")
            return
        
        result_dir = result_dirs[0]
        results_path = result_dir / "results"
        
        if not results_path.exists():
            print(f"   ⚠️  No results subdirectory found")
            return
        
        # Load all result files
        result_files = list(results_path.glob("result_*.json"))
        results = []
        
        for result_file in result_files:
            try:
                with open(result_file, 'r') as f:
                    result_data = json.load(f)
                    if result_data.get('success', False):
                        results.append(result_data)
            except Exception as e:
                print(f"   ⚠️  Could not load {result_file}: {e}")
        
        if not results:
            print(f"   ❌ No successful results found")
            return
        
        # Analyze convergence patterns
        analysis = self.analyze_convergence_patterns(results)
        self.test_results[description] = {
            'experiment_id': experiment_id,
            'results': results,
            'analysis': analysis
        }
        
        # Print summary
        print(f"   📊 Results Summary:")
        print(f"      Total experiments: {len(results)}")
        print(f"      Success rate: {analysis['success_rate']:.1f}%")
        print(f"      Avoided local minima: {analysis['avoided_local_minima']}/{len(results)}")
        print(f"      Best error: {analysis['best_error']:.6f} Ha")
        print(f"      Average error: {analysis['avg_error']:.6f} Ha")
    
    def analyze_convergence_patterns(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze convergence patterns in results."""
        
        total_experiments = len(results)
        local_minima_count = 0
        errors = []
        final_energies = []
        
        for result in results:
            final_energy = result['final_energy']
            energy_error = result['energy_error']
            exact_energy = result['exact_energy']
            
            errors.append(energy_error)
            final_energies.append(final_energy)
            
            # Check if converged to obvious local minima (near integer values)
            if (abs(final_energy - (-1.0)) < 0.1 or 
                abs(final_energy - 0.0) < 0.1 or 
                abs(final_energy - 1.0) < 0.1):
                local_minima_count += 1
        
        avoided_local_minima = total_experiments - local_minima_count
        success_rate = (avoided_local_minima / total_experiments) * 100
        
        return {
            'total_experiments': total_experiments,
            'local_minima_count': local_minima_count,
            'avoided_local_minima': avoided_local_minima,
            'success_rate': success_rate,
            'best_error': min(errors) if errors else float('inf'),
            'avg_error': np.mean(errors) if errors else float('inf'),
            'std_error': np.std(errors) if errors else 0.0,
            'final_energies': final_energies,
            'errors': errors
        }
    
    def generate_comparative_analysis(self):
        """Generate comparative analysis of all tests."""
        
        if not self.test_results:
            print("❌ No test results to analyze")
            return
        
        print("\n" + "=" * 60)
        print("📊 COMPARATIVE ANALYSIS OF OPTIMIZATION FIXES")
        print("=" * 60)
        
        # Create comparison table
        print(f"{'Fix':<25} {'Success Rate':<12} {'Best Error':<12} {'Avg Error':<12} {'Local Minima':<12}")
        print("-" * 75)
        
        for description, data in self.test_results.items():
            analysis = data['analysis']
            print(f"{description:<25} "
                  f"{analysis['success_rate']:>10.1f}% "
                  f"{analysis['best_error']:>10.6f} "
                  f"{analysis['avg_error']:>10.6f} "
                  f"{analysis['local_minima_count']:>10d}")
        
        # Generate plots
        self.create_comparison_plots()
        
        # Recommendations
        self.generate_recommendations()
    
    def create_comparison_plots(self):
        """Create comparison plots."""
        
        if len(self.test_results) < 2:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        descriptions = list(self.test_results.keys())
        success_rates = [self.test_results[desc]['analysis']['success_rate'] for desc in descriptions]
        best_errors = [self.test_results[desc]['analysis']['best_error'] for desc in descriptions]
        avg_errors = [self.test_results[desc]['analysis']['avg_error'] for desc in descriptions]
        local_minima_counts = [self.test_results[desc]['analysis']['local_minima_count'] for desc in descriptions]
        
        # Success rate comparison
        bars1 = ax1.bar(range(len(descriptions)), success_rates, color='green', alpha=0.7)
        ax1.set_title('Success Rate (% Avoiding Local Minima)', fontweight='bold')
        ax1.set_ylabel('Success Rate (%)')
        ax1.set_xticks(range(len(descriptions)))
        ax1.set_xticklabels([desc.replace(' ', '\n') for desc in descriptions], rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars1, success_rates):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Best error comparison (log scale)
        ax2.bar(range(len(descriptions)), best_errors, color='blue', alpha=0.7)
        ax2.set_title('Best Error Achieved', fontweight='bold')
        ax2.set_ylabel('Energy Error (Ha)')
        ax2.set_yscale('log')
        ax2.set_xticks(range(len(descriptions)))
        ax2.set_xticklabels([desc.replace(' ', '\n') for desc in descriptions], rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # Average error comparison (log scale)
        ax3.bar(range(len(descriptions)), avg_errors, color='orange', alpha=0.7)
        ax3.set_title('Average Error', fontweight='bold')
        ax3.set_ylabel('Energy Error (Ha)')
        ax3.set_yscale('log')
        ax3.set_xticks(range(len(descriptions)))
        ax3.set_xticklabels([desc.replace(' ', '\n') for desc in descriptions], rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        
        # Local minima count
        bars4 = ax4.bar(range(len(descriptions)), local_minima_counts, color='red', alpha=0.7)
        ax4.set_title('Local Minima Convergence Count', fontweight='bold')
        ax4.set_ylabel('Number of Local Minima')
        ax4.set_xticks(range(len(descriptions)))
        ax4.set_xticklabels([desc.replace(' ', '\n') for desc in descriptions], rotation=45, ha='right')
        ax4.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars4, local_minima_counts):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # Save plot
        output_file = "optimization_fixes_comparison.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n📊 Comparison plots saved to: {output_file}")
        
        plt.show()
    
    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        
        print("\n💡 RECOMMENDATIONS:")
        print("-" * 60)
        
        # Find best performing fix
        best_fix = max(self.test_results.items(), 
                      key=lambda x: x[1]['analysis']['success_rate'])
        
        print(f"🏆 BEST PERFORMING FIX: {best_fix[0]}")
        print(f"   Success Rate: {best_fix[1]['analysis']['success_rate']:.1f}%")
        print(f"   Best Error: {best_fix[1]['analysis']['best_error']:.6f} Ha")
        
        # Identify most effective individual fixes
        individual_fixes = [k for k in self.test_results.keys() 
                          if k not in ['Baseline (Problematic)', 'Combined Fixes']]
        
        if individual_fixes:
            best_individual = max([(k, self.test_results[k]) for k in individual_fixes],
                                key=lambda x: x[1]['analysis']['success_rate'])
            
            print(f"\n🎯 BEST INDIVIDUAL FIX: {best_individual[0]}")
            print(f"   Success Rate: {best_individual[1]['analysis']['success_rate']:.1f}%")
        
        # Check if combined fixes are better
        if 'Combined Fixes' in self.test_results:
            combined_success = self.test_results['Combined Fixes']['analysis']['success_rate']
            print(f"\n🔧 COMBINED FIXES EFFECTIVENESS:")
            print(f"   Success Rate: {combined_success:.1f}%")
            
            if combined_success > best_fix[1]['analysis']['success_rate']:
                print("   ✅ Combined fixes are most effective!")
            else:
                print("   ⚠️  Individual fixes may be sufficient")


def main():
    """Main function."""
    tester = OptimizationFixTester()
    tester.run_all_tests()
    return 0


if __name__ == "__main__":
    exit(main())
