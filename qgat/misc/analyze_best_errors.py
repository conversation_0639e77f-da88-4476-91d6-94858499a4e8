#!/usr/bin/env python3
"""
Best Error Analysis

Analyzes the best errors achieved during training for each model
and provides detailed convergence statistics.
"""

import json
import numpy as np
from pathlib import Path
import sys


def analyze_best_errors(results_dir: Path):
    """Analyze best errors achieved by each model during training."""
    
    # Load results
    result_files = list((results_dir / "results").glob("result_*.json"))
    results = []
    
    for result_file in result_files:
        try:
            with open(result_file, 'r') as f:
                result_data = json.load(f)
                if result_data.get('success', False):
                    results.append(result_data)
        except Exception as e:
            print(f"Warning: Could not load {result_file}: {e}")
    
    if not results:
        print("No successful results found!")
        return
    
    print("🔬 DETAILED CONVERGENCE ANALYSIS")
    print("=" * 60)
    
    exact_energy = results[0]['exact_energy']
    print(f"📊 System: 8-site Heisenberg 1D")
    print(f"⚛️ Exact Ground State Energy: {exact_energy:.8f} Ha")
    print()
    
    # Analyze each model
    for i, result in enumerate(results):
        model_name = result['model_name']
        energies = result['energies']
        n_parameters = result['n_parameters']
        converged = result['converged']
        convergence_step = result['convergence_step']
        
        # Calculate energy errors
        energy_errors = [abs(e - exact_energy) for e in energies]
        
        # Find best error and when it occurred
        best_error = min(energy_errors)
        best_step = energy_errors.index(best_error)
        best_energy = energies[best_step]
        
        # Final error
        final_error = energy_errors[-1]
        final_energy = energies[-1]
        
        # Calculate improvement metrics
        initial_error = energy_errors[0]
        total_improvement = initial_error - final_error
        best_improvement = initial_error - best_error
        
        # Calculate convergence rate (steps to reach 90% of best improvement)
        target_error = initial_error - 0.9 * best_improvement
        convergence_90_step = next((i for i, err in enumerate(energy_errors) 
                                  if err <= target_error), len(energy_errors))
        
        print(f"🏆 MODEL {i+1}: {model_name}")
        print(f"   Parameters: {n_parameters:,}")
        print(f"   Converged: {'✅ Yes' if converged else '❌ No'} (step {convergence_step})")
        print()
        print(f"   📈 TRAINING PROGRESS:")
        print(f"   Initial Error:  {initial_error:.6f} Ha")
        print(f"   Best Error:     {best_error:.6f} Ha (step {best_step})")
        print(f"   Final Error:    {final_error:.6f} Ha (step {len(energies)-1})")
        print()
        print(f"   🎯 ENERGY VALUES:")
        print(f"   Best Energy:    {best_energy:.6f} Ha")
        print(f"   Final Energy:   {final_energy:.6f} Ha")
        print(f"   Exact Energy:   {exact_energy:.6f} Ha")
        print()
        print(f"   📊 IMPROVEMENT METRICS:")
        print(f"   Total Improvement:     {total_improvement:.6f} Ha")
        print(f"   Best Improvement:      {best_improvement:.6f} Ha")
        print(f"   Improvement Efficiency: {(best_improvement/total_improvement)*100:.1f}%")
        print(f"   90% Convergence Step:   {convergence_90_step}")
        print()
        print(f"   🔬 RELATIVE PERFORMANCE:")
        print(f"   Best Relative Error:    {(best_error/abs(exact_energy))*100:.3f}%")
        print(f"   Final Relative Error:   {(final_error/abs(exact_energy))*100:.3f}%")
        print(f"   Chemical Accuracy:      {'✅ Yes' if best_error < 0.001 else '❌ No'}")
        print()
        print("-" * 60)
        print()
    
    # Comparison analysis
    if len(results) > 1:
        print("🏁 COMPARATIVE ANALYSIS")
        print("=" * 60)
        
        # Sort by best error
        sorted_results = sorted(results, key=lambda r: min([abs(e - exact_energy) for e in r['energies']]))
        
        best_model = sorted_results[0]
        best_model_name = best_model['model_name']
        best_model_error = min([abs(e - exact_energy) for e in best_model['energies']])
        best_model_params = best_model['n_parameters']
        
        print(f"🥇 WINNER: {best_model_name}")
        print(f"   Best Error: {best_model_error:.6f} Ha")
        print(f"   Parameters: {best_model_params:,}")
        print()
        
        # Compare with other models
        for result in sorted_results[1:]:
            model_name = result['model_name']
            model_error = min([abs(e - exact_energy) for e in result['energies']])
            model_params = result['n_parameters']
            
            error_ratio = model_error / best_model_error
            param_ratio = model_params / best_model_params
            
            print(f"📊 {model_name} vs {best_model_name}:")
            print(f"   Error Ratio: {error_ratio:.2f}x worse")
            print(f"   Parameter Ratio: {param_ratio:.2f}x {'more' if param_ratio > 1 else 'fewer'}")
            print(f"   Efficiency: {best_model_name} is {((error_ratio-1)*100):.1f}% more accurate")
            print(f"              with {((1-1/param_ratio)*100):.1f}% {'fewer' if param_ratio > 1 else 'more'} parameters")
            print()


def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python analyze_best_errors.py <results_directory>")
        return 1
    
    results_dir = Path(sys.argv[1])
    if not results_dir.exists():
        print(f"Error: Directory {results_dir} does not exist")
        return 1
    
    analyze_best_errors(results_dir)
    return 0


if __name__ == "__main__":
    exit(main())
