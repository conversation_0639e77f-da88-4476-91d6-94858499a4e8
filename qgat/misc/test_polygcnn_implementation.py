"""
Unit tests for Polynomial GCNN implementation.

Tests the core functionality of polynomial layers and models to ensure
they work correctly and preserve symmetry properties.
"""

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

import sys
import os

# Add the qgat directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
qgat_dir = os.path.dirname(current_dir)
sys.path.insert(0, qgat_dir)

from core.polygcnn import PolyDenseEquivariantFFT, PolyGCNN_FFT, PolyGCNN
from core.polygcnn.factory import create_poly_gcnn_for_spin_system


class TestPolyDenseEquivariantFFT:
    """Test polynomial dense equivariant layers."""
    
    def setup_method(self):
        """Setup test fixtures."""
        # Create simple 1D chain
        self.system_size = 4
        self.g = nk.graph.Hypercube(length=self.system_size, n_dim=1, pbc=True)
        self.symmetries = self.g.translation_group()
        
        # Create test input
        self.batch_size = 2
        self.input_features = 4
        self.output_features = 8
        self.test_input = jnp.ones((self.batch_size, self.input_features, len(self.symmetries)))
        
    def test_poly_layer_creation(self):
        """Test that polynomial layers can be created."""
        layer = PolyDenseEquivariantFFT(
            product_table=self.symmetries,
            shape=(self.system_size,),
            features=self.output_features,
            degree=2,
        )

        # Initialize layer
        key = jax.random.PRNGKey(42)
        params = layer.init(key, self.test_input)

        # Check that parameters were created (layer should initialize successfully)
        assert params is not None
        assert 'params' in params
        
    def test_poly_layer_forward_pass(self):
        """Test forward pass through polynomial layer."""
        layer = PolyDenseEquivariantFFT(
            product_table=self.symmetries,
            shape=(self.system_size,),
            features=self.output_features,
            degree=3,
        )
        
        # Initialize and run
        key = jax.random.PRNGKey(42)
        params = layer.init(key, self.test_input)
        output = layer.apply(params, self.test_input)
        
        # Check output shape
        expected_shape = (self.batch_size, self.output_features, len(self.symmetries))
        assert output.shape == expected_shape
        
    def test_different_degrees(self):
        """Test polynomial layers with different degrees."""
        for degree in [1, 2, 3, 4]:
            layer = PolyDenseEquivariantFFT(
                product_table=self.symmetries,
                shape=(self.system_size,),
                features=self.output_features,
                degree=degree,
            )
            
            key = jax.random.PRNGKey(42)
            params = layer.init(key, self.test_input)
            output = layer.apply(params, self.test_input)
            
            # Should work for all degrees
            assert output.shape[0] == self.batch_size
            assert output.shape[1] == self.output_features
            
    def test_symmetry_preservation(self):
        """Test that polynomial layers preserve symmetry properties."""
        layer = PolyDenseEquivariantFFT(
            product_table=self.symmetries,
            shape=(self.system_size,),
            features=self.output_features,
            degree=2,
        )
        
        key = jax.random.PRNGKey(42)
        params = layer.init(key, self.test_input)
        
        # Create symmetric input (all same values)
        symmetric_input = jnp.ones_like(self.test_input)
        output = layer.apply(params, symmetric_input)
        
        # Output should be finite and well-behaved
        assert jnp.all(jnp.isfinite(output))
        assert not jnp.all(output == 0)  # Should produce non-trivial output


class TestPolyGCNN_FFT:
    """Test polynomial GCNN models."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.system_size = 4
        self.g = nk.graph.Hypercube(length=self.system_size, n_dim=1, pbc=True)
        self.symmetries = self.g.translation_group()
        
        # Create test spin configuration
        self.batch_size = 2
        self.test_input = jnp.array([
            [1, -1, 1, -1],  # Alternating spins
            [1, 1, -1, -1]   # Domain wall
        ], dtype=jnp.float64)
        
    def test_poly_gcnn_creation(self):
        """Test that polynomial GCNN can be created."""
        model = PolyGCNN_FFT(
            symmetries=self.symmetries,
            product_table=self.symmetries,
            shape=(self.system_size,),
            layers=2,
            features=[8, 16],
            characters=jnp.ones(len(self.symmetries)),
            degree=2,
        )
        
        # Initialize model
        key = jax.random.PRNGKey(42)
        params = model.init(key, self.test_input)

        # Should initialize successfully
        assert params is not None
        assert 'params' in params
        
    def test_poly_gcnn_forward_pass(self):
        """Test forward pass through polynomial GCNN."""
        model = PolyGCNN_FFT(
            symmetries=self.symmetries,
            product_table=self.symmetries,
            shape=(self.system_size,),
            layers=2,
            features=[8, 16],
            characters=jnp.ones(len(self.symmetries)),
            degree=2,
        )
        
        key = jax.random.PRNGKey(42)
        params = model.init(key, self.test_input)
        output = model.apply(params, self.test_input)
        
        # Check output shape (should be scalar per batch element)
        assert output.shape == (self.batch_size,)
        assert jnp.all(jnp.isfinite(output))
        
    def test_no_activation_mode(self):
        """Test polynomial GCNN with no_activation flag."""
        model = PolyGCNN_FFT(
            symmetries=self.symmetries,
            product_table=self.symmetries,
            shape=(self.system_size,),
            layers=3,
            features=[8, 16, 32],
            characters=jnp.ones(len(self.symmetries)),
            degree=2,
            no_activation=True,  # Pure polynomial
        )
        
        key = jax.random.PRNGKey(42)
        params = model.init(key, self.test_input)
        output = model.apply(params, self.test_input)
        
        # Should still work
        assert output.shape == (self.batch_size,)
        assert jnp.all(jnp.isfinite(output))
        
    def test_poly_output_mode(self):
        """Test polynomial GCNN with poly_output flag."""
        model = PolyGCNN_FFT(
            symmetries=self.symmetries,
            product_table=self.symmetries,
            shape=(self.system_size,),
            layers=2,
            features=[8, 16],
            characters=jnp.ones(len(self.symmetries)),
            degree=2,
            poly_output=True,  # Simple sum output
        )
        
        key = jax.random.PRNGKey(42)
        params = model.init(key, self.test_input)
        output = model.apply(params, self.test_input)
        
        # Should still produce scalar output per batch
        assert output.shape == (self.batch_size,)
        assert jnp.all(jnp.isfinite(output))
        
    def test_per_layer_degrees(self):
        """Test polynomial GCNN with different degrees per layer."""
        model = PolyGCNN_FFT(
            symmetries=self.symmetries,
            product_table=self.symmetries,
            shape=(self.system_size,),
            layers=3,
            features=[8, 16, 32],
            characters=jnp.ones(len(self.symmetries)),
            poly_degrees=(2, 3),  # Different degrees per layer
        )
        
        key = jax.random.PRNGKey(42)
        params = model.init(key, self.test_input)
        output = model.apply(params, self.test_input)
        
        assert output.shape == (self.batch_size,)
        assert jnp.all(jnp.isfinite(output))


class TestPolyGCNNFactory:
    """Test factory functions."""
    
    def test_factory_function(self):
        """Test PolyGCNN factory function."""
        # Create simple system
        g = nk.graph.Hypercube(length=4, n_dim=1, pbc=True)
        symmetries = g.translation_group()

        model = PolyGCNN(
            symmetries=symmetries,
            product_table=symmetries,
            shape=(4,),
            layers=2,
            features=[8, 16],
            characters=jnp.ones(len(symmetries)),
            degree=2,
            mode="fft"
        )

        # Should create PolyGCNN_FFT instance
        assert isinstance(model, PolyGCNN_FFT)
        
    def test_spin_system_factory(self):
        """Test convenience factory for spin systems."""
        model = create_poly_gcnn_for_spin_system(
            system_size=4,
            layers=2,
            features=[8, 16],
            degree=2,
            dimension=1
        )
        
        # Should create working model
        assert isinstance(model, PolyGCNN_FFT)
        
        # Test with 2D system
        model_2d = create_poly_gcnn_for_spin_system(
            system_size=4,  # 2x2 system
            layers=2,
            features=[8, 16],
            degree=2,
            dimension=2
        )
        
        assert isinstance(model_2d, PolyGCNN_FFT)


if __name__ == "__main__":
    # Run basic tests
    test_poly_layer = TestPolyDenseEquivariantFFT()
    test_poly_layer.setup_method()
    test_poly_layer.test_poly_layer_creation()
    test_poly_layer.test_poly_layer_forward_pass()
    print("✅ Polynomial layer tests passed!")
    
    test_poly_gcnn = TestPolyGCNN_FFT()
    test_poly_gcnn.setup_method()
    test_poly_gcnn.test_poly_gcnn_creation()
    test_poly_gcnn.test_poly_gcnn_forward_pass()
    print("✅ Polynomial GCNN tests passed!")
    
    test_factory = TestPolyGCNNFactory()
    test_factory.test_factory_function()
    test_factory.test_spin_system_factory()
    print("✅ Factory function tests passed!")
    
    print("🎉 All tests passed! Polynomial GCNN implementation is working.")
